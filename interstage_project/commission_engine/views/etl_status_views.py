import logging

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.services.etl_sync_status_service import (
    get_all_failed_cron_jobs,
    get_all_failed_manual_jobs,
    get_sync_without_end_time,
    get_sync_without_end_time_across_clients,
    mark_cron_job_as_failed,
    mark_manual_job_as_failed,
    mark_sync_as_skipped,
    remove_etl_lock_for_e2e_id,
)
from commission_engine.services.etl_tasks_service import retry_cron_job
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope

logger = logging.getLogger(__name__)


class AllRunningSync(APIView):
    """
    If the client_id is provided, it will return all the running syncs for that client
    else it will return all the running syncs across all the clients.
    If subscription_plans is provided, only returns syncs for clients with those subscription plans.
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ETLSTATUS.value,
                RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, request):
        client_id = request.GET.get("client_id", None)
        subscription_plans = request.GET.getlist("subscription_plans", None)

        if client_id:
            running_syncs = get_sync_without_end_time(client_id)
        else:
            running_syncs = get_sync_without_end_time_across_clients(
                subscription_plans=subscription_plans if subscription_plans else None
            )
        return Response(running_syncs, status=status.HTTP_200_OK)


class RemoveEtlLock(APIView):
    """
    Removes lock for the given e2e sync run id
    """

    @method_decorator(
        requires_scope(
            ["write:everstageopsadmin", RbacPermissions.MANAGE_ETLSTATUS.value]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        user_email = request.user
        try:
            client_id = int(params.get("client_id"))
            e2e_sync_run_id = params.get("e2e_sync_run_id")
            remove_etl_lock_for_e2e_id(client_id, e2e_sync_run_id, user_email)
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_200_OK,
            )
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class AllFailedCronJobs(APIView):
    """
    Returns all the failed cron jobs
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ETLSTATUS.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, request):

        try:
            failed_cron_jobs = get_all_failed_cron_jobs()
            return Response(failed_cron_jobs, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class MarkCronJobAsFailed(APIView):
    """
    Marks a 'partially failed' cron job as failed
    """

    @method_decorator(
        requires_scope(
            ["write:everstageopsadmin", RbacPermissions.MANAGE_ETLSTATUS.value]
        ),
        name="dispatch",
    )
    def post(self, request):
        client_id = request.GET.get("client_id")
        e2e_sync_run_id = request.GET.get("e2e_sync_run_id")
        user_email = request.user

        try:
            mark_cron_job_as_failed(client_id, e2e_sync_run_id, user_email)
            return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class RetryCronJob(APIView):
    """
    Retries a cron job
    """

    @method_decorator(
        requires_scope(
            ["write:everstageopsadmin", RbacPermissions.MANAGE_ETLSTATUS.value]
        ),
        name="dispatch",
    )
    def post(self, request):
        client_id = request.GET.get("client_id")
        e2e_sync_run_id = request.GET.get("e2e_sync_run_id")

        try:
            retry_cron_job(client_id, e2e_sync_run_id)
            return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class AllFailedManualJobs(APIView):
    """
    Returns all the failed manual jobs
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ADMINUI.value), name="dispatch"
    )
    def get(self, request):
        try:
            failed_manual_jobs = get_all_failed_manual_jobs()
            return Response(failed_manual_jobs, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class MarkManualJobAsFailed(APIView):
    """
    Marks a 'partially failed' manual job as failed
    """

    @method_decorator(requires_scope("write:everstageopsadmin"), name="dispatch")
    def post(self, request):
        client_id = request.GET.get("client_id")
        e2e_sync_run_id = request.GET.get("e2e_sync_run_id")
        user_email = request.user

        try:
            mark_manual_job_as_failed(client_id, e2e_sync_run_id, user_email)
            return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class MarkSyncAsSkipped(APIView):
    """
    Marks a sync as skipped
    """

    @method_decorator(
        requires_scope(
            ["write:everstageopsadmin", RbacPermissions.MANAGE_ETLSTATUS.value]
        ),
        name="dispatch",
    )
    def post(self, request):
        client_id = request.GET.get("client_id")
        e2e_sync_run_id = request.GET.get("e2e_sync_run_id")
        user_email = request.user
        reason = request.GET.get("reason")

        logger.info(
            f"Marking sync as skipped for client_id: {client_id}, e2e_sync_run_id: {e2e_sync_run_id}, reason: {reason}"
        )
        try:
            mark_sync_as_skipped(client_id, e2e_sync_run_id, user_email, reason)
            return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)
