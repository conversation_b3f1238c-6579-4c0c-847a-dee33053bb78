import traceback

from dateutil.parser import parse
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.custom_exceptions.tracing_exceptions import (
    EmployeePayrollNotFound,
    LineItemNotFound,
    PlanCriteriaNotFound,
    TracingException,
)
from commission_engine.services.tracing_services.tracing_services import TracingService
from commission_engine.utils.date_utils import end_of_day, start_of_day
from commission_engine.utils.general_data import RbacPermissions
from commission_engine.utils.payout_utils import can_user_view_payout_numbers
from interstage_project.auth_utils import requires_scope
from interstage_project.global_utils.localization_utils import (
    get_localized_message_utils,
)
from spm.services.plan_details_services import is_payee_in_plan


class CriteriaTracing(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_PAYOUTS.value,
                RbacPermissions.VIEW_STATEMENTS.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        try:
            plan_id = request.data["plan_id"]
            criteria_id = request.data["criteria_id"]
            payee_email = request.data["payee_email"]
            login_user_id = str(request.user)
            client_id = request.client_id
            period_start_date = start_of_day(
                parse(request.data["period_start_date"], dayfirst=True)
            )
            period_end_date = end_of_day(
                parse(request.data["period_end_date"], dayfirst=True)
            )
            if not is_payee_in_plan(client_id, payee_email, plan_id):
                return Response(
                    {
                        "status": "FAILED",
                        "reason": get_localized_message_utils(
                            "PAYEE_NOT_FOUND_IN_PLAN",
                            client_id,
                        ),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if not can_user_view_payout_numbers(client_id, login_user_id, payee_email):
                return Response(
                    {
                        "status": "FAILED",
                        "reason": get_localized_message_utils(
                            "UNAUTHORIZED",
                            client_id,
                        ),
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            row_key = request.data["row_key"]
            is_base_currency = request.data.get("is_base_currency")
            res = TracingService(request.client_id).get_criteria_trace(
                plan_id,
                criteria_id,
                payee_email,
                period_start_date,
                period_end_date,
                row_key,
                is_base_currency=is_base_currency,
            )
            return Response(res, status=status.HTTP_200_OK)
        except TracingException as ex:
            return Response(
                {"status": "FAILED", "reason": str(ex)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception:
            traceback.print_exc()
            return Response({"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST)
