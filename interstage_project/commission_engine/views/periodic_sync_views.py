from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.services.periodic_sync_services.daily_sync_status_service import (
    SyncInDeploymentWindowException,
    add_new_daily_sync,
    delete_daily_sync,
    get_daily_sync_status_across_clients,
    update_daily_sync_status,
)
from commission_engine.services.periodic_sync_services.exceptions import (
    AdditionalDeleteSyncError,
    AdditionalDeleteSyncExistsError,
    DailySyncError,
)
from commission_engine.services.periodic_sync_services.hard_delete_sync_status_service import (
    add_new_hard_delete_sync,
    delete_hard_delete_sync,
    get_hard_delete_sync_status_for_client,
    update_hard_delete_sync_status,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import LogWithContext


class AllDailySyncStatus(APIView):
    """
    Returns daily sync status of all clients
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, _):
        running_syncs = get_daily_sync_status_across_clients()
        return Response(running_syncs, status=status.HTTP_200_OK)


class UpdateDailySyncForClient(APIView):
    """
    Update daily sync status for a particular client
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        try:
            client_id = params.get("client_id")
            sync_status = params.get("status")
            task_name = params.get("task_name")
            cron_expression = params.get("cron_expression")
            verify_upstream = params.get("verify_upstream", True)
            description = params.get("description", "")
            description = description.strip() + " - " + str(request.user)

            update_daily_sync_status(
                client_id=client_id,
                status=sync_status,
                task_name=task_name,
                cron_expression=cron_expression,
                verify_upstream=verify_upstream,
                description=description,
            )
            logger = LogWithContext({"client_id": params.get("client_id")})
            logged_in_user = request.user
            logger.info(
                f"{logged_in_user} has updated the daily sync status for client id: {params.get('client_id')}"
            )
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_200_OK,
            )
        except DailySyncError:
            return Response(
                {"error_msg": "Tasks must be separated by at least one hour"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class AddNewDailySyncForClient(APIView):
    """
    Add new daily sync entry for a particular client
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        try:
            add_new_daily_sync(
                client_id=params.get("client_id"),
                cron_expression=params.get("cron_expression"),
            )
            logger = LogWithContext({"client_id": params.get("client_id")})
            logged_in_user = request.user
            logger.info(
                f"{logged_in_user} has scheduled a new daily sync for client id: {params.get('client_id')}"
            )
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_201_CREATED,
            )
        except DailySyncError:
            return Response(
                {"error_msg": "Tasks must be separated by at least one hour"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except SyncInDeploymentWindowException as e:
            return Response(
                {"error_msg": e.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DeleteDailySyncForClient(APIView):
    """
    Delete daily sync entry for a particular client
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        try:
            delete_daily_sync(
                client_id=params.get("client_id"),
                task_name=params.get("task_name"),
            )
            logger = LogWithContext({"client_id": params.get("client_id")})
            logged_in_user = request.user
            logger.info(
                f"{logged_in_user} has deleted the daily sync scheduled for client id: {params.get('client_id')}"
            )
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AllAdditionalDeleteSyncStatusForClient(APIView):
    """
    Returns daily sync status of all clients
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_INTEGRATIONS.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, request):
        try:
            client_id = request.query_params.get("client_id")
            running_syncs = get_hard_delete_sync_status_for_client(client_id)
            return Response(running_syncs, status=status.HTTP_200_OK)
        except ValueError as verr:
            return Response(
                {"error_msg": str(verr)}, status=status.HTTP_400_BAD_REQUEST
            )


class UpdateAdditionalDeleteSyncForClient(APIView):
    """
    Update daily sync status for a particular client
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_INTEGRATIONS.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        try:
            update_hard_delete_sync_status(
                client_id=params.get("client_id"),
                status=params.get("status"),
                cron_expression=params.get("cron_expression"),
                audit=request.audit,
            )
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_200_OK,
            )
        except AdditionalDeleteSyncError:
            return Response(
                {"error_msg": "Tasks must be separated by at least two hours"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class AddNewAdditionalDeleteSyncForClient(APIView):
    """
    Add new daily sync entry for a particular client
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        try:
            add_new_hard_delete_sync(
                client_id=params.get("client_id"),
                cron_expression=params.get("cron_expression"),
                audit=request.audit,
            )
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_201_CREATED,
            )
        except AdditionalDeleteSyncError:
            return Response(
                {"error_msg": "Tasks must be separated by at least two hours"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except AdditionalDeleteSyncExistsError:
            return Response(
                {"error_msg": "An additional delete sync exists for the client"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DeleteAdditionalDeleteSyncForClient(APIView):
    """
    Delete daily sync entry for a particular client
    """

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        try:
            delete_hard_delete_sync(
                client_id=params.get("client_id"), audit=request.audit
            )
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
