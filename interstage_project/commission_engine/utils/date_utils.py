import calendar
import datetime
import logging
from datetime import date
from typing import List, <PERSON><PERSON>, Union
from uuid import UUID

import pandas as pd
import pytz
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from django.core.cache import caches
from django.utils import timezone
from django.utils.timezone import make_aware

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.utils.general_data import Freq, static_frequencies
from spm.accessors.custom_calendar_accessors import CustomPeriodsAccessor
from spm.accessors.variable_accessor import VariableAccessor, VariableDataTypeAccessor
from spm.services.custom_calendar_services import get_period_for_curr_time

from .general_data import TimePeriodWt

logger = logging.getLogger(__name__)


MONTHS = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
]

PANDAS_MAX_TIMESTAMP = pd.Timestamp.max

cache = caches["default"]

MICROSECONDS_TIMESTAMP_LENGTH = 16
MILLISECONDS_TIMESTAMP_LENGTH = 13


def get_month_num(month):
    return MONTHS.index(month) + 1


def get_period_start_months(
    quota_schedule_type: str, fiscal_start_month: int, fiscal_year: int
) -> List[str]:
    """
    Return the period start months for the given quota_schedule_type, fiscal_start_month and fiscal_year
    Return format: ['Apr 2021', 'Jul 2021', 'Oct 2021', 'Jan 2022']
    """
    months = MONTHS
    start_month = fiscal_start_month - 1
    fiscal_year = int(fiscal_year)

    def get_year(month_idx):
        if start_month == 0:  # January
            return fiscal_year
        return fiscal_year if month_idx < start_month else fiscal_year - 1

    if quota_schedule_type == "Monthly":
        return [
            f"{months[(start_month + i) % 12]} {get_year((start_month + i) % 12)}"
            for i in range(0, 12)
        ]
    elif quota_schedule_type == "Quarterly":
        return [
            f"{months[(start_month + i) % 12]} {get_year((start_month + i) % 12)}"
            for i in (0, 3, 6, 9)
        ]
    elif quota_schedule_type == "Halfyearly":
        return [
            f"{months[(start_month + i) % 12]} {get_year((start_month + i) % 12)}"
            for i in (0, 6)
        ]
    elif quota_schedule_type == "Annual":
        return [f"{months[start_month]} {get_year(start_month)}"]

    else:
        return []


def make_aware_wrapper(_date):
    _date = make_aware(_date) if _date.tzinfo is None else _date
    return _date


def get_fiscal_calendar(start_month):
    arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    ind = arr.index(start_month)
    fiscal_year = arr[ind:12] + arr[:ind]
    return fiscal_year


def start_of_day(_date: datetime.datetime) -> datetime.datetime:
    _date = _date.replace(minute=0, hour=0, second=0, microsecond=0)
    return _date


def end_of_day(_date: datetime.datetime) -> datetime.datetime:
    _date = _date.replace(minute=59, hour=23, second=59, microsecond=999999)
    return _date


def last_day_of_month(_date: datetime.datetime) -> datetime.datetime:
    ld = calendar.monthrange(_date.year, _date.month)[1]
    return end_of_day(datetime.datetime(_date.year, _date.month, ld))


def first_day_of_month(_date: datetime.datetime) -> datetime.datetime:
    sd = start_of_day(datetime.datetime(_date.year, _date.month, 1))
    return sd


def last_day_of_month_from_month_year(month: str, year: str | int):
    """
    Get last day of month from month and year. `month` should be in the format
    Jan, Feb, Mar, etc.
    """
    month_num = get_month_num(month)
    last_day = make_aware(last_day_of_month(datetime.datetime(int(year), month_num, 1)))
    return last_day


def first_day_of_month_from_month_year(month: str, year: str | int):
    """
    Get first day of month from month and year. `month` should be in the format
    Jan, Feb, Mar, etc.
    """
    month_num = get_month_num(month)
    first_day = make_aware(
        first_day_of_month(datetime.datetime(int(year), month_num, 1))
    )
    return first_day


def get_quarters(fiscal_yr):
    qs = []
    for i in range(4):
        qs.append((fiscal_yr[i * 3], fiscal_yr[(i * 3) + 2]))
    return qs


def get_half_yearly(fiscal_yr):
    hs = []
    for i in range(2):
        hs.append((fiscal_yr[i * 6], fiscal_yr[(i * 6) + 5]))
    return hs


def find_period(month_no, start_month, time_prd):
    time_prds = []
    fiscal_yr = get_fiscal_calendar(start_month)
    month_ind = fiscal_yr.index(month_no)
    if time_prd.lower() == Freq.QUARTERLY.value:
        time_prds = get_quarters(fiscal_yr)
    elif time_prd.lower() == Freq.HALFYEARLY.value:
        time_prds = get_half_yearly(fiscal_yr)
    elif time_prd.lower() == Freq.ANNUAL.value:
        end_month = (start_month - 1) % 12 if (start_month - 1) % 12 > 0 else 12
        time_prds = [(start_month, end_month)]
    elif time_prd.lower() == Freq.MONTHLY.value:
        time_prds = []
        for i in fiscal_yr:
            time_prds.append((i, i))
    period_index = -1
    period = None
    if time_prd.lower() == Freq.ANNUAL.value:
        period_index = 0
        period = time_prds[0]
    else:
        for ind, q in enumerate(time_prds):
            if month_ind >= fiscal_yr.index(q[0]) and month_ind <= fiscal_yr.index(
                q[1]
            ):
                period_index = ind
                period = q
                break
    return {"index": period_index, "period": period}


def get_period_start_and_end_date(
    curr_time,
    start_month,
    time_prd,
    prev_required=False,
    next_required=False,
    client_id=None,
):
    if client_id and time_prd.lower() not in static_frequencies:
        return get_period_for_curr_time(
            client_id, curr_time, time_prd, prev_required, next_required
        )
    month_no = curr_time.month
    period_result = find_period(month_no, start_month, time_prd)
    period = period_result["period"]
    start_yr = curr_time.year
    end_yr = curr_time.year
    # get period start time
    if curr_time.month >= start_month and period[0] < start_month:
        start_yr += 1
    elif curr_time.month < start_month and period[0] >= start_month:
        start_yr -= 1
    start_date = start_of_day(make_aware(datetime.datetime(start_yr, period[0], 1)))
    # get period end time
    if curr_time.month >= start_month and period[1] < start_month:
        end_yr += 1
    elif curr_time.month < start_month and period[1] >= start_month:
        end_yr -= 1
    end_date = last_day_of_month(datetime.datetime(end_yr, period[1], 1))
    end_date = make_aware_wrapper(end_date)
    start_date = make_aware_wrapper(start_date)
    result = {"start_date": start_date, "end_date": end_date}
    result["period_index"] = period_result["index"]
    if prev_required:
        wt = TimePeriodWt[time_prd]
        prev_start_date = start_of_day(start_date - relativedelta(months=wt))
        prev_end_date = last_day_of_month(end_date - relativedelta(months=wt))
        prev_end_date = make_aware_wrapper(prev_end_date)
        prev_start_date = make_aware_wrapper(prev_start_date)
        result["prev_start_date"] = prev_start_date
        result["prev_end_date"] = prev_end_date
    if next_required:
        wt = TimePeriodWt[time_prd]
        next_start_date = start_of_day(start_date + relativedelta(months=wt))
        next_end_date = last_day_of_month(end_date + relativedelta(months=wt))
        next_end_date = make_aware_wrapper(next_end_date)
        next_start_date = make_aware_wrapper(next_start_date)
        result["next_start_date"] = next_start_date
        result["next_end_date"] = next_end_date
    return result


# this code is not used anywhere
def get_prev_periods(period, curr_start_date, curr_end_date, number_of_prev_required=1):
    """returns a list of dict containing start and end date for the previous periods"""
    number_of_months_in_period = TimePeriodWt[period]
    prev_periods = []
    for _ in range(0, number_of_prev_required):
        prev_start_date = start_of_day(
            curr_start_date - relativedelta(months=number_of_months_in_period)
        )
        prev_end_date = last_day_of_month(
            curr_end_date - relativedelta(months=number_of_months_in_period)
        )
        prev_start_date = make_aware_wrapper(prev_start_date)
        prev_end_date = make_aware_wrapper(prev_end_date)
        prev_periods.append({"start_date": prev_start_date, "end_date": prev_end_date})
        curr_start_date = prev_start_date
        curr_end_date = prev_end_date

    return prev_periods


# the below function is used only in dashboard services, skipping custom calendar refractor
def get_previous_different_payroll_periods(
    current_psd, no_of_prev_periods, all_payroll
):
    """Calculate the previous period start date and end date for different payout frequency"""
    count = 0
    result = []
    current_period_sd = current_psd
    payout_freq = None
    for payroll in all_payroll:
        payout_freq = payroll.payout_frequency.lower()
        while count < no_of_prev_periods:
            number_of_months_in_period = TimePeriodWt[payout_freq]
            prev_start_date = make_aware_wrapper(
                current_period_sd - relativedelta(months=number_of_months_in_period)
            )
            prev_end_date = make_aware_wrapper(
                last_day_of_month(
                    prev_start_date
                    + relativedelta(months=(number_of_months_in_period - 1))
                )
            )
            payroll.effective_start_date = payroll.effective_start_date.replace(day=1)
            if prev_start_date >= payroll.effective_start_date and (
                not payroll.effective_end_date
                or prev_end_date <= payroll.effective_end_date
            ):
                result.append(
                    {
                        "start_date": prev_start_date,
                        "end_date": prev_end_date,
                        "payout_frequency": payout_freq,
                    }
                )
                current_period_sd = prev_start_date
                count += 1
            else:
                break
    if count < no_of_prev_periods:
        remaining_dates = get_previous_periods(
            current_period_sd, no_of_prev_periods - count, payout_freq
        )
        for prv_date in remaining_dates:
            result.append(
                {
                    "start_date": prv_date[0],
                    "end_date": prv_date[1],
                    "payout_frequency": payout_freq,
                }
            )

    return result


# the below function is used only in dashboard services, skipping custom calendar refractor
def get_all_previous_different_payroll_periods_within_range(all_payroll, date_range):
    """
    for a given date range, returns all the payout date for multiple payout frequemcy
    ex:
    Employee payroll: Jan - Mar -> Monthly
                      April - Dec -> Quarterly
    and range_date = {
            "date_from": dt.datetime(2022, 1, 1, 0, 0, tzinfo=pytz.UTC),
            "date_to": dt.datetime(2022, 6, 30, 23, 59, 59, 999999, tzinfo=pytz.UTC)
        }
    then result will be
    [
        {
            "start_date": "2022-04-01 00:00:00+00:00",
            "end_date": "2022-06-30 23:59:59.999999+00:00",
        },
        {
            "start_date": "2022-03-01 00:00:00+00:00",
            "end_date": "2022-03-31 23:59:59.999999+00:00",
        },
        {
            "start_date": "2022-02-01 00:00:00+00:00",
            "end_date": "2022-02-28 23:59:59.999999+00:00",
        },
        {
            "start_date": "2022-01-01 00:00:00+00:00",
            "end_date": "2022-01-31 23:59:59.999999+00:00",
        }
    ]
    """
    result = []
    current_period_sd = make_aware_wrapper(
        first_day_of_month(date_range["date_to"] + relativedelta(months=1))
    )
    for payroll in all_payroll:
        payout_freq = payroll.payout_frequency.lower()
        if payout_freq in static_frequencies:
            while True:
                number_of_months_in_period = TimePeriodWt[payout_freq]
                prev_start_date = make_aware_wrapper(
                    current_period_sd - relativedelta(months=number_of_months_in_period)
                )
                prev_end_date = make_aware_wrapper(
                    last_day_of_month(
                        prev_start_date
                        + relativedelta(months=(number_of_months_in_period - 1))
                    )
                )
                payroll.effective_start_date = payroll.effective_start_date.replace(
                    day=1
                )
                if prev_start_date < date_range["date_from"]:
                    break
                if prev_start_date >= payroll.effective_start_date and (
                    not payroll.effective_end_date
                    or prev_end_date <= payroll.effective_end_date
                ):
                    result.append(
                        {
                            "start_date": prev_start_date,
                            "end_date": prev_end_date,
                            "payout_frequency": payout_freq,
                        }
                    )
                    current_period_sd = prev_start_date
                else:
                    break
    return result


def convert_str_to_date(data, fields=None):
    if isinstance(data, str):
        d = parse(data, ignoretz=True)
        return make_aware(d) if d.tzinfo is None else d
    elif isinstance(data, list):
        return [convert_str_to_date(x, fields) for x in data]
    elif isinstance(data, dict):
        for key, val in data.items():
            if fields and val and key in fields:
                data[key] = make_aware(
                    datetime.datetime.strptime(val, "%Y-%m-%dT%H:%M:%SZ")
                )
            else:
                if isinstance(val, dict):
                    data[key] = convert_str_to_date(val, fields)
        return data
    elif isinstance(data, int):
        # Determine the scale of the timestamp
        if len(str(data)) == MICROSECONDS_TIMESTAMP_LENGTH:  # Microseconds
            timestamp = data / int(1e6)
        elif len(str(data)) == MILLISECONDS_TIMESTAMP_LENGTH:  # Milliseconds
            timestamp = data / int(1e3)
        else:  # Seconds
            timestamp = data
        return datetime.datetime.utcfromtimestamp(timestamp)
    else:
        return data


def get_diff_in_sec(date1, date2):
    sec = None
    if date1 and date2:
        diff = date1 - date2
        sec = diff.days * 24 * 60 * 60
        sec = sec + diff.seconds + (diff.microseconds / 1000000)
    return round(sec) if sec else None


def get_diff_in_hours(date1, date2):
    hrs = None
    if date2 and date1:
        diff = date1 - date2
        hrs = diff.days * 24
        hrs = hrs + (diff.seconds / 3600) + (diff.microseconds / (1000000 * 3600))
    return round(hrs) if hrs else None


def get_diff_in_days(date1, date2):
    d = None
    if date1 and date2:
        diff = date1 - date2
        d = diff.days
        d = (
            d
            + (diff.seconds / (3600 * 24))
            + (diff.microseconds / (1000000 * 3600 * 24))
        )
    return round(d) if d else None


# the below function is used only in quota services, skipping custom calendar refractor
def get_previous_periods(current_psd, no_of_prev_periods, payout_freq):
    prev_start_dates = []
    wt = TimePeriodWt[payout_freq]
    for i in range(no_of_prev_periods):
        sd = make_aware_wrapper(current_psd - relativedelta(months=(i + 1) * wt))
        ed = make_aware_wrapper(last_day_of_month(sd + relativedelta(months=(wt - 1))))
        prev_start_dates.append((sd, ed))
    return prev_start_dates


def get_monthly_periods(
    min_date: datetime.datetime, max_date: datetime.datetime
) -> List[Tuple[datetime.datetime, datetime.datetime]]:
    """
    Generates a list of tuples with monthly periods between min_date and max_date.

    Args:
        min_date (datetime): The start date.
        max_date (datetime): The end date.

    Returns:
        List[Tuple[datetime, datetime]]: List of (start_date, end_date) tuples for each month.
    """

    period_min_date: datetime.datetime = make_aware_wrapper(start_of_day(min_date))
    period_max_date: datetime.datetime = make_aware_wrapper(end_of_day(max_date))

    periods: List[Tuple[datetime.datetime, datetime.datetime]] = []
    current_start_date: datetime.datetime = period_min_date

    while current_start_date <= period_max_date:
        # Calculate end date as the last day of the current month
        current_end_date = make_aware_wrapper(
            end_of_day(
                current_start_date + relativedelta(months=1) - relativedelta(days=1)
            )
        )

        # Ensure that end date does not go beyond the max_date
        if current_end_date > period_max_date:
            current_end_date = period_max_date

        periods.append((current_start_date, current_end_date))

        # Move to the start of the next month
        current_start_date = make_aware_wrapper(
            start_of_day(current_start_date + relativedelta(months=1))
        )

    return periods


def get_date_variables():
    v_names = cache.get("all_date_variables")
    if v_names is None:
        vda = VariableDataTypeAccessor()
        va = VariableAccessor()
        vd = vda.get_data_type("Date")
        date_id = vd.id
        variables = va.get_variables_by_data_type(date_id)
        v_names = []
        for v in variables:
            v_names.append(v.system_name)
        cache.set("all_date_variables", v_names, 3600)
    return v_names


def get_fiscal_year(start_month, curr_date=None):
    if curr_date:
        curr_month = curr_date.month
        curr_year = curr_date.year
    else:
        curr_month = date.today().month
        curr_year = date.today().year
    if start_month == 1:
        return str(curr_year)
    else:
        if curr_month < start_month:
            return str(curr_year)
        else:
            return str(curr_year + 1)


def get_first_month_of_fiscal_year(fiscal_start_month, fiscal_year):
    if fiscal_start_month == 1:
        return fiscal_year
    else:
        return fiscal_year - 1


def get_fiscal_year_from_month_year(start_month, month, year):
    curr_date = first_day_of_month(datetime.datetime(int(year), int(month), 1))
    return get_fiscal_year(start_month, curr_date=curr_date)


def get_custom_obj_date_variables(client_id, custom_object_id):
    vda = VariableDataTypeAccessor()
    vd = vda.get_data_type("Date")
    variables = CustomObjectVariableAccessor(
        client_id
    ).get_variables_in_obj_by_data_type(custom_object_id, vd.id)
    date_variables = [v.system_name for v in variables]
    return date_variables


def get_datasheet_date_variables(client_id, datasheet_id):
    vda = VariableDataTypeAccessor()
    vd = vda.get_data_type("Date")
    variables = DatasheetVariableAccessor(client_id).get_datasheet_objects_by_datatype(
        datasheet_id, vd.id
    )
    date_variables = [v.system_name for v in variables]
    return date_variables


def get_period_freq(start_date: datetime, end_date: datetime) -> str:
    if isinstance(start_date, datetime.datetime) and isinstance(
        end_date, datetime.datetime
    ):
        diff = relativedelta(end_date, start_date)
        diff_in_months = diff.months
        if diff_in_months == 0:
            return Freq.MONTHLY.value
        if diff_in_months == 2:
            return Freq.QUARTERLY.value
        if diff_in_months == 5:
            return Freq.HALFYEARLY.value
        if diff_in_months == 11:
            return Freq.ANNUAL.value
        if diff_in_months > 11:
            raise ValueError("Unexpected Date Range")


def get_period_label(index, payout_freq, fiscal_start_month=None):
    quarterly = ["Q1", "Q2", "Q3", "Q4"]
    halfyearly = ["H1", "H2"]
    monthly = [
        "December",
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
    ]
    if payout_freq.lower() == Freq.MONTHLY.value:
        return monthly[(index + fiscal_start_month) % 12]
    if payout_freq.lower() == Freq.QUARTERLY.value:
        return quarterly[index]
    if payout_freq.lower() == Freq.HALFYEARLY.value:
        return halfyearly[index]
    if payout_freq.lower() == Freq.ANNUAL.value:
        return "FY"


def convert_datetime_to_timestamp(date_time):
    return int(
        datetime.datetime.timestamp(date_time) * 1000
    )  # time in unix milliseconds


# used in tracing for quota functions
def get_period_for_statement(psd, ped, fiscal_start, fiscal_year):
    payout_freq = get_period_freq(start_date=psd, end_date=ped)
    payout_period = find_period(int(psd.month), fiscal_start, payout_freq)
    period_index = payout_period.get("index")
    period_label = get_period_label(period_index, payout_freq, fiscal_start)
    if payout_freq == Freq.MONTHLY.value:
        fiscal_year = ped.year
    return f"{period_label} {fiscal_year}"


def get_period_label_email(
    psd: datetime.datetime,
    ped: datetime.datetime,
    fiscal_start_month: int,
    payout_freq: Union[str, UUID],
    client_id: int,
) -> str:
    """
    Get period label for to send in email. Format depends on the payout frequency.

    Formats:
    --------
        Monthly:
            January 2020
            February 2020 ...

        Quarterly:
            Q1 (Jan 2020 - Mar 2020)
            Q2 (Apr 2020 - Jun 2020) ...

        Half Yearly:
            H1 (Jan 2020 - Jun 2020)
            H2 (Jul 2020 - Dec 2020) ...

        Annual:
            FY 2020
    """
    psd = make_aware_wrapper(psd)
    ped = make_aware_wrapper(ped)

    if str(payout_freq).lower() in static_frequencies:
        freq = get_period_freq(start_date=psd, end_date=ped)
        period = find_period(ped.month, fiscal_start_month, freq)
        label = get_period_label(period["index"], freq, fiscal_start_month)

        if freq in (Freq.MONTHLY.value, Freq.ANNUAL.value):
            label = f"{label} {ped.year}"

        elif freq in (Freq.QUARTERLY.value, Freq.HALFYEARLY.value):
            label = f"{label} ({psd.strftime('%b %Y')} - {ped.strftime('%b %Y')})"

        return label
    else:
        period = CustomPeriodsAccessor(client_id).find_period_by_date(
            payout_freq, psd, ped
        )
        if period is not None:
            return period["period_label"]
        else:
            raise Exception("Period Not found")


def get_payee_period_label(
    client_id: int,
    period_tuple: Tuple[datetime.datetime, datetime.datetime],
    fiscal_month: int,
    payout_freq: str,
    has_period_label_feature: bool,
):
    from spm.services.period_label_services import get_custom_period_label

    period_label = None
    if has_period_label_feature:
        # Get custom period label, if present
        period_label = get_custom_period_label(client_id, [period_tuple])

    if not period_label:
        period_label = get_period_label_email(
            period_tuple[0], period_tuple[1], fiscal_month, payout_freq, client_id
        )

    return period_label


def get_period_label_for_static_frequencies(psd, ped, fiscal_start_month, payout_freq):
    """
    Get period label for static payout frequencies.
    The year in the label is the year of the start and end dates not the fiscal year.

    Formats:
    --------
        Monthly:
            January 2020
            February 2020 ...

        Quarterly:
            Q1 (Jan 2020 - Mar 2020)
            Q2 (Apr 2020 - Jun 2020) ...

        Half Yearly:
            H1 (Jan 2020 - Jun 2020)
            H2 (Jul 2020 - Dec 2020) ...

        Annual:
            2020
    """
    if str(payout_freq).lower() not in static_frequencies:
        return None
    freq = payout_freq.lower()
    psd = make_aware_wrapper(psd)
    ped = make_aware_wrapper(ped)
    period = find_period(ped.month, fiscal_start_month, freq)
    label = get_period_label(period["index"], freq, fiscal_start_month)

    if freq in (Freq.MONTHLY.value):
        label = f"{label} {ped.year}"
    elif freq in (Freq.QUARTERLY.value, Freq.HALFYEARLY.value):
        label = f"{label} ({psd.strftime('%b %Y')} - {ped.strftime('%b %Y')})"
    elif freq in (Freq.ANNUAL.value):
        label = f"{ped.year}"

    return label


def get_start_date_by_freq_and_end_date(ped, payout_freq):
    wt = TimePeriodWt[payout_freq.lower()]
    sd = make_aware_wrapper(
        start_of_day(
            last_day_of_month(make_aware_wrapper(ped - relativedelta(months=wt)))
            + relativedelta(days=1)
        )
    )
    return sd


# the below function is used only in draws related code, skipping custom calendar refractor
def get_period_label_for_draws(index, payout_freq, fiscal_start_month=None):
    quarterly = ["Q1", "Q2", "Q3", "Q4"]
    halfyearly = ["H1", "H2"]
    monthly = [
        "Dec",
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
    ]
    if payout_freq.lower() == Freq.MONTHLY.value:
        return monthly[(index + fiscal_start_month) % 12]
    if payout_freq.lower() == Freq.QUARTERLY.value:
        return quarterly[index]
    if payout_freq.lower() == Freq.HALFYEARLY.value:
        return halfyearly[index]
    if payout_freq.lower() == Freq.ANNUAL.value:
        return "Annual"


# the below function is used only in draws related code, skipping custom calendar refractor
def get_period_draws(psd, ped, fiscal_start):
    payout_freq = get_period_freq(start_date=psd, end_date=ped)
    payout_period = find_period(int(psd.month), fiscal_start, payout_freq)
    period_index = payout_period.get("index")
    period_label = get_period_label_for_draws(period_index, payout_freq, fiscal_start)
    return period_label


# not used anywhere
def get_monthly_start_and_end_dates(
    start_month, start_month_year, end_month, end_month_year
):
    start_date_string = str(start_month) + "/" + "1" + "/" + str(start_month_year)
    end_date_string = str(end_month) + "/" + "1" + "/" + str(end_month_year)
    periods = pd.period_range(start_date_string, end_date_string, freq="M")
    periods = [
        (p.start_time.to_pydatetime(), p.end_time.to_pydatetime()) for p in periods
    ]
    return periods


# the below function is used only in quota services, skipping custom calendar refractor
def get_period_timeframe(
    fiscal_start_month,
    period_start_date,
    period_end_date,
    payout_frequency,
    period_number,
):
    fiscal_year = get_fiscal_year(fiscal_start_month, period_end_date)
    payout_frequency = payout_frequency.lower()
    if payout_frequency == Freq.MONTHLY.value:
        return period_end_date.strftime("%b %Y")
    if payout_frequency == Freq.QUARTERLY.value:
        return f"Q{period_number} {fiscal_year} ({period_start_date.strftime('%b %Y')} - {period_end_date.strftime('%b %Y')})"
    if payout_frequency == Freq.HALFYEARLY.value:
        return f"H{period_number} {fiscal_year} ({period_start_date.strftime('%b %Y')} - {period_end_date.strftime('%b %Y')})"
    if payout_frequency == Freq.ANNUAL.value:
        return f"{fiscal_year} ({period_start_date.strftime('%b %Y')} - {period_end_date.strftime('%b %Y')})"


# the below function is used only in draws related code, skipping custom calendar refractor
def get_date_range_for_period(
    period: str, year: str or int, fy_start_month: int
) -> Tuple[datetime.datetime, datetime.datetime]:
    """
    Utility function to get start and end dates for a given labelled period and fiscal year.

    Args:
        period (str): Labelled period
        year (str | int): Fiscal year

    Raises:
        Exception: Raised when period is ivalid.

    Returns:
        Tuple[datetime.datetime, datetime.datetime]: Tuple of start and end dates for given period in fiscal year.
    """
    quarterly = ["Q1", "Q2", "Q3", "Q4"]
    halfyearly = ["H1", "H2"]
    monthly = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
    ]
    annual = ["Year"]

    if period not in quarterly + halfyearly + monthly + annual:
        raise Exception("Invalid period value")

    _year: int = int(year)
    start_month_number: int = fy_start_month

    if fy_start_month != 1:
        _year -= 1

    start_date = datetime.datetime(year=int(_year), month=start_month_number, day=1)
    end_date = datetime.datetime.now()

    if period in monthly:
        act_year = int(_year)
        act_month = monthly.index(period)
        if act_month < start_month_number != act_month + 1:
            act_year += 1
        start_date = datetime.datetime(day=1, month=act_month + 1, year=act_year)
        end_date = last_day_of_month(start_date)

    elif period == "Q1":
        end_date = start_date + relativedelta(months=3) - relativedelta(days=1)

    elif period == "Q2":
        start_date = start_date + relativedelta(months=3)
        end_date = start_date + relativedelta(months=3) - relativedelta(days=1)

    elif period == "Q3":
        start_date = start_date + relativedelta(months=6)
        end_date = start_date + relativedelta(months=3) - relativedelta(days=1)

    elif period == "Q4":
        start_date = start_date + relativedelta(months=9)
        end_date = start_date + relativedelta(months=3) - relativedelta(days=1)

    elif period == "H1":
        end_date = start_date + relativedelta(months=6) - relativedelta(days=1)

    elif period == "H2":
        start_date = start_date + relativedelta(months=6)
        end_date = start_date + relativedelta(months=6) - relativedelta(days=1)

    elif period == "Year":
        end_date = start_date + relativedelta(months=12) - relativedelta(days=1)

    start_date = make_aware_wrapper(start_of_day(start_date))
    end_date = make_aware_wrapper(end_of_day(end_date))

    return (start_date, end_date)


def add_days_to_date_in_time_zone(no_of_days: int, time_zone: str, current_date=None):
    """
    Function to add no_of_days days from current date. Time will be end of day's time in time_zone converted to UTC
    Parameter
    ---------
    no_of_days: number of days to be added
    time_zone: "Asia/Calcutta"
    Returns
    -------
    datetime
    Eg:
        If current date is 21/12/2022 and no_of_days is 2 and time_zone is Asia/Calcutta,
        then resolve datetime will be datetime.datetime(2022, 12, 23, 18, 29, 59, 999999, tzinfo=<UTC>)
    """
    if not current_date:
        current_date = timezone.now()
    days_added_date = current_date + datetime.timedelta(days=no_of_days)
    time_zone_date = days_added_date.astimezone(pytz.timezone(time_zone))
    end_of_day_time_zone_date = end_of_day(time_zone_date)
    return end_of_day_time_zone_date.astimezone(pytz.timezone("UTC"))


def date_string_to_time_zone_aware_eod_utc(date_string: str, _timezone):
    """
    Function that accepts date string and converts end of day of the date in timezone provided and converts back to UTC.
    Example:
        date_string: "2022-12-28"
        timezone: "Asia/Kolkata"

        Output: 2022-12-28 18:29:59.999999+00:00
    """
    string_date_object = datetime.datetime.strptime(date_string, "%Y-%m-%d")
    time_zone_aware_date = string_date_object.astimezone(pytz.timezone(_timezone))
    time_zone_aware_date_eod = end_of_day(time_zone_aware_date)
    time_zone_aware_date_eod_utc = time_zone_aware_date_eod.astimezone(
        pytz.timezone("UTC")
    )
    return time_zone_aware_date_eod_utc


def datetime_to_timezone_utc(_datetime: datetime.datetime, _timezone):
    time_zone_aware_date = _datetime.astimezone(pytz.timezone(_timezone))
    time_zone_aware_date_utc = time_zone_aware_date.astimezone(pytz.timezone("UTC"))
    return time_zone_aware_date_utc


def get_last_and_current_month_date():
    curr_month = date.today().month
    curr_year = date.today().year
    if curr_month == 1:
        last_month = 12
        last_month_year = curr_year - 1
    else:
        last_month = curr_month - 1
        last_month_year = curr_year
    curr_month_days = calendar.monthrange(curr_year, curr_month)
    last_mont_days = calendar.monthrange(last_month_year, last_month)
    curr_start_date = date(curr_year, curr_month, 1).strftime("%Y-%m-%d")
    curr_end_date = date(curr_year, curr_month, curr_month_days[1]).strftime("%Y-%m-%d")
    prev_start_date = date(last_month_year, last_month, 1).strftime("%Y-%m-%d")
    prev_end_date = date(last_month_year, last_month, last_mont_days[1]).strftime(
        "%Y-%m-%d"
    )
    curr_psd = make_aware(start_of_day(parse(curr_start_date)))
    curr_ped = make_aware(end_of_day(parse(curr_end_date, dayfirst=False)))
    prev_psd = make_aware(start_of_day(parse(prev_start_date)))
    prev_ped = make_aware(end_of_day(parse(prev_end_date, dayfirst=False)))

    data = {
        "current_month_date": {"start_date": curr_psd, "end_date": curr_ped},
        "last_month_date": {"start_date": prev_psd, "end_date": prev_ped},
    }
    return data


def get_start_year(curr_time, start_month) -> datetime.datetime:
    """
    Get the start of the fiscal / calendar year based on the current time and start month.

    Eg:
    1. If the current time is 2022-07-01 and the start month is 4 (April),
    the function will return 2022-04-01 00:00:00+00:00.

    2. If the current time is 2022-03-01 and the start month is 4 (April),
    the function will return 2021-04-01 00:00:00+00:00.
    """
    year_start = datetime.datetime(
        curr_time.year if curr_time.month >= start_month else curr_time.year - 1,
        start_month,
        1,
    )
    year_start = make_aware_wrapper(year_start)
    return year_start


def get_end_year(curr_time, start_month) -> datetime.datetime:
    """
    Get the end of the fiscal / calendar year based on the current time and start month.

    Eg:
    1. If the current time is 2022-07-01 and the start month is 4 (April),
    the function will return 2023-03-31 23:59:59.999999+00:00.

    2. If the current time is 2022-03-01 and the start month is 4 (April),
    the function will return 2022-03-31 23:59:59.999999+00:00.
    """
    year_end = last_day_of_month(
        get_start_year(curr_time, start_month)
        + relativedelta(years=1)
        - relativedelta(days=1)
    )
    year_end = make_aware_wrapper(year_end)
    return year_end


def get_future_periods_for_static_frequencies(
    client_id, curr_date, start_month, time_prd
) -> List[dict]:
    """
    Generate time periods based on the payout frequency.

    Args:
        client_id: Client ID for custom calendar periods
        curr_date: Current date to start from
        start_month: Start month of the fiscal year
        time_prd: Time period type ('monthly', 'quarterly', 'halfyearly', 'annual')

    Returns:
        Example 01:
        --------
        >>> curr_date = datetime.datetime(2022, 7, 1)
        >>> start_month = 4
        >>> time_prd = 'monthly'
        >>> get_future_periods_for_static_frequencies(curr_date, start_month, time_prd)
        [
            {
                'start_date': datetime.datetime(2022, 7, 1, 0, 0, tzinfo=<UTC>),
                'end_date': datetime.datetime(2022, 7, 30, 23, 59, 59, 999999, tzinfo=<UTC>)
            },
            {
                'start_date': datetime.datetime(2022, 8, 1, 0, 0, tzinfo=<UTC>),
                'end_date': datetime.datetime(2022, 8, 31, 23, 59, 59, 999999, tzinfo=<UTC>)
            },
            ...
            ...
            {
                'start_date': datetime.datetime(2023, 7, 1, 0, 0, tzinfo=<UTC>),
                'end_date': datetime.datetime(2023, 7, 31, 23, 59, 59, 999999, tzinfo=<UTC>)
            }
        ]

        Example 02:
        --------
        >>> curr_date = datetime.datetime(2022, 7, 1)
        >>> start_month = 4
        >>> time_prd = 'quarterly'
        >>> get_future_periods_for_static_frequencies(curr_date, start_month, time_prd)
        [
            {
                'start_date': datetime.datetime(2022, 4, 1, 0, 0, tzinfo=<UTC>),
                'end_date': datetime.datetime(2022, 6, 30, 23, 59, 59, 999999, tzinfo=<UTC>)
            },
            {
                'start_date': datetime.datetime(2022, 7, 1, 0, 0, tzinfo=<UTC>),
                'end_date': datetime.datetime(2022, 9, 30, 23, 59, 59, 999999, tzinfo=<UTC>)
            },
            ...
            ...
            {
                'start_date': datetime.datetime(2023, 4, 1, 0, 0, tzinfo=<UTC>),
                'end_date': datetime.datetime(2023, 6, 31, 23, 59, 59, 999999, tzinfo=<UTC>)
            }
        ]
    """
    logger.info("BEGIN: get_future_periods_for_static_frequencies")
    periods = []

    # Define number of periods based on time_prd
    static_freq_mapping = {"monthly": 12, "quarterly": 4, "halfyearly": 2, "annual": 1}

    num_periods = static_freq_mapping[time_prd.lower()]

    # Initial period calculation
    res = get_period_start_and_end_date(
        curr_date, start_month, time_prd, client_id=client_id
    )
    start_date = res["start_date"]
    end_date = res["end_date"]

    if start_date is None or end_date is None:
        logger.info("Invalid start_date or end_date")
        logger.info("END: get_future_periods_for_static_frequencies")
        return periods

    # Append the initial period
    periods.append({"start_date": start_date, "end_date": end_date})

    # Generate subsequent periods based on the number of periods required
    for _ in range(num_periods):
        last_period = periods[-1]

        # Calculate the next period's start and end dates
        start_date = make_aware_wrapper(
            start_of_day(
                last_period["start_date"] + relativedelta(months=TimePeriodWt[time_prd])
            )
        )
        end_date = make_aware_wrapper(
            last_day_of_month(
                last_period["end_date"] + relativedelta(months=TimePeriodWt[time_prd])
            )
        )

        periods.append({"start_date": start_date, "end_date": end_date})

    logger.info("END: get_future_periods_for_static_frequencies")
    return periods


def get_timestamp(
    _date: str | datetime.datetime, _format: str = "%Y-%m-%dT%H:%M:%S.%fZ"
) -> int | None:
    """
    Get the timestamp of given date in string or datetime format.
    """
    if isinstance(_date, datetime.datetime):
        return int(_date.timestamp())
    elif isinstance(_date, str):
        return int(datetime.datetime.strptime(_date, _format).timestamp())
    return None


def get_fiscal_start_end_dates(fiscal_start_month, year=None):
    """
    This function returns the fiscal start and end dates given a fiscal start month and a fiscal year

    fiscal_start_month: The month (1-12) that marks the start of the fiscal year
    year: The fiscal year of consideration

    """
    current_date = datetime.datetime.now()

    # If year is None, consider with respect to current date
    if year is None:
        fiscal_start_year = (
            current_date.year
            if current_date.month >= fiscal_start_month
            else current_date.year - 1
        )
    else:
        fiscal_start_year = year - 1 if fiscal_start_month > 1 else year

    fiscal_start_date = datetime.datetime(fiscal_start_year, fiscal_start_month, 1)
    fiscal_end_year = fiscal_start_year + 1
    fiscal_end_date = datetime.datetime(
        fiscal_end_year, fiscal_start_month, 1
    ) - datetime.timedelta(days=1)
    fiscal_start_date = fiscal_start_date.strftime("%d %b %Y")
    fiscal_end_date = fiscal_end_date.strftime("%d %b %Y")
    final_dates = {
        "start_date": make_aware(start_of_day(parse(fiscal_start_date, dayfirst=True))),
        "end_date": make_aware(end_of_day(parse(fiscal_end_date, dayfirst=False))),
    }
    return final_dates
