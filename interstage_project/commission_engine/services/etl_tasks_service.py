import datetime
import traceback
import uuid
from collections import defaultdict
from logging import getLogger
from typing import Optional

from celery import chain, group, shared_task
from django.utils.timezone import make_aware, now

import interstage_project.utils as iputils
from commission_engine.accessors.client_accessor import (
    can_run_payout_snapshot_etl,
    can_run_settlement_snapshot_etl,
    can_run_sf_payout_snapshot,
    get_client_subscription_plan,
    get_settlement_v3_execution_mode,
    write_commission_to_snowflake,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    CommissionETLStatusAccessor,
    CommissionETLStatusReaderAccessor,
    DatabookETLStatusAccessor,
    ETLLockAccessor,
    ETLSyncStatusAccessor,
    ETLSyncStatusReaderAccessor,
    ReportETLStatusAccessor,
    SettlementETLStatusAccessor,
    UpstreamETLStatusAccessor,
)
from commission_engine.accessors.schedule_accessor import (
    CrontabScheduleAccessor,
    HardDeleteAccessor,
)
from commission_engine.custom_exceptions.etl_exceptions import (
    ETLConcurrencyException,
    FivetranSyncRunningError,
)
from commission_engine.services import etl_sync_status_service
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.etl_global_sync_status_service import (
    EtlGlobalSyncStatusService,
)
from commission_engine.utils import (
    ETL_ACTIVITY,
    ETL_STATUS,
    SYNC_OBJECT,
    CommCalcLevels,
)
from commission_engine.utils.databook_utils import get_associated_report_objects
from commission_engine.utils.date_utils import last_day_of_month
from commission_engine.utils.general_data import COMMISSION_TYPE, ETLTriggers
from commission_engine.utils.log_utils import merge_log_context
from commission_engine.utils.report_utils import get_report_object_ids
from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.settlement_v3.common import ExecutionMode
from everstage_ddd.upstream import FivetranSyncLogService, MissingApiKeyOrSecretError
from everstage_etl.tasks import (
    commission_wrapper_task,
    get_datasheet_wrapper_sync_task,
    inter_comm_snapshot_sync_task,
    optimized_snapshot_sync_wrapper_task,
    plan_modifications_sync,
    report_etl_wrapper,
    report_object_etl_by_period_wrapper,
    settlement_snapshot_sync_task,
    settlement_wrapper_sync,
    team_criteria_wrapper,
    upstream_wrapper_sync,
)
from everstage_etl.tasks.payee_inter_object_wrapper_sync import (
    payee_inter_object_wrapper_sync,
)
from everstage_etl.tasks.payout_status_wrapper import get_update_payout_tasks
from interstage_project.celery import TaskGroupEnum
from interstage_project.threadlocal_log_context import set_threadlocal_context
from spm.accessors.commission_plan_accessor import CommissionPlanAccessor
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.services.commission_actions_service.commission_slack_services import (
    notify_attainment,
)

logger = getLogger(__name__)
# upstream sync tasks
#   databook_wrapper_sync tasks
#       databook_sync
#       report_etl_wrapper tasks
#           report_object_etl
#           databook_wrapper_sync
#       commission_wrapper_task tasks
#           commission_payee_sync
#           team_criteria_wrapper
#               team_payee_sync
#               settlement_wrapper_sync
#                   settlement_payee_sync
#                   report_etl_wrapper
#                       report_object_etl
#                       databook_wrapper_sync


@shared_task(base=EverCeleryBaseTask)
def log_sync_end(logger_loc, log_message=None):
    if not log_message:
        log_message = "END: ETL Sync"
    logger_loc.info(log_message)


class ETLSync:
    def __init__(
        self,
        client_id,
        e2e_sync_run_id=None,
        log_context=None,
        notification_email_id=None,
        skip_archived_books: bool = True,
        run_previous_period_sync: bool = False,
        post_upstream_disabled=False,
    ):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id if e2e_sync_run_id else uuid.uuid4()

        subscription_plan = get_client_subscription_plan(self.client_id)
        self.log_context = merge_log_context(
            log_context,
            {
                "client_id": client_id,
                "e2e_sync_run_id": e2e_sync_run_id,
                "subscription": subscription_plan,
            },
        )
        self.run_intermediate_sync = has_feature(
            client_id, "expose_comm_reports_in_plan"
        )
        self.split_commission_etl_sync = has_feature(
            client_id, "split_commission_etl_sync"
        )
        self.chain_tasks = []
        self.notification_email_id = notification_email_id
        self.skip_archived_books = skip_archived_books
        self.run_previous_period_sync = run_previous_period_sync
        self.logger = iputils.LogWithContext(self.log_context)

        self.misc_queue_name = iputils.get_queue_name_respect_to_task_group(
            self.client_id, subscription_plan, TaskGroupEnum.MISC.value
        )
        self.commission_queue_name = iputils.get_queue_name_respect_to_task_group(
            self.client_id, subscription_plan, TaskGroupEnum.COMMISSION.value
        )
        self.upstream_queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.UPSTREAM.value
        )
        self.send_email_queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
        )
        self.report_queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.REPORT.value
        )
        self.maestro_queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MAESTRO_ROUTER.value
        )
        self.settlement_queue_name = iputils.get_queue_name_respect_to_task_group(
            self.client_id, subscription_plan, TaskGroupEnum.SETTLEMENT.value
        )
        self.settlement_v3_dry_run_queue_name = (
            iputils.get_queue_name_respect_to_task_group(
                self.client_id,
                subscription_plan,
                TaskGroupEnum.SETTLEMENT_V3_DRY_RUN.value,
            )
        )
        self.update_payouts_task = None
        # Maps payee email to periods for which attainment notification needs to be sent
        # Form:
        # {
        #     "<EMAIL>": {
        #         "prev_period": (01-10-2023, 31-10-2023),
        #         "curr_period": (01-11-2023, 30-11-2023),
        #     },
        #     ...
        # }
        self.payee_period_map = None
        self.upstream_sync_required = has_feature(
            self.client_id, "is_upstream_etl"
        ) in [
            True,
            None,
        ]
        self.post_upstream_required = has_feature(self.client_id, "is_db_comm_etl") in [
            True,
            None,
        ]
        self.post_upstream_required = (
            not post_upstream_disabled
        ) and self.post_upstream_required
        self.task_orchestration = has_feature(self.client_id, "task_orchestration")

    def get_databook_wrapper_tasks(
        self,
        sync_type=None,
        databook_ids=None,
        is_flow_eligible_for_trigger_only_associated_ds=False,
    ):
        """
        This method returns the databook_wrapper_sync task
        """
        return [
            get_datasheet_wrapper_sync_task(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_type=sync_type,
                databook_ids=databook_ids,
                notification_email_id=self.notification_email_id,
                skip_archived_books=self.skip_archived_books,
                is_flow_eligible_for_trigger_only_associated_ds=is_flow_eligible_for_trigger_only_associated_ds,
            )
        ]

    def get_commission_wrapper_tasks(
        self,
        secondary_kd,
        level,
        payee_list=None,
        curr_date=None,
        is_after_db_refresh=False,
    ):
        tasks, update_payout_status_details, payee_period_map = commission_wrapper_task(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            secondary_kd=secondary_kd,
            level=level,
            curr_date=curr_date,
            payee_list=payee_list,
            notification_email_id=self.notification_email_id,
            log_context=self.log_context,
            is_after_db_refresh=is_after_db_refresh,
            run_previous_period_sync=self.run_previous_period_sync,
        )
        if not self.update_payouts_task:
            self.update_payouts_task = update_payout_status_details
        if not self.payee_period_map:
            self.payee_period_map = payee_period_map

        tasks.extend(
            team_criteria_wrapper(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                secondary_kd=secondary_kd,
                level=level,
                payee_list=payee_list,
                curr_date=curr_date,
                notification_email_id=self.notification_email_id,
                log_context=self.log_context,
                is_after_db_refresh=is_after_db_refresh,
                run_previous_period_sync=self.run_previous_period_sync,
            )
        )

        return tasks

    def get_forecast_wrapper_tasks(
        self,
        secondary_kd,
        level,
        payee_list=None,
        curr_date=None,
        is_after_db_refresh=False,
    ):
        # update_payout_status_details is not used in forecast
        tasks, _, _ = commission_wrapper_task(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            secondary_kd=secondary_kd,
            level=level,
            curr_date=curr_date,
            payee_list=payee_list,
            notification_email_id=self.notification_email_id,
            log_context=self.log_context,
            is_after_db_refresh=is_after_db_refresh,
            commission_type=COMMISSION_TYPE.FORECAST,
            run_previous_period_sync=self.run_previous_period_sync,
        )

        tasks.extend(
            team_criteria_wrapper(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                secondary_kd=secondary_kd,
                level=level,
                payee_list=payee_list,
                curr_date=curr_date,
                notification_email_id=self.notification_email_id,
                log_context=self.log_context,
                is_after_db_refresh=is_after_db_refresh,
                commission_type=COMMISSION_TYPE.FORECAST,
                run_previous_period_sync=self.run_previous_period_sync,
            )
        )
        return tasks

    def get_settlement_wrapper_tasks(
        self,
        payee_list=None,
        is_after_comm_sync=False,
        primary_kd=None,
        curr_date=None,
        set_update_payout_status_details=False,
    ):
        tasks, update_payout_status_details = settlement_wrapper_sync(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            payee_list=payee_list,
            notification_email_id=self.notification_email_id,
            log_context=self.log_context,
            is_after_comm_sync=is_after_comm_sync,
            primary_kd=primary_kd,
            curr_date=curr_date,
        )
        if set_update_payout_status_details:
            self.update_payouts_task = update_payout_status_details
        return tasks

    def get_report_etl_wrapper_tasks(
        self, report_objects=None, sync_mode="changes", sync_type=None
    ):
        return report_etl_wrapper(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            is_async=True,
            notification_email_id=self.notification_email_id,
            log_context=self.log_context,
            report_object_ids=report_objects,
            sync_mode=sync_mode,
            sync_type=sync_type,
        )

    def insert_sync_status(
        self, task, audit=None, etl_status_params=None, additional_info=None
    ):
        etl_sync_status_service.insert_etl_sync_status(
            self.client_id,
            self.e2e_sync_run_id,
            task,
            ETL_STATUS.STARTED.value,
            make_aware(datetime.datetime.now()),
            audit,
            etl_status_params,
            additional_info,
        )

    def set_complete_status(self, log_message=None):
        return [
            etl_sync_status_service.update_completion_time_and_send_email_notification.si(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                email_id=self.notification_email_id,
                log_context=self.log_context,
            ).set(
                queue=self.send_email_queue_name
            ),
            log_sync_end.si(self.logger, log_message).set(queue=self.misc_queue_name),
        ]

    def get_commission_tasks(
        self,
        level,
        payee_list=None,
        curr_date=None,
        is_after_db_refresh=False,
    ):
        secondary_kd = make_aware(datetime.datetime.now())
        if level == CommCalcLevels.L1.value and write_commission_to_snowflake(
            self.client_id
        ):
            self.chain_tasks.extend(
                [
                    plan_modifications_sync.si(
                        client_id=self.client_id,
                        e2e_sync_run_id=self.e2e_sync_run_id,
                        log_context=self.log_context,
                    ).set(queue=self.commission_queue_name)
                ]
            )
        self.chain_tasks.extend(
            self.get_commission_wrapper_tasks(
                secondary_kd=secondary_kd,
                level=level,
                payee_list=payee_list,
                curr_date=curr_date,
                is_after_db_refresh=is_after_db_refresh,
            )
        )
        if (
            level == CommCalcLevels.L1.value
            and can_run_sf_payout_snapshot(self.client_id)
            and self.run_intermediate_sync
        ):
            self.chain_tasks.extend(
                [
                    inter_comm_snapshot_sync_task.si(
                        client_id=self.client_id,
                        e2e_sync_run_id=self.e2e_sync_run_id,
                        secondary_kd=secondary_kd,
                        curr_date=curr_date,
                        payee_list=payee_list,
                        log_context=self.log_context,
                        only_curr_period=not self.run_previous_period_sync,
                    ).set(queue=self.commission_queue_name)
                ]
            )

    def get_settlement_tasks(
        self, payee_list=None, curr_date=None, is_after_comm_sync=True
    ):
        self.chain_tasks.extend(
            self.get_settlement_wrapper_tasks(
                payee_list=payee_list,
                is_after_comm_sync=is_after_comm_sync,
                curr_date=curr_date,
            )
        )

    def get_snapshot_tasks(
        self,
        payee_list=None,
        curr_date=None,
        snapshot_type="commission",
    ):
        """
        This method will append snapshot sync tasks to the chain_tasks
        """
        secondary_kd = make_aware(datetime.datetime.now())
        if snapshot_type == "commission":
            # This flag will be removed in future if we decide to keep only optimized snapshot sync
            # We will remove the existing snapshot sync tasks and keep only optimized snapshot sync tasks
            if can_run_payout_snapshot_etl(self.client_id):
                self.chain_tasks.extend(
                    optimized_snapshot_sync_wrapper_task(
                        client_id=self.client_id,
                        e2e_sync_run_id=self.e2e_sync_run_id,
                        secondary_kd=secondary_kd,
                        curr_date=curr_date,
                        payee_list=payee_list,
                        log_context=self.log_context,
                        only_curr_period=not self.run_previous_period_sync,
                    )
                )

        elif snapshot_type == "settlement" and can_run_settlement_snapshot_etl(
            self.client_id
        ):
            self.chain_tasks.extend(
                settlement_snapshot_sync_task(
                    client_id=self.client_id,
                    e2e_sync_run_id=self.e2e_sync_run_id,
                    secondary_kd=secondary_kd,
                    curr_date=curr_date,
                    log_context=self.log_context,
                )
            )

    def get_forecast_tasks(
        self,
        level,
        payee_list=None,
        curr_date=None,
        is_after_db_refresh=False,
    ):
        secondary_kd = make_aware(datetime.datetime.now())
        self.chain_tasks.extend(
            self.get_forecast_wrapper_tasks(
                secondary_kd=secondary_kd,
                level=level,
                payee_list=payee_list,
                curr_date=curr_date,
                is_after_db_refresh=is_after_db_refresh,
            )
        )

    def handle_failure(self):
        # since we are calling all wrapper tasks initially,
        # in case of failures at this point, housekeeping tables will only have
        # wrapper tasks entries. So we are updating status to failed for those entries.

        self.logger.info("BEGIN: Invalidating E2E records")
        kd = datetime.datetime.now()
        UpstreamETLStatusAccessor(
            self.client_id, self.e2e_sync_run_id, None
        ).handle_etl_failure(kd)

        ReportETLStatusAccessor(
            self.client_id, self.e2e_sync_run_id, None
        ).handle_etl_failure(kd)

        DatabookETLStatusAccessor(
            self.client_id, self.e2e_sync_run_id, None
        ).handle_etl_failure(kd)

        CommissionETLStatusAccessor(
            self.client_id, self.e2e_sync_run_id, None
        ).handle_etl_failure(kd)

        SettlementETLStatusAccessor(
            self.client_id, self.e2e_sync_run_id, None
        ).handle_etl_failure(kd)

        ETLLockAccessor(self.client_id, self.e2e_sync_run_id, None).release_lock()
        ETLSyncStatusAccessor(self.client_id, self.e2e_sync_run_id).handle_etl_failure(
            kd
        )

        self.logger.info("END: Invalidating E2E records")

    def get_inter_and_l2_tasks(
        self, payee_list=None, curr_date=None, refresh_databook=False
    ):
        self.chain_tasks.extend(
            self.get_report_etl_wrapper_tasks(
                report_objects=get_report_object_ids(data_origin=["inter_object"]),
                sync_type="inter_report",
            )
        )
        self.chain_tasks.extend(
            self.get_databook_wrapper_tasks(
                sync_type=SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value
            )
        )
        self.get_commission_tasks(
            level=CommCalcLevels.L2.value,
            payee_list=payee_list,
            curr_date=curr_date,
            is_after_db_refresh=refresh_databook,
        )

    def get_inter_and_l2_tasks_for_forecast(
        self, payee_list=None, curr_date=None, refresh_databook=False
    ):
        self.chain_tasks.extend(
            self.get_report_etl_wrapper_tasks(
                report_objects=get_report_object_ids(
                    data_origin=["inter_forecast_object"]
                ),
                sync_type="inter_forecast_report",
            )
        )
        self.chain_tasks.extend(
            self.get_databook_wrapper_tasks(
                sync_type=SYNC_OBJECT.DATABOOK_INTER_FORECAST_OBJECT_SYNC.value
            )
        )
        self.get_forecast_tasks(
            level=CommCalcLevels.L2.value,
            payee_list=payee_list,
            curr_date=curr_date,
            is_after_db_refresh=refresh_databook,
        )

    def get_notify_attainment_payee_periods(self):
        records = CommissionETLStatusReaderAccessor(
            self.client_id
        ).get_all_payee_periods_for_e2e(self.e2e_sync_run_id)
        commission_period_dict = []
        for record in records:
            commission_period_dict.append(
                {
                    "period_start_date": record["period_start_date"],
                    "period_end_date": record["period_end_date"],
                    "payee_email_id": record["payee_email_id"],
                }
            )
        return commission_period_dict

    def get_notify_attainment_tasks(self):
        """
        Adds notification tasks to the `self.chain_tasks` for previous period and
        current period for each payee.

        Note that when both L1 and L2 commission sync are enabled for the client,
        the notification tasks will be triggered only once after completion of L2
        calculations.
        """
        payee_period_records = self.get_notify_attainment_payee_periods()
        if not payee_period_records:
            return

        grp_tasks = []
        for record in payee_period_records:
            grp_tasks.append(
                notify_attainment.si(
                    client_id=self.client_id,
                    payee_email=record["payee_email_id"],
                    period_start_date=record["period_start_date"],
                    period_end_date=record["period_end_date"],
                    log_context=self.log_context,
                ).set(queue=self.misc_queue_name)
            )
        self.chain_tasks.extend([group(grp_tasks)])

    def add_etl_sync_status(
        self,
        integration_ids: Optional[list],
        audit: Optional[dict],
        additional_info: Optional[dict] = None,
    ) -> None:
        """
        Adds the ETL sync status lock for the given parameters.

        This method determines which ETL sync tasks are required based on the client's
        features and the given parameters, and then inserts the corresponding ETL sync
        status records into the database.

        Args:
            post_upstream_disabled (bool): Whether or not post-upstream sync is disabled.
            integration_ids (list): The IDs of the integrations to sync.
            audit (dict): The audit details for the ETL sync status.
        """
        time_now = make_aware(datetime.datetime.now())
        etl_status_params = (
            {"integration_ids": integration_ids} if self.upstream_sync_required else {}
        )
        if self.post_upstream_required:
            etl_status_params = add_sync_period_to_params(
                self.client_id, etl_status_params, time_now
            )

        if self.upstream_sync_required and self.post_upstream_required:
            self.insert_sync_status(
                task=ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
                audit=audit,
                etl_status_params=etl_status_params,
                additional_info=additional_info,
            )
        elif self.upstream_sync_required and not self.post_upstream_required:
            self.insert_sync_status(
                task=ETL_ACTIVITY.CONNECTOR_UPSTREAM_SYNC.value,
                audit=audit,
                etl_status_params=etl_status_params,
                additional_info=additional_info,
            )
        elif not self.upstream_sync_required and self.post_upstream_required:
            self.insert_sync_status(
                task=ETL_ACTIVITY.COMMISSION_CALCULATION.value,
                audit=audit,
                etl_status_params=etl_status_params,
                additional_info=additional_info,
            )

    def run_daily_sync_wrapper(
        self,
        include_upstream_hard_delete_sync=False,
        all_objects_selected=True,
        integration_ids=None,
        audit=None,
        additional_info=None,
        trigger=None,
    ):
        """
        Run the daily sync process, with special handling for Fivetran sync if enabled.

        This method checks if Fivetran sync is enabled for the client and triggers the
        appropriate sync process. If Fivetran sync is running for the requested integration
        IDs, it raises a custom exception. It also logs the sync status and handles any
        exceptions that may occur.

        Args:
            include_upstream_hard_delete_sync (bool): Flag to include hard delete sync in upstream.
            all_objects_selected (bool): Flag indicating if all objects are selected for sync.
            post_upstream_disabled (bool): Flag indicating if post-upstream sync is disabled.
            integration_ids (list, optional): List of integration IDs to sync.
            audit (dict, optional): Audit information for the sync process.
            additional_info (dict, optional): Additional information for the sync process.
            trigger (str, optional): Trigger for the sync process.
        """
        # Check if Fivetran sync is enabled for the client
        fivetran_sync_enabled = has_feature(self.client_id, "fivetran_sync")

        fivetran_sync_log_service = FivetranSyncLogService(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            passed_integration_ids=integration_ids,
            log_context=self.log_context,
        )

        # Add ETL sync status logs
        self.add_etl_sync_status(
            integration_ids=integration_ids,
            audit=audit,
            additional_info=additional_info,
        )

        # Trigger fivetran sync when flag is enabled
        # If integation ids are passed we are checking if those ids belong to any fivetran integration
        if (
            fivetran_sync_enabled
            and fivetran_sync_log_service.check_is_fivetran_integration()
        ):
            try:
                # We should run the fivetran sync only when upstream sync is required
                if self.upstream_sync_required:
                    self.logger.info("BEGIN: TRIGGER FIVETRAN UPSTREAM SYNC")
                    # Save functional params in fivetran sync log
                    etl_params = {
                        "include_upstream_hard_delete_sync": include_upstream_hard_delete_sync,
                        "notification_email_id": self.notification_email_id,
                        "skip_archived_books": self.skip_archived_books,
                        "all_objects_selected": all_objects_selected,
                        "post_upstream_disabled": not self.post_upstream_required,
                        "integration_ids": integration_ids,
                        "trigger": trigger,
                    }
                    # Trigger Fivetran sync
                    triggered_fivetran_sync = (
                        fivetran_sync_log_service.run_fivetran_sync(
                            all_objects_selected, integration_ids or [], etl_params
                        )
                    )
                    if not triggered_fivetran_sync:
                        self.logger.exception(
                            "Requested ETL sync didn't trigger fivetran sync as all the requested objects were running in fivetran scheduled sync"
                        )
                        self.run_daily_sync(
                            include_upstream_hard_delete_sync=include_upstream_hard_delete_sync,
                            all_objects_selected=all_objects_selected,
                            integration_ids=integration_ids,
                            trigger=trigger,
                        )
                    self.logger.info("END: TRIGGERED FIVETRAN UPSTREAM SYNC")
            except MissingApiKeyOrSecretError:
                # Handle failure and log the exception
                self.handle_failure()
                self.logger.exception("API key and API secret must be provided")
            except FivetranSyncRunningError:
                # Handle failure and log the exception
                self.handle_failure()
                self.logger.exception(
                    f"Fivetran sync running for one or more requested integration ids {integration_ids}"
                )
            except Exception as err:
                # Handle failure and log the exception
                self.handle_failure()
                self.logger.exception(f"Exception: While running Fivetran sync {err}")
        else:
            self.logger.info("Running Normal sync")
            self.run_daily_sync(
                include_upstream_hard_delete_sync=include_upstream_hard_delete_sync,
                all_objects_selected=all_objects_selected,
                integration_ids=integration_ids,
                trigger=trigger,
            )

    def run_daily_sync(
        self,
        include_upstream_hard_delete_sync=False,
        all_objects_selected=True,
        integration_ids=None,
        fivetran_failed_integration_ids=None,
        trigger=None,
    ):
        self.logger.info("BEGIN: End to End sync")
        kd = make_aware(datetime.datetime.now())
        try:
            if self.upstream_sync_required:
                self.chain_tasks.extend(
                    upstream_wrapper_sync(
                        self.client_id,
                        self.e2e_sync_run_id,
                        log_context=self.log_context,
                        all_objects_selected=all_objects_selected,
                        integration_ids=integration_ids,
                        include_upstream_hard_delete_sync=include_upstream_hard_delete_sync,
                        fivetran_failed_integration_ids=fivetran_failed_integration_ids,
                        trigger=trigger,
                    )
                )
            if self.post_upstream_required:
                self.chain_tasks.append(
                    maestro_daily_sync.si(
                        client_id=self.client_id,
                        e2e_sync_run_id=self.e2e_sync_run_id,
                        run_previous_period_sync=self.run_previous_period_sync,
                        sync_date=kd,
                        secondary_kd=kd,
                    ).set(queue=self.maestro_queue_name)
                )

            else:
                self.chain_tasks.extend(
                    self.set_complete_status(
                        log_message="END: End to End sync completed"
                    )
                )
            chain(*self.chain_tasks).apply_async(
                compression="lzma", serializer="pickle"
            )
            self.logger.info("TRIGGERED: Tasks for End to End sync")
        except Exception:
            self.handle_failure()
            self.logger.exception("Exception: Tasks for End to End sync")

    def sync_analytics_default_dashboard_databook(
        self, databook_id, status_update_task
    ):
        """
        Triggers the first time sync of the analytics default dashboard databook
        """
        self.logger.info("BEGIN: Analytics default dashboard databook sync")
        databook_ids = [databook_id]
        try:
            activity = ETL_ACTIVITY.REFRESH_DATABOOK.value
            etl_status_params = (
                {
                    "refresh_databook": True,
                    "run_databook_sync_only": True,
                },
            )
            self.insert_sync_status(task=activity, etl_status_params=etl_status_params)
            report_objs = get_associated_report_objects(self.client_id, databook_ids)
            self.chain_tasks.extend(
                self.get_report_etl_wrapper_tasks(report_objs),
            )
            self.chain_tasks.extend(
                self.get_databook_wrapper_tasks(databook_ids=databook_ids)
            )
            self.chain_tasks.extend(
                self.set_complete_status(log_message="END: Databook sync")
            )
            self.chain_tasks.append(status_update_task)
            chain(*self.chain_tasks).apply_async(
                compression="lzma", serializer="pickle"
            )
            self.logger.info("TRIGGERED: Analytics default dashboard databook sync")
        except Exception:
            self.logger.exception("Error generating databook %s", databook_id)
            self.handle_failure()

    def run_databook_commission_sync(
        self,
        refresh_databook=False,
        run_databook_sync_only=False,
        databook_ids=None,
        payee_list=None,
        audit=None,
        etl_status_params=None,
        curr_date=None,
        end_date=None,
        is_flow_eligible_for_trigger_only_associated_ds=False,
        additional_info=None,
    ):
        self.logger.info("BEGIN: Databook/Commission sync")
        try:
            activity = (
                ETL_ACTIVITY.REFRESH_DATABOOK.value
                if run_databook_sync_only
                else ETL_ACTIVITY.COMMISSION_CALCULATION.value
            )

            etl_status_params = add_sync_period_to_params(
                self.client_id, etl_status_params, curr_date
            )
            self.insert_sync_status(
                task=activity,
                audit=audit,
                etl_status_params=etl_status_params,
                additional_info=additional_info,
            )
            report_objs = []
            if databook_ids:
                report_objs = get_associated_report_objects(
                    self.client_id, databook_ids
                )
            if run_databook_sync_only:
                params = {
                    "client_id": self.client_id,
                    "e2e_sync_run_id": self.e2e_sync_run_id,
                    "is_run_databook_sync_only": True,
                    "databook_refresh_params": {
                        "databook_ids_to_refresh": databook_ids,
                        "system_report_objects": report_objs,
                    },
                }
                logger.info(
                    f"Running databook sync only for client_id - {self.client_id} and e2e_sync_run_id - {self.e2e_sync_run_id} - {is_flow_eligible_for_trigger_only_associated_ds}"
                )
                self.chain_tasks.append(
                    maestro_on_demand_sync.si(**params).set(
                        queue=self.maestro_queue_name
                    )
                )
            else:
                if refresh_databook:
                    if report_objs:
                        system_report_objs = []
                        for obj in report_objs:
                            if obj in get_report_object_ids(
                                data_origin=["system_object"]
                            ):
                                system_report_objs.append(obj)

                if self.task_orchestration == "celery-dynamic-chaining":
                    kd = make_aware(datetime.datetime.now())
                    sync_date = kd.strftime("%d/%m/%Y")
                    if curr_date:
                        sync_date = curr_date.strftime("%d/%m/%Y")
                    if end_date:
                        sync_end_date = end_date.strftime("%d/%m/%Y")
                    if not refresh_databook:
                        # The databook IDs to refresh and the system report_objs are explicitly set to empty list
                        # (previously it will have stale report & stale datasheet details related to the commission plan)
                        # However, if refresh_databook is false, we can skip this generation
                        # since the user is aware that the system report and datasheet will not be generated, even if the data is stale.
                        databook_ids = []
                        report_objs = []
                    params = {
                        "client_id": self.client_id,
                        "e2e_sync_run_id": self.e2e_sync_run_id,
                        "run_previous_period_sync": self.run_previous_period_sync,
                        "sync_date": sync_date,
                        "sync_end_date": sync_end_date,
                        "secondary_kd": kd,
                        "payee_list": payee_list,
                        "notification_email_id": self.notification_email_id,
                        "databook_refresh_params": {
                            "databook_ids_to_refresh": databook_ids,
                            "system_report_objects": report_objs,
                        },
                    }
                    self.chain_tasks.append(
                        maestro_on_demand_sync.si(**params).set(
                            queue=self.maestro_queue_name
                        )
                    )
                else:
                    self.get_commission_tasks(
                        level=CommCalcLevels.L1.value,
                        payee_list=payee_list,
                        curr_date=curr_date,
                        is_after_db_refresh=refresh_databook,
                    )
                    if self.split_commission_etl_sync:
                        self.logger.info(
                            "SPLIT COMMISSION ETL SYNC calling post_l1_commission_wrapper_sync"
                        )
                        self.chain_tasks.append(
                            post_l1_commission_wrapper_sync.si(
                                client_id=self.client_id,
                                e2e_sync_run_id=self.e2e_sync_run_id,
                                log_context=self.log_context,
                                notification_email=self.notification_email_id,
                                skip_archived_books=self.skip_archived_books,
                                run_previous_period_sync=self.run_previous_period_sync,
                                payee_list=payee_list,
                                curr_date=curr_date,
                                refresh_databook=refresh_databook,
                            ).set(queue=self.commission_queue_name)
                        )
                    else:
                        if self.run_intermediate_sync:
                            self.get_inter_and_l2_tasks(
                                payee_list, curr_date, refresh_databook
                            )

                        self.get_notify_attainment_tasks()

                        # This should be the last task in the chain.
                        # It will create post commission tasks only after all the commission tasks are completed.
                        self.chain_tasks.append(
                            post_commission_wrapper_sync.si(
                                client_id=self.client_id,
                                e2e_sync_run_id=self.e2e_sync_run_id,
                                log_context=self.log_context,
                                notification_email=self.notification_email_id,
                                skip_archived_books=self.skip_archived_books,
                                sync_end_message="END: Commission sync",
                                payee_list=payee_list,
                                curr_date=curr_date,
                                run_previous_period_sync=self.run_previous_period_sync,
                            ).set(queue=self.commission_queue_name)
                        )
            chain(*self.chain_tasks).apply_async(
                compression="lzma", serializer="pickle"
            )
            self.logger.info("TRIGGERED: Databook/Commission sync")

        # if the sync is already running, raise an ETLConcurrencyException and bubble it up
        except ETLConcurrencyException:
            self.logger.info("ETLConcurrencyException: Databook/Commission sync")
            self.handle_failure()
            self.logger.info("Followed through with the handle_failure")
            raise

        except Exception:
            self.logger.info(traceback.format_exc())
            self.handle_failure()
            self.logger.info("Exception: Databook/Commission sync")

    def run_report_sync(self, report_object_id, audit=None):
        self.logger.info("BEGIN: Report sync")
        etl_status_params = {"object": report_object_id, "run_report_sync": 1}
        self.insert_sync_status(
            task="Report ETL", audit=audit, etl_status_params=etl_status_params
        )
        self.chain_tasks.extend(
            self.get_report_etl_wrapper_tasks([report_object_id], "all")
        )
        self.chain_tasks.extend(
            self.set_complete_status(log_message="END: Report sync")
        )
        chain(*self.chain_tasks).apply_async(compression="lzma", serializer="pickle")
        self.logger.info("TRIGGERED: Report sync")

    def run_report_sync_by_period(
        self,
        report_object_id,
        period_list,
        payee_list,
        ped_email_filter_map,
        sync_period,
        audit=None,
    ):
        self.logger.info("BEGIN: Report sync by Period")
        etl_status_params = {
            "object": report_object_id,
            "run_report_sync": 1,
            "periods": [period.strftime("%Y-%m-%d") for period in (period_list or [])],
            "payees": payee_list,
        }
        etl_status_params = add_sync_period_to_params(
            self.client_id, etl_status_params, sync_period
        )
        self.insert_sync_status(
            task="Report ETL", audit=audit, etl_status_params=etl_status_params
        )
        self.chain_tasks.extend(
            report_object_etl_by_period_wrapper(
                client_id=self.client_id,
                ped=period_list,
                sped=None,
                payee_email_id=payee_list,
                is_unfreeze_mode=False,
                is_custom_mode=True,
                e2e_sync_run_id=self.e2e_sync_run_id,
                report_object_id=report_object_id,
                ped_email_filter_map=ped_email_filter_map,
            )
        )
        self.chain_tasks.extend(
            self.set_complete_status(log_message="END: Report sync")
        )
        chain(*self.chain_tasks).apply_async(compression="lzma", serializer="pickle")
        self.logger.info("TRIGGERED: Report sync by Period")

    def copy_inter_object_data(self, psd, object_type, audit):
        self.logger.info("BEGIN: Inter Object Migration sync")
        try:
            etl_status_params = add_sync_period_to_params(self.client_id, {}, psd)
            self.insert_sync_status(
                task=ETL_ACTIVITY.MIGRATE_INTER_DATA.value,
                audit=audit,
                etl_status_params=etl_status_params,
            )
            self.chain_tasks.extend(
                payee_inter_object_wrapper_sync(
                    client_id=self.client_id,
                    e2e_sync_run_id=self.e2e_sync_run_id,
                    period_start_date=psd,
                    notification_email_id=self.notification_email_id,
                    object_type=object_type,
                )
            )
            self.chain_tasks.extend(
                self.set_complete_status(log_message="END: Inter Object Migration sync")
            )
            chain(*self.chain_tasks).apply_async(
                compression="lzma", serializer="pickle"
            )
            self.logger.info("TRIGGERED: Inter Object Migration sync")

        # if the sync is already running, raise an ETLConcurrencyException and bubble it up
        except ETLConcurrencyException:
            self.logger.info("ETLConcurrencyException: Inter Object Migration sync")
            self.handle_failure()
            self.logger.info("Followed through with the handle_failure")
            raise

        except Exception:
            self.logger.info(traceback.format_exc())
            self.handle_failure()
            self.logger.info("Exception: Inter Object Migration sync")

    def run_settlement_sync(
        self,
        refresh_databook=False,
        databook_ids=None,
        payee_list=None,
        audit=None,
        etl_status_params=None,
        curr_date=None,
        is_flow_eligible_for_trigger_only_associated_ds=False,
    ):
        self.logger.info("BEGIN: Settlement sync")
        settlement_v3_execution_mode = get_settlement_v3_execution_mode(self.client_id)
        try:
            activity = ETL_ACTIVITY.SETTLEMENT_CALCULATION.value

            etl_status_params = add_sync_period_to_params(
                self.client_id, etl_status_params, curr_date
            )
            self.insert_sync_status(
                task=activity, audit=audit, etl_status_params=etl_status_params
            )
            report_objs = []
            if databook_ids:
                report_objs = get_associated_report_objects(
                    self.client_id, databook_ids
                )
            if refresh_databook:
                if report_objs:
                    system_report_objs = []
                    for obj in report_objs:
                        if obj in get_report_object_ids(data_origin=["system_object"]):
                            system_report_objs.append(obj)
                    if system_report_objs:
                        self.chain_tasks.extend(
                            self.get_report_etl_wrapper_tasks(
                                report_objects=system_report_objs,
                                sync_type="system_report",
                            )
                        )
                self.chain_tasks.extend(
                    self.get_databook_wrapper_tasks(
                        sync_type=SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value,
                        databook_ids=databook_ids,
                        is_flow_eligible_for_trigger_only_associated_ds=is_flow_eligible_for_trigger_only_associated_ds,
                    )
                )
            settlement_v3_sync_params = {
                "client_id": self.client_id,
                "e2e_sync_run_id": self.e2e_sync_run_id,
                "log_context": self.log_context,
                "notification_email_id": self.notification_email_id,
                "run_previous_period_sync": self.run_previous_period_sync,
                "curr_date": curr_date,
                "payee_list": payee_list,
            }
            if settlement_v3_execution_mode != ExecutionMode.ACTUAL_RUN:
                self.chain_tasks.extend(
                    self.get_settlement_wrapper_tasks(
                        payee_list=payee_list,
                        is_after_comm_sync=False,
                        curr_date=curr_date,
                        set_update_payout_status_details=True,
                    )
                )
            else:
                self.chain_tasks.append(
                    run_settlement_v3_task.si(
                        settlement_v3_sync_params, settlement_v3_execution_mode
                    ).set(queue=self.settlement_queue_name)
                )
            if settlement_v3_execution_mode == ExecutionMode.DRY_RUN:
                self.chain_tasks.append(
                    dry_run_settlement_v3_sync.si(settlement_v3_sync_params).set(
                        queue=self.settlement_v3_dry_run_queue_name
                    )
                )
            # This should be the last task in the chain.
            # It will create post settlement tasks only after all the settlement tasks are completed.
            self.chain_tasks.append(
                post_settlement_wrapper_sync.si(
                    client_id=self.client_id,
                    e2e_sync_run_id=self.e2e_sync_run_id,
                    log_context=self.log_context,
                    notification_email=self.notification_email_id,
                    skip_archived_books=self.skip_archived_books,
                    payee_list=payee_list,
                    curr_date=curr_date,
                ).set(queue=self.commission_queue_name)
            )
            chain(*self.chain_tasks).apply_async(
                compression="lzma", serializer="pickle"
            )
            self.logger.info("TRIGGERED: Settlement sync")

        # if the sync is already running, raise an ETLConcurrencyException and bubble it up
        except ETLConcurrencyException:
            self.logger.info("ETLConcurrencyException: Settlement sync")
            self.handle_failure()
            self.logger.info("Followed through with the handle_failure")
            raise

        except Exception:
            self.logger.info(traceback.format_exc())
            self.handle_failure()
            self.logger.info("Exception: Settlement sync")

    def run_forecast_sync(
        self,
        refresh_databook=False,
        databook_ids=None,
        payee_list=None,
        audit=None,
        etl_status_params=None,
        curr_date=None,
        is_flow_eligible_for_trigger_only_associated_ds=False,
    ):
        self.logger.info("BEGIN: Forecast sync")
        try:
            activity = ETL_ACTIVITY.FORECAST_CALCULATION.value

            etl_status_params = add_sync_period_to_params(
                self.client_id, etl_status_params, curr_date
            )
            self.insert_sync_status(
                task=activity, audit=audit, etl_status_params=etl_status_params
            )
            report_objs = []
            if databook_ids:
                report_objs = get_associated_report_objects(
                    self.client_id, databook_ids
                )

            if refresh_databook:
                if report_objs:
                    system_report_objs = []
                    for obj in report_objs:
                        if obj in get_report_object_ids(data_origin=["system_object"]):
                            system_report_objs.append(obj)
                    if system_report_objs:
                        self.chain_tasks.extend(
                            self.get_report_etl_wrapper_tasks(
                                report_objects=system_report_objs,
                                sync_type="system_report",
                            )
                        )
                self.chain_tasks.extend(
                    self.get_databook_wrapper_tasks(
                        sync_type=SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value,
                        databook_ids=databook_ids,
                        is_flow_eligible_for_trigger_only_associated_ds=is_flow_eligible_for_trigger_only_associated_ds,
                    )
                )

            self.get_forecast_tasks(
                level=CommCalcLevels.L1.value,
                payee_list=payee_list,
                curr_date=curr_date,
                is_after_db_refresh=refresh_databook,
            )
            if self.run_intermediate_sync:
                self.get_inter_and_l2_tasks_for_forecast(
                    payee_list, curr_date, refresh_databook
                )

            self.chain_tasks.extend(
                self.get_report_etl_wrapper_tasks(
                    report_objects=get_report_object_ids(
                        data_origin=["forecast_object"]
                    ),
                    sync_type="forecast_report",
                )
            )
            self.chain_tasks.extend(
                self.get_databook_wrapper_tasks(
                    sync_type=SYNC_OBJECT.DATABOOK_FORECAST_OBJECT.value
                )
            )

            self.chain_tasks.extend(
                self.set_complete_status(log_message="END: Forecast sync")
            )
            chain(*self.chain_tasks).apply_async(
                compression="lzma", serializer="pickle"
            )
            self.logger.info("TRIGGERED: Forecast sync")

        # if the sync is already running, raise an ETLConcurrencyException and bubble it up
        except ETLConcurrencyException:
            self.logger.info("ETLConcurrencyException: Forecast sync")
            self.handle_failure()
            self.logger.info("Followed through with the handle_failure")
            raise

        except Exception:
            self.logger.info(traceback.format_exc())
            self.handle_failure()
            self.logger.info("Exception: Forecast sync")


def get_valid_payout_freq(client_id, params):
    try:
        # when payees in commission plan is selected
        if params["plan_list"]:
            plans = CommissionPlanAccessor(client_id).get_plans_by_ids(
                params["plan_list"]
            )
            # converting string to naive datetime
            start_date = datetime.datetime.strptime(params["start_date"], "%Y/%m/%d")
            end_date = datetime.datetime.strptime(params["end_date"], "%Y/%m/%d")

            # converting naive to aware datetime
            start_date = start_date.replace(tzinfo=datetime.timezone.utc)
            end_date = end_date.replace(tzinfo=datetime.timezone.utc)

            grouped = defaultdict(
                lambda: {"plan_start_date": None, "plan_end_date": None}
            )

            for item in plans:
                freq = item.payout_frequency
                plan_start_date = item.plan_start_date
                plan_end_date = item.plan_end_date

                if (
                    grouped[freq]["plan_start_date"] is None
                    or plan_start_date < grouped[freq]["plan_start_date"]
                ):
                    grouped[freq]["plan_start_date"] = plan_start_date
                if (
                    grouped[freq]["plan_end_date"] is None
                    or plan_end_date > grouped[freq]["plan_end_date"]
                ):
                    grouped[freq]["plan_end_date"] = plan_end_date

            # Convert the grouped data to the desired format
            result = [
                {
                    "payout_freq": freq,
                    "start_date": (
                        max(details["plan_start_date"], start_date).isoformat()
                        if details["plan_start_date"]
                        else start_date.isoformat()
                    ),
                    "end_date": (
                        min(details["plan_end_date"], end_date).isoformat()
                        if details["plan_end_date"]
                        else end_date.isoformat()
                    ),
                }
                for freq, details in grouped.items()
            ]

        # if all or selected payees is selected
        else:
            pay_list = (
                tuple(params["payee_list"]) if params["payee_list"] is not None else ()
            )
            new_params = {
                "client_id": client_id,
                "start_date": params["start_date"],
                "end_date": params["end_date"],
                "payee_list": pay_list,
            }
            # converting string to naive datetime
            start_date = datetime.datetime.strptime(params["start_date"], "%Y/%m/%d")
            end_date = datetime.datetime.strptime(params["end_date"], "%Y/%m/%d")

            # converting naive to aware datetime
            start_date = start_date.replace(tzinfo=datetime.timezone.utc)
            end_date = end_date.replace(tzinfo=datetime.timezone.utc)

            data = EmployeePayrollAccessor(client_id).get_payee_payout_freq_map(
                new_params
            )
            grouped = defaultdict(lambda: {"start_date": None, "end_date": None})

            for item in data:
                freq = item["payout_frequency"]
                plan_start_date = item["effective_start_date"]
                plan_end_date = item["effective_end_date"]

                if (
                    grouped[freq]["start_date"] is None
                    or plan_start_date < grouped[freq]["start_date"]
                ):
                    grouped[freq]["start_date"] = plan_start_date
                if (
                    grouped[freq]["end_date"] is None
                    or plan_end_date > grouped[freq]["end_date"]
                ):
                    grouped[freq]["end_date"] = plan_end_date

            # Convert to desired output format
            result = [
                {
                    "payout_freq": freq,
                    "start_date": (
                        max(details["start_date"], start_date).isoformat()
                        if details["start_date"]
                        else start_date.isoformat()
                    ),
                    "end_date": (
                        min(details["end_date"], end_date).isoformat()
                        if details["end_date"]
                        else end_date.isoformat()
                    ),
                }
                for freq, details in grouped.items()
            ]

    except Exception as e:
        logger.exception("Get valid payout freq service: %s", e)
    return result


def should_run_in_delete_sync_mode(client_id):
    """
    Runs before running Daily Sync
    Compares the schduled hard delete sync date and the current date
    Returns the flag as true when:
        1. The task is scheduled as daily
        2. The current day of the week matches the scheduled day of the week(Eg: Monday)
        3. The current day of the month matches the scheduled day of the month(Eg: 10)

    params:
        client_id: client_id for the hard delete task

    returns:
        include_upstream_hard_delete_sync: return true or false based on the cron
    """

    all_hard_delete_tasks = HardDeleteAccessor(client_id).get_active_hard_delete_tasks()
    if all_hard_delete_tasks:
        current_time = now()
        current_day = current_time.day
        current_weekday = current_time.weekday()
        for task in all_hard_delete_tasks:
            cron = CrontabScheduleAccessor().get_cron_schedule_by_id(
                cron_id=task.crontab_id
            )[0]
            if (
                cron["day_of_week"] == "*"
                and cron["month_of_year"] == "*"
                and cron["day_of_month"] == "*"
            ):
                return True
            elif (
                (
                    cron["day_of_week"] == str(current_weekday + 1)
                    or (
                        cron["day_of_week"] == "0" and current_weekday == 6
                    )  # To handle sunday as 0 in cron schedule
                )
                and cron["month_of_year"] == "*"
                and cron["day_of_month"] == "*"
            ):  # Monday-Sunday in CronSchedule is denoted by 1-7 whereas in timezone.now() is denoted by 0-6
                return True
            elif (
                cron["day_of_week"] == "*"
                and cron["month_of_year"] == "*"
                and cron["day_of_month"] == str(current_day)
            ):
                return True
    return False


@shared_task(base=EverCeleryBaseTask)
def run_daily_sync_from_cron(client_id, retry_signature=None):
    e2e_sync_run_id = uuid.uuid4()
    include_upstream_hard_delete_sync = should_run_in_delete_sync_mode(
        client_id=client_id
    )
    log_context = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        include_upstream_hard_delete_sync: include_upstream_hard_delete_sync,
    }
    set_threadlocal_context(log_context)

    if EtlGlobalSyncStatusService.is_global_sync_enabled_for_client(client_id):

        # if the retry history is not empty, that means the sync currently running is a retry of a previous failed cron job
        # so we need to set the retry signature as the retry history
        additional_info = {
            "is_cron_job": True,
            "retry_signature": retry_signature or [],
            "marked_as_failed": False,
            "marked_as_failed_by": None,
            "was_retried": False,
        }

        ETLSync(
            client_id, e2e_sync_run_id, log_context, run_previous_period_sync=True
        ).run_daily_sync_wrapper(
            include_upstream_hard_delete_sync=include_upstream_hard_delete_sync,
            additional_info=additional_info,
            trigger=ETLTriggers.SCHEDULED_RUN.value,
        )
    else:
        logger.info(
            "global sync not set, skipping daily sync task for client: %s", client_id
        )


@shared_task(base=EverCeleryBaseTask)
def retry_cron_job(client_id, e2e_sync_run_id):
    try:
        # if the type of the sync is e2e sync/upstream sync
        # then we need to run the sync again
        accessor = ETLSyncStatusAccessor(client_id, e2e_sync_run_id)
        failed_sync = accessor.get_object()[0]

        if failed_sync.sync_status == ETL_STATUS.PARTIALLY_FAILED.value:
            # set the given sync as retired
            accessor.mark_cron_job_as_retried()

            # run the cron job again
            retry_signature = failed_sync.additional_info["retry_signature"]
            retry_signature.append(e2e_sync_run_id)
            run_daily_sync_from_cron(client_id, retry_signature=retry_signature)
        else:
            raise Exception(
                f"Failed to retry cron job: Sync is not partially failed. Sync status: {failed_sync.sync_status}"
            )

        logger.info(
            f"Cron job retried for client {client_id} and e2e sync run id {e2e_sync_run_id}"
        )

    except Exception as e:
        logger.error(
            f"Failed to retry cron job for client {client_id} and e2e sync run id {e2e_sync_run_id}: {e}"
        )
        raise e


@shared_task(base=EverCeleryBaseTask)
def run_daily_databook_sync_from_cron(client_id, retry_signature=None):
    """
    Runs daily databook sync (no commission sync).  This is useful for cases (like everstage-engineering) where there are
    no commission numbers to calculate
    """
    e2e_sync_run_id = uuid.uuid4()
    log_context = {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
    set_threadlocal_context(log_context)
    if EtlGlobalSyncStatusService.is_global_sync_enabled_for_client(client_id):
        ETLSync(client_id, e2e_sync_run_id, log_context).run_databook_commission_sync(
            refresh_databook=True,
            run_databook_sync_only=True,
            etl_status_params={
                "refresh_databook": True,
                "run_databook_sync_only": True,
            },
            additional_info={
                "is_cron_job": True,
                "retry_signature": retry_signature or [],
                "marked_as_failed": False,
                "marked_as_failed_by": None,
                "was_retried": False,
            },
        )
    else:
        logger.info(
            "global sync not set, skipping databook sync task for client: %s", client_id
        )


@shared_task(base=EverCeleryBaseTask)
def run_databook_commission_async(
    params, is_flow_eligible_for_trigger_only_associated_ds=False
):
    logger_loc = params.get("logger")
    logger_loc.info("BEGIN: Run Databook Commission task schedule sync")
    ETLSync(
        client_id=params.get("client_id"),
        e2e_sync_run_id=params.get("e2e_sync_run_id"),
        log_context=params.get("log_context"),
        notification_email_id=params.get("notification_email_id"),
        skip_archived_books=params.get("skip_archived_books"),
        run_previous_period_sync=params.get("run_previous_period_sync", False),
    ).run_databook_commission_sync(
        refresh_databook=params.get("refresh_databook"),
        run_databook_sync_only=params.get("run_databook_sync_only"),
        databook_ids=params.get("databook_ids"),
        payee_list=params.get("payee_list"),
        audit=params.get("audit"),
        etl_status_params=params.get("etl_status_params"),
        curr_date=params.get("curr_date"),
        end_date=params.get("end_date"),
        is_flow_eligible_for_trigger_only_associated_ds=is_flow_eligible_for_trigger_only_associated_ds,
    )
    logger_loc.info("END: Run Databook Commission task schedule sync")


def add_sync_period_to_params(client_id, etl_status_params, sync_period):
    if not sync_period:
        return etl_status_params

    custom_calendar = has_feature(client_id, "custom_calendar")
    if not etl_status_params:
        etl_status_params = {}
    if not custom_calendar:
        sync_period = last_day_of_month(sync_period)
    etl_status_params["sync_period"] = sync_period.strftime("%d %b %Y")

    return etl_status_params


@shared_task(base=EverCeleryBaseTask)
def post_commission_wrapper_sync(
    client_id,
    e2e_sync_run_id,
    log_context,
    notification_email,
    skip_archived_books,
    sync_end_message="END: End to End sync completed",
    payee_list=None,
    curr_date=None,
    run_previous_period_sync=False,
):
    """
    Shared task that executes all tasks following the commission sync.
    The update payouts tasks are created based on the records created by commission and settlement sync.
    So this should not be called before the commission sync is completed.

    The order of the tasks is important and should not be changed.

    1. payout snapshot tasks
    2. settlement calculation tasks
    3. update_payouts_tasks
    4. settlement snapshot tasks
    5. report etl tasks
    6. report databook etl tasks
    7. set complete status task
    """

    # Get the latest sync start time for commission calculation or e2e sync tasks which got completed
    etl_start_time = ETLSyncStatusReaderAccessor(
        client_id
    ).get_latest_successful_sync_start_time_for_activities(
        e2e_sync_run_id,
        [
            ETL_ACTIVITY.COMMISSION_CALCULATION.value,
            ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
        ],
    )

    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context,
        notification_email,
        skip_archived_books,
        run_previous_period_sync=run_previous_period_sync,
    )
    update_payouts_tasks = []

    etl_sync_obj.get_snapshot_tasks(payee_list=payee_list, curr_date=curr_date)
    etl_sync_obj.get_settlement_tasks(
        payee_list=payee_list, curr_date=curr_date, is_after_comm_sync=True
    )
    if etl_start_time:
        update_payouts_tasks = get_update_payout_tasks(
            client_id,
            e2e_sync_run_id,
            etl_start_time,
            etl_sync_obj.commission_queue_name,
            notification_email_id=notification_email,
        )
        etl_sync_obj.chain_tasks.extend(update_payouts_tasks)
    etl_sync_obj.get_snapshot_tasks(
        payee_list=payee_list, curr_date=curr_date, snapshot_type="settlement"
    )
    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.get_report_etl_wrapper_tasks(
            report_objects=get_report_object_ids(data_origin=["commission_object"]),
            sync_type="commission_report",
        )
    )
    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.get_databook_wrapper_tasks(
            sync_type=SYNC_OBJECT.DATABOOK_COMMISSION_OBJECT.value
        )
    )

    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.set_complete_status(log_message=sync_end_message)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )


@shared_task(base=EverCeleryBaseTask)
def post_settlement_wrapper_sync(
    client_id,
    e2e_sync_run_id,
    log_context,
    notification_email,
    skip_archived_books,
    payee_list=None,
    curr_date=None,
):
    """
    Shared task that executes all tasks following the settlement sync (datasheet changes mode).
    The update payouts tasks are created based on the records created by commission and settlement sync.
    So this should not be called before the settlement sync is completed.
    """
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context,
        notification_email,
        skip_archived_books,
    )

    # Get the latest sync start time for commission calculation or e2e sync tasks which got completed
    etl_start_time = ETLSyncStatusReaderAccessor(
        client_id
    ).get_latest_successful_sync_start_time_for_activities(
        e2e_sync_run_id,
        [
            ETL_ACTIVITY.COMMISSION_CALCULATION.value,
            ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
        ],
    )
    if etl_start_time:
        etl_sync_obj.chain_tasks.extend(
            get_update_payout_tasks(
                client_id,
                e2e_sync_run_id,
                etl_start_time,
                etl_sync_obj.commission_queue_name,
                notification_email_id=notification_email,
            )
        )

    etl_sync_obj.get_snapshot_tasks(
        payee_list=payee_list, curr_date=curr_date, snapshot_type="settlement"
    )
    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.get_report_etl_wrapper_tasks(
            report_objects=["settlement"],
            sync_type="settlement_report",
        )
    )
    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.get_databook_wrapper_tasks(
            sync_type=SYNC_OBJECT.DATABOOK_SETTLEMENT_OBJECT.value
        )
    )

    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.set_complete_status(log_message="END: Settlement sync")
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )


@shared_task(base=EverCeleryBaseTask)
def post_l1_commission_wrapper_sync(
    client_id,
    e2e_sync_run_id,
    log_context,
    notification_email,
    skip_archived_books,
    run_previous_period_sync,
    payee_list=None,
    curr_date=None,
    refresh_databook=False,
):
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context,
        notification_email,
        skip_archived_books,
        run_previous_period_sync=run_previous_period_sync,
    )
    if etl_sync_obj.run_intermediate_sync:
        etl_sync_obj.get_inter_and_l2_tasks(
            payee_list=payee_list,
            curr_date=curr_date,
            refresh_databook=refresh_databook,
        )
    etl_sync_obj.logger.info(
        "SPLIT COMMISSION ETL SYNC calling post_l2_commission_wrapper_sync"
    )
    etl_sync_obj.chain_tasks.append(
        post_l2_commission_wrapper_sync.si(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            log_context=log_context,
            notification_email=notification_email,
            skip_archived_books=skip_archived_books,
            run_previous_period_sync=run_previous_period_sync,
        ).set(queue=etl_sync_obj.commission_queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )


@shared_task(base=EverCeleryBaseTask)
def post_l2_commission_wrapper_sync(
    client_id,
    e2e_sync_run_id,
    log_context,
    notification_email,
    skip_archived_books,
    run_previous_period_sync,
):
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context,
        notification_email,
        skip_archived_books,
        run_previous_period_sync=run_previous_period_sync,
    )
    etl_sync_obj.get_notify_attainment_tasks()
    etl_sync_obj.logger.info(
        "SPLIT COMMISSION ETL SYNC calling post_commission_wrapper_sync"
    )
    # This should be the last task in the chain.
    # It will create post commission tasks only after all the commission tasks are completed.
    etl_sync_obj.chain_tasks.append(
        post_commission_wrapper_sync.si(
            client_id=etl_sync_obj.client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            log_context=log_context,
            notification_email=notification_email,
            skip_archived_books=skip_archived_books,
            run_previous_period_sync=run_previous_period_sync,
        ).set(queue=etl_sync_obj.commission_queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )


@shared_task(base=EverCeleryBaseTask)
def maestro_daily_sync(
    client_id,
    e2e_sync_run_id,
    run_previous_period_sync,
    sync_date,
    secondary_kd,
):
    from commission_engine.services.maestro import run_sync_stage

    logger.info(
        f"MAESTRO: Inside maestro_daily_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    run_sync_stage(
        None,
        {
            "client_id": client_id,
            "e2e_sync_run_id": e2e_sync_run_id,
            "run_previous_period_sync": run_previous_period_sync,
            "sync_date": sync_date,
            "secondary_kd": secondary_kd,
            "is_maestro_daily_sync": True,
        },
    )


@shared_task(base=EverCeleryBaseTask)
def maestro_on_demand_sync(
    client_id,
    e2e_sync_run_id,
    databook_refresh_params,
    sync_date=None,
    run_previous_period_sync=None,
    sync_end_date=None,
    secondary_kd=None,
    payee_list=None,
    notification_email_id=None,
    is_run_databook_sync_only=False,
):
    from commission_engine.services.maestro import (
        get_all_stage_names,
        get_stages,
        run_commission_for_multiple_period_prework,
        run_sync_stage,
    )

    logger.info(
        f"MAESTRO: Inside maestro_on_demand_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id} and databook_refresh_params: {databook_refresh_params}"
    )
    stage_names = get_all_stage_names()
    # Ignoring the first `e2e_prework` task as it is already put in chain when triggering
    # from UI.
    stage_names = stage_names[1:]
    if is_run_databook_sync_only:
        stage_names = get_stages(task="databook_sync")
        run_sync_stage(
            ",".join(stage_names),
            {
                "client_id": client_id,
                "e2e_sync_run_id": e2e_sync_run_id,
                "system_report_objects": databook_refresh_params[
                    "system_report_objects"
                ],
                "databook_ids_to_refresh": databook_refresh_params[
                    "databook_ids_to_refresh"
                ],
                "sync_date": sync_date,
            },
        )
    elif sync_date != sync_end_date:
        run_commission_for_multiple_period_prework(
            {
                "client_id": client_id,
                "e2e_sync_run_id": e2e_sync_run_id,
                "run_previous_period_sync": run_previous_period_sync,
                "sync_date": sync_date,
                "sync_end_date": sync_end_date,
                "secondary_kd": secondary_kd,
                "payee_list": payee_list,
                "notification_email_id": notification_email_id,
                "databook_refresh_params": databook_refresh_params,
            }
        )
    else:
        run_sync_stage(
            ",".join(stage_names),
            {
                "client_id": client_id,
                "e2e_sync_run_id": e2e_sync_run_id,
                "run_previous_period_sync": run_previous_period_sync,
                "sync_date": sync_date,
                "secondary_kd": secondary_kd,
                "payee_list": payee_list,
                "notification_email_id": notification_email_id,
                "system_report_objects": databook_refresh_params[
                    "system_report_objects"
                ],
                "databook_ids_to_refresh": databook_refresh_params[
                    "databook_ids_to_refresh"
                ],
            },
        )


# settlement v3 maestro tasks
@shared_task(base=EverCeleryBaseTask)
def run_settlement_v3_task(params, execution_mode):
    from commission_engine.services.maestro import get_stage_key, run_settlement_v3_sync
    from everstage_etl.tasks.maestro_tasks import mark_stage_as_started

    e2e_sync_run_id = params["e2e_sync_run_id"]
    client_id = params["client_id"]
    stage_name = "settlement_v3_sync"
    if execution_mode == ExecutionMode.DISABLED:
        logger.info(f"Skipping settlement_v3_sync for client_id: {params['client_id']}")
        return
    if execution_mode == ExecutionMode.DRY_RUN:
        stage_name = stage_name + "_dry_run"
    stage_key = get_stage_key(client_id, e2e_sync_run_id, stage_name)
    mark_stage_as_started(stage_key)
    run_settlement_v3_sync(params, stage_key, execution_mode)


@shared_task(base=EverCeleryBaseTask)
def dry_run_settlement_v3_sync(params):
    subscription_plan = get_client_subscription_plan(params["client_id"])
    task_group = TaskGroupEnum.SETTLEMENT_V3_DRY_RUN.value
    queue_name = iputils.get_queue_name_respect_to_task_group(
        params["client_id"], subscription_plan, task_group
    )
    task = run_settlement_v3_task.si(params, ExecutionMode.DRY_RUN).set(
        queue=queue_name
    )
    task.apply_async(compression="lzma", serializer="pickle")
