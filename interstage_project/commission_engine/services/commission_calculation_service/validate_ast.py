from commission_engine.services.expression_designer import get_expression_format
from spm.accessors.variable_accessor import OperatorAccessor, VariableDataTypeAccessor


class Validate:
    def __init__(self):
        self.errors = []
        self.dataTypeMap = {}
        self.dataTypeRevMap = {}
        self.compatibleTypes = {}
        data_type_list = VariableDataTypeAccessor().get_all_data_types()
        for item in data_type_list:
            self.dataTypeMap[item.data_type.upper()] = item.id
            self.dataTypeRevMap[item.id] = item.data_type.upper()
            self.compatibleTypes[item.id] = item.compatible_type_ids

        self.ops = {}
        operator_list = OperatorAccessor().get_all_operators()
        for op in operator_list:
            ha = {}
            ha["operandType"] = op.operand_type_ids
            ha["outputType"] = op.output_type_ids[0]
            self.ops[op.name] = ha

    def simple_loop_function(self, token):
        if "args" in token and len(token["args"]) == 1:
            return {
                "status": "VALID",
                "msg": token["data_type"],
                "data_type_id": self.dataTypeMap[token["data_type"].upper()],
                "data_type": token["data_type"].upper(),
            }

    def integer_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap["INTEGER"],
            "data_type": token["data_type"].upper(),
        }

    def string_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap["STRING"],
            "data_type": token["data_type"].upper(),
        }

    def boolean_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap["BOOLEAN"],
            "data_type": token["data_type"].upper(),
        }

    def date_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap["DATE"],
            "data_type": token["data_type"].upper(),
        }

    def hierarchy_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap["HIERARCHY"],
            "data_type": token["data_type"].upper(),
        }

    def coalesce_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap[token["data_type"].upper()],
            "data_type": token["data_type"].upper(),
        }

    def get_line_item_value_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap[token["data_type"].upper()],
            "data_type": token["data_type"].upper(),
        }

    def get_value_from_hierarchy_function(self, token):
        return {
            "status": "VALID",
            "msg": token["data_type"],
            "data_type_id": self.dataTypeMap[token["data_type"].upper()],
            "data_type": token["data_type"].upper(),
        }

    def get_user_property_function(self, token):
        data_type = token["args"][2]["data_type"]
        data_type_id = self.dataTypeMap[data_type.upper()]
        return {
            "status": "VALID",
            "msg": data_type,
            "data_type_id": data_type_id,
            "data_type": data_type,
        }

    def loop_with_condition(self, token):
        # pylint: disable=unused-variable
        element, condition = token["args"]
        res = self.validate_exp(condition)
        if res["data_type_id"] == self.dataTypeMap["BOOLEAN"]:
            return {
                "status": "VALID",
                "msg": token["data_type"],
                "data_type_id": self.dataTypeMap[token["data_type"].upper()],
                "data_type": token["data_type"].upper(),
            }
        else:
            self.errors.append("Condition Should Return Boolean")
            return {
                "status": "INVALID",
                "msg": ",".join(self.errors),
                "data_type_id": -1,
                "data_type": "INVALID",
            }

    def constant(self, token):
        data_type = token["args"][0].upper()
        if data_type == "PERCENTAGE":
            return {
                "status": "VALID",
                "msg": "Integer",
                "data_type_id": self.dataTypeMap["INTEGER"],
                "data_type": "INTEGER",
            }
        else:
            return {
                "status": "VALID",
                "msg": data_type,
                "data_type_id": self.dataTypeMap[data_type.upper()],
                "data_type": data_type,
            }

    def handle_config(self, token):
        data_type = token["args"][0]["data_type"].upper()
        if data_type == "PERCENTAGE":
            return {
                "status": "VALID",
                "msg": "Integer",
                "data_type_id": self.dataTypeMap["INTEGER"],
                "data_type": "INTEGER",
            }
        else:
            return {
                "status": "VALID",
                "msg": data_type,
                "data_type_id": self.dataTypeMap[data_type],
                "data_type": data_type,
            }

    def if_function(self, token, all_results=None):
        """
        This method validates the data-types of a if-else expression (including nested if-else) recursively.
        """
        all_results = [] if all_results is None else all_results
        if "args" in token:
            condition_result = self.validate_exp(token["args"][0])
            if (
                condition_result["status"] == "VALID"
                and condition_result["data_type"] == "BOOLEAN"
            ):
                for exp in token["args"][1:]:
                    # Else can be None if "Do Nothing"/"NULL" is enabled
                    if exp is None:
                        continue
                    if (
                        "function_name" in exp
                        and exp["function_name"] == "IF"
                        and "args" in exp
                    ):
                        result = self.if_function(exp, all_results=all_results)
                        all_results.append(result)
                    else:
                        result = self.validate_exp(exp)
                        all_results.append(result)
            else:
                return {
                    "status": "INVALID",
                    "msg": "If condition is not a Boolean expression",
                    "data_type_id": -1,
                    "data_type": "INVALID",
                }
        # Obtaining all the resultant data types from the expressions of "then", "else" statements
        # (excluding "if" and "else if" statements)
        data_types = set()
        for result in all_results:
            if result["status"] == "INVALID":
                return result
            else:
                data_types.add(result["data_type"])
        # Treating INTEGER & PERCENTAGE as INTEGER data type
        if "INTEGER" in data_types and "PERCENTAGE" in data_types:
            data_types.remove("PERCENTAGE")
        # All those expressions ("then","else" statements) should have the same resultant data type
        if len(data_types) > 1:
            return {
                "status": "INVALID",
                "msg": "Expressions are returning different data types",
                "data_type_id": -1,
                "data_type": "INVALID",
            }
        else:
            data_type = list(data_types)[0]
            return {
                "status": "VALID",
                "msg": data_type,
                "data_type_id": self.dataTypeMap[data_type],
                "data_type": data_type,
            }

    functions = {
        "COUNTIF": loop_with_condition,
        "SUMIF": loop_with_condition,
        "CONSTANT": constant,
        "COUNT": simple_loop_function,
        "SUM": simple_loop_function,
        "AVG": simple_loop_function,
        "MIN": simple_loop_function,
        "MAX": simple_loop_function,
        "TieredValue": integer_function,
        "QuotaAttainment": integer_function,
        "QuotaErosion": integer_function,
        "Quota": integer_function,
        "TEAM-SUM": simple_loop_function,
        "TEAM-COUNT": simple_loop_function,
        "TEAM-AVG": simple_loop_function,
        "TEAM-MIN": simple_loop_function,
        "TEAM-MAX": simple_loop_function,
        "TEAM-SUMIF": loop_with_condition,
        "TEAM-COUNTIF": loop_with_condition,
        "TEAM-QuotaAttainment": integer_function,
        "TEAM-QuotaErosion": integer_function,
        "TEAM-Quota": integer_function,
        "Config": handle_config,
        "TieredPercentage": integer_function,
        "CurrentPayoutPeriod": integer_function,
        "GetDate": integer_function,
        "DistinctCount": integer_function,
        "CountNotNull": integer_function,
        "TEAM-COUNT-NOT-NULL": simple_loop_function,
        "TEAM-DISTINCT-COUNT": simple_loop_function,
        "IsEmpty": boolean_function,
        "IsNotEmpty": boolean_function,
        "Contains": boolean_function,
        "NotContains": boolean_function,
        "Trim": string_function,
        "Concat": string_function,
        "Coalesce": coalesce_function,
        "Lower": string_function,
        "Len": integer_function,
        "Left": string_function,
        "Right": string_function,
        "Mid": string_function,
        "Find": integer_function,
        "DATEDIFF": integer_function,
        "IF": if_function,
        "StartDate": date_function,
        "LastDate": date_function,
        "DateAdd": date_function,
        "Round": integer_function,
        "RoundUp": integer_function,
        "RoundDown": integer_function,
        "Rank": integer_function,
        "Timezone": date_function,
        "Rolling": integer_function,
        "Hierarchy": hierarchy_function,
        "GetNthLevelNode": get_value_from_hierarchy_function,
        "GetUserProperty": get_user_property_function,
        "StartsWith": boolean_function,
        "EndsWith": boolean_function,
        "DateIsIn": boolean_function,
        "GetLineItemValue": get_line_item_value_function,
    }

    def handle_variable(self, token):
        if "function_name" in token:
            function = token["function_name"]
            res = Validate.functions[function](self, token)
        else:
            res = {
                "status": "VALID",
                "msg": token["data_type"].upper(),
                "data_type_id": token["meta"]["data_type_id"],
                "data_type": token["data_type"].upper(),
            }
        return res

    def validate_brackets(self, infix):
        cnt = 0
        infix_exp_version = get_expression_format(infix)
        for token in infix:
            token_name = None
            if infix_exp_version == "v2":
                if token["token_type"] == "GROUPING_OPERATORS":
                    token_name = token["token"]["name"]
            else:
                token_name = token["name"]

            if token_name == "(":
                cnt += 1
            if token_name == ")":
                cnt -= 1
            if cnt < 0:
                self.errors.append("Mismatching Brackets")
                return {"status": "INVALID", "msg": "Mismatching Brackets"}
        if cnt == 0:
            return {"status": "VALID", "msg": "VALID"}
        else:
            self.errors.append("Mismatching Brackets")
            return {"status": "INVALID", "msg": "Mismatching Brackets"}

    def validate_exp(self, ast, op_stack=None):
        if not op_stack:
            if isinstance(ast, dict) and "type" in ast and ast["type"] == "VARIABLE":
                return self.handle_variable(ast)
            operator = list(ast.keys())[0] if ast else None
            if operator:
                if operator == "(" or operator == ")":
                    self.errors.append("Invalid Expression. Brackets doesn't match...")
                    return {
                        "status": "INVALID",
                        "msg": "Invalid Expression. Brackets doesn't match...",
                        "data_type": "INVALID",
                        "data_type_id": -1,
                    }
                values = ast[operator]
                if operator == "IN" or operator == "NOTIN":
                    if "data_type" in values[0] and (
                        values[0]["data_type"] == "IntArray"
                        or values[0]["data_type"] == "StringArray"
                    ):
                        return {
                            "status": "INVALID",
                            "msg": "Left operand of "
                            + operator
                            + " cannot be an array",
                            "data_type": "INVALID",
                            "data_type_id": -1,
                        }
                values = [self.validate_exp(val)["data_type_id"] for val in values]
                if (
                    values[0] in self.ops[operator]["operandType"]
                    and values[1] in self.ops[operator]["operandType"]
                    and (
                        (values[0] == values[1])
                        or (
                            values[0] in self.compatibleTypes[values[1]]
                            and values[1] in self.compatibleTypes[values[0]]
                        )
                    )
                ):
                    msg = self.dataTypeRevMap[self.ops[operator]["outputType"]]
                    return {
                        "status": "VALID",
                        "msg": msg,
                        "data_type": msg,
                        "data_type_id": self.ops[operator]["outputType"],
                    }
                else:
                    if not self.errors:
                        operand_types = ",".join(
                            [
                                self.dataTypeRevMap[i]
                                for i in self.ops[operator]["operandType"]
                            ]
                        )
                        self.errors.append(
                            operator + " expects operands of type " + operand_types
                        )
                        return {
                            "status": "INVALID",
                            "msg": operator
                            + " expects operands of type "
                            + operand_types,
                            "data_type": "INVALID",
                            "data_type_id": -1,
                        }
                    else:
                        return {
                            "status": "INVALID",
                            "data_type": "INVALID",
                            "msg": ",".join(self.errors),
                            "data_type_id": -1,
                        }
            else:
                self.errors.append("Operator Missing")
                return {
                    "status": "INVALID",
                    "msg": "Operator Missing",
                    "data_type": "INVALID",
                    "data_type_id": -1,
                }

        else:
            self.errors.append("Invalid Expression")
            return {
                "status": "INVALID",
                "msg": "Invalid Expression",
                "data_type": "INVALID",
                "data_type_id": -1,
            }
