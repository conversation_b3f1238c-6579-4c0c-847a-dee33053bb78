import copy
import decimal
import json
import logging
from collections import defaultdict
from collections.abc import Iterable
from itertools import chain
from statistics import mean
from uuid import UUID, uuid4

import pandas as pd
import pydash
from django.utils import timezone

from commission_engine.accessors.client_accessor import (
    can_use_custom_metrics,
    check_everai_access,
    get_client,
    get_client_features,
    should_use_multi_engine_stormbreaker,
)
from commission_engine.accessors.commission_accessor import QuotaErosionAccessor
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.custom_exceptions.tracing_exceptions import (
    CommissionPlanNotFound,
    CommissionPlanNotPublished,
    DatasheetVariableNotFound,
    EmployeePayrollNotFound,
    LineItemNotFound,
    PlanCriteriaNotFound,
)
from commission_engine.services.commission_calculation_service.criteria_calculation_service import (
    filter_line_items_greater_than_effective_date,
    get_data,
    map_effective_dates_start_end,
    remove_locked_data,
)
from commission_engine.services.commission_calculation_service.tier_calculator import (
    line_item_tier_allocation_logic,
)
from commission_engine.services.datasheet_data_services.variable_extractor import (
    VariableExtractor,
)
from commission_engine.services.expression_designer import (
    convert_calculated_field_meta_data,
    converter,
)
from commission_engine.services.expression_designer.expression_converter import (
    update_get_user_property_custom_field_names,
)
from commission_engine.services.hyperlink_service import HyperlinkService
from commission_engine.services.tracing_services.custom_metrics_trace_service import (
    get_static_trace_for_fx_rated_pay,
    get_static_trace_for_max_cap,
)
from commission_engine.services.tracing_services.evaluate import (
    aggregate_functions,
    evaluate,
    get_prev_psd_ped,
)
from commission_engine.services.tracing_services.quota_calculator import (
    get_quota_details,
)
from commission_engine.services.tracing_services.team_calculator import (
    get_team_context,
    get_team_members,
    get_team_members_with_effective_date,
    get_team_type,
)
from commission_engine.services.tracing_services.tier_calculator import (
    get_override_slab_map,
    part1_line_item_level,
    summated_value_tier_allocation_logic,
)
from commission_engine.utils import get_slab_details, get_slab_infix
from commission_engine.utils.criteria_calculator_utils import create_ast
from commission_engine.utils.date_utils import (
    get_fiscal_year,
    get_period_for_statement,
    make_aware_wrapper,
)
from commission_engine.utils.fx_utils import (
    change_to_base_currency_by_fx_rate,
    change_to_payee_currency_by_fx_rate,
)
from commission_engine.utils.general_data import COMMISSION_TYPE, Freq, payoutFreqMap
from commission_engine.utils.payroll_utils import get_variable_pay_per_period
from common.datatypes import EverDecimal
from everstage_ddd.custom_metrics import (
    does_plan_has_custom_metrics,
    get_all_custom_metric_def_for_plan,
    get_all_custom_metric_values_for_payee_plan_period,
)
from everstage_ddd.llm_agent.mannai_interface import execute_skill
from everstage_ddd.stormbreaker import StormBreakerDS as StormBreakerDSMultiEngine
from everstage_ddd.stormbreaker import StormBreakerDSFactory, StormBreakerDSInitType
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanCriteriaAccessor,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeePayrollAccessor,
    PlanDetailsAllAccessor,
)
from spm.accessors.custom_calendar_accessors import CustomCalendarAccessor
from spm.accessors.quota_acessors import QuotaAccessor
from spm.accessors.variable_accessor import VariableAccessor
from spm.constants.localization_constants import CommissionTraceMessages
from spm.services.commission_data_services import get_commission_records
from spm.services.config_services.client_config_services import fx_rate_by_date
from spm.services.dashboard_services.commission_services import get_payee_payroll_date
from spm.services.localization_services import get_localized_message_service
from spm.services.rbac_services import does_user_have_permission
from spm.services.stormbreaker.stormbreaker import (
    StormBreakerDS as StormBreakerDSVariant,
)
from spm.utils import get_window_based_calculated_fields, is_valid_uuid

logger = logging.getLogger(__name__)


class TracingService:
    """Get the evaluation trace of formula

    MAIN METHODS:
    ------------
        1. get_criteria_trace : Get the evaluation trace of criteria
            - extract_data_for_criteria
            - get_line_items_for_criteria
            - evaluate_criteria
            - construct_response_for_criteria

        2. get_calculated_field_trace : Get the evaluation trace of calculated field
            - extract_data_for_calculated_field
            - get_line_items_for_calculated_field
            - evaluate_calculated_field
            - construct_response_for_calculated_field


    HELPER METHODS:
    --------------
        1. Helper functions to construct the trace response:
            - resolve_infix_expression
            - resolve_infix_token
            - resolve_infix_function_args
            - resolve_value
            - get_sub_trace_id

        2. Helper functions to construct sub-traces:
            - get_static_trace_for_variable_pay
            - get_calculated_field_sub_trace
    """

    def __init__(self, client_id):
        # Common
        self.client_id = client_id
        self.client = get_client(self.client_id)
        self.ever_comparison = getattr(self.client, "client_features", {}).get(
            "enable_ever_comparison", True
        )
        self.fiscal_start_month = self.client.fiscal_start_month
        self.fiscal_year = None
        self.knowledge_date = None
        self.log_context = {}

        # For Tracing Response
        self.resolved_variables = {}
        self.sub_traces = {}
        self.sub_traces_sys_name_id_map = {}

        # For Tracing Commission Plan Criteria
        self.plan_id = None
        self.criteria_id = None
        self.payee_email = None
        self.period_start_date = None
        self.period_end_date = None
        self.employee_payroll = None
        self.ds_vars = None
        self.sys_vars = None
        self.ds_vars_map = {}
        self.has_aggregate_functions = False
        self.is_base_currency = False
        self.base_currency = self.client.base_currency
        self.payee_currency = None
        self.currency_symbol_map = {}
        self.fx_rate = None
        self.is_team_criteria = False
        self.team_type = None
        self.team_params = None
        self.criteria_type = None
        self.payee_context = None
        self.quota_schedule = None
        self.quota_value = 0
        self.plan_start_date = None
        self.plan_end_date = None

        # For Tracing Calculated Field
        self.databook_id = None
        self.datasheet_id = None
        self.calc_field_system_name = None

        # Common
        self.row_key = None
        self.line_items = []
        self.ds_vars = []
        self.calculated_fields_map = {}
        self.calendar_name_map = {}
        self._should_use_multi_engine_stormbreaker = (
            should_use_multi_engine_stormbreaker(self.client_id)
        )
        self.ordered_line_items_from_commission = None

        # custom_metric_res is used to store the trace of custom metrics
        # where the key is the metric_id and the value is the trace
        self.custom_metric_res = {}
        # The is_custom_metric_resolve is used to check if the custom metric is resolved
        # This is set to True when the custom metric is resolved
        # Once the custom metric is resolved, we make the is_custom_metric_resolve to False again
        self.is_custom_metric_resolve = False
        # custom_metric_values_in_plan is used to store the values of custom metrics in the plan
        # The key is the metric_id and the value is the value of the custom metric
        self.custom_metric_values_in_plan = {}

    def get_sub_trace_id(self, sys_name):
        if sys_name not in self.sub_traces_sys_name_id_map:
            self.sub_traces_sys_name_id_map[sys_name] = str(uuid4())
        return self.sub_traces_sys_name_id_map[sys_name]

    def resolve_value(self, val):
        if isinstance(val, (float, decimal.Decimal)):
            val = float(EverDecimal(val, scale=2))
        return val

    def resolve_infix_function_args(self, function_args, skip_variable_value=False):
        """Converts simple infix function arguments to trace structure"""
        res = []
        for arg in function_args:
            if isinstance(arg, str):
                res.append(
                    self.resolve_infix_token(
                        {
                            "type": "VARIABLE",
                            "function_name": "CONSTANT",
                            "data_type": "String",
                            "args": ["String", arg],
                        },
                        skip_variable_value=skip_variable_value,
                    )
                )
            elif isinstance(arg, int):
                res.append(
                    self.resolve_infix_token(
                        {
                            "type": "VARIABLE",
                            "function_name": "CONSTANT",
                            "data_type": "Integer",
                            "args": ["Integer", arg],
                        },
                        skip_variable_value=skip_variable_value,
                    )
                )
            elif isinstance(arg, list):
                res.append(
                    {
                        "type": "expression",
                        "value": self.resolve_infix_expression(
                            arg, skip_variable_value=skip_variable_value
                        ),
                    }
                )
            elif isinstance(arg, dict):
                if "type" in arg:
                    res.append(
                        self.resolve_infix_token(
                            arg, skip_variable_value=skip_variable_value
                        )
                    )
                else:
                    res.append(
                        self.resolve_infix_token(
                            {
                                "type": "VARIABLE",
                                "function_name": "CONSTANT",
                                "data_type": "String",
                                "args": ["String", arg["name"]],
                            },
                            skip_variable_value=skip_variable_value,
                        )
                    )
            elif isinstance(arg, bool):
                res.append(
                    self.resolve_infix_token(
                        {
                            "type": "VARIABLE",
                            "function_name": "CONSTANT",
                            "data_type": "Boolean",
                            "args": ["Boolean", arg],
                        },
                    )
                )

        return res

    def resolve_infix_token(
        self, token, skip_variable_value=False, resolved_variables=None
    ):
        """Converts simple infix token to trace structure"""
        if resolved_variables is None:
            resolved_variables = self.resolved_variables
        if can_use_custom_metrics(self.client_id) and resolved_variables is not None:
            resolved_variables.update(self.custom_metric_values_in_plan)
        obj = {}
        if token["type"] == "VARIABLE":
            if token.get("function_name") == "CONSTANT":
                obj["type"] = "constant"
                obj["value"] = self.resolve_value(token["args"][1])
                # Required by GetUserProperty function
                # Can be removed when User Properties are handled with a separate token type
                if "name" in token:
                    obj["name"] = token["name"]
            elif "function_name" in token:
                obj["type"] = "function"
                if token["name"] in resolved_variables:
                    val = resolved_variables[token["name"]]
                    data_type = token.get("data_type")
                    function_name = token.get("function_name")
                    if val is not None and (
                        function_name in ("QuotaAttainment", "TEAM-QuotaAttainment")
                        or data_type == "Percentage"
                    ):
                        val = val * 100
                    obj["value"] = self.resolve_value(val)
                obj["meta"] = {
                    "function_name": token["function_name"],
                    "args": self.resolve_infix_function_args(
                        token["args"],
                        skip_variable_value=token["function_name"]
                        in aggregate_functions,
                    ),
                }
                if token["function_name"] in (
                    "Quota",
                    "QuotaAttainment",
                    "QuotaErosion",
                    "TEAM-Quota",
                    "TEAM-QuotaAttainment",
                    "TEAM-QuotaErosion",
                ):
                    look_back_period = token["args"][1]
                    if not pydash.has(
                        resolved_variables,
                        ["look_back_period_sed_map", look_back_period],
                    ):
                        # Obtainng the look back period start and end dates if not already present
                        data = {
                            # Partial data sufficient for `get_prev_psd_ped` function
                            "period_start_date": self.period_start_date,
                            "period_end_date": self.period_end_date,
                            "client_id": self.client_id,
                            "payee_email": self.payee_email,
                            "context": self.payee_context,
                        }
                        look_back_psd, look_back_ped = get_prev_psd_ped(
                            look_back_period, data
                        )
                        pydash.set_(
                            resolved_variables,
                            ["look_back_period_sed_map", look_back_period],
                            [look_back_psd, look_back_ped],
                        )
                    look_back_psd, look_back_ped = resolved_variables[
                        "look_back_period_sed_map"
                    ][look_back_period]
                    fiscal_year = get_fiscal_year(
                        self.fiscal_start_month, look_back_psd
                    )
                    period_label = get_period_for_statement(
                        look_back_psd,
                        look_back_ped,
                        self.fiscal_start_month,
                        fiscal_year,
                    )
                    obj["meta"]["args"][1] = self.resolve_infix_token(
                        {
                            "type": "VARIABLE",
                            "function_name": "CONSTANT",
                            "data_type": "String",
                            "args": ["String", period_label],
                        }
                    )
                    # update quota category name to display name
                    meta_args = obj.get("meta", {}).get("args")
                    quota_arg = meta_args[0] if meta_args else None

                    display_name = QuotaAccessor(
                        self.client_id
                    ).get_display_name_for_quota_category_name(
                        quota_arg.get("value")
                    ) or quota_arg.get(
                        "value"
                    )
                    if display_name:
                        obj["meta"]["args"][0]["value"] = display_name

                elif token["function_name"] == "Config":
                    obj["title"] = token["args"][0]["meta"]["system_name"]
                elif token["function_name"] == "TieredPercentage":
                    trace_id = str(uuid4())
                    obj["trace_id"] = trace_id
                    tiered_value = resolved_variables.get("TieredValue()", 0)
                    tiered_percentage = resolved_variables.get(token["name"], 0)
                    obj["value"] = self.resolve_value(tiered_percentage * 100)
                    self.sub_traces[trace_id] = self.get_trace_for_tiered_percentage(
                        tiered_value, tiered_percentage
                    )
            elif (
                "meta" in token
                and "category" in token["meta"]
                and token["meta"]["category"] == "user_defined_functions"
            ):
                obj["type"] = "variable"
                obj["title"] = token["name"]
                if (
                    token["meta"]["system_name"] in resolved_variables
                    and not skip_variable_value
                ):
                    obj["value"] = self.resolve_value(
                        resolved_variables[token["meta"]["system_name"]]
                    )
                custom_metric_trace_id = self.get_sub_trace_id(
                    token["meta"]["system_name"]
                )
                if custom_metric_trace_id not in self.sub_traces:
                    self.sub_traces[custom_metric_trace_id] = self.custom_metric_res[
                        token["meta"]["system_name"]
                    ]
                obj["trace_id"] = custom_metric_trace_id

            else:
                obj["type"] = "variable"
                obj["title"] = token["name"]
                if (
                    token["meta"]["system_name"] in resolved_variables
                    and not skip_variable_value
                ):
                    obj["value"] = self.resolve_value(
                        resolved_variables[token["meta"]["system_name"]]
                    )
                if token["meta"]["system_name"] == "variable_pay":
                    if self.is_custom_metric_resolve:
                        variable_pay_amount = change_to_base_currency_by_fx_rate(
                            self.employee_payroll["variable_pay"], self.fx_rate
                        )
                        currency_symbol_to_show = self.base_currency

                    else:
                        variable_pay_amount = self.employee_payroll["variable_pay"]
                        currency_symbol_to_show = self.payee_currency
                    obj["currency_symbol"] = self.currency_symbol_map[
                        currency_symbol_to_show
                    ]
                    if (
                        self.criteria_type in ("Quota", "CustomQuota")
                        and self.quota_schedule
                    ):
                        period_variable_pay = get_variable_pay_per_period(
                            self.client_id,
                            variable_pay_amount,
                            self.quota_schedule.lower(),
                            self.period_end_date,
                        )
                    else:
                        period_variable_pay = get_variable_pay_per_period(
                            self.client_id,
                            variable_pay_amount,
                            self.employee_payroll["payout_frequency"],
                            self.period_end_date,
                        )
                    obj["value"] = period_variable_pay
                    if self.employee_payroll is not None:
                        if (
                            self.criteria_type in ("Quota", "CustomQuota")
                            and self.quota_schedule
                        ):
                            obj["title"] = " ".join(
                                [
                                    self.quota_schedule.capitalize(),
                                    obj["title"],
                                ]
                            )
                        else:
                            if (
                                self.employee_payroll["payout_frequency"].lower()
                                in self.calendar_name_map
                            ):
                                obj["title"] = " ".join(
                                    [
                                        self.calendar_name_map[
                                            self.employee_payroll[
                                                "payout_frequency"
                                            ].lower()
                                        ],
                                        obj["title"],
                                    ]
                                )
                            else:
                                obj["title"] = " ".join(
                                    [
                                        self.employee_payroll["payout_frequency"],
                                        obj["title"],
                                    ]
                                )
                        if (
                            self.employee_payroll["payout_frequency"].lower()
                            != Freq.ANNUAL.value
                        ):
                            trace_id = self.get_sub_trace_id(
                                token["meta"]["system_name"]
                            )
                            if trace_id not in self.sub_traces:
                                if (
                                    self.criteria_type in ("Quota", "CustomQuota")
                                    and self.quota_schedule
                                ):
                                    freq = payoutFreqMap[self.quota_schedule.lower()]
                                else:
                                    freq = payoutFreqMap.get(
                                        self.employee_payroll[
                                            "payout_frequency"
                                        ].lower()
                                    )
                                self.sub_traces[trace_id] = (
                                    self.get_static_trace_for_variable_pay(
                                        self.employee_payroll["variable_pay"],
                                        freq,
                                        period_variable_pay,
                                    )
                                )
                            obj["trace_id"] = trace_id
                        if self.is_custom_metric_resolve:
                            # The trace id will be none on resolving variable_pay
                            # in Annual payout freq criteria
                            if (
                                self.employee_payroll["payout_frequency"].lower()
                                == Freq.ANNUAL.value
                            ):
                                trace_id = None
                            # Will show the fx rate for variable_pay only in custom_metrics
                            variable_pay_fx_rate_trace_id = self.get_sub_trace_id(
                                "variable_pay_fx_rate"
                            )
                            period_variable_pay_in_base = get_variable_pay_per_period(
                                self.client_id,
                                variable_pay_amount,
                                self.employee_payroll["payout_frequency"],
                                self.period_end_date,
                            )
                            # annual_variable_pay retrived from employee_payroll will be in paye_currency
                            # for custom metrics we converted the calculaton of period_variable_pay in base currency
                            # so we need to change the period_variable_pay to payee currency
                            period_variable_pay = change_to_payee_currency_by_fx_rate(
                                period_variable_pay, self.fx_rate
                            )
                            self.sub_traces[variable_pay_fx_rate_trace_id] = (
                                get_static_trace_for_fx_rated_pay(
                                    period_variable_pay,
                                    period_variable_pay_in_base,
                                    obj["title"] + " in payee currency",
                                    trace_id,
                                    self.currency_symbol_map[self.payee_currency],
                                    self.fx_rate,
                                    self.currency_symbol_map[self.base_currency],
                                )
                            )
                            obj["title"] += " in base currency"
                            obj["trace_id"] = variable_pay_fx_rate_trace_id

                elif token["meta"]["system_name"] in self.calculated_fields_map:
                    trace_id = self.get_sub_trace_id(token["meta"]["system_name"])
                    if trace_id not in self.sub_traces:
                        self.sub_traces[trace_id] = self.get_calculated_field_sub_trace(
                            token["meta"]["system_name"]
                        )
                    obj["trace_id"] = trace_id

                elif token["meta"]["system_name"] == "actual_variable_pay":
                    variable_pay_in_base = change_to_base_currency_by_fx_rate(
                        self.employee_payroll["variable_pay"], self.fx_rate
                    )
                    obj["value"] = variable_pay_in_base
                    obj["currency_symbol"] = self.currency_symbol_map[
                        self.base_currency
                    ]
                    variable_pay_fx_rate_trace_id = self.get_sub_trace_id(
                        "actual_variable_pay_fx_rate"
                    )
                    self.sub_traces[variable_pay_fx_rate_trace_id] = (
                        get_static_trace_for_fx_rated_pay(
                            self.employee_payroll["variable_pay"],
                            variable_pay_in_base,
                            obj["title"] + " in payee currency",
                            None,
                            self.currency_symbol_map[self.payee_currency],
                            self.fx_rate,
                            self.currency_symbol_map[self.base_currency],
                        )
                    )
                    obj["title"] += " in base currency"
                    obj["trace_id"] = variable_pay_fx_rate_trace_id

            obj["data_type"] = token["data_type"]
        elif token["type"] == "OPERATOR":
            obj = {
                "type": "operator",
                "value": token["name"],
            }
        elif token["type"] in ("LBRACKET", "RBRACKET"):
            obj = {
                "type": "operator",
                "value": token["name"],
            }
        elif token["type"] in get_window_based_calculated_fields():
            token_type = token["type"]
            calc_field = self.calculated_fields_map[token["system_name"]]
            obj["type"] = "function"
            obj["value"] = self.resolve_value(resolved_variables[token["system_name"]])
            value_column_arg = self.ds_vars_map[
                calc_field["meta_data"][token_type]["value_column"]
            ]
            value_column_arg = self.resolve_infix_token(
                {
                    "type": "VARIABLE",
                    "name": value_column_arg["display_name"],
                    "meta": {
                        "system_name": value_column_arg["system_name"],
                    },
                    "data_type": value_column_arg["data_type__data_type"],
                },
                skip_variable_value=True,
            )
            partition_arg = [
                self.ds_vars_map[var_name]
                for var_name in calc_field["meta_data"][token_type]["partition"]
            ]
            partition_arg = [
                self.resolve_infix_token(
                    {
                        "type": "VARIABLE",
                        "name": item["display_name"],
                        "meta": {
                            "system_name": item["system_name"],
                        },
                        "data_type": item["data_type__data_type"],
                    },
                    skip_variable_value=True,
                )
                for item in partition_arg
            ]
            strategey_key = token_type + "_strategy"
            strategy_val = calc_field["meta_data"][token_type][strategey_key]
            strategy_arg = self.resolve_infix_token(
                {
                    "type": "VARIABLE",
                    "function_name": "CONSTANT",
                    "args": [
                        "String",
                        strategy_val,
                    ],
                    "data_type": "String",
                }
            )

            if token_type == "rank":
                ordering_arg = self.resolve_infix_token(
                    {
                        "type": "VARIABLE",
                        "function_name": "CONSTANT",
                        "args": [
                            "Boolean",
                            calc_field["meta_data"]["rank"]["ascending"],
                        ],
                        "data_type": "Boolean",
                    }
                )
                rank_args = [
                    value_column_arg,
                    partition_arg,
                    ordering_arg,
                    strategy_arg,
                ]
                obj["meta"] = {
                    "function_name": "Rank",
                    "args": rank_args,
                }
            elif token_type == "rolling":
                sort_data = [
                    self.ds_vars_map[var_name["column_name"]]
                    for var_name in calc_field["meta_data"][token_type]["sort_data"]
                ]
                sort_arg = [
                    self.resolve_infix_token(
                        {
                            "type": "VARIABLE",
                            "name": item["display_name"],
                            "meta": {
                                "system_name": item["system_name"],
                            },
                            "data_type": item["data_type__data_type"],
                        },
                        skip_variable_value=True,
                    )
                    for item in sort_data
                ]
                rolling_args = [
                    value_column_arg,
                    partition_arg,
                    sort_arg,
                ]
                obj["meta"] = {
                    "function_name": "Rolling" + strategy_val.capitalize(),
                    "args": rolling_args,
                }
        return obj

    def resolve_infix_expression(
        self, infix_exp, skip_variable_value=False, resolved_variables=None
    ):
        """Converts simple infix expressions to trace structure"""
        if isinstance(infix_exp, dict):
            res = {}
            if infix_exp.get("function_name") == "IF" and infix_exp.get("args"):
                for key in infix_exp:
                    if key != "args":
                        res[key] = infix_exp[key]
                res["args"] = []
                for arg in infix_exp["args"]:
                    res["args"].append(
                        self.resolve_infix_expression(
                            arg,
                            skip_variable_value=skip_variable_value,
                            resolved_variables=resolved_variables,
                        )
                    )
            elif "rank" in infix_exp or "rolling" in infix_exp:
                res = [
                    self.resolve_infix_token(
                        infix_exp,
                        skip_variable_value=True,
                        resolved_variables=resolved_variables,
                    )
                ]
            return res
        res = []
        if not isinstance(infix_exp, list):
            return res
        for item in infix_exp:
            obj = self.resolve_infix_token(
                item,
                skip_variable_value=skip_variable_value,
                resolved_variables=resolved_variables,
            )
            res.append(obj)
        return res

    def get_static_trace_for_variable_pay(
        self, annual_variable_pay, payout_frequency_period, period_variable_pay
    ):
        if self.is_custom_metric_resolve:
            # annual_variable_pay retrived from employee_payroll will be in paye_currency
            # for custom metrics we converted the calculaton of period_variable_pay in base currency
            # so we need to change the period_variable_pay to payee currency
            period_variable_pay = change_to_payee_currency_by_fx_rate(
                period_variable_pay, self.fx_rate
            )
        currency_symbol = self.currency_symbol_map[self.payee_currency]
        if self.employee_payroll and is_valid_uuid(
            self.employee_payroll.get("payout_frequency")
        ):
            return {
                "expression": [
                    {
                        "type": "variable",
                        "title": "Periodic Variable Pay",
                        "value": self.resolve_value(period_variable_pay),
                        "data_type": "Integer",
                        "currency_symbol": currency_symbol,
                    },
                ],
                "result": {
                    "data_type": "Integer",
                    "value": self.resolve_value(period_variable_pay),
                    "currency_symbol": currency_symbol,
                },
            }
        return {
            "expression": [
                {
                    "type": "variable",
                    "title": "Annual Variable Pay",
                    "value": self.resolve_value(annual_variable_pay),
                    "data_type": "Integer",
                    "currency_symbol": currency_symbol,
                },
                {
                    "type": "operator",
                    "value": "/",
                },
                {
                    "type": "constant",
                    "value": payout_frequency_period,
                    "data_type": "Integer",
                },
            ],
            "result": {
                "data_type": "Integer",
                "value": self.resolve_value(period_variable_pay),
                "currency_symbol": currency_symbol,
            },
        }

    def get_trace_for_tiered_percentage(self, tiered_value, quota_attainment):
        localized_qe = get_localized_message_service(
            CommissionTraceMessages.QUOTA_EROSION.value, self.client_id
        )
        localized_quota = get_localized_message_service(
            CommissionTraceMessages.QUOTA.value, self.client_id
        )
        return {
            "expression": [
                {
                    "type": "variable",
                    "title": localized_qe,
                    "value": self.resolve_value(tiered_value),
                    "data_type": "Integer",
                },
                {
                    "type": "operator",
                    "value": "/",
                },
                {
                    "type": "variable",
                    "title": localized_quota,
                    "value": self.resolve_value(self.quota_value),
                    "data_type": "Integer",
                },
            ],
            "result": {
                "data_type": "Percentage",
                "value": self.resolve_value(quota_attainment * 100),
            },
        }

    def get_criteria_trace(
        self,
        plan_id,
        criteria_id,
        payee_email,
        period_start_date,
        period_end_date,
        row_key,
        is_base_currency=False,
    ):
        """Provides the criteria evaluation trace based on the given
        plan, criteria, payee for a sepcific row_key"""
        self.plan_id = plan_id
        self.criteria_id = criteria_id
        self.payee_email = payee_email
        self.period_start_date = make_aware_wrapper(period_start_date)
        self.period_end_date = make_aware_wrapper(period_end_date)
        self.row_key = row_key
        self.is_base_currency = is_base_currency
        self.log_context = {
            "client_id": self.client_id,
            "plan_id": self.plan_id,
            "criteria_id": self.criteria_id,
            "payee_email": self.payee_email,
            "period_start_date": self.period_start_date,
            "period_end_date": self.period_end_date,
            "row_key": self.row_key,
            "is_base_currency": self.is_base_currency,
        }
        calendars = CustomCalendarAccessor(self.client_id).get_all_custom_calendars(
            ["calendar_id", "name"]
        )
        self.calendar_name_map = {
            str(calendar["calendar_id"]): calendar["name"] for calendar in calendars
        }
        plan_details = list(
            PlanDetailsAllAccessor(self.client_id).get_selected_payees_in_plan(
                self.plan_id, [self.payee_email]
            )
        )
        if plan_details:
            self.plan_start_date = plan_details[0].effective_start_date
            self.plan_end_date = plan_details[0].effective_end_date
        logger.info("BEGIN: Tracing criteria", extra=self.log_context)
        (criteria, infix, ast) = self.extract_data_for_criteria()

        custom_metric_value_map = {}
        if can_use_custom_metrics(self.client_id) and does_plan_has_custom_metrics(
            self.client_id, self.plan_id
        ):
            custom_metrics = get_all_custom_metric_def_for_plan(
                self.client_id, self.plan_id
            )
            custom_metric_res = {}
            custom_metric_value_map = (
                get_all_custom_metric_values_for_payee_plan_period(
                    self.client_id,
                    self.payee_email,
                    self.period_start_date,
                    self.period_end_date,
                    self.plan_id,
                )
            )
            for custom_metric in custom_metrics:
                custom_metric_res[str(custom_metric.metric_id)] = (
                    self.resolve_custom_metrics(
                        custom_metric.metric_definition,
                        custom_metric.data_type_id,
                        custom_metric.metric_name,
                    )
                )
            self.custom_metric_res = custom_metric_res
            self.custom_metric_values_in_plan.update(custom_metric_value_map)

        commission, com_result = self.evaluate_criteria(criteria, infix, ast)
        res = self.construct_response_for_criteria(
            criteria, infix, commission, com_result
        )
        response = {"data": res}
        # Check if Ever AI Flag is enabled and user has permission to use AI commission trace
        if check_everai_access(self.client_id) and does_user_have_permission(
            client_id=self.client_id,
            logged_email_id=self.payee_email,
            ui_permission={"manage:traceai"},
        ):
            logger.info("Executing AI commission trace", extra=self.log_context)
            ai_generated_content, llm_status = execute_skill(
                client_id=self.client_id,
                skill_tag="commission-trace-ai-explanation",
                employee_email_id=self.payee_email,
                user_prompt=json.dumps(res, default=str),
                tools_required=False,
            )
            logger.info("AI commission trace executed", extra=self.log_context)
            if llm_status == "Success" and ai_generated_content.get(
                "ai_generated_content"
            ):
                response["ai_generated_content"] = ai_generated_content[
                    "ai_generated_content"
                ]
        logger.info("END: Tracing criteria", extra=self.log_context)
        return response

    def get_line_items_for_criteria(self, criteria):
        """Extracts line items used for the given criteria"""
        databook_id = criteria["criteria_data"]["databook_id"]
        datasheet_id = criteria["criteria_data"]["datasheet_id"]
        date_field = criteria["criteria_data"]["date_field"]
        payee_field = criteria["criteria_data"]["payee_field"]
        is_line_item_level = criteria["criteria_data"]["is_line_item_level"]
        sort_columns = criteria["criteria_config"].get("sort_cols", [])
        # Process custom field args for GetUserProperty
        update_get_user_property_custom_field_names(
            self.client_id,
            criteria["criteria_data"],
        )
        # converting infix expression back to v1 format (in case of infix v2)
        criteria["criteria_data"] = converter(
            client_id=self.client_id, expression=criteria["criteria_data"]
        )
        start_date = self.period_start_date
        end_date = self.period_end_date
        if self.plan_start_date and self.plan_start_date > self.period_start_date:
            start_date = self.plan_start_date
        if self.plan_end_date and self.plan_end_date < self.period_end_date:
            end_date = self.plan_end_date

        infix = None
        ast = None

        if self.criteria_type in (
            "Simple",
            "CustomSimple",
            "Conditional",
            "CustomConditional",
        ):
            infix = criteria["criteria_data"]["ast"]
            ast = create_ast(copy.deepcopy(infix))
            ast = ast["ast"]
            functions_used = VariableExtractor().get_functions_used(ast)
            self.has_aggregate_functions = (
                len(pydash.intersection(functions_used, aggregate_functions)) > 0
            )
        else:
            part1_exp = criteria["criteria_data"]["part1"]
            part2_exp = criteria["criteria_data"]["part2"]
            functions_used = set()
            for record in part2_exp:
                expression_stack = record["expression_stack"]
                exp_ast = create_ast(copy.deepcopy(expression_stack))["ast"]
                functions_used.update(VariableExtractor().get_functions_used(exp_ast))
            infix = part1_exp
            ast = create_ast(copy.deepcopy(part1_exp))
            ast = ast["ast"]
            functions_used.update(VariableExtractor().get_functions_used(ast))
            functions_used = list(functions_used)
            self.has_aggregate_functions = (
                len(pydash.intersection(functions_used, aggregate_functions)) > 0
            )
        logger.info("Obtained criteria infix and ast", extra=self.log_context)
        line_items = []
        if self._should_use_multi_engine_stormbreaker:
            params = StormBreakerDSInitType(
                compute_strategy="duckdb_fallback_variant_snowflake",
                client_id=self.client_id,
                databook_id=UUID(str(databook_id)),
                datasheet_id=UUID(str(datasheet_id)),
                knowledge_date=self.knowledge_date,
            )
            ds_storm_breaker = StormBreakerDSFactory.init(params)
        else:
            ds_storm_breaker = StormBreakerDSVariant(
                self.client_id, UUID(databook_id), UUID(datasheet_id)
            )
        ds_storm_breaker.set_knowledge_date(self.knowledge_date)
        ds_storm_breaker.reset_apply_datasheet_permissions()
        ds_storm_breaker.set_fetch_null_as_null()
        if sort_columns:
            all_columns_and_order = []
            for col_name_and_order in sort_columns:
                entry = {"col": col_name_and_order[0], "order": col_name_and_order[1]}
                all_columns_and_order.append(entry)
            ds_storm_breaker.order_by(all_columns_and_order)
        else:
            ds_storm_breaker.order_by([{"col": date_field, "order": "asc"}])
        if (
            is_line_item_level
            and not self.has_aggregate_functions
            and not self.criteria_type in ("Tier", "CustomTier")
        ):
            # For quota criteria, records will be available in quota_erosion table.
            # So, not requered to fetch all records from snowflake.
            if not self.is_team_criteria:
                ds_storm_breaker.filter(payee_field, "eq", str(self.payee_email))
            ds_storm_breaker.filter("row_key", "eq", self.row_key)
            if self._should_use_multi_engine_stormbreaker and isinstance(
                ds_storm_breaker, StormBreakerDSMultiEngine
            ):
                line_items = ds_storm_breaker.fetch_datasheet_data_as_of_date(self.knowledge_date, limit=1, offset=None)  # type: ignore
            elif not self._should_use_multi_engine_stormbreaker and isinstance(
                ds_storm_breaker, StormBreakerDSVariant
            ):
                line_items = ds_storm_breaker.fetch(limit=1, offset=None)
            else:
                raise TypeError("Invalid type of StormbreakerDS")
        elif not self.is_team_criteria:
            ds_storm_breaker.filter(date_field, "gte", str(start_date))
            ds_storm_breaker.filter(date_field, "lte", str(end_date))
            ds_storm_breaker.filter(payee_field, "eq", self.payee_email)
            if self._should_use_multi_engine_stormbreaker and isinstance(
                ds_storm_breaker, StormBreakerDSMultiEngine
            ):
                records = ds_storm_breaker.fetch_datasheet_data_as_of_date(self.knowledge_date, limit=None, offset=None)  # type: ignore
            elif not self._should_use_multi_engine_stormbreaker and isinstance(
                ds_storm_breaker, StormBreakerDSVariant
            ):
                records = ds_storm_breaker.fetch(limit=None, offset=None)
            else:
                raise TypeError("Invalid type of StormbreakerDS")
            key = "row_key"
            filtered_data = remove_locked_data(
                self.client_id,
                self.period_start_date,
                self.period_end_date,
                self.knowledge_date,
                self.plan_id,
                self.criteria_id,
                {
                    "merged_data": records,
                },
                key,
                self.payee_email,
                commission_type=COMMISSION_TYPE.COMMISSION,
            )
            line_items = filtered_data["merged_data"]
        if self.is_team_criteria:
            criteria_conditions = {
                "databook_id": databook_id,
                "datasheet_id": datasheet_id,
                "payee_field": payee_field,
                "date_field": date_field,
                "criteria_id": self.criteria_id,
                "team_name": criteria["criteria_data"]["team"],
                "sort_columns": criteria["criteria_config"].get("sort_cols"),
            }
            line_items = self.get_team_data(
                criteria_conditions,
                line_items=line_items,
            )
        logger.info("Obtained %d line item(s)", len(line_items), extra=self.log_context)
        return (line_items, infix, ast)

    def extract_data_for_criteria(self):
        """Extracts data for criteria tracing"""
        logger.info("Extracting data for criteria tracing", extra=self.log_context)
        self.fiscal_year = get_fiscal_year(
            self.fiscal_start_month, self.period_start_date
        )
        self.knowledge_date = get_payee_payroll_date(
            self.client_id, self.payee_email, self.period_end_date
        )
        plan = CommissionPlanAccessor(self.client_id).get_commission_plan(
            self.plan_id, projection=["is_draft"]
        )
        plan = plan[0] if plan else None
        if plan is None:
            raise CommissionPlanNotFound()
        if pydash.get(plan, "is_draft"):
            raise CommissionPlanNotPublished()
        criteria = PlanCriteriaAccessor(self.client_id).get_criteria(
            self.criteria_id,
            ["criteria_type", "criteria_data", "criteria_column", "criteria_config"],
            knowledge_date=self.knowledge_date,
        )
        if not criteria:
            raise PlanCriteriaNotFound()
        self.is_team_criteria = "team" in criteria["criteria_data"]
        if self.is_team_criteria:
            self.team_type = get_team_type(criteria["criteria_data"]["team"])
        self.criteria_type = (
            criteria["criteria_type"]
            if not self.is_team_criteria
            else criteria["criteria_data"]["type"]
        )
        datasheet_id = criteria["criteria_data"]["datasheet_id"]
        databook_id = criteria["criteria_data"]["databook_id"]
        employee_payroll = EmployeePayrollAccessor(
            self.client_id
        ).get_payroll_for_given_date_first(
            self.knowledge_date,
            self.period_end_date,
            self.payee_email,
            projection=["variable_pay", "payout_frequency", "pay_currency"],
        )
        if not employee_payroll:
            raise EmployeePayrollNotFound()
        self.employee_payroll = employee_payroll
        self.payee_currency = employee_payroll["pay_currency"]
        currency_codes = [self.payee_currency]

        if can_use_custom_metrics(self.client_id) and does_plan_has_custom_metrics(
            self.client_id, self.plan_id
        ):
            currency_codes.append(self.base_currency)

        if self.is_base_currency:
            currency_codes.append(self.base_currency)
        country = CountriesAccessor(self.client_id).get_currency_symbols(
            currency_codes=currency_codes,
            projection=["currency_code", "currency_symbol"],
        )
        self.currency_symbol_map = {
            c["currency_code"]: c["currency_symbol"] for c in country
        }
        self.fx_rate = fx_rate_by_date(
            self.client_id,
            self.period_end_date,
            self.payee_currency,
            knowledge_date=self.knowledge_date,
        )
        self.ds_vars = DatasheetVariableAccessor(
            self.client_id
        ).get_objects_by_datasheet_id(
            datasheet_id,
            knowledge_date=self.knowledge_date,
            projection=[
                "system_name",
                "display_name",
                "data_type__data_type",
                "field_order",
                "meta_data",
            ],
        )
        self.ds_vars_map, self.calculated_fields_map = self.get_vars_map_from_ds_vars(
            databook_id, datasheet_id, self.ds_vars
        )
        self.payee_context = self.get_payee_context()
        line_items, infix, ast = self.get_line_items_for_criteria(criteria)
        if not line_items:
            raise LineItemNotFound()
        self.line_items = line_items
        self.sys_vars = VariableAccessor().get_all_variables(
            projection=["name", "system_name", "data_type__data_type"]
        )
        return (criteria, infix, ast)

    def get_vars_map_from_ds_vars(self, databook_id, datasheet_id, ds_vars):
        """
        Returns ds_vars_map and calculated_fields_map
        """
        ds_vars_map = {}
        calculated_fields_map = {}

        for ds_var in ds_vars:
            # meta_data of datasheet_variable can have both v1 infix expression or v2 infix expression
            # For tracing, we need to convert `v2 infix expression` to `v1 infix expression`
            meta_data = ds_var["meta_data"]
            field_order = ds_var["field_order"]
            if meta_data is not None:
                new_ds_var = {
                    "databook_id": databook_id,
                    "datasheet_id": datasheet_id,
                    "meta_data": meta_data,
                }
                ds_var["meta_data"] = convert_calculated_field_meta_data(
                    client_id=self.client_id,
                    datasheet_var_obj=new_ds_var,
                )

            if field_order > 0:
                calculated_fields_map[ds_var["system_name"]] = ds_var

            ds_vars_map[ds_var["system_name"]] = ds_var

        return ds_vars_map, calculated_fields_map

    def evaluate_criteria(self, criteria, infix, ast):
        """Evaluates lines items for a criteria"""
        data = {
            "context": self.payee_context,
            "merged_data": self.line_items,
            "period_start_date": self.period_start_date,
            "period_end_date": self.period_end_date,
            "client_id": self.client_id,
            "start_month": self.fiscal_start_month,
            "team_params": self.team_params,
            "plan_id": self.plan_id,
            "payee_email": self.payee_email,
        }
        is_line_item_level = criteria["criteria_data"]["is_line_item_level"]
        tier_type = criteria["criteria_data"].get("tier_type")
        is_override_on = criteria["criteria_data"].get("is_override", False)
        if is_line_item_level:
            data["line_item"] = pydash.find(
                self.line_items, lambda line_item: line_item["row_key"] == self.row_key
            )
            if not data["line_item"]:
                raise LineItemNotFound()
        if self.is_team_criteria:
            email = self.payee_email
            payee_field = criteria["criteria_data"]["payee_field"]
            if (
                is_line_item_level
                and self.criteria_type
                in (
                    "Simple",
                    "CustomSimple",
                    "Conditional",
                    "CustomConditional",
                )
                and self.team_type in ("leader_type", "manager_type", "pod")
            ):
                email = data["line_item"].get(payee_field)
                data["merged_data"] = (
                    [
                        line_item
                        for line_item in self.line_items
                        if line_item.get(payee_field) == email
                    ]
                    if self.line_items
                    else []
                )

        logger.info("BEGIN: Evaluating criteria for tracing", extra=self.log_context)
        commission = 0
        result = None
        if self.criteria_type in (
            "Simple",
            "CustomSimple",
            "Conditional",
            "CustomConditional",
        ):
            commission = evaluate(
                ast,
                data=data,
                ever_comparison=self.ever_comparison,
                resolved_variables=self.resolved_variables,
                infix=infix,
                knowledge_date=self.knowledge_date,
            )
            if "line_item" in data:
                self.resolved_variables.update(data["line_item"])
            if commission == float("-inf"):
                commission = 0
            if not self.is_base_currency:
                commission = change_to_payee_currency_by_fx_rate(
                    commission, self.fx_rate
                )
            commission = self.resolve_value(commission)
            for line_item in self.line_items:  # Adding commission to line items
                line_item["commission"] = commission
        else:
            result = {}
            if self.criteria_type in ("CustomTier", "Tier"):
                part1_exp = criteria["criteria_data"]["part1"]
                part2_exp = criteria["criteria_data"]["part2"]
                result = self.evaluate_tier_criteria(
                    data,
                    part1_exp,
                    part2_exp,
                    tier_type,
                    is_line_item_level,
                    is_override_on=is_override_on,
                )
            elif self.criteria_type in ("Quota", "CustomQuota"):
                part1_exp = criteria["criteria_data"]["part1"]
                part2_exp = criteria["criteria_data"]["part2"]
                category = criteria["criteria_data"]["quota_name"]
                part1_type = criteria["criteria_data"]["part_1_type"]
                tier_type = criteria["criteria_data"]["tier_type"]
                result = self.evaluate_quota_criteria(
                    data,
                    part1_exp,
                    part2_exp,
                    part1_type,
                    tier_type,
                    is_line_item_level,
                    category=category,
                )
            part2_result = result.get("part2_result")
            if part2_result:
                for record in part2_result:
                    cur_commision = record.get("commission", 0)
                    if not self.is_base_currency:
                        cur_commision = change_to_payee_currency_by_fx_rate(
                            cur_commision, self.fx_rate
                        )
                    record["commission"] = cur_commision
                    commission += cur_commision
                commission = self.resolve_value(commission)
        logger.info("END: Evaluating criteria for tracing", extra=self.log_context)
        return commission, result

    def get_sorted_part1_result(self, part1_result):
        """
        Sorts the part1_result based on the ordered_line_items_from_commission.
        """
        sorted_part1_result = None
        self.ordered_line_items_from_commission = (
            self.get_ordered_line_items_from_commission()
        )
        if self.ordered_line_items_from_commission:
            ordered_keys = chain(
                (
                    key
                    for key in self.ordered_line_items_from_commission
                    if key in part1_result
                ),
                (
                    key
                    for key in part1_result
                    if key not in self.ordered_line_items_from_commission
                ),
            )
            sorted_part1_result = {key: part1_result[key] for key in ordered_keys}
        return sorted_part1_result if sorted_part1_result else part1_result

    def evaluate_tier_criteria(
        self,
        data,
        part1_exp,
        part2_exp,
        tier_type,
        is_line_item_level,
        is_override_on,
    ):
        data_key = "row_key"
        part1_ast = create_ast(copy.deepcopy(part1_exp))["ast"]
        tiered_result = None
        (slab_bound, slab_ast, slab_override, slab_name) = get_slab_details(
            copy.deepcopy(part2_exp)
        )
        slab_infix = get_slab_infix(part2_exp)
        part1_value = 0
        final_result = []
        part1_resolved_variables = {}
        if not is_line_item_level:
            data_copy = copy.deepcopy(data)
            part1_result, part1_resolved_variables = part1_line_item_level(
                data_copy,
                data_key,
                tier_type,
                part1_ast,
                infix=part1_exp,
                knowledge_date=self.knowledge_date,
                ever_comparison=self.ever_comparison,
            )
            part1_resolved_variables = part1_resolved_variables.get(self.row_key, {})
            for key, val in part1_result.items():
                if val == float("-inf"):
                    part1_result[key] = 0
            cumulative_value = sum(part1_result.values())
            data["tiered_data"] = cumulative_value
            data["original_part1_result"] = cumulative_value
            part1_value = cumulative_value
            # get tier and override value from commission table

            final_result = summated_value_tier_allocation_logic(
                cumulative_value,
                data,
                slab_bound,
                slab_name,
                slab_ast,
                tier_key="tier_value",
                slab_infix=slab_infix,
                ever_comparison=self.ever_comparison,
                knowledge_date=self.knowledge_date,
            )
            slab_id = final_result[0]["original_tier_id"]
            final_result[0]["commission_date"] = self.period_end_date
            final_result[0]["infix"] = slab_infix.get(slab_id)
            final_result[0]["slab_bound"] = slab_bound.get(slab_id)
            final_result[0]["tiered_value"] = cumulative_value
        else:
            data_copy = copy.deepcopy(data)
            part1_result, part1_resolved_data = part1_line_item_level(
                data_copy,
                data_key,
                tier_type,
                part1_ast,
                infix=part1_exp,
                knowledge_date=self.knowledge_date,
                ever_comparison=self.ever_comparison,
            )

            part1_result = self.get_sorted_part1_result(part1_result)

            part1_resolved_variables = part1_resolved_data.get(self.row_key, {})
            keys_to_delete = set()
            for key, val in part1_result.items():
                if val == float("-inf"):
                    keys_to_delete.add(key)
            for key in keys_to_delete:
                part1_result.pop(key)
            part1_value = part1_result.get(self.row_key, 0)
            tiered_result = line_item_tier_allocation_logic(part1_result, slab_bound)

            max_slab_id = 0
            overridden_slab_id_map = {}
            if is_override_on:
                for trs in tiered_result.values():
                    for item in trs:
                        for slab_id, res in item.items():
                            max_slab_id = max(slab_id, max_slab_id)
                overridden_slab_id_map = get_override_slab_map(
                    slab_ast, slab_override, max_slab_id
                )
            tiered_result = tiered_result.get(self.row_key, [])
            final_result = []
            for item in tiered_result:
                for slab_id, res in item.items():
                    result = {}
                    data["tiered_data"] = res
                    result["original_tier_id"] = slab_id
                    result["actual_tier_name"] = slab_name.get(slab_id)
                    result["slab_bound"] = slab_bound.get(slab_id)
                    slab_id = overridden_slab_id_map.get(slab_id, slab_id)
                    ast = slab_ast.get(slab_id)
                    resolved_variables = {}
                    result["tiered_value"] = res
                    result["commission"] = evaluate(
                        ast,
                        infix=slab_infix[slab_id],
                        data=data,
                        ever_comparison=self.ever_comparison,
                        resolved_variables=resolved_variables,
                        knowledge_date=self.knowledge_date,
                    )
                    if "line_item" in data:
                        resolved_variables.update(data["line_item"])
                    result["resolved_variables"] = resolved_variables
                    result["tier_id"] = slab_id

                    result["tier_name"] = slab_name.get(slab_id)
                    result["row_key"] = self.row_key
                    result["infix"] = slab_infix.get(slab_id)
                    final_result.append(result)
        return {
            "part1_result": part1_value,
            "part1_resolved_variables": part1_resolved_variables,
            "part2_result": final_result,
        }

    def evaluate_quota_criteria(
        self,
        data,
        part1_exp,
        part2_exp,
        part1_type,
        tier_type,
        is_line_item_level,
        category,
    ):
        final_result = []
        payout = self.employee_payroll["payout_frequency"].lower()
        part1_ast = create_ast(copy.deepcopy(part1_exp))["ast"]
        team_data = None
        part1_value = 0
        part1_resolved_variables = {}
        if self.is_team_criteria:
            team_data = {
                "team_type": self.team_type,
                "team_owner_email_id": self.payee_email,
            }
        [schedule, _, qv] = get_quota_details(
            self.client_id,
            self.payee_email,
            category,
            self.period_start_date,
            self.period_end_date,
            team_data=team_data,
            knowledge_date=self.knowledge_date,
        )
        (slab_bound, slab_ast, _, slab_name) = get_slab_details(
            copy.deepcopy(part2_exp)
        )
        slab_infix = get_slab_infix(part2_exp)
        qe_records = QuotaErosionAccessor(
            self.client_id
        ).get_quota_erosion_records_in_period_for_payee(
            payee_email=self.payee_email,
            period_end_date=self.period_end_date,
            plan_id=self.plan_id,
            criteria_id=self.criteria_id,
            category=category,
            is_team=self.is_team_criteria,
            knowledge_date=self.knowledge_date,
        )
        qe_records = sort_qe_records_based_on_tier_id(qe_records)
        if qv is not None and payout is not None:
            cum_qe = qe_records[0]["cumulative_qe"] if qe_records else 0
        else:
            cum_qe = 0
            qv = 0
        self.quota_value = qv
        data["schedule"] = schedule
        self.quota_schedule = schedule
        data_copy = copy.deepcopy(data)
        part1_result, part1_resolved_variables = part1_line_item_level(
            data_copy,
            "row_key",
            tier_type,
            part1_ast,
            infix=part1_exp,
            knowledge_date=self.knowledge_date,
            ever_comparison=self.ever_comparison,
        )
        keys_to_delete = set()
        for key, val in part1_result.items():
            if val == float("-inf"):
                keys_to_delete.add(key)
        for key in keys_to_delete:
            part1_result.pop(key)
        part1_resolved_variables = part1_resolved_variables.get(self.row_key, {})
        if not is_line_item_level:
            part1_value = sum(part1_result.values())
            cumulative_value = cum_qe + part1_value
            cumulative_qa = (cumulative_value / qv) * 100 if qv else 0
            data["tiered_data"] = cumulative_value
            data["tiered_percentage"] = (cumulative_value / qv) if qv else 0
            data["original_part1_result"] = part1_value
            if part1_type.lower() == "value":
                if self.quota_value == 0:
                    final_result = [
                        {
                            "id": None,
                            "quota_erosion": cumulative_value,
                            "tier_id": 0,
                            "original_tier_id": 0,
                            "actual_tier_name": slab_name[0],
                            "tier_name": slab_name[0],
                            "commission": 0,
                            "resolved_variables": {},
                        }
                    ]
                else:
                    final_result = summated_value_tier_allocation_logic(
                        cumulative_value,
                        data,
                        slab_bound,
                        slab_name,
                        slab_ast,
                        slab_infix=slab_infix,
                        ever_comparison=self.ever_comparison,
                        knowledge_date=self.knowledge_date,
                    )
            elif part1_type.lower() == "quotaattainment":
                final_result = summated_value_tier_allocation_logic(
                    cumulative_qa,
                    data,
                    slab_bound,
                    slab_name,
                    slab_ast,
                    slab_infix=slab_infix,
                    ever_comparison=self.ever_comparison,
                    knowledge_date=self.knowledge_date,
                )
            slab_id = final_result[0]["original_tier_id"]
            final_result[0]["commission_date"] = self.period_end_date
            final_result[0]["quota_erosion"] = part1_value
            final_result[0]["cumulative_quota_erosion"] = cumulative_value
            final_result[0]["tiered_value"] = cumulative_value
            final_result[0]["quota_attainment"] = (part1_value / qv) * 100 if qv else 0
            final_result[0]["cumulative_quota_attainment"] = cumulative_qa
            final_result[0]["infix"] = slab_infix.get(slab_id, 0)
            final_result[0]["slab_bound"] = slab_bound.get(slab_id)
        else:
            part1_value = part1_result.get(self.row_key, 0)
            cumulative_qe = cum_qe

            tiered_result = []

            # Cumulative QE for current line item is the sum of all the QE records till the current line item.
            # E.g For quaterly quota, (Jan to Mar), let's say quota erosion of Jan is 100
            # so, Cum_qe for 4th record in  Feb is 100 + Feb's quota erosion for 1st 3 records.

            for i in range(len(qe_records)):
                if qe_records[i]["line_item_id"] == self.row_key:
                    for j in range(i, len(qe_records)):
                        if qe_records[j]["line_item_id"] != self.row_key:
                            break
                        tiered_result.append(qe_records[j])
                    break
                cumulative_qe += qe_records[i]["quota_erosion"]

            for item in tiered_result:
                result = {}
                qe = item["quota_erosion"]
                tier_id = item.get("tier_id", 0)
                if tier_id == "-":
                    original_tier_id = tier_id
                else:
                    tier_id = int(tier_id) if qv else 0
                    original_tier_id = (
                        int(
                            item["original_tier_id"]
                            if item["original_tier_id"]
                            else tier_id
                        )
                        if qv
                        else 0
                    )
                data["tiered_data"] = qe
                data["tiered_percentage"] = (qe / qv) if qv else 0
                result["original_tier_id"] = original_tier_id
                result["actual_tier_name"] = slab_name.get(original_tier_id)
                ast = slab_ast.get(tier_id)
                infix = slab_infix.get(tier_id, None)
                resolved_variables = {}
                result["tiered_value"] = qe
                result["quota_erosion"] = qe
                cumulative_qe = cumulative_qe + qe
                cumulative_qa = (cumulative_qe / qv) * 100 if qv else 0
                result["cumulative_quota_erosion"] = cumulative_qe
                result["cumulative_quota_attainment"] = cumulative_qa
                result["quota_attainment"] = data["tiered_percentage"] * 100
                result["commission"] = (
                    evaluate(
                        ast,
                        data=data,
                        infix=infix,
                        ever_comparison=self.ever_comparison,
                        resolved_variables=resolved_variables,
                        knowledge_date=self.knowledge_date,
                    )
                    if qv and infix
                    else 0
                )
                if "line_item" in data:
                    resolved_variables.update(data["line_item"])
                result["resolved_variables"] = resolved_variables if qv else {}
                result["tier_id"] = tier_id
                result["slab_bound"] = slab_bound.get(original_tier_id)
                result["tier_name"] = slab_name.get(tier_id)
                result["row_key"] = self.row_key
                result["infix"] = slab_infix.get(tier_id, {})
                final_result.append(result)
        return {
            "part1_result": part1_value,
            "part1_resolved_variables": part1_resolved_variables,
            "part2_result": final_result,
        }

    def construct_response_for_criteria(self, criteria, infix, commission, result=None):
        """Constructs tracing response for a criteria"""
        res = {
            "columns": {},
            "records": [],
            "trace_info": {},
            "trace": [],
            "sub_traces": {},
            "criteria_columns": criteria["criteria_column"],
        }
        logger.info("Constructing tracing response", extra=self.log_context)
        all_columns_map = {ds_var["system_name"]: ds_var for ds_var in self.ds_vars}
        # Adding system defined columns
        all_columns_map.update(
            {sys_var["system_name"]: sys_var for sys_var in self.sys_vars}
        )
        qe_localized = get_localized_message_service(
            CommissionTraceMessages.QUOTA_EROSION.value, self.client_id
        )
        qa_localized = get_localized_message_service(
            CommissionTraceMessages.QUOTA_ATTAINMENT.value, self.client_id
        )
        cummulaive_qe_localized = get_localized_message_service(
            CommissionTraceMessages.CUMULATIVE_QE.value, self.client_id
        )
        cummulaive_qa_localized = get_localized_message_service(
            CommissionTraceMessages.CUMULATIVE_QA.value, self.client_id
        )
        all_columns_map.update(
            {
                "actualTierName": {
                    "system_name": "actualTierName",
                    "display_name": "Actual Tier Name",
                    "data_type__data_type": "String",
                },
                "tierName": {
                    "system_name": "tierName",
                    "display_name": "Tier Name",
                    "data_type__data_type": "String",
                },
                "quotaErosion": {
                    "system_name": "quotaErosion",
                    "display_name": qe_localized,
                    "data_type__data_type": "Integer",
                },
                "cumulativeQuotaErosion": {
                    "system_name": "cumulativeQuotaErosion",
                    "display_name": cummulaive_qe_localized,
                    "data_type__data_type": "Integer",
                },
                "cumulativeQuotaAttainment": {
                    "system_name": "cumulativeQuotaAttainment",
                    "display_name": cummulaive_qa_localized,
                    "data_type__data_type": "Percentage",
                },
                "quotaAttainment": {
                    "system_name": "quotaAttainment",
                    "display_name": qa_localized,
                    "data_type__data_type": "Percentage",
                },
            }
        )
        criteria_columns = criteria["criteria_column"]
        is_crm_hyperlinks_enabled = get_client_features(client_id=self.client_id).get(
            "crm_hyperlinks", False
        )
        hyperlink_required_columns = []

        if is_crm_hyperlinks_enabled:
            hyperlink_map = HyperlinkService(
                client_id=self.client_id
            ).get_hyperlink_map_for_statements(
                criteria_id=self.criteria_id, plan_id=self.plan_id
            )

            for item in hyperlink_map:
                hyperlink_required_columns += hyperlink_map[item][
                    "url_identifier_fields"
                ]

        criteria_columns = criteria["criteria_column"] + hyperlink_required_columns
        is_override = criteria["criteria_data"].get("is_override", False)
        # TODO: Remove this once actual tier name and qa are added to criteria columns
        if self.criteria_type in ("Tier", "CustomTier", "Quota", "CustomQuota"):
            if is_override:
                criteria_columns.append("actualTierName")
            if "tierName" not in criteria_columns:
                criteria_columns.append("tierName")
        if self.criteria_type in ("Quota", "CustomQuota"):
            criteria_columns.append("quotaAttainment")
            criteria_columns.append("cumulativeQuotaErosion")
            criteria_columns.append("cumulativeQuotaAttainment")
        # This is a subset of all_columns_map containing only the columns used in the criteria
        columns_map = {
            col: {"_id": idx, **all_columns_map[col]}
            for idx, col in enumerate(criteria_columns)
            if col in all_columns_map
        }
        # This if statement is to localize the display name of commission which is a system_variable in Variable table
        if "commission" in columns_map:
            columns_map["commission"]["display_name"] = get_localized_message_service(
                CommissionTraceMessages.COMMISSION.value, self.client_id
            )
        res["columns"] = {
            col_data["_id"]: {
                "system_name": system_name,
                "display_name": col_data.get("display_name") or col_data.get("name"),
                "data_type": col_data["data_type__data_type"],
            }
            for system_name, col_data in columns_map.items()
        }
        # Not sending line items if criteria is not line item level
        if not criteria["criteria_data"]["is_line_item_level"]:
            res["columns"] = {}
            res["records"] = []
        # Otherwise, sending all line items if criteria is Tier
        elif self.criteria_type in ("Tier", "CustomTier", "Quota", "CustomQuota"):
            commission_records = []
            line_item = {}
            for item in self.line_items:
                if item["row_key"] == self.row_key:
                    line_item = item
                    break
            if result:
                part2_result = result["part2_result"]
                for record in part2_result:
                    tier_record = {
                        **line_item,
                        "commission": self.resolve_value(record["commission"]),
                        "tierName": record["tier_name"],
                    }
                    if is_override:
                        tier_record["actualTierName"] = record["actual_tier_name"]
                    if "quota_erosion" in record:
                        tier_record["quotaErosion"] = self.resolve_value(
                            record["quota_erosion"]
                        )
                        tier_record["quotaAttainment"] = self.resolve_value(
                            record["quota_attainment"]
                        )
                        tier_record["cumulativeQuotaErosion"] = self.resolve_value(
                            record["cumulative_quota_erosion"]
                        )
                        tier_record["cumulativeQuotaAttainment"] = self.resolve_value(
                            record["cumulative_quota_attainment"]
                        )
                    commission_records.append(tier_record)
            res["records"] = commission_records
        else:
            res["records"] = [
                pydash.find(
                    self.line_items,
                    lambda line_item: line_item["row_key"] == self.row_key,
                )
            ]
        if res["records"]:
            res["records"] = pydash.map_(
                res["records"],
                lambda line_item: {  # Convereting system_name->value map to unique_id->value map
                    columns_map[system_name]["_id"]: value
                    for system_name, value in line_item.items()
                    if system_name in columns_map
                },
            )
        trace = None
        currency_symbol = self.currency_symbol_map[
            self.base_currency if self.is_base_currency else self.payee_currency
        ]
        if self.criteria_type in ("Simple", "CustomSimple"):
            trace = {
                "expression": self.resolve_infix_expression(infix),
                "result": {
                    "data_type": "Integer",
                    "value": commission,
                    "currency_symbol": currency_symbol,
                },
            }
        elif self.criteria_type in ("Conditional", "CustomConditional"):
            used_infix = self.resolved_variables.get("evaluated_expression", {})
            trace = {
                "condition": [
                    {
                        "expression": self.resolve_infix_expression(item["condition"]),
                        "result": {
                            "data_type": "Boolean",
                            "value": self.resolve_value(item["result"]),
                        },
                    }
                    for item in self.resolved_variables.get("if_res", [])
                ],
                "expression": (
                    self.resolve_infix_expression(used_infix)
                    if used_infix
                    else [
                        self.resolve_infix_token(
                            {
                                "type": "VARIABLE",
                                "function_name": "CONSTANT",
                                "args": ["Integer", 0],
                                "data_type": "Integer",
                            }
                        ),
                    ]
                ),
                "result": {
                    "data_type": "Integer",
                    "value": commission,
                    "currency_symbol": currency_symbol,
                },
            }
        elif self.criteria_type in ("Tier", "CustomTier", "Quota", "CustomQuota"):
            traces = []
            tier_records = []
            is_quota_criteria = self.criteria_type in ("Quota", "CustomQuota")
            if result:
                part1_value = result.get("part1_result", 0)
                part1_resolved_variables = result.get("part1_resolved_variables", {})
                part1_trace = self.get_part_1_trace(
                    criteria,
                    infix,
                    part1_value,
                    part1_resolved_variables,
                )
                traces.append(part1_trace)
                part2_result = result["part2_result"]
                for record in part2_result:
                    _infix = record["infix"]
                    resolved_vars = record["resolved_variables"]
                    if "TieredValue()" not in resolved_vars:
                        resolved_vars["TieredValue()"] = record["tiered_value"]
                    tier_trace = {
                        "name": record["actual_tier_name"],
                        "actual_tier_name": record["actual_tier_name"],
                    }
                    if self.quota_value == 0 and is_quota_criteria:
                        tier_trace["expression"] = [
                            self.resolve_infix_token(
                                {
                                    "type": "VARIABLE",
                                    "function_name": "CONSTANT",
                                    "args": ["Integer", 0],
                                    "data_type": "Integer",
                                }
                            ),
                        ]
                        tier_trace["result"] = {
                            "data_type": "Integer",
                            "value": 0,
                            "currency_symbol": currency_symbol,
                        }

                    else:
                        # For conditional tier criteria
                        if resolved_vars.get("if_res", []):
                            used_infix = resolved_vars.get("evaluated_expression", {})
                            tier_trace["condition"] = [
                                {
                                    "expression": self.resolve_infix_expression(
                                        item["condition"],
                                        resolved_variables=resolved_vars,
                                    ),
                                    "result": {
                                        "data_type": "Boolean",
                                        "value": self.resolve_value(item["result"]),
                                    },
                                }
                                for item in resolved_vars.get("if_res", [])
                            ]
                            tier_trace["expression"] = (
                                self.resolve_infix_expression(
                                    used_infix, resolved_variables=resolved_vars
                                )
                                if used_infix
                                else [
                                    self.resolve_infix_token(
                                        {
                                            "type": "VARIABLE",
                                            "function_name": "CONSTANT",
                                            "args": ["Integer", 0],
                                            "data_type": "Integer",
                                        }
                                    ),
                                ]
                            )
                        else:
                            tier_trace["expression"] = self.resolve_infix_expression(
                                _infix, resolved_variables=resolved_vars
                            )
                        tier_trace["result"] = {
                            "data_type": "Integer",
                            "value": self.resolve_value(record["commission"]),
                            "currency_symbol": currency_symbol,
                        }
                    lower_bound = (
                        record["slab_bound"][0]
                        if record.get("slab_bound")
                        and record["slab_bound"][0].is_finite()
                        else None
                    )
                    upper_bound = (
                        record["slab_bound"][1]
                        if record.get("slab_bound")
                        and record["slab_bound"][1].is_finite()
                        else None
                    )
                    tier_record = {
                        "slab_lower_bound": lower_bound,
                        "slab_upper_bound": upper_bound,
                        "tiered_value": self.resolve_value(record["tiered_value"]),
                        "actual_tier_name": record["actual_tier_name"],
                    }
                    if is_quota_criteria:
                        tier_record["quota_retirement"] = self.resolve_value(
                            record["quota_erosion"]
                        )
                        tier_record["quota_attainment"] = self.resolve_value(
                            record["quota_attainment"]
                        )
                        tier_record["cumulative_quota_retirement"] = self.resolve_value(
                            record["cumulative_quota_erosion"]
                        )
                        tier_record["cumulative_quota_attainment"] = self.resolve_value(
                            record["cumulative_quota_attainment"]
                        )
                    if is_override:
                        tier_trace["tier_name"] = record["tier_name"]
                        tier_record["tier_name"] = record["tier_name"]
                    if tier_trace.get("name") is not None:
                        traces.append(tier_trace)
                    tier_records.append(tier_record)
                res["tier_records"] = tier_records
                res["trace"] = traces
        if trace:
            res["trace"].append(trace)
        res["sub_traces"] = self.sub_traces
        res["criteria_type"] = self.criteria_type
        if self.is_team_criteria:
            res["trace_info"]["team_name"] = criteria["criteria_data"]["team"]
        if self.criteria_type in ("Quota", "CustomQuota"):
            # modify quota name
            displayName = QuotaAccessor(
                self.client_id
            ).get_display_name_for_quota_category_name(
                criteria["criteria_data"]["quota_name"]
            )
            res["trace_info"]["quota_name"] = displayName
            res["trace_info"]["tier_comparison_type"] = criteria["criteria_data"][
                "part_1_type"
            ]
        return res

    def get_part_1_trace(self, criteria, infix, result, resolved_variables):
        trace = {}
        is_quota_criteria = self.criteria_type in ("Quota", "CustomQuota")
        is_line_item_level = criteria["criteria_data"]["is_line_item_level"]
        expression_type = criteria["criteria_data"]["expression_type"]
        localized_qe = get_localized_message_service(
            CommissionTraceMessages.QUOTA_EROSION.value, self.client_id
        )
        localized_total_qe = get_localized_message_service(
            CommissionTraceMessages.TOTAL_QE.value, self.client_id
        )
        trace["name"] = localized_qe if is_quota_criteria else "Tier Value"
        result = self.resolve_value(result)
        tier_type = criteria["criteria_data"]["tier_type"]
        trace["tier_type"] = tier_type
        if is_quota_criteria:
            title = localized_qe if is_line_item_level else localized_total_qe
        else:
            title = "Tier Value" if is_line_item_level else "Total Tier Value"
        if not is_line_item_level:
            trace["expression"] = [
                self.resolve_infix_token(
                    {
                        "type": "VARIABLE",
                        "function_name": "CONSTANT",
                        "args": ["Integer", result],
                        "data_type": "Integer",
                    }
                ),
            ]
            trace["result"] = {
                "data_type": "Integer",
                "value": result,
            }
            trace["expression_type"] = (
                "Simple" if tier_type == "TieredCount" else expression_type
            )
            trace["infix"] = self.resolve_infix_expression(
                infix,
                skip_variable_value=True,
                resolved_variables=resolved_variables,
            )
            trace["expression"][0]["title"] = title
        else:
            if tier_type == "TieredCount":
                trace["condition"] = [
                    {
                        "expression": self.resolve_infix_expression(
                            infix, resolved_variables=resolved_variables
                        ),
                        "result": {
                            "data_type": "Boolean",
                            "value": self.resolve_value(bool(result)),
                        },
                    }
                ]
                trace["expression"] = [
                    self.resolve_infix_token(
                        {
                            "type": "VARIABLE",
                            "function_name": "CONSTANT",
                            "args": ["Integer", result],
                            "data_type": "Integer",
                        }
                    )
                ]
                trace["expression"][0]["title"] = title
            elif expression_type == "Conditional":
                used_infix = resolved_variables.get("evaluated_expression", {})
                trace["condition"] = [
                    {
                        "expression": self.resolve_infix_expression(
                            item["condition"], resolved_variables=resolved_variables
                        ),
                        "result": {
                            "data_type": "Boolean",
                            "value": self.resolve_value(item["result"]),
                        },
                    }
                    for item in resolved_variables.get("if_res", [])
                ]
                trace["expression"] = (
                    self.resolve_infix_expression(
                        used_infix, resolved_variables=resolved_variables
                    )
                    if used_infix
                    else [
                        self.resolve_infix_token(
                            {
                                "type": "VARIABLE",
                                "function_name": "CONSTANT",
                                "args": ["Integer", 0],
                                "data_type": "Integer",
                            }
                        ),
                    ]
                )
            else:
                trace["expression"] = self.resolve_infix_expression(
                    infix, resolved_variables=resolved_variables
                )

            trace["result"] = {
                "data_type": "Integer",
                "value": result,
            }
        return trace

    def get_team_data(
        self,
        criteria_conditions,
        line_items=None,
    ):
        date_field = str(criteria_conditions["date_field"])
        team_name = criteria_conditions["team_name"]
        team_members = get_team_members(
            self.client_id,
            self.period_start_date,
            self.period_end_date,
            self.payee_email,
            team_name,
            self.knowledge_date,
        )
        effective_date_map = {}
        client_features = get_client_features(client_id=self.client_id)
        is_manager_rollup_ed = client_features.get("manager_rollup_ed")
        if is_manager_rollup_ed:
            team_members_start = get_team_members(
                self.client_id,
                self.period_start_date,
                self.period_start_date,
                self.payee_email,
                team_name,
                self.knowledge_date,
            )
            team_members = list(set(team_members).union(team_members_start))
        if line_items and isinstance(line_items, Iterable):
            payee_field = criteria_conditions["payee_field"]
            if team_members and isinstance(team_members, list):
                team_members.append(str(self.payee_email))
            else:
                team_members = [str(self.payee_email)]
            result_line_items = [
                line for line in line_items if line[payee_field] in team_members
            ]
            if not result_line_items:
                raise LineItemNotFound()
            team_data = {"merged_data": line_items, "context": self.payee_context}
            self.team_params = {
                "team_context": team_data,
                "team_name": team_name,
                "team_type": self.team_type,
                "team_owner_email_id": self.payee_email,
            }
            return result_line_items
        dat = get_data(
            self.client_id,
            self.period_start_date,
            self.period_end_date,
            self.knowledge_date,
            criteria_conditions,
            what_if_params=None,
            logger=logger,
            payee_email_tuple=tuple(team_members),
            team_owner_email=self.payee_email,
            plan_effective_date={
                "effective_start_date": self.plan_start_date,
                "effective_end_date": self.plan_end_date,
            },
        )
        key = "row_key"
        team_data = get_team_context(team_members, dat, self.payee_email)

        filtered_dat = remove_locked_data(
            self.client_id,
            self.period_start_date,
            self.period_end_date,
            self.knowledge_date,
            self.plan_id,
            self.criteria_id,
            {
                "merged_data": team_data["merged_data"],
                "context": team_data["context"],
            },
            key,
            self.payee_email,
            commission_type=COMMISSION_TYPE.COMMISSION,
        )
        team_data["merged_data"] = filtered_dat["merged_data"]
        team_data["context"] = filtered_dat["context"]
        if is_manager_rollup_ed:
            effective_date_start = get_team_members_with_effective_date(
                self.client_id,
                self.period_start_date,
                self.payee_email,
                team_name,
                knowledge_date=self.knowledge_date,
            )
            effective_date_end = get_team_members_with_effective_date(
                self.client_id,
                self.period_end_date,
                self.payee_email,
                team_name,
                knowledge_date=self.knowledge_date,
            )
            effective_date_map = map_effective_dates_start_end(
                effective_date_start, effective_date_end
            )
            team_data["merged_data"] = filter_line_items_greater_than_effective_date(
                team_data["merged_data"],
                criteria_conditions["date_field"],
                effective_date_map,
                payee_field=criteria_conditions["payee_field"],
            )
        # Sort data based on mentioned columns, else default by date field
        sorted_columns = criteria_conditions.get("sort_columns", [])
        if team_data["merged_data"]:
            if sorted_columns:
                sort_order_map = {"asc": True, "desc": False}
                sort_cols = []
                sort_order = []
                for entry in sorted_columns:
                    sort_cols.append(entry[0])
                    sort_order.append(sort_order_map[entry[1]])
                team_merged_data_as_dataframe = pd.DataFrame.from_records(
                    team_data["merged_data"]
                )
                team_merged_data_as_dataframe = (
                    team_merged_data_as_dataframe.sort_values(
                        sort_cols, ascending=sort_order
                    )
                )
                team_data["merged_data"] = team_merged_data_as_dataframe.to_dict(
                    "records"
                )
            else:
                team_data["merged_data"] = sorted(
                    team_data["merged_data"], key=lambda d: d[date_field]
                )
        self.team_params = {
            "team_context": team_data,
            "team_name": team_name,
            "team_type": self.team_type,
            "team_owner_email_id": self.payee_email,
            "team_members": team_members,
            "effective_date_map": effective_date_map,
        }
        return team_data["merged_data"]

    def get_payee_context(self):
        annual_variable_payee_in_base = change_to_base_currency_by_fx_rate(
            self.employee_payroll["variable_pay"], self.fx_rate
        )
        period_variable_pay = get_variable_pay_per_period(
            self.client_id,
            annual_variable_payee_in_base,
            self.employee_payroll["payout_frequency"],
            self.period_end_date,
        )
        return {
            "payee": [
                {
                    "email": self.payee_email,
                    "actual_variable_pay": annual_variable_payee_in_base,
                    "payout_frequency": self.employee_payroll["payout_frequency"],
                    "variable_pay": period_variable_pay,
                }
            ]
        }

    # NOTE: Commenting out the below methods as they are not currently being used.
    # def get_calculated_field_trace(
    #     self,
    #     databook_id,
    #     datasheet_id,
    #     calc_field_system_name,
    #     row_key,
    # ):
    #     """Provides the calculated field evaluation trace based on the
    #     given datasheet variable for a sepcific row_key

    #     NOTE: API for this service is not exposed yet!
    #     """
    #     self.databook_id = databook_id
    #     self.datasheet_id = datasheet_id
    #     self.calc_field_system_name = calc_field_system_name
    #     self.row_key = row_key
    #     self.log_context = {
    #         "client_id": self.client_id,
    #         "databook_id": self.databook_id,
    #         "datasheet_id": self.datasheet_id,
    #         "calc_field_system_name": self.calc_field_system_name,
    #         "row_key": self.row_key,
    #     }
    #     logger.info("BEGIN: Tracing calculated field", extra=self.log_context)
    #     infix, ast, is_row_calc_field = self.extract_data_for_calculated_field()
    #     result = self.evaluate_calculated_field(infix, ast, is_row_calc_field)
    #     res = self.construct_response_for_calculated_field(infix, result)
    #     logger.info("END: Tracing calculated field", extra=self.log_context)
    #     return res

    # def extract_data_for_calculated_field(self):
    #     """Extracts data for calculated field tracing"""
    #     logger.info(
    #         "Extracting data for calculated field tracing", extra=self.log_context
    #     )
    #     self.knowledge_date = timezone.now()
    #     datasheet_var = DatasheetVariableAccessor(
    #         self.client_id
    #     ).get_objects_by_datasheet_id(
    #         self.datasheet_id,
    #         knowledge_date=self.knowledge_date,
    #         system_name=self.calc_field_system_name,
    #         projection=[
    #             "system_name",
    #             "display_name",
    #             "data_type__data_type",
    #             "field_order",
    #             "meta_data",
    #         ],
    #     )
    #     datasheet_var = datasheet_var[0] if datasheet_var else None
    #     if not datasheet_var:
    #         raise DatasheetVariableNotFound()
    #     infix = None
    #     ast = None
    #     is_row_calc_field = True
    #     if pydash.has(datasheet_var, ["meta_data", "infix"]):
    #         infix = pydash.get(datasheet_var, ["meta_data", "infix"])
    #         ast = pydash.get(datasheet_var, ["meta_data", "ast"])
    #     elif pydash.has(datasheet_var, ["meta_data", "rank"]):
    #         is_row_calc_field = False
    #         rank_field_meta_data = pydash.get(datasheet_var, ["meta_data", "rank"])
    #         rank_field_meta_data.update(
    #             {"rank_column_name": self.calc_field_system_name}
    #         )
    #         infix = ast = {
    #             "type": "rank",
    #             "rank": rank_field_meta_data,
    #             "system_name": self.calc_field_system_name,
    #         }
    #     elif pydash.has(datasheet_var, ["meta_data", "rolling"]):
    #         is_row_calc_field = False
    #         rolling_field_meta_data = pydash.get(
    #             datasheet_var, ["meta_data", "rolling"]
    #         )
    #         rolling_field_meta_data.update(
    #             {"rolling_column_name": self.calc_field_system_name}
    #         )
    #         infix = ast = {
    #             "type": "rolling",
    #             "rolling": rolling_field_meta_data,
    #             "system_name": self.calc_field_system_name,
    #         }
    #     variables_used = VariableExtractor().get_variables_used(ast)
    #     if variables_used:
    #         self.ds_vars = DatasheetVariableAccessor(
    #             self.client_id
    #         ).get_objects_by_datasheet_id(
    #             self.datasheet_id,
    #             knowledge_date=self.knowledge_date,
    #             system_name=variables_used,
    #             projection=[
    #                 "system_name",
    #                 "display_name",
    #                 "data_type__data_type",
    #                 "field_order",
    #                 "meta_data",
    #             ],
    #         )
    #         self.ds_vars_map = {
    #             ds_var["system_name"]: ds_var for ds_var in self.ds_vars
    #         }
    #         self.calculated_fields_map = {
    #             ds_var["system_name"]: ds_var
    #             for ds_var in self.ds_vars
    #             if ds_var["field_order"] > 0
    #         }
    #     self.calculated_fields_map[self.calc_field_system_name] = datasheet_var
    #     self.line_items = self.get_line_items_for_calculated_field(
    #         is_row_calc_field=is_row_calc_field
    #     )
    #     if not self.line_items:
    #         raise LineItemNotFound()
    #     if not is_row_calc_field:
    #         line_item = pydash.find(
    #             self.line_items, lambda line_item: line_item["row_key"] == self.row_key
    #         )
    #         self.resolved_variables = copy.deepcopy(line_item)
    #     return (infix, ast, is_row_calc_field)

    def get_line_items_for_calculated_field(self, is_row_calc_field=True):
        if self._should_use_multi_engine_stormbreaker:
            params = StormBreakerDSInitType(
                compute_strategy="duckdb_fallback_variant_snowflake",
                client_id=self.client_id,
                databook_id=UUID(str(self.databook_id)),
                datasheet_id=UUID(str(self.datasheet_id)),
                knowledge_date=self.knowledge_date,
            )
            ds_storm_breaker = StormBreakerDSFactory.init(params)
        else:
            ds_storm_breaker = StormBreakerDSVariant(
                self.client_id, UUID(self.databook_id), UUID(self.datasheet_id)
            )
        ds_storm_breaker.reset_apply_datasheet_permissions()
        ds_storm_breaker.set_knowledge_date(self.knowledge_date)
        limit = None
        if is_row_calc_field:
            line_items = ds_storm_breaker.filter("row_key", "eq", self.row_key)
            limit = 1
        if self._should_use_multi_engine_stormbreaker and isinstance(
            ds_storm_breaker, StormBreakerDSMultiEngine
        ):
            line_items = ds_storm_breaker.fetch_datasheet_data_as_of_date(self.knowledge_date, limit=limit, offset=None)  # type: ignore
        elif not self._should_use_multi_engine_stormbreaker and isinstance(
            ds_storm_breaker, StormBreakerDSVariant
        ):
            line_items = ds_storm_breaker.fetch(limit=limit, offset=None)
        else:
            raise TypeError("Invalid type of StormbreakerDS")
        return line_items

    def extract_data_for_calculated_field(self):
        """Extracts data for calculated field tracing"""
        logger.info(
            "Extracting data for calculated field tracing", extra=self.log_context
        )
        self.knowledge_date = timezone.now()
        datasheet_var = DatasheetVariableAccessor(
            self.client_id
        ).get_objects_by_datasheet_id(
            self.datasheet_id,
            knowledge_date=self.knowledge_date,
            system_name=self.calc_field_system_name,
            projection=[
                "system_name",
                "display_name",
                "data_type__data_type",
                "field_order",
                "meta_data",
            ],
        )
        datasheet_var = datasheet_var[0] if datasheet_var else None
        if not datasheet_var:
            raise DatasheetVariableNotFound()

        if isinstance(datasheet_var, dict):
            meta_data = datasheet_var.get("meta_data", None)
            if meta_data is not None:
                new_datasheet_var = {
                    "databook_id": self.databook_id,
                    "datasheet_id": self.datasheet_id,
                    "meta_data": meta_data,
                }
                converted_meta_data = convert_calculated_field_meta_data(
                    client_id=self.client_id,
                    datasheet_var_obj=new_datasheet_var,
                )
                datasheet_var.update({"meta_data": converted_meta_data})

        infix = None
        ast = None
        is_row_calc_field = True
        if pydash.has(datasheet_var, ["meta_data", "infix"]):
            infix = pydash.get(datasheet_var, ["meta_data", "infix"])
            ast = pydash.get(datasheet_var, ["meta_data", "ast"])
        elif pydash.has(datasheet_var, ["meta_data", "rank"]):
            is_row_calc_field = False
            rank_field_meta_data = pydash.get(datasheet_var, ["meta_data", "rank"])
            rank_field_meta_data.update(
                {"rank_column_name": self.calc_field_system_name}
            )
            infix = ast = {
                "type": "rank",
                "rank": rank_field_meta_data,
                "system_name": self.calc_field_system_name,
            }
        elif pydash.has(datasheet_var, ["meta_data", "rolling"]):
            is_row_calc_field = False
            rolling_field_meta_data = pydash.get(
                datasheet_var, ["meta_data", "rolling"]
            )
            rolling_field_meta_data.update(
                {"rolling_column_name": self.calc_field_system_name}
            )
            infix = ast = {
                "type": "rolling",
                "rolling": rolling_field_meta_data,
                "system_name": self.calc_field_system_name,
            }
        variables_used = VariableExtractor().get_variables_used(ast)
        if variables_used:
            self.ds_vars = DatasheetVariableAccessor(
                self.client_id
            ).get_objects_by_datasheet_id(
                self.datasheet_id,
                knowledge_date=self.knowledge_date,
                system_name=variables_used,
                projection=[
                    "system_name",
                    "display_name",
                    "data_type__data_type",
                    "field_order",
                    "meta_data",
                ],
            )
            (
                self.ds_vars_map,
                self.calculated_fields_map,
            ) = self.get_vars_map_from_ds_vars(
                self.databook_id, self.datasheet_id, self.ds_vars
            )
        self.calculated_fields_map[self.calc_field_system_name] = datasheet_var
        self.line_items = self.get_line_items_for_calculated_field(
            is_row_calc_field=is_row_calc_field
        )
        if not self.line_items:
            raise LineItemNotFound()
        if not is_row_calc_field:
            line_item = pydash.find(
                self.line_items, lambda line_item: line_item["row_key"] == self.row_key
            )
            self.resolved_variables = copy.deepcopy(line_item)
        return (infix, ast, is_row_calc_field)

    def evaluate_calculated_field(
        self,
        infix,
        ast,
        is_row_calc_field,
        calc_field_system_name=None,
        resolved_variables=None,
    ):
        """Evaluates lines items for a calculated field"""
        calc_field_system_name = (
            self.calc_field_system_name
            if calc_field_system_name is None
            else calc_field_system_name
        )
        resolved_variables = (
            self.resolved_variables
            if resolved_variables is None
            else resolved_variables
        )
        data = {
            "context": {},
            "merged_data": self.line_items,
            "client_id": self.client_id,
            "start_month": self.fiscal_start_month,
        }
        result = 0
        if is_row_calc_field:
            data["line_item"] = pydash.find(
                self.line_items, lambda line_item: line_item["row_key"] == self.row_key
            )
            if not data["line_item"]:
                raise LineItemNotFound()
            logger.info(
                "BEGIN: Evaluating calculated field for tracing", extra=self.log_context
            )
            result = evaluate(
                ast,
                data=data,
                ever_comparison=self.ever_comparison,
                resolved_variables=resolved_variables,
                infix=infix,
            )
            logger.info(
                "END: Evaluating calculated field for tracing", extra=self.log_context
            )
            resolved_variables.update(data["line_item"])
        else:
            if "rank" in ast or "rolling" in ast:
                logger.info(
                    "Using predetermined value for rank and rolling calculated field",
                    extra=self.log_context,
                )
                # Not evaluating ranked calculated field as it requires
                # the entire dataset to be evaluated
                result = resolved_variables[calc_field_system_name]
        if result == float("-inf"):
            result = 0
        return result

    def construct_response_for_calculated_field(
        self, infix, result, resolved_variables=None
    ):
        """Constructs tracing response for a calculated field"""
        resolved_variables = (
            self.resolved_variables
            if resolved_variables is None
            else resolved_variables
        )
        res = {
            "trace": [],
            "sub_traces": {},
        }
        logger.info("Constructing tracing response", extra=self.log_context)
        trace = None
        if resolved_variables.get("if_res", []):
            used_infix = resolved_variables.get("evaluated_expression", {})
            if not used_infix:
                used_infix = [
                    {
                        "type": "VARIABLE",
                        "function_name": "CONSTANT",
                        "args": ["Integer", 0],
                        "data_type": "Integer",
                    }
                ]
            trace = {
                "condition": [
                    {
                        "expression": self.resolve_infix_expression(
                            item["condition"], resolved_variables=resolved_variables
                        ),
                        "result": {
                            "data_type": "Boolean",
                            "value": self.resolve_value(item["result"]),
                        },
                    }
                    for item in resolved_variables.get("if_res", [])
                ],
                "expression": self.resolve_infix_expression(
                    used_infix, resolved_variables=resolved_variables
                ),
                "result": {
                    "data_type": "Integer",
                    "value": self.resolve_value(result),
                },
            }
        else:
            trace = {
                "expression": self.resolve_infix_expression(
                    infix, resolved_variables=resolved_variables
                ),
                "result": {
                    "data_type": "Integer",
                    "value": self.resolve_value(result),
                },
            }
        res["trace"].append(trace)
        res["sub_traces"] = self.sub_traces
        return res

    def get_calculated_field_sub_trace(self, calc_field_system_name):
        calc_field = self.calculated_fields_map.get(calc_field_system_name)
        if not calc_field:
            raise DatasheetVariableNotFound()
        is_row_calc_field = True
        if pydash.has(calc_field, ["meta_data", "infix"]):
            infix = calc_field["meta_data"]["infix"]
            ast = calc_field["meta_data"]["ast"]
        else:
            for window_function in get_window_based_calculated_fields():
                if pydash.has(calc_field, ["meta_data", window_function]):
                    is_row_calc_field = False
                    meta_data = calc_field["meta_data"][window_function]
                    column_name = f"{window_function}_column_name"
                    if column_name not in meta_data:
                        meta_data[column_name] = calc_field_system_name
                    infix = ast = {
                        "type": window_function,
                        window_function: calc_field["meta_data"][window_function],
                        "system_name": calc_field_system_name,
                    }
                    break
        resolved_variables = copy.deepcopy(
            pydash.find(
                self.line_items,
                lambda line_item: line_item["row_key"] == self.row_key,
            )
        )
        result = self.evaluate_calculated_field(
            infix,
            ast,
            is_row_calc_field,
            calc_field_system_name=calc_field_system_name,
            resolved_variables=resolved_variables,
        )
        res = self.construct_response_for_calculated_field(
            infix, result, resolved_variables=resolved_variables
        )
        return res["trace"][0]

    def get_ordered_line_items_from_commission(self):
        """
        Get ordered line_item_ids from commission data.

            1. Get commission data from commission_records table.
            2. Sort the commission data by tier_id.
            3. Return the sorted list of line_item_ids.

        Example: Commission data dataframe:

        | line_item_id | tier_id |
        |--------------|---------|
        | B            | 0       |
        | A            | 0       |
        | F            | 2       |
        | G            | 2       |
        | H            | 2       |
        | I            | 3       |
        | J            | 3       |
        | J            | 4       |
        | D            | 1       |
        | E            | 1       |
        | E            | 2       |
        | C            | 0       |
        | C            | 1       |

        Example: Grouped and sorted DataFrame (avg tier_id for each line_item_id):

        | line_item_id | tier_id  |
        |--------------|----------|
        | A            | 0.000000 |
        | B            | 0.000000 |
        | C            | 0.500000 |
        | D            | 1.000000 |
        | E            | 1.500000 |
        | F            | 2.000000 |
        | G            | 2.000000 |
        | H            | 2.000000 |
        | I            | 3.000000 |
        | J            | 3.500000 |

        Final ordered line_item_ids:
            ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
        """
        commission_data = get_commission_records(
            client_id=self.client_id,
            psd=self.period_start_date,
            ped=self.period_end_date,
            payee_email=self.payee_email,
            locked_kd=self.knowledge_date,
            logger=logger,
            additional_filters={
                "commission_plan_id": self.plan_id,
                "criteria_id": self.criteria_id,
            },
            fields=["line_item_id", "tier_id"],
        )
        logger.info("Fetched commission records (for trace) = %d", len(commission_data))

        if not commission_data:
            return None

        commission_df = pd.DataFrame(commission_data)

        # Convert tier_id (varchar) to integer, ignoring non-convertible values
        commission_df["tier_id"] = pd.to_numeric(
            commission_df["tier_id"], errors="coerce"
        )

        # Remove rows with NaN tier_id (non-convertible values like '-')
        commission_df = commission_df[commission_df["tier_id"].notna()]

        commission_df["tier_id"] = commission_df["tier_id"].astype(int)

        # Group by line_item_id, calculate average tier_id, and sort
        grouped = commission_df.groupby("line_item_id")["tier_id"].mean().reset_index()
        grouped = grouped.sort_values("tier_id", ascending=True)

        # Get the sorted list of line_item_ids
        ordered_line_item_ids = grouped["line_item_id"].tolist()

        return ordered_line_item_ids

    def resolve_custom_metrics(self, metric_definition, data_type_id, metric_name):
        """
        Resolve custom metrics in the expression.
        """
        # For trace alone we need to convert the infix to v1 format
        infix = converter(self.client_id, expression=metric_definition)["ast"]
        ast = create_ast(copy.deepcopy(infix))
        ast = ast["ast"]

        # form template data to evaluate the expression
        data = {
            "context": self.payee_context,
            "merged_data": [],
            "period_start_date": self.period_start_date,
            "period_end_date": self.period_end_date,
            "client_id": self.client_id,
            "start_month": self.fiscal_start_month,
            "team_params": self.team_params,
            "plan_id": self.plan_id,
            "payee_email": self.payee_email,
        }
        custom_metrics_resolved_vars = {}

        # Since we need to show the computed_value in case of max_cap, we need to evaluate the expression
        # We store only the final_value of the metric in the custom_metrics_value table
        # Also, the other token like getUserProp or quota will be resolved only in the evaluate function
        computed_value = evaluate(
            ast,
            data=data,
            ever_comparison=self.ever_comparison,
            resolved_variables=custom_metrics_resolved_vars,
            infix=infix,
            knowledge_date=self.knowledge_date,
        )

        computed_value = self.resolve_value(computed_value)
        self.resolved_variables.update(custom_metrics_resolved_vars)

        # Use this flag to distinguish forming trace for custom_metrics and normal commission_trace
        self.is_custom_metric_resolve = True
        trace = {
            "expression": self.resolve_infix_expression(
                infix, resolved_variables=self.resolved_variables
            ),
            "result": {
                "data_type": "Integer" if data_type_id == 1 else "Percentage",
                "value": computed_value,
            },
        }
        self.is_custom_metric_resolve = False

        # If max_cap is present, form the static trace for max_cap
        if metric_definition.get("max_cap"):
            sub_trace_trace_id = str(uuid4())
            self.sub_traces[sub_trace_trace_id] = trace
            max_cap_trace = get_static_trace_for_max_cap(
                computed_value,
                metric_definition.get("max_cap"),
                data_type_id,
                sub_trace_trace_id,
                metric_name,
            )
            return max_cap_trace

        return trace


def sort_qe_records_based_on_tier_id(qe_records: list[dict]) -> list[dict]:
    """
    This method sorts the quota erosion records based on the tier_id.
    """
    logger.info("Begining to sort quota erosion records")

    # Filter out entries with tier_id "-"
    filtered_records = [record for record in qe_records if record["tier_id"] != "-"]

    # Group by line_item_id and calculate the average tier_id for each group
    grouped_records = defaultdict(list)
    for record in filtered_records:
        grouped_records[record["line_item_id"]].append(int(record["tier_id"]))

    average_tier_id = {}
    for line_item_id, tier_ids in grouped_records.items():
        avg_tier_id = mean(tier_ids)
        average_tier_id[line_item_id] = avg_tier_id

    # Sort the original qe_records based on the computed average tier_id
    sorted_records = sorted(
        filtered_records,
        key=lambda x: (
            average_tier_id.get(x["line_item_id"], float("inf")),
            int(x["tier_id"]),
        ),
    )
    logger.info("Sorted quota erosion records")
    return sorted_records
