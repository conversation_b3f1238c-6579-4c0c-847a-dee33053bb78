import datetime
import logging

from django.utils.timezone import make_aware
from snowflake.snowpark.types import StringType, StructField, StructType, TimestampType

import interstage_project.utils as iputils
from commission_engine.accessors.client_accessor import (
    can_run_sf_payout_snapshot,
    get_client_features,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    PayoutSnapshotETLStatusAccessor,
    PayoutSnapshotETLStatusReaderAccessor,
)
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.database.snowflake_query_utils import (
    snowflake_run_sql,
    snowflake_writer_handler,
)
from commission_engine.snowflake_accessors.utils import escape_single_quotes
from commission_engine.utils import CommissionChangesTypes, PlanModificationTypes
from commission_engine.utils.general_data import SYNC_OBJECT
from commission_engine.utils.snowflake_commission_utils import (
    get_inter_comm_snapshot_table_name_sf,
    get_inter_commission_table_name_sf,
    get_inter_quota_erosion_table_name_sf,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context
from spm.accessors.commission_plan_accessor import (
    CommissionChangesAccessor,
    PlanModificationChangesAccessor,
)

module_logger = logging.getLogger(__name__)


class InterCommissionSnapshotSync:
    """
    1. Use the same logic in PayoutSnapshotV3 to compute the locked and unlocked payees for sync

    The change would be in how we run the sync process.
    """

    def __init__(
        self,
        client_id: int,
        all_payees_ped_map: dict,
        sec_kd: datetime.datetime,
        etl_params: dict,
        unlocked_objs=None,
        only_current_period=False,
    ):
        self.client_id = client_id
        self.secondary_kd = sec_kd
        self.all_payees_ped_map = all_payees_ped_map
        self.etl_params = etl_params if etl_params is not None else {}
        self.unlocked_comm_objs = unlocked_objs
        self.only_current_period = only_current_period
        log_context = {
            "client_id": client_id,
            "secondary_kd": sec_kd,
            "e2e_sync_run_id": self.etl_params.get("e2e_sync_run_id"),
            "sync_run_id": self.etl_params.get("sync_run_id"),
        }
        self.current_data = self.get_sync_changes_for_snapshot()
        self.logger = iputils.LogWithContext(log_context)
        (
            self.locked_payee_psd_ped_map,
            self.unlocked_payee_psd_ped_map,
        ) = self.get_payee_psd_ped_map()

    def get_sync_changes_for_snapshot(self):
        """
        This method is used to get the sync changes for the snapshot.
        """
        last_sucessful_sync = PayoutSnapshotETLStatusReaderAccessor(
            self.client_id
        ).get_latest_sucessful_sync(SYNC_OBJECT.PAYOUT_SNAPSHOT_INTER_COMM_SYNC.value)
        if not last_sucessful_sync:
            last_sucessful_sync = make_aware(datetime.datetime(2021, 1, 1))
        commission_changes_aggregated = CommissionChangesAccessor(
            self.client_id
        ).get_changes_by_types_after_last_sync(
            last_sucessful_sync,
            [CommissionChangesTypes.SYNC.value, CommissionChangesTypes.UNFREEZE.value],
        )

        # While un-freeze the snapshot-sync starts before the transaction is committed on the web.
        # Therefore we won't get the unfreezed payees in the commission changes.
        # We need to include them from self.unlocked_comm_objs
        # Even when the transaction is complete, adding it again here won't cause any issues.
        if self.only_current_period and self.unlocked_comm_objs is not None:
            for unlocked_obj in self.unlocked_comm_objs:
                commission_changes_aggregated.append(
                    {
                        "payee_email_id": unlocked_obj.payee_email_id,
                        "period_end_date": unlocked_obj.period_end_date,
                    }
                )

        commission_changes_map = {}
        for comm_change in commission_changes_aggregated:
            payee = comm_change["payee_email_id"]
            if payee not in commission_changes_map:
                commission_changes_map[payee] = []
            if comm_change["period_end_date"] not in commission_changes_map[payee]:
                commission_changes_map[payee].append(comm_change["period_end_date"])
        return commission_changes_map

    def update_payees_in_snapshot_etl_status(self, unlocked_payee_psd_ped_map: dict):
        """
        This method is used to update the payees in the snapshot etl status table.
        """
        if self.etl_params is not None:
            e2e_sync_run_id = self.etl_params.get("e2e_sync_run_id")
            sync_run_id = self.etl_params.get("sync_run_id")

            ssa = PayoutSnapshotETLStatusAccessor(
                self.client_id, e2e_sync_run_id, sync_run_id
            )
            payee_dict = {}

            for payee in unlocked_payee_psd_ped_map.keys():
                peds_list = [
                    ped.strftime("%Y-%m-%d")
                    for ped in unlocked_payee_psd_ped_map[payee]
                ]
                payee_dict[payee] = ",".join(peds_list)

            ssa.update_payees(payee_dict)

    def create_dataframe_with_payee_peds(self, snowpark_session, records):
        """
        We are creating a dataframe of payess and peds for which the sync is running.
        This records contsins only the unlocked payees and peds.

        We don't fetch payee peds from CommissionChanges table because
        It also includes loced payee peds of cureent period.
        For snapshot sync we need only the unlocked payees and peds.
        """

        self.logger.info("BEGIN: create_dataframe_with_payee_peds")
        payee_ped_df_schema = StructType(
            [
                StructField("payee_email_id", StringType()),
                StructField("period_end_date", TimestampType()),
            ]
        )
        payee_ped_records = []
        for payee, peds in records.items():
            for ped in peds:
                payee_ped_records.append((payee, ped))
        df = snowpark_session.create_dataframe(
            payee_ped_records, schema=payee_ped_df_schema
        )
        table_name = f"temp_payee_ped_icss_{self.client_id}"
        df.write.save_as_table(
            table_name=table_name, table_type="temporary"
        )  # table_type="temporary"
        self.logger.info("END: create_dataframe_with_payee_peds")
        return table_name

    def handle_deletes_inter_commission_snapshot(
        self, snowpark_session, payee_ped_table: str
    ):
        """
        1. Delete the records from inter_commission_snapshot_data_table where
        (PAYEE_EMAIL_ID, PERIOD_END_DATE) IN payee_ped_table (current and prev period payees)
        2. Delete records matching plan_modification_changes
        """

        self.logger.info("BEGIN: handle_deletes_inter_commission_snapshot")
        inter_commission_snapshot_data_table = get_inter_comm_snapshot_table_name_sf(
            self.client_id
        )

        delete_query = f"""
            DELETE FROM {inter_commission_snapshot_data_table}
            WHERE (PAYEE_EMAIL_ID, to_date(PERIOD_END_DATE)) IN (
                SELECT PAYEE_EMAIL_ID, to_date(PERIOD_END_DATE)
                FROM {payee_ped_table}
            );
            """
        snowflake_run_sql(
            snowpark_session=snowpark_session,
            query=delete_query,
            client_id=self.client_id,
        )
        delete_commission_records_frm_inter_commission_snapshot(
            self.client_id, snowpark_session
        )
        self.logger.info("END: handle_deletes_inter_commission_snapshot")

    def run_sync(self):
        """
        First create inter_commission_snapshot_data if it does not exist
        1. delete the existing data in inter_commission_snapshot_data before insert
        2. Use meta_table_payee_ped to delete records from inter_commission_snapshot_data

        Fetch commission and corresponding inter_quota_erosion for payees in meta_table_payee_ped
        1. Fetch commission_data with payee_email_id and period_end_date on meta_table_payee_ped
        2. Fetch commission_data with payee_email_id and period_end_date on meta_table_payee_ped
        3. Use object_construct() to create tier_id_split and inter_quota_erosion
        4. Use an arraay_agg() on the result to obtain the final result
        5. Insert the result into the inter_commission_snapshot_data table

        If u already have a inter_commission_snapshot_data table, execute the following queries:
        ALTER TABLE inter_commission_snapshot_data_<client_id> ADD COLUMN IF NOT EXISTS TIER_ID_SPLIT VARIANT NOT NULL DEFAULT '[]';
        ALTER TABLE inter_commission_snapshot_data_<client_id> ADD COLUMN IF NOT EXISTS QUOTA VARIANT NOT NULL DEFAULT '[]';

        """

        self.logger.info("BEGIN: InterCommSnapshot")
        client_features = get_client_features(self.client_id)
        can_run_sf_snapshot = can_run_sf_payout_snapshot(self.client_id)

        if (
            not can_run_sf_snapshot
            or not client_features.get("write_commission_to_snowflake", False)
            or not client_features.get("write_quota_erosion_to_snowflake", False)
        ):
            raise Exception("This SnapshotSync is only for flash ETL")
        with snowflake_writer_handler(client_id=self.client_id) as snowpark_session:
            meta_table_payee_ped = self.create_dataframe_with_payee_peds(
                snowpark_session, self.unlocked_payee_psd_ped_map
            )

            inter_commission_snapshot_data_table = (
                get_inter_comm_snapshot_table_name_sf(self.client_id)
            )
            inter_comm_sf_table = get_inter_commission_table_name_sf(self.client_id)
            inter_quota_erosion_snowflake_table = get_inter_quota_erosion_table_name_sf(
                self.client_id
            )

            interim_table_1_name = f"temp_comm_quota_join_{self.client_id}"

            fetch_result_data_query = f"""
                CREATE TEMP TABLE {interim_table_1_name} AS SELECT
                c.payee_email_id,
                c.period_start_date,
                c.period_end_date,
                c.commission_plan_id,
                c.criteria_id,
                c.line_item_id,
                c.knowledge_begin_date,
                c.amount,
                qe.quota_erosion,
                qe.qv,
                qe.cumulative_qe,
                qe.quota_category_name,
                c.tier_id,
                c.original_tier_id,
                c.additional_data,
                c.datasheet_data,
                OBJECT_CONSTRUCT_KEEP_NULL('tier_id', c.tier_id, 'original_tier_id', c.original_tier_id, 'commission', c.amount, 'quota_erosion', qe.quota_erosion, 'tier_name', c.additional_data:tier_name, 'amount_payee_currency', c.additional_data:amount_payee_currency) as tier_id_split
            FROM (SELECT
                payee_email_id,
                period_start_date,
                period_end_date,
                commission_plan_id,
                criteria_id,
                line_item_id,
                knowledge_begin_date,
                amount,
                tier_id,
                original_tier_id,
                object_insert(
                    object_insert(additional_data, 'comm_secondary_kd', secondary_kd, true), 
                    'commission_date', commission_date, 
                    true
                ) AS additional_data,
                datasheet_data  FROM {inter_comm_sf_table} where knowledge_end_date IS NULL AND (is_deleted = FALSE OR is_deleted IS NULL) AND (payee_email_id, to_date(period_end_date)) IN
                (
                SELECT
                    payee_email_id,
                    to_date(period_end_date)
                FROM
                    {meta_table_payee_ped}
                    ) ) c        
            LEFT JOIN (
                SELECT
                    *
                FROM
                    {inter_quota_erosion_snowflake_table}
                WHERE
                    knowledge_end_date IS NULL
                    AND (is_deleted = FALSE OR is_deleted IS NULL)
                    AND (payee_email_id, to_date(period_end_date)) IN
                    (
                    SELECT
                        payee_email_id,
                        to_date(period_end_date)
                    FROM
                        {meta_table_payee_ped}
                        )
                    ) qe 
            ON
                qe.payee_email_id = c.payee_email_id   
                AND qe.period_start_date = c.period_start_date             
                AND qe.period_end_date = c.period_end_date
                AND qe.commission_plan_id = c.commission_plan_id
                AND qe.criteria_id = c.criteria_id
                AND (qe.line_item_id = c.line_item_id
                    OR (qe.line_item_id IS NULL
                        AND c.line_item_id IS NULL))
                AND qe.tier_id = c.tier_id
                AND (qe.original_tier_id = c.original_tier_id
                    OR (qe.original_tier_id IS NULL
                        AND c.original_tier_id IS NULL));"""

            aggregate_and_insert_query = f"""
                INSERT INTO {inter_commission_snapshot_data_table} (line_item_id, criteria_id, payee_email_id, period_start_date, period_end_date, plan_id, commission, quota, tier_id_split, snapshot_date, additional_data, datasheet_data)
            Select
				line_item_id,
                criteria_id,
                payee_email_id,
                period_start_date,
                period_end_date,
                commission_plan_id,
                sum(amount),
                OBJECT_CONSTRUCT_KEEP_NULL('quota_erosion', sum(quota_erosion), 'qv', ANY_VALUE(qv), 'cumulative_qe', ANY_VALUE(cumulative_qe), 'quota_category_name', ANY_VALUE(quota_category_name)),
                ARRAY_AGG(tier_id_split),
                knowledge_begin_date,
                ANY_VALUE(additional_data),
                ANY_VALUE(datasheet_data)
            FROM
                {interim_table_1_name}
            GROUP BY
                (line_item_id,
                criteria_id,
                payee_email_id,
                period_start_date,
                period_end_date,
                commission_plan_id,
                knowledge_begin_date
                );
            """

            self.handle_deletes_inter_commission_snapshot(
                snowpark_session, meta_table_payee_ped
            )
            snowflake_run_sql(
                snowpark_session=snowpark_session,
                query=fetch_result_data_query,
                client_id=self.client_id,
            )
            snowflake_run_sql(
                snowpark_session=snowpark_session,
                query=aggregate_and_insert_query,
                client_id=self.client_id,
            )
            self.logger.info("END: InterCommSnapshot")

    def get_locked_kd_map(self) -> dict:
        """
        This method is used to get the locked kd map for the given payee list and period.
        this returna a list of dict with payee email as key and peds as value.
        Here the list of peds are the peds where the commissions are loacked for the payee.
        Eg:
            {
                '<EMAIL>': ['2024-05-01 05:29:59.999 +0530', 2024-04-01 05:29:59.999 +0530],
                '<EMAIL>': ['2024-07-01 05:29:59.999 +0530', 2024-04-01 05:29:59.999 +0530], .....
            }
        """
        self.logger.info("BEGIN: Get locked kd map")

        payees_comm_locked_peds = CommissionLockAccessor(
            self.client_id
        ).get_locked_keys_for_payee_in_periods(self.current_data)
        payees_comm_locked_peds_map = {}
        for payee_peds in payees_comm_locked_peds:
            payees_comm_locked_peds_map[payee_peds["payee_email_id"]] = payee_peds[
                "period_end_dates"
            ]
        self.logger.info("END: Get locked kd map")
        return payees_comm_locked_peds_map

    def get_comm_locked_ped_from_unlocked_objs(self):
        """
        This method is used to get the comm ped from the unlocked objs.

        While unlocking the db operations takes place as a transaction.
        In the transaction, the unlocked objs are inserted into the commission lock table.

        unlocked objs are the objects of CommissionLock which are inserted while unlocking.
        current_data is the dict of payees as keys and list of peds as values.

        We filter the payees from the current_data which are not present in the unlocked objs.
        That gives us the payees for which the commission is locked.
        """
        payees_comm_unlocked_peds = {}
        if self.unlocked_comm_objs is not None:
            for unlocked_obj in self.unlocked_comm_objs:
                payees_comm_unlocked_peds[unlocked_obj.payee_email_id] = [
                    unlocked_obj.period_end_date
                ]
        payees_comm_locked_peds = {}
        for payee, peds in self.current_data.items():
            if payee not in payees_comm_unlocked_peds:
                payees_comm_locked_peds[payee] = peds
        return payees_comm_locked_peds

    def get_payee_psd_ped_map(self) -> tuple:
        """
        This method will return the payee psd ped map for locked and unlocked payees.
        """
        if self.only_current_period and self.unlocked_comm_objs is not None:
            # We need not check locked_payees for un-freeze mode.
            payees_comm_locked_peds = {}
        else:
            payees_comm_locked_peds = self.get_locked_kd_map()
        self.logger.info(
            "Commission is locked for {}".format(str(payees_comm_locked_peds))
        )
        unlocked_payees_peds = {}
        for payee, peds in self.current_data.items():
            if payee not in payees_comm_locked_peds:
                unlocked_payees_peds[payee] = peds
            else:
                locked_peds = payees_comm_locked_peds[payee]
                if len(locked_peds) < len(peds):
                    unlocked_peds = list(set(peds) - set(locked_peds))
                    unlocked_payees_peds[payee] = unlocked_peds
        self.logger.info(
            "Commission is unlocked for  {}".format(str(unlocked_payees_peds))
        )

        self.update_payees_in_snapshot_etl_status(unlocked_payees_peds)

        return payees_comm_locked_peds, unlocked_payees_peds


def delete_commission_records_frm_inter_commission_snapshot(
    client_id: int,
    snowpark_session,
    log_context=None,
):
    """
    This is just a Skeleton, The col names and table names are yet to be decided
    This function is behind a feature flag.

    1. Fetch type=PlanModificationTypes.PLAN_DELETE.value from PlanModificationChanges and delete by plan_id
    2. Fetch type=PlanModificationTypes.REMOVE_PAYEE.value from PlanModificationChanges and delete by plan_id and payee_email_id
    3. fetch type=PlanModificationTypes.EDIT_PERIOD.value from PlanModificationChanges and delete by plan_id and payee_email_id and ped
    """
    set_threadlocal_context(log_context)
    module_logger.info("BEGIN: delete_commission_records_frm_inter_commission_snapshot")

    last_sucessful_sync = PayoutSnapshotETLStatusReaderAccessor(
        client_id
    ).get_latest_sucessful_sync(SYNC_OBJECT.PAYOUT_SNAPSHOT_INTER_COMM_SYNC.value)
    if not last_sucessful_sync:
        last_sucessful_sync = make_aware(datetime.datetime(2021, 1, 1))
    inter_comm_snapshot_sf = get_inter_comm_snapshot_table_name_sf(client_id)
    plan_deletes = PlanModificationChangesAccessor(
        client_id
    ).get_plan_changes_by_last_sync_and_type(
        last_sucessful_sync, PlanModificationTypes.PLAN_DELETE
    )

    if plan_deletes:
        temp_table_name = get_dataframe_for_del_type(
            client_id,
            snowpark_session,
            plan_deletes,
            PlanModificationTypes.PLAN_DELETE,
        )
        where_clause = f"PLAN_ID IN (SELECT PLAN_ID_STR FROM {temp_table_name})"
        delete_query = f"DELETE FROM {inter_comm_snapshot_sf} WHERE {where_clause}"

        snowflake_run_sql(
            snowpark_session=snowpark_session,
            query=delete_query,
            client_id=client_id,
        )
    payee_removals = PlanModificationChangesAccessor(
        client_id
    ).get_plan_changes_by_last_sync_and_type(
        last_sucessful_sync, PlanModificationTypes.REMOVE_PAYEE
    )

    if payee_removals:
        temp_table_name = get_dataframe_for_del_type(
            client_id,
            snowpark_session,
            payee_removals,
            PlanModificationTypes.REMOVE_PAYEE,
        )
        where_clause = f"(PLAN_ID, PAYEE_EMAIL_ID) IN (SELECT PLAN_ID_STR, PAYEE_EMAIL_ID FROM {temp_table_name})"
        delete_query = f"DELETE FROM {inter_comm_snapshot_sf} WHERE {where_clause}"

        snowflake_run_sql(
            snowpark_session=snowpark_session,
            query=delete_query,
            client_id=client_id,
        )
    ped_changes = PlanModificationChangesAccessor(
        client_id
    ).get_plan_changes_by_last_sync_and_type(
        last_sucessful_sync, PlanModificationTypes.EDIT_PERIOD
    )

    if ped_changes:
        temp_table_name = get_dataframe_for_del_type(
            client_id,
            snowpark_session,
            ped_changes,
            PlanModificationTypes.EDIT_PERIOD,
        )
        where_clause = f"(PLAN_ID, PAYEE_EMAIL_ID, to_date(PERIOD_END_DATE)) IN (SELECT PLAN_ID_STR, PAYEE_EMAIL_ID, to_date(PERIOD_END_DATE) FROM {temp_table_name})"
        delete_query = f"DELETE FROM {inter_comm_snapshot_sf} WHERE {where_clause}"

        snowflake_run_sql(
            snowpark_session=snowpark_session,
            query=delete_query,
            client_id=client_id,
        )
    module_logger.info("END: delete_commission_records_frm_inter_commission_snapshot")


def get_dataframe_for_del_type(
    client_id: int,
    snowpark_session,
    records: list,
    del_type: PlanModificationTypes,
) -> str:
    temp_table_schema = ""
    payee_ped_plan_id_schema = StructType(
        [
            StructField("plan_id_str", StringType()),
            StructField("payee_email_id", StringType()),
            StructField("period_end_date", TimestampType()),
        ]
    )
    payee_plan_id_schema = StructType(
        [
            StructField("plan_id_str", StringType()),
            StructField("payee_email_id", StringType()),
        ]
    )
    plan_id_schema = StructType(
        [
            StructField("plan_id_str", StringType()),
        ]
    )
    if del_type == PlanModificationTypes.PLAN_DELETE:
        temp_table_schema = plan_id_schema
    elif del_type == PlanModificationTypes.REMOVE_PAYEE:
        temp_table_schema = payee_plan_id_schema
    else:  # del_type == PlanModificationTypes.EDIT_PERIOD.value
        temp_table_schema = payee_ped_plan_id_schema

    df = snowpark_session.create_dataframe(records, schema=temp_table_schema)
    table_name = f"temp_payee_ped_{del_type.value}_{client_id}"
    df.write.save_as_table(table_name=table_name, table_type="temporary")
    return table_name


def get_inter_comm_snapshot_locked_kd_query(
    client_id: int, filters: dict, locked_kd: datetime.datetime
):
    """
    This method is used to get the update query for the given filters.
    The Inputs:
        1. filters: dict
            - The dict where 'payees' is the list of payees and 'ped' is the period end date.
        2. table_name: str
            - The table name payout_snapshot_table.
    Eg:

    UPDATE payout_snapshot_data_3
    SET
        IS_LOCKED = TRUE,
        LOCKED_DATE = locked_date
    WHERE (payee_email_id = '<EMAIL>' AND period_end_date::DATETIME = '2023-12-31 23:59:59.999999+00:00'::DATETIME)
            OR (payee_email_id = '<EMAIL>' AND period_end_date::DATETIME = '2023-12-31 23:59:59.999999+00:00'::DATETIME);

    Note: We are using CASE WHEN because we need to update the `locked_date` for each payee, psd, ped combination.
    """

    # Can usesnowflake utils to construct the query
    if filters.get("payees", []):
        single_quote_escaped_values = [
            escape_single_quotes(val) for val in filters["payees"]
        ]
        values_str = "'" + "','".join(single_quote_escaped_values) + "'"
        where_conditions = f"payee_email_id in ({values_str}) AND period_end_date::DATETIME = '{filters['ped']}'::DATETIME"
    else:
        return None
    # where_clause = " OR ".join(where_conditions)

    table_name = get_inter_comm_snapshot_table_name_sf(client_id)

    update_query = f"""UPDATE {table_name} SET IS_LOCKED = TRUE, LOCKED_DATE = '{locked_kd}' WHERE {where_conditions};"""
    return update_query


def update_inter_comm_snapshot_locked_kd(
    client_id: int, filters: dict, locked_kd: datetime.datetime
):
    """
    This method is used to update the inter_comm_snapshot table with the locked kd.
    Currently this runs only when 'can_run_sf_snapshot' is True.
    """
    try:
        module_logger.info("Begin: Update inter_comm_snapshot table with locked kd")
        update_query = get_inter_comm_snapshot_locked_kd_query(
            client_id, filters, locked_kd
        )

        with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
            if update_query:
                snowpark_session.sql(update_query).show()

        module_logger.info(
            "End: Update inter_comm_snapshot table with locked kd for %s payees",
            len(filters.get("payees", [])),
        )
    except Exception:  # pylint: disable=broad-except
        module_logger.exception(
            "Error while updating inter_comm_snapshot table with locked kd"
        )


def get_queries_to_update_inter_comm_snapshot_on_payout(
    client_id: int,
    filters: dict,
    payout_kd: datetime.datetime,
    locked_kd: datetime.datetime,
):
    """
    This method is used to get the update query for the given filters when payout is made.
    The Inputs:
        1. filters: dict
            - The dict where 'payees' is the list of payees and 'ped' is the period end date.
        2. payout_kd
        3. locked_kd
    Eg:

    UPDATE inter_comm_snapshot_data_<client_id>
    SET
        IS_LOCKED = TRUE,
        LOCKED_DATE = locked_kd
    WHERE (payee_email_id = '<EMAIL>' AND period_end_date::DATETIME = '2023-12-31 23:59:59.999999+00:00'::DATETIME)
            OR (payee_email_id = '<EMAIL>' AND period_end_date::DATETIME = '2023-12-31 23:59:59.999999+00:00'::DATETIME);
    """

    if filters.get("payees", []):
        single_quote_escaped_values = [
            escape_single_quotes(val) for val in filters["payees"]
        ]
        values_str = "'" + "','".join(single_quote_escaped_values) + "'"
        where_conditions = f"payee_email_id in ({values_str}) AND period_end_date::DATETIME = '{filters['ped']}'::DATETIME"
    else:
        return []

    table_name = get_inter_comm_snapshot_table_name_sf(client_id)

    update_locked_kd = f"""UPDATE {table_name} SET IS_LOCKED = TRUE, LOCKED_DATE = '{locked_kd}' WHERE {where_conditions} AND IS_LOCKED = FALSE;"""
    update_payout_kd = f"""UPDATE {table_name} SET ADDITIONAL_DATA = OBJECT_INSERT(OBJECT_INSERT(ADDITIONAL_DATA, 'is_paid', true, true), 'payout_kd', '{payout_kd}', true) WHERE {where_conditions};"""
    return [update_locked_kd, update_payout_kd]


def update_inter_comm_snapshot_on_payout(
    client_id: int,
    filters: dict,
    payout_kd: datetime.datetime,
    locked_kd: datetime.datetime,
):
    """
    This method is used to update the inter_comm_snapshot table with the payout kd.
    In bulk csv upload register payments, we need to update the inter_comm_snapshot table with the payout kd that can be different from the locked kd.
    locked kd is current time when it is locked, payout kd is the date they pick to pay.
    """
    try:
        module_logger.info("Begin: Update inter_comm_snapshot table with payout kd")
        update_query = get_queries_to_update_inter_comm_snapshot_on_payout(
            client_id, filters, payout_kd, locked_kd
        )

        module_logger.info(
            "Update query for inter_comm_snapshot table: %s", update_query
        )
        with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
            for query in update_query:
                snowpark_session.sql(query).show()

        module_logger.info(
            "End: Update inter_comm_snapshot table with locked kd for %s payees",
            len(filters.get("payees", [])),
        )
    except Exception:  # pylint: disable=broad-except
        module_logger.exception(
            "Error while updating inter_comm_snapshot table with locked kd"
        )
