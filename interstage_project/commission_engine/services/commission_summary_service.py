# pylint: disable=unsubscriptable-object
import json
import logging
import traceback
from collections import defaultdict
from copy import deepcopy
from datetime import datetime
from decimal import Decimal

import pandas as pd
import pydash
from django.db import connection
from django.utils import timezone
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import get_client
from commission_engine.accessors.commission_accessor import CommissionAccessor
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.accessors.payout_status_accessor import PayoutStatusAccessor
from commission_engine.accessors.report_object_accessor import (
    FrozenPayrollDataAccessor,
    latest_knowledge_date_for_report_objects,
)
from commission_engine.database.snowflake_query_utils import (
    snowflake_insert_records,
    snowflake_run_sql,
    snowflake_writer_handler,
)
from commission_engine.models.report_object_models import FrozenPayrollData
from commission_engine.snowflake_accessors.report_object_data_accessor import (
    does_record_exist_by_object_id_payee_id,
    get_data_by_object_id_year_plan_id_payee_id,
)
from commission_engine.utils import (
    date,
    end_of_day,
    get_fiscal_year,
    make_aware,
    make_aware_wrapper,
    start_of_day,
)
from commission_engine.utils.date_utils import first_day_of_month
from commission_engine.utils.fx_utils import (
    change_to_base_currency,
    change_to_payee_currency_by_fx_rate,
)
from commission_engine.utils.general_data import CURRENCY_TYPE
from commission_engine.utils.report_utils import get_report_object_data_table_name
from interstage_project.global_utils.localization_utils import (
    get_localized_message_utils,
)
from interstage_project.utils import LogWithContext
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanPayeeAccessor,
)
from spm.accessors.config_accessors.client_config_accessor import ClientConfigAccessor
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.accessors.draws_accessor import DrawsAccessor
from spm.accessors.manual_adjustment_accessor import DrawAdjustmentAccessor
from spm.accessors.payout_accessor import PayoutAccessor
from spm.services.dashboard_services.commission_services import (
    calculate_qtd_values,
    get_draw_period,
    get_draws_awarded,
    get_draws_recovered,
    get_payee_payroll_date,
)
from spm.services.dashboard_services.dashboard_services import (
    get_end_date_of_month,
    get_end_dates_list,
)
from spm.services.manual_adjustment_services.commission_adjustment_services import (
    commission_adjustments_for_payee_and_period,
)
from spm.services.query_builder.commissions import CommissionQueryBuilder


def find_element_in_list(element, list_element):
    try:
        return list_element.index(element)
    except ValueError:
        return None


def get_final_draw_amount(
    client_id,
    payee_email_id,
    period_end_date,
    list_of_draws,
    fiscal_year_we_are_calculating,
    total_commission,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_base_currency,
    payee_currency,
    client_start_month,
    month_end_dates_for_year,
):
    index = find_element_in_list(period_end_date, month_end_dates_for_year)
    draws_awarded = get_draws_awarded(
        {"draws": list_of_draws},
        index,
        total_commission,
        client_start_month,
        client_base_currency,
        payee_currency,
        client_id,
        period_end_date,
    )
    freq_len = len(list_of_draws)
    draw_period = get_draw_period(index, freq_len, client_start_month)
    draw_adjustments = (
        draw_adj_payee_id_fiscal_year_period_to_object_map[
            (payee_email_id, fiscal_year_we_are_calculating, draw_period)
        ]
        if (payee_email_id, fiscal_year_we_are_calculating, draw_period)
        in draw_adj_payee_id_fiscal_year_period_to_object_map
        else 0
    )
    draws_recovered = 0
    if draw_adjustments:
        draws_recovered = get_draws_recovered(
            index,
            draw_adjustments.amount,
            freq_len,
        )
    if client_base_currency != payee_currency:
        draws_recovered = change_to_base_currency(
            client_id,
            draws_recovered,
            payee_currency,
            period_end_date,
        )
    return draws_awarded - draws_recovered


def comm_adjustment_create_plan_email_psd_ped_to_amount_map(
    commission_adjustments_for_year,
):
    plan_email_psd_ped_to_amount = {}
    for commission_object in commission_adjustments_for_year:
        start_date_of_adjustment = commission_object[
            "period_start_date"
        ]  # 1st feb 2021
        end_date_of_adjustment = commission_object["period_end_date"]  # 31st dec 2022
        plan_email_psd_ped_tuple = (
            str(commission_object["plan_id"]),
            commission_object["payee_id"],
            start_date_of_adjustment,
            end_date_of_adjustment,
        )
        if plan_email_psd_ped_tuple not in plan_email_psd_ped_to_amount:
            plan_email_psd_ped_to_amount[plan_email_psd_ped_tuple] = 0
        plan_email_psd_ped_to_amount[plan_email_psd_ped_tuple] += commission_object[
            "amount"
        ]
    return plan_email_psd_ped_to_amount


def comm_lock_create_plan_email_psd_ped_to_locked_date_map(lock_objects):
    plan_email_psd_ped_to_locked_date_map = {}
    for comm_lock_object in lock_objects:
        if comm_lock_object["commission_plan_ids"]:
            for plan_id in comm_lock_object["commission_plan_ids"]:
                plan_email_psd_ped_key = (
                    str(plan_id),
                    comm_lock_object["payee_email_id"],
                    comm_lock_object["period_start_date"],
                    comm_lock_object["period_end_date"],
                )
                plan_email_psd_ped_to_locked_date_map[plan_email_psd_ped_key] = (
                    comm_lock_object["locked_knowledge_date"]
                )
    return plan_email_psd_ped_to_locked_date_map


def construct_comm_lock_email_ped_to_locked_date_map(
    plan_email_psd_ped_to_locked_date_map,
):
    comm_lock_email_ped_to_locked_date_map = {}
    for key, value in plan_email_psd_ped_to_locked_date_map.items():
        comm_lock_email_ped_to_locked_date_map[(key[1], key[3])] = value
    return comm_lock_email_ped_to_locked_date_map


def format_time(t):
    return t.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]


def comm_create_plan_email_psd_ped_to_amount_map(commission_entries, payees_set):
    plan_email_psd_ped_to_amount_map = {}
    for entry in commission_entries:
        comm_entry_payee_email = entry.payee_email_id
        if comm_entry_payee_email in payees_set:
            comm_entry_plan_id = str(entry.commission_plan_id)
            comm_entry_psd = entry.period_start_date
            comm_entry_ped = entry.period_end_date
            comm_entry_amount = entry.amount
            entry_key = (
                comm_entry_plan_id,
                comm_entry_payee_email,
                comm_entry_psd,
                comm_entry_ped,
            )
            # psd_ped_set.add((entry.period_start_date, entry.period_end_date))
            plan_email_psd_ped_to_amount_map[entry_key] = (
                plan_email_psd_ped_to_amount_map.get(entry_key, 0) + comm_entry_amount
            )
    return plan_email_psd_ped_to_amount_map


def comm_create_plan_email_psd_ped_to_secondary_kd_map(commission_entries, payees_set):
    plan_email_psd_ped_to_secondary_kd_map = {}
    for entry in commission_entries:
        comm_entry_payee_email = entry.payee_email_id
        if comm_entry_payee_email in payees_set:
            comm_entry_plan_id = str(entry.commission_plan_id)
            comm_entry_psd = entry.period_start_date
            comm_entry_ped = entry.period_end_date
            comm_entry_secondary_kd = entry.secondary_kd
            entry_key = (
                comm_entry_plan_id,
                comm_entry_payee_email,
                comm_entry_psd,
                comm_entry_ped,
            )
            # psd_ped_set.add((entry.period_start_date, entry.period_end_date))
            if entry_key not in plan_email_psd_ped_to_secondary_kd_map:
                plan_email_psd_ped_to_secondary_kd_map[entry_key] = (
                    comm_entry_secondary_kd
                )
    return plan_email_psd_ped_to_secondary_kd_map


def comm_create_plan_email_psd_ped_to_amount_and_secondary_kd_map(commission_entries):
    plan_email_psd_ped_to_amount_map = {}
    plan_email_psd_ped_to_secondary_kd_map = {}
    for entry in commission_entries:
        comm_entry_plan_id = str(entry["commission_plan_id"])
        comm_entry_payee_email = entry["payee_email_id"]
        comm_entry_psd = entry["period_start_date"]
        comm_entry_ped = entry["period_end_date"]
        entry_key = (
            comm_entry_plan_id,
            comm_entry_payee_email,
            comm_entry_psd,
            comm_entry_ped,
        )
        plan_email_psd_ped_to_secondary_kd_map[entry_key] = entry["secondary_kd"]
        plan_email_psd_ped_to_amount_map[entry_key] = entry["total_amount"]
    return (plan_email_psd_ped_to_amount_map, plan_email_psd_ped_to_secondary_kd_map)


def fetch_commission_entries_for_locked_payees(
    client_id, start_date, end_date, locked_payee_map, logger=None
) -> list:
    # pylint: disable=pointless-string-statement
    logger = logger if logger else LogWithContext({"client_id": client_id})
    logger.info("Building query for locked payees.")
    """
    The SQL Query to build using Query Builder

    select
        "commission"."commission_plan_id",
        "commission"."payee_email_id",
        "commission"."period_start_date",
        "commission"."period_end_date",
        MAX("commission"."secondary_kd") "secondary_kd",
        SUM("commission"."amount") "total_amount"
    from
        "commission"
    join (
        select
            "payee_email_id",
            "period_start_date",
            "period_end_date",
            MAX("locked_knowledge_date") "max_locked_knowledge_date"
        from
            "commission_lock"
        where
            "client_id" = CLIENT_ID
            and not "is_deleted"
            and "knowledge_end_date" is null
            and "is_locked" = true
            and "period_start_date" >= START_DATE
            and "period_end_date" <= END_DATE
        group by
            "payee_email_id",
            "period_start_date",
            "period_end_date") "unique_comm_lock" on
        "commission"."client_id" = CLIENT_ID
        and not "commission"."is_deleted"
        and "commission"."knowledge_begin_date" <= "unique_comm_lock"."max_locked_knowledge_date"
        and ("commission"."knowledge_end_date">"unique_comm_lock"."max_locked_knowledge_date"
            or "commission"."knowledge_end_date" is null)
        and "commission"."payee_email_id" = "unique_comm_lock"."payee_email_id"
        and "commission"."period_start_date" = "unique_comm_lock"."period_start_date"
        and "commission"."period_end_date" = "unique_comm_lock"."period_end_date"
    where
        "commission"."client_id" = CLIENT_ID
    group by
        "commission"."commission_plan_id",
        "commission"."payee_email_id",
        "commission"."period_start_date",
        "commission"."period_end_date"
    """
    query = (
        CommissionQueryBuilder(client_id)
        .inner_join_with_commission_lock_for_locked_payees_v2(start_date, end_date)
        .group_by_plan_payee_period_secondary_kd_and_sum_amount()
    )
    sql_query = query.clone_deep().get_sql() + "/*commission_summary_locked_payees*/"
    logger.info(f"SQL Query for locked payees : {sql_query}")
    with connection.cursor() as cursor:
        cursor.execute(sql_query)
        result: list = cursor.fetchall()

    if result:
        result_df: pd.DataFrame = pd.DataFrame(result).rename(
            columns={
                0: "commission_plan_id",
                1: "payee_email_id",
                2: "period_start_date",
                3: "period_end_date",
                4: "secondary_kd",
                5: "total_amount",
            }
        )
        """
        The plan period for some payee has been changed after his commission has been locked.
        Say the period was initially the entire year (Jan-Dec). And the commissions for the
        period of July was locked, later the period was changed to Jan - Jun. Joining will
        include the records for July, though he's not in the plan for July as of now. To
        avoid those records, we're filtering it out using locked_payee_map.
        """

        df_for_all_periods = [
            result_df[
                (result_df["period_end_date"] == period)
                & (result_df["payee_email_id"].isin(locked_payee_map[period]))
            ]
            for period in locked_payee_map
        ]
        # Concat only if there is at least one item in the list
        if df_for_all_periods:
            result = pd.concat(df_for_all_periods).to_dict("records")

    logger.info(f"Executed and fetched {len(result)} records for locked payees.")
    return result


def fetch_commission_entries_for_not_locked_payees(
    client_id, start_date, end_date, not_locked_not_paid_map, logger=None
) -> list:
    # pylint: disable=pointless-string-statement
    logger = logger if logger else LogWithContext({"client_id": client_id})
    logger.info("Building query for not locked payees")
    """
    select
        "commission"."commission_plan_id",
        "commission"."payee_email_id",
        "commission"."period_start_date",
        "commission"."period_end_date",
        MAX("commission"."secondary_kd") "secondary_kd",
        SUM("commission"."amount") "total_amount"
    from
        "commission"
    left join (
        select
            "payee_email_id",
            "period_start_date",
            "period_end_date",
            MAX("locked_knowledge_date") "max_locked_knowledge_date"
        from
            "commission_lock"
        where
            "client_id" = CLIENT_ID
            and not "is_deleted"
            and "knowledge_end_date" is null
            and "is_locked" = true
        group by
            "payee_email_id",
            "period_start_date",
            "period_end_date") "unique_comm_lock" on
        "commission"."client_id" = CLIENT_ID
        and "commission"."payee_email_id" = "unique_comm_lock"."payee_email_id"
        and "commission"."period_start_date" = "unique_comm_lock"."period_start_date"
        and "commission"."period_end_date" = "unique_comm_lock"."period_end_date"
    where
        "commission"."client_id" = CLIENT_ID
        and not "commission"."is_deleted"
        and "commission"."knowledge_end_date" is null
        and "commission"."period_start_date" >= START_DATE
        and "commission"."period_end_date" <= END_DATE
        and "unique_comm_lock"."payee_email_id" is null
    group by
        "commission"."commission_plan_id",
        "commission"."payee_email_id",
        "commission"."period_start_date",
        "commission"."period_end_date"
    """
    query = (
        CommissionQueryBuilder(client_id)
        .inner_join_with_payout_status_for_unlocked_payees(start_date, end_date)
        .group_by_plan_payee_period_secondary_kd_and_sum_amount()
    )
    sql_query = query.clone_deep().get_sql()
    logger.info(f"SQL Query for not locked payees : {sql_query}")
    with connection.cursor() as cursor:
        cursor.execute(sql_query)
        result: list = cursor.fetchall()

    if result:
        result_df: pd.DataFrame = pd.DataFrame(result).rename(
            columns={
                0: "commission_plan_id",
                1: "payee_email_id",
                2: "period_start_date",
                3: "period_end_date",
                4: "secondary_kd",
                5: "total_amount",
            }
        )
        df_for_all_periods = [
            result_df[
                (result_df["period_end_date"] == period)
                & (result_df["payee_email_id"].isin(not_locked_not_paid_map[period]))
            ]
            for period in not_locked_not_paid_map
        ]
        # Concat only if there is at least one item in the list
        if df_for_all_periods:
            result = pd.concat(df_for_all_periods).to_dict("records")
    logger.info(f"Executed and fetched {len(result)} records for not locked payees")
    return result


def construct_comm_amount_at_payee_period_level(
    plan_email_psd_ped_to_amount_map: dict,
) -> defaultdict:
    email_psd_ped_to_amount_map = defaultdict(int)
    for (
        plan_email_psd_ped_key,
        amount_value,
    ) in plan_email_psd_ped_to_amount_map.items():
        _, email_id, psd, ped = plan_email_psd_ped_key
        email_psd_ped_key = (email_id, psd, ped)
        email_psd_ped_to_amount_map[email_psd_ped_key] += amount_value

    return email_psd_ped_to_amount_map


def construct_comm_adj_amount_at_payee_period_level(
    comm_adjustment_plan_email_psd_ped_to_amount_map: dict,
) -> defaultdict:
    email_psd_ped_to_comm_adj_amount_map = defaultdict(int)
    for (
        plan_email_psd_ped_key,
        amount_value,
    ) in comm_adjustment_plan_email_psd_ped_to_amount_map.items():
        _, email_id, psd, ped = plan_email_psd_ped_key
        email_psd_ped_key = (email_id, psd, ped)
        email_psd_ped_to_comm_adj_amount_map[email_psd_ped_key] += amount_value
    return email_psd_ped_to_comm_adj_amount_map


def create_summary_objects_for_a_period(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    commission_entries,
    payees_set,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    payout_payee_psd_ped_to_object_map,
    comm_lock_plan_email_psd_ped_to_locked_date_map,
    client_base_currency,
    email_to_draw_list_map,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_start_month,
    month_end_dates_for_year,
    logger=None,
):
    current_period_commission_summary_objects = []
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "current_time": current_time})
    )
    logger.info(
        f"Creating summary objects - commission entries: {len(commission_entries)}"
    )
    plan_email_psd_ped_to_amount_map = comm_create_plan_email_psd_ped_to_amount_map(
        commission_entries, payees_set
    )
    plan_email_psd_ped_to_secondary_kd_map = (
        comm_create_plan_email_psd_ped_to_secondary_kd_map(
            commission_entries, payees_set
        )
    )
    payee_kd_ed_to_payroll_object_map = {}
    email_period_to_draw_adjustment_map = {}
    email_period_to_commission_amount_map = construct_comm_amount_at_payee_period_level(
        plan_email_psd_ped_to_amount_map
    )
    email_period_to_commission_adjustment_amount_map = (
        construct_comm_adj_amount_at_payee_period_level(
            comm_adjustment_plan_email_psd_ped_to_amount_map
        )
    )
    none_plan_email_ped_psd_map = {}
    logger.info(
        f"Iterating over plan_email_ped_psd_tuple, commission_amount in map of length {len(plan_email_psd_ped_to_amount_map.items())}"
    )
    for (
        plan_email_ped_psd_tuple,
        commission_amount,
    ) in plan_email_psd_ped_to_amount_map.items():
        plan_id = plan_email_ped_psd_tuple[0]
        payee_email_id = plan_email_ped_psd_tuple[1]
        period_start_date = plan_email_ped_psd_tuple[2]
        period_end_date = plan_email_ped_psd_tuple[3]
        (
            commission_adjustment_amount,
            draw_adjustment_amount,
            is_paid,
            is_locked,
            payout_kd,
            locked_kd,
            payee_period_commission_amount,
            payee_period_comm_adj_amount,
        ) = (
            0,
            0,
            False,
            False,
            None,
            None,
            email_period_to_commission_amount_map[
                (payee_email_id, period_start_date, period_end_date)
            ],
            email_period_to_commission_adjustment_amount_map[
                (payee_email_id, period_start_date, period_end_date)
            ],
        )
        secondary_kd = (
            plan_email_psd_ped_to_secondary_kd_map[plan_email_ped_psd_tuple]
            if plan_email_ped_psd_tuple
            else timezone.now()
        )
        payroll_object_map_key = (payee_email_id, secondary_kd, period_end_date)
        if payroll_object_map_key not in payee_kd_ed_to_payroll_object_map:
            payroll_object = EmployeePayrollAccessor(
                client_id
            ).get_payee_variable_pay_record_for_given_date_first(
                secondary_kd, period_end_date, payee_email_id
            )
            payee_kd_ed_to_payroll_object_map[payroll_object_map_key] = payroll_object
        if payee_kd_ed_to_payroll_object_map[payroll_object_map_key]:
            payee_currency = payee_kd_ed_to_payroll_object_map[
                payroll_object_map_key
            ].pay_currency
            # payout_frequency = payee_kd_ed_to_payroll_object_map[payroll_object_map_key].payout_frequency.lower()
            yearly_variable_pay_from_db = payee_kd_ed_to_payroll_object_map[
                payroll_object_map_key
            ].variable_pay
        else:
            payee_currency = None
            yearly_variable_pay_from_db = 0
        if client_base_currency != payee_currency:
            # start_time = time()
            variable_pay_per_year_in_base_currency = change_to_base_currency(
                client_id,
                yearly_variable_pay_from_db,
                payee_currency,
                period_end_date,
                secondary_kd,
            )
            # end_time = time()
            # logger.info(f"Changing to base currency took {end_time - start_time} seconds - Variable Pay per year in base currency")
        else:
            variable_pay_per_year_in_base_currency = yearly_variable_pay_from_db
        variable_pay_per_month_in_base_currency = (
            variable_pay_per_year_in_base_currency / 12
        )

        if plan_email_ped_psd_tuple in comm_adjustment_plan_email_psd_ped_to_amount_map:
            commission_adjustment_amount = (
                comm_adjustment_plan_email_psd_ped_to_amount_map[
                    plan_email_ped_psd_tuple
                ]
            )
            # Removing the adjustment to avoid duplicates
            del comm_adjustment_plan_email_psd_ped_to_amount_map[
                plan_email_ped_psd_tuple
            ]
            # adjustment is fetched in base currency. conversion is not required
            # if client_base_currency != payee_currency:
            #     # start_time = time()
            #     commission_adjustment_amount = change_to_base_currency(
            #         client_id,
            #         commission_adjustment_amount,
            #         payee_currency,
            #         period_end_date,
            #     )
            # end_time = time()
            # logger.info(f"Changing to base currency took {end_time - start_time} seconds - Commission Adjustment amount")
        none_plan_email_ped_psd_list = list(plan_email_ped_psd_tuple)
        none_plan_email_ped_psd_list[0] = "None"
        # To handle global commission adjustment
        if (
            tuple(none_plan_email_ped_psd_list)
            in comm_adjustment_plan_email_psd_ped_to_amount_map
        ):
            none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)] = {
                "is_paid": None,
                "is_locked": None,
                "payout_kd": None,
                "locked_kd": None,
            }
            none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                "comm_adj_amount"
            ] = comm_adjustment_plan_email_psd_ped_to_amount_map[
                tuple(none_plan_email_ped_psd_list)
            ]
            # adjustment is fetched in base currency. conversion is not required
            # if client_base_currency != payee_currency:
            #     # start_time = time()
            #     none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
            #         "comm_adj_amount"
            #     ] = change_to_base_currency(
            #         client_id,
            #         none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
            #             "comm_adj_amount"
            #         ],
            #         payee_currency,
            #         period_end_date,
            #     )
            #     # end_time = time()
            #     # logger.info(f"Changing to base currency took {end_time - start_time} seconds - none_plan_email comm_adj_amount")
        key_to_check_in_payout = plan_email_ped_psd_tuple[1:]
        if key_to_check_in_payout in payout_payee_psd_ped_to_object_map:
            is_paid = True
            draw_adjustment_amount = Decimal(
                payout_payee_psd_ped_to_object_map[
                    key_to_check_in_payout
                ].payout_adjustments["draw_adj"]
            )
            payout_kd = payout_payee_psd_ped_to_object_map[
                key_to_check_in_payout
            ].payout_knowledge_date
            if (
                tuple(none_plan_email_ped_psd_list)
                in comm_adjustment_plan_email_psd_ped_to_amount_map
            ):
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "is_paid"
                ] = True
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "payout_kd"
                ] = payout_kd
        if plan_email_ped_psd_tuple in comm_lock_plan_email_psd_ped_to_locked_date_map:
            locked_kd = comm_lock_plan_email_psd_ped_to_locked_date_map[
                plan_email_ped_psd_tuple
            ]
            is_locked = True
            if (
                tuple(none_plan_email_ped_psd_list)
                in comm_adjustment_plan_email_psd_ped_to_amount_map
            ):
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "is_locked"
                ] = True
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "locked_kd"
                ] = locked_kd
        if payee_email_id in email_to_draw_list_map:
            if (
                payee_email_id,
                period_end_date,
            ) not in email_period_to_draw_adjustment_map:
                email_period_to_draw_adjustment_map[
                    (payee_email_id, period_end_date)
                ] = get_final_draw_amount(
                    client_id,
                    payee_email_id,
                    period_end_date,
                    email_to_draw_list_map[payee_email_id],
                    str(fiscal_year_we_are_calculating),
                    payee_period_commission_amount + payee_period_comm_adj_amount,
                    draw_adj_payee_id_fiscal_year_period_to_object_map,
                    client_base_currency,
                    payee_currency,
                    client_start_month,
                    month_end_dates_for_year,
                )
                logger.info(
                    f"Got final draw amount - {payee_email_id}, {period_end_date}"
                )
            draw_adjustment_amount = email_period_to_draw_adjustment_map[
                (payee_email_id, period_end_date)
            ]
            # NEED TO CHECK GLOBAL MAP FOR DRAW AMOUNT, IF YES USE IT, ELSE CONSTRUCT
        else:
            draw_adjustment_amount = 0
        row_key = "##::##".join(
            [
                str(fiscal_year_we_are_calculating),
                str(payee_email_id),
                str(period_start_date),
                str(period_end_date),
                str(plan_id),
            ]
        )
        current_period_commission_summary_objects.append(
            {
                "client_id": client_id,
                "knowledge_begin_date": current_time,
                "object_id": "commission_summary",
                "object_type": "commission_object",
                "row_key": row_key,
                "snapshot_key": row_key,
                "data": {
                    "fiscal_year": fiscal_year_we_are_calculating,
                    "commission_plan_id": plan_id,
                    "payee_email_id": payee_email_id,
                    "period_start_date": period_start_date,
                    "period_end_date": period_end_date,
                    "commission_amount": commission_amount,
                    "comm_adj_for_plan": commission_adjustment_amount,
                    "is_locked": is_locked,
                    "is_paid": is_paid,
                    "variable_pay": round(variable_pay_per_month_in_base_currency, 2),
                    "payout_kd": payout_kd,
                    "locked_kd": locked_kd,
                    "draw_adjustment_amount": draw_adjustment_amount,
                },
            }
        )

    for none_plan_email_ped_psd_tuple, value in none_plan_email_ped_psd_map.items():
        row_key = "##::##".join(
            [
                str(fiscal_year_we_are_calculating),
                str(none_plan_email_ped_psd_tuple[1]),
                str(none_plan_email_ped_psd_tuple[2]),
                str(none_plan_email_ped_psd_tuple[3]),
                str(None),
            ]
        )
        current_period_commission_summary_objects.append(
            {
                "client_id": client_id,
                "knowledge_begin_date": current_time,
                "object_id": "commission_summary",
                "object_type": "commission_object",
                "row_key": row_key,
                "snapshot_key": row_key,
                "data": {
                    "fiscal_year": fiscal_year_we_are_calculating,
                    "commission_plan_id": None,
                    "payee_email_id": none_plan_email_ped_psd_tuple[1],
                    "period_start_date": none_plan_email_ped_psd_tuple[2],
                    "period_end_date": none_plan_email_ped_psd_tuple[3],
                    "commission_amount": 0,
                    "comm_adj_for_plan": value["comm_adj_amount"],
                    "is_locked": value["is_locked"],
                    "is_paid": value["is_paid"],
                    "variable_pay": 0,
                    "payout_kd": value["payout_kd"],
                    "locked_kd": value["locked_kd"],
                    "draw_adjustment_amount": 0,
                },
            }
        )
    return current_period_commission_summary_objects


def entries_for_locked_payees(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    locked_payee_map,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    comm_lock_email_ped_to_locked_date_map,
    payout_payee_psd_ped_to_object_map,
    comm_lock_plan_email_psd_ped_to_locked_date_map,
    client_base_currency,
    email_to_draw_list_map,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_start_month,
    month_end_dates_for_year,
    logger=None,
):
    comm_summary_for_all_periods = []
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "current_time": current_time})
    )
    logger.info(
        f"Iterating over period and payees of map length: {len(locked_payee_map.items())}"
    )
    for period, payees in locked_payee_map.items():
        period_end_date = period
        commission_entries = []
        logger.info(
            f"Iterating over {len(payees)} in period {period} to fetch commission entries"
        )
        for payee in tuple(payees):
            locked_kd = comm_lock_email_ped_to_locked_date_map[(payee, period)]
            commission_entries.extend(
                CommissionAccessor(client_id).get_entries_for_period_end_date_locked_kd(
                    payee, locked_kd, period_end_date
                )
            )
        logger.info(f"Creating summary objects for period: {period}")
        comm_summary_for_one_period = create_summary_objects_for_a_period(
            client_id,
            current_time,
            fiscal_year_we_are_calculating,
            commission_entries,
            payees,
            comm_adjustment_plan_email_psd_ped_to_amount_map,
            payout_payee_psd_ped_to_object_map,
            comm_lock_plan_email_psd_ped_to_locked_date_map,
            client_base_currency,
            email_to_draw_list_map,
            draw_adj_payee_id_fiscal_year_period_to_object_map,
            client_start_month,
            month_end_dates_for_year,
            logger,
        )
        comm_summary_for_all_periods.extend(comm_summary_for_one_period)
    return comm_summary_for_all_periods


def entries_for_not_locked_payees(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    not_locked_not_paid_map,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    client_base_currency,
    email_to_draw_list_map,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_start_month,
    month_end_dates_for_year,
    logger=None,
):
    # {'31stjan': {'dg','nivedha'}}
    comm_summary_for_all_periods = []
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "current_time": current_time})
    )
    logger.info(
        f"Iterating over period and payees of map length: {len(not_locked_not_paid_map.items())}"
    )
    for period, payees in not_locked_not_paid_map.items():
        # period_start_date = first_day_of_month(period)
        period_end_date = period
        logger.info(f"Fetching commission entries for period: {period}")
        commission_entries = CommissionAccessor(
            client_id
        ).get_entries_for_period_end_date(period_end_date)
        # psd_ped_set = set()
        (
            payout_payee_psd_ped_to_object_map,
            comm_lock_plan_email_psd_ped_to_locked_date_map,
        ) = ({}, {})
        logger.info(f"Creating summary objects for period: {period}")
        comm_summary_for_one_period = create_summary_objects_for_a_period(
            client_id,
            current_time,
            fiscal_year_we_are_calculating,
            commission_entries,
            payees,
            comm_adjustment_plan_email_psd_ped_to_amount_map,
            payout_payee_psd_ped_to_object_map,
            comm_lock_plan_email_psd_ped_to_locked_date_map,
            client_base_currency,
            email_to_draw_list_map,
            draw_adj_payee_id_fiscal_year_period_to_object_map,
            client_start_month,
            month_end_dates_for_year,
            logger,
        )
        comm_summary_for_all_periods.extend(comm_summary_for_one_period)
    return comm_summary_for_all_periods


def create_summary_objects_for_all_periods(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    commission_entries,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    payout_payee_psd_ped_to_object_map,
    comm_lock_plan_email_psd_ped_to_locked_date_map,
    client_base_currency,
    email_to_draw_list_map,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_start_month,
    month_end_dates_for_year,
    logger=None,
):
    current_period_commission_summary_objects = []
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "current_time": current_time})
    )
    logger.info(
        f"Creating summary objects - commission entries: {len(commission_entries)}"
    )
    (
        plan_email_psd_ped_to_amount_map,
        plan_email_psd_ped_to_secondary_kd_map,
    ) = comm_create_plan_email_psd_ped_to_amount_and_secondary_kd_map(
        commission_entries
    )
    payee_kd_ed_to_payroll_object_map = {}
    email_period_to_draw_adjustment_map = {}
    email_period_to_commission_amount_map = construct_comm_amount_at_payee_period_level(
        plan_email_psd_ped_to_amount_map
    )
    email_period_to_commission_adjustment_amount_map = (
        construct_comm_adj_amount_at_payee_period_level(
            comm_adjustment_plan_email_psd_ped_to_amount_map
        )
    )
    none_plan_email_ped_psd_map = {}
    logger.info(
        f"Iterating over plan_email_ped_psd_tuple, commission_amount in map of length {len(plan_email_psd_ped_to_amount_map.items())}"
    )
    for (
        plan_email_ped_psd_tuple,
        commission_amount,
    ) in plan_email_psd_ped_to_amount_map.items():
        plan_id = plan_email_ped_psd_tuple[0]
        payee_email_id = plan_email_ped_psd_tuple[1]
        period_start_date = plan_email_ped_psd_tuple[2]
        period_end_date = plan_email_ped_psd_tuple[3]
        (
            commission_adjustment_amount,
            draw_adjustment_amount,
            is_paid,
            is_locked,
            payout_kd,
            locked_kd,
            payee_period_commission_amount,
            payee_period_comm_adj_amount,
        ) = (
            0,
            0,
            False,
            False,
            None,
            None,
            email_period_to_commission_amount_map[
                (payee_email_id, period_start_date, period_end_date)
            ],
            email_period_to_commission_adjustment_amount_map[
                (payee_email_id, period_start_date, period_end_date)
            ],
        )
        secondary_kd = (
            plan_email_psd_ped_to_secondary_kd_map[plan_email_ped_psd_tuple]
            if plan_email_ped_psd_tuple
            else timezone.now()
        )
        payroll_object_map_key = (payee_email_id, secondary_kd, period_end_date)
        if payroll_object_map_key not in payee_kd_ed_to_payroll_object_map:
            payroll_object = EmployeePayrollAccessor(
                client_id
            ).get_payee_variable_pay_record_for_given_date_first(
                secondary_kd, period_end_date, payee_email_id
            )
            payee_kd_ed_to_payroll_object_map[payroll_object_map_key] = payroll_object
        if payee_kd_ed_to_payroll_object_map[payroll_object_map_key]:
            payee_currency = payee_kd_ed_to_payroll_object_map[
                payroll_object_map_key
            ].pay_currency
            # payout_frequency = payee_kd_ed_to_payroll_object_map[payroll_object_map_key].payout_frequency.lower()
            yearly_variable_pay_from_db = payee_kd_ed_to_payroll_object_map[
                payroll_object_map_key
            ].variable_pay
        else:
            payee_currency = None
            yearly_variable_pay_from_db = 0
        if client_base_currency != payee_currency:
            # start_time = time()
            variable_pay_per_year_in_base_currency = change_to_base_currency(
                client_id,
                yearly_variable_pay_from_db,
                payee_currency,
                period_end_date,
                secondary_kd,
            )
            # end_time = time()
            # logger.info(
            #     f"Changing to base currency took {end_time - start_time} seconds - Variable Pay per year in base currency"
            # )
        else:
            variable_pay_per_year_in_base_currency = yearly_variable_pay_from_db
        variable_pay_per_month_in_base_currency = (
            variable_pay_per_year_in_base_currency / 12
        )

        if plan_email_ped_psd_tuple in comm_adjustment_plan_email_psd_ped_to_amount_map:
            commission_adjustment_amount = (
                comm_adjustment_plan_email_psd_ped_to_amount_map[
                    plan_email_ped_psd_tuple
                ]
            )
            # Removing the adjustment to avoid duplicates
            del comm_adjustment_plan_email_psd_ped_to_amount_map[
                plan_email_ped_psd_tuple
            ]
            # adjustments are fetched in base currency so conversion is not required
            # if client_base_currency != payee_currency:
            #     # start_time = time()
            #     commission_adjustment_amount = change_to_base_currency(
            #         client_id,
            #         commission_adjustment_amount,
            #         payee_currency,
            #         period_end_date,
            #     )
            # end_time = time()
            # logger.info(
            #     f"Changing to base currency took {end_time - start_time} seconds - Commission Adjustment amount"
            # )
        none_plan_email_ped_psd_list = list(plan_email_ped_psd_tuple)
        none_plan_email_ped_psd_list[0] = "None"
        # To handle global commission adjustment
        if (
            tuple(none_plan_email_ped_psd_list)
            in comm_adjustment_plan_email_psd_ped_to_amount_map
        ):
            none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)] = {
                "is_paid": None,
                "is_locked": None,
                "payout_kd": None,
                "locked_kd": None,
            }
            none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                "comm_adj_amount"
            ] = comm_adjustment_plan_email_psd_ped_to_amount_map[
                tuple(none_plan_email_ped_psd_list)
            ]
            # adjustments are fetched in base currency so conversion is not required
            # if client_base_currency != payee_currency:
            #     # start_time = time()
            #     none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
            #         "comm_adj_amount"
            #     ] = change_to_base_currency(
            #         client_id,
            #         none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
            #             "comm_adj_amount"
            #         ],
            #         payee_currency,
            #         period_end_date,
            #     )
            # end_time = time()
            # logger.info(
            #     f"Changing to base currency took {end_time - start_time} seconds - none_plan_email comm_adj_amount"
            # )
        key_to_check_in_payout = plan_email_ped_psd_tuple[1:]
        if key_to_check_in_payout in payout_payee_psd_ped_to_object_map:
            is_paid = True
            draw_adjustment_amount = Decimal(
                payout_payee_psd_ped_to_object_map[
                    key_to_check_in_payout
                ].payout_adjustments["draw_adj"]
            )
            payout_kd = payout_payee_psd_ped_to_object_map[
                key_to_check_in_payout
            ].payout_knowledge_date
            if (
                tuple(none_plan_email_ped_psd_list)
                in comm_adjustment_plan_email_psd_ped_to_amount_map
            ):
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "is_paid"
                ] = True
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "payout_kd"
                ] = payout_kd
        if plan_email_ped_psd_tuple in comm_lock_plan_email_psd_ped_to_locked_date_map:
            locked_kd = comm_lock_plan_email_psd_ped_to_locked_date_map[
                plan_email_ped_psd_tuple
            ]
            is_locked = True
            if (
                tuple(none_plan_email_ped_psd_list)
                in comm_adjustment_plan_email_psd_ped_to_amount_map
            ):
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "is_locked"
                ] = True
                none_plan_email_ped_psd_map[tuple(none_plan_email_ped_psd_list)][
                    "locked_kd"
                ] = locked_kd
        if payee_email_id in email_to_draw_list_map:
            if (
                payee_email_id,
                period_end_date,
            ) not in email_period_to_draw_adjustment_map:
                email_period_to_draw_adjustment_map[
                    (payee_email_id, period_end_date)
                ] = get_final_draw_amount(
                    client_id,
                    payee_email_id,
                    period_end_date,
                    email_to_draw_list_map[payee_email_id],
                    str(fiscal_year_we_are_calculating),
                    payee_period_commission_amount + payee_period_comm_adj_amount,
                    draw_adj_payee_id_fiscal_year_period_to_object_map,
                    client_base_currency,
                    payee_currency,
                    client_start_month,
                    month_end_dates_for_year,
                )
                logger.info(
                    f"Got final draw amount - {payee_email_id}, {period_end_date}"
                )
            draw_adjustment_amount = email_period_to_draw_adjustment_map[
                (payee_email_id, period_end_date)
            ]
            # NEED TO CHECK GLOBAL MAP FOR DRAW AMOUNT, IF YES USE IT, ELSE CONSTRUCT
        else:
            draw_adjustment_amount = 0
        row_key = "##::##".join(
            [
                str(fiscal_year_we_are_calculating),
                str(payee_email_id),
                str(period_start_date),
                str(period_end_date),
                str(plan_id),
            ]
        )
        current_period_commission_summary_objects.append(
            {
                "client_id": client_id,
                "knowledge_begin_date": current_time,
                "object_id": "commission_summary",
                "object_type": "commission_object",
                "row_key": row_key,
                "snapshot_key": row_key,
                "data": {
                    "fiscal_year": fiscal_year_we_are_calculating,
                    "commission_plan_id": plan_id,
                    "payee_email_id": payee_email_id,
                    "period_start_date": period_start_date,
                    "period_end_date": period_end_date,
                    "commission_amount": commission_amount,
                    "comm_adj_for_plan": commission_adjustment_amount,
                    "is_locked": is_locked,
                    "is_paid": is_paid,
                    "variable_pay": round(variable_pay_per_month_in_base_currency, 2),
                    "payout_kd": payout_kd,
                    "locked_kd": locked_kd,
                    "draw_adjustment_amount": draw_adjustment_amount,
                },
            }
        )

    for none_plan_email_ped_psd_tuple, value in none_plan_email_ped_psd_map.items():
        row_key = "##::##".join(
            [
                str(fiscal_year_we_are_calculating),
                str(none_plan_email_ped_psd_tuple[1]),
                str(none_plan_email_ped_psd_tuple[2]),
                str(none_plan_email_ped_psd_tuple[3]),
                str(None),
            ]
        )
        current_period_commission_summary_objects.append(
            {
                "client_id": client_id,
                "knowledge_begin_date": current_time,
                "object_id": "commission_summary",
                "object_type": "commission_object",
                "row_key": row_key,
                "snapshot_key": row_key,
                "data": {
                    "fiscal_year": fiscal_year_we_are_calculating,
                    "commission_plan_id": None,
                    "payee_email_id": none_plan_email_ped_psd_tuple[1],
                    "period_start_date": none_plan_email_ped_psd_tuple[2],
                    "period_end_date": none_plan_email_ped_psd_tuple[3],
                    "commission_amount": 0,
                    "comm_adj_for_plan": value["comm_adj_amount"],
                    "is_locked": value["is_locked"],
                    "is_paid": value["is_paid"],
                    "variable_pay": 0,
                    "payout_kd": value["payout_kd"],
                    "locked_kd": value["locked_kd"],
                    "draw_adjustment_amount": 0,
                },
            }
        )
    return current_period_commission_summary_objects


def entries_for_locked_payees_using_query_builder(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    locked_payee_map,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    payout_payee_psd_ped_to_object_map,
    comm_lock_plan_email_psd_ped_to_locked_date_map,
    client_base_currency,
    email_to_draw_list_map,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_start_month,
    month_end_dates_for_year,
    logger=None,
):
    comm_summary_for_all_periods = []
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "current_time": current_time})
    )
    start_date, end_date = (
        first_day_of_month(month_end_dates_for_year[0]),
        month_end_dates_for_year[-1],
    )
    commission_entries = fetch_commission_entries_for_locked_payees(
        client_id, start_date, end_date, locked_payee_map, logger
    )
    comm_summary_for_all_periods = create_summary_objects_for_all_periods(
        client_id,
        current_time,
        fiscal_year_we_are_calculating,
        commission_entries,
        comm_adjustment_plan_email_psd_ped_to_amount_map,
        payout_payee_psd_ped_to_object_map,
        comm_lock_plan_email_psd_ped_to_locked_date_map,
        client_base_currency,
        email_to_draw_list_map,
        draw_adj_payee_id_fiscal_year_period_to_object_map,
        client_start_month,
        month_end_dates_for_year,
        logger,
    )
    return comm_summary_for_all_periods


def entries_for_not_locked_payees_using_query_builder(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    not_locked_not_paid_map,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    client_base_currency,
    email_to_draw_list_map,
    draw_adj_payee_id_fiscal_year_period_to_object_map,
    client_start_month,
    month_end_dates_for_year,
    logger=None,
):
    # {'31stjan': {'dg','nivedha'}}
    comm_summary_for_all_periods = []
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "current_time": current_time})
    )
    start_date, end_date = (
        first_day_of_month(month_end_dates_for_year[0]),
        month_end_dates_for_year[-1],
    )
    commission_entries = fetch_commission_entries_for_not_locked_payees(
        client_id, start_date, end_date, not_locked_not_paid_map, logger
    )
    (
        payout_payee_psd_ped_to_object_map,
        comm_lock_plan_email_psd_ped_to_locked_date_map,
    ) = ({}, {})
    comm_summary_for_all_periods = create_summary_objects_for_all_periods(
        client_id,
        current_time,
        fiscal_year_we_are_calculating,
        commission_entries,
        comm_adjustment_plan_email_psd_ped_to_amount_map,
        payout_payee_psd_ped_to_object_map,
        comm_lock_plan_email_psd_ped_to_locked_date_map,
        client_base_currency,
        email_to_draw_list_map,
        draw_adj_payee_id_fiscal_year_period_to_object_map,
        client_start_month,
        month_end_dates_for_year,
        logger,
    )
    return comm_summary_for_all_periods


def entries_for_remaining_adjustments(
    client_id,
    current_time,
    fiscal_year_we_are_calculating,
    comm_adjustment_plan_email_psd_ped_to_amount_map,
    payout_payee_psd_ped_to_object_map,
    comm_lock_plan_email_psd_ped_to_locked_date_map,
    row_keys_list,
):
    """Provides commission summary objects for the commission adjustments which
    are not having any commission entries

    For example,

    Consider the following:
    - Published plan 'Plan1' between period 'Jan 2023 - Mar 2023'
    - Published plan 'Plan2' between period 'Apr 2023 - Dec 2023'
    - Commission Adjustment on 'Plan1' for the period 'Apr 2023'.

    Even though the plan 'Plan1' is not active for the period 'Apr 2023', the
    commission adjustment created on it should be considered and hence the
    commission summary object should be created for it.
    """
    comm_lock_email_psd_ped_to_locked_date_map = {
        (
            plan_email_ped_psd_tuple[1],
            plan_email_ped_psd_tuple[2],
            plan_email_ped_psd_tuple[3],
        ): locked_kd
        for plan_email_ped_psd_tuple, locked_kd in comm_lock_plan_email_psd_ped_to_locked_date_map.items()
    }
    additional_commission_summary_objects = []
    for (
        plan_email_ped_psd_tuple
    ) in comm_adjustment_plan_email_psd_ped_to_amount_map.keys():
        plan_id = plan_email_ped_psd_tuple[0]
        payee_email_id = plan_email_ped_psd_tuple[1]
        period_start_date = plan_email_ped_psd_tuple[2]
        period_end_date = plan_email_ped_psd_tuple[3]
        row_key = "##::##".join(
            [
                str(fiscal_year_we_are_calculating),
                str(payee_email_id),
                str(period_start_date),
                str(period_end_date),
                str(plan_id),
            ]
        )
        # plan id none records if processed in
        # entries_for_locked_payees_using_query_builder function, will skip it here.
        if row_key in row_keys_list:
            continue
        commission_adjustment_amount = comm_adjustment_plan_email_psd_ped_to_amount_map[
            plan_email_ped_psd_tuple
        ]
        key_to_check = plan_email_ped_psd_tuple[1:]
        is_paid = False
        payout_kd = None
        if key_to_check in payout_payee_psd_ped_to_object_map:
            is_paid = True
            payout_kd = payout_payee_psd_ped_to_object_map[
                key_to_check
            ].payout_knowledge_date
        is_locked = False
        locked_kd = None
        if key_to_check in comm_lock_email_psd_ped_to_locked_date_map:
            is_locked = True
            locked_kd = comm_lock_email_psd_ped_to_locked_date_map[key_to_check]
        additional_commission_summary_objects.append(
            {
                "client_id": client_id,
                "knowledge_begin_date": current_time,
                "object_id": "commission_summary",
                "object_type": "commission_object",
                "row_key": row_key,
                "snapshot_key": row_key,
                "data": {
                    "fiscal_year": fiscal_year_we_are_calculating,
                    "commission_plan_id": plan_id,
                    "payee_email_id": payee_email_id,
                    "period_start_date": period_start_date,
                    "period_end_date": period_end_date,
                    "commission_amount": 0,
                    "comm_adj_for_plan": commission_adjustment_amount,
                    "is_locked": is_locked,
                    "is_paid": is_paid,
                    "variable_pay": 0,
                    "payout_kd": payout_kd,
                    "locked_kd": locked_kd,
                    "draw_adjustment_amount": 0,
                },
            }
        )
    return additional_commission_summary_objects


def construct_payout_payee_psd_ped_to_object_map(payout_for_year):
    payee_psd_ped_to_object_map = {}
    for payout_object in payout_for_year:
        payee_psd_ped_to_object_map[
            (
                payout_object.payee_id,
                payout_object.period_start_date,
                payout_object.period_end_date,
            )
        ] = payout_object
    return payee_psd_ped_to_object_map


def generate_commission_summary(client_id, year_to_calculate, logger=None):
    current_time = timezone.now()
    log_context = {"client_id": client_id, "current_time": str(current_time)}
    logger = logger if logger else LogWithContext()
    logger.update_context(log_context)
    logger.info(
        f"BEGIN: Generate commission summary for client {client_id} and year {year_to_calculate}"
    )
    client = get_client(client_id)
    client_start_month = client.fiscal_start_month - 1
    client_base_currency = client.base_currency
    logger.info(
        f"Get end dates list for year {year_to_calculate} and start_month {client_start_month}"
    )
    month_end_dates_for_year = get_end_dates_list(
        int(year_to_calculate), client_start_month
    )
    fiscal_year_we_are_calculating = month_end_dates_for_year[-1].year
    start_date, end_date = (
        first_day_of_month(month_end_dates_for_year[0]),
        month_end_dates_for_year[-1],
    )
    plan_ids = CommissionPlanAccessor(client_id).get_plan_ids_fall_between_dates(
        start_date, end_date
    )
    plan_ids = list(map(str, plan_ids))
    logger.info(f"Fetched {len(plan_ids)} plans falling in fiscal year")
    logger.info(f"Fetch commission adjustments from {start_date} to {end_date}")
    commission_lock_for_year = CommissionLockAccessor(
        client_id
    ).get_locked_data_for_year(start_date, end_date)
    commission_adjustments_for_year = commission_adjustments_for_payee_and_period(
        client_id, start_date, end_date, CURRENCY_TYPE.BASE
    )
    logger.info(
        f"Fetched commission adjustments from {start_date} to {end_date} : {len(commission_adjustments_for_year)}"
    )
    logger.info(f"Get commission lock from {start_date} to {end_date}")
    # commission_lock_for_year = CommissionLockAccessor(client_id).get_locked_data_for_year(start_date, end_date)

    comm_adjustment_plan_email_psd_ped_to_amount_map = comm_adjustment_create_plan_email_psd_ped_to_amount_map(
        commission_adjustments_for_year
        # , commission_lock_for_year, client_id
    )
    comm_lock_plan_email_psd_ped_to_locked_date_map = (
        comm_lock_create_plan_email_psd_ped_to_locked_date_map(commission_lock_for_year)
    )  # for summary table use
    comm_lock_email_ped_to_locked_date_map = (
        construct_comm_lock_email_ped_to_locked_date_map(
            comm_lock_plan_email_psd_ped_to_locked_date_map
        )
    )  # for picking commission use
    # coomm_adj_dict = {'plan1id' : {('jan1','jan31'): 100 }, 'plan2': }
    # {('jan1','jan31') : {('plan1','payee'): 100 , ('plan1','payee2')},
    logger.info(f"Fetch draws for year {year_to_calculate}")
    draws_for_year = DrawsAccessor(client_id).get_draws_for_year(year_to_calculate)
    logger.info(f"Fetched draws for year {year_to_calculate} - {len(draws_for_year)}")
    email_to_draw_list_map = {}
    for draw_object in draws_for_year:
        email_to_draw_list_map[draw_object.employee_email_id] = draw_object.draws
    draw_adjustments_for_year = DrawAdjustmentAccessor(
        client_id
    ).get_draw_adjustments_for_payees_and_year(year_to_calculate)
    draw_adj_payee_id_fiscal_year_period_to_object_map = {}
    for draw_adj_object in draw_adjustments_for_year:
        draw_adj_payee_id_fiscal_year_period_to_object_map[
            (
                draw_adj_object.payee_id,
                draw_adj_object.fiscal_year,
                draw_adj_object.period,
            )
        ] = draw_adj_object
    logger.info(f"Fetch Payout from {start_date} to {end_date}")
    payouts_for_year = PayoutAccessor(client_id).payout_for_payee_falls_in_year(
        start_date, end_date
    )
    logger.info(
        f"Fetched Payout from {start_date} to {end_date} : {len(payouts_for_year)}"
    )
    payout_payee_psd_ped_to_object_map = construct_payout_payee_psd_ped_to_object_map(
        payouts_for_year
    )
    locked_payee_map, not_locked_payee_map = {}, {}
    # # THIS PAYEE THIS PERIOD LOCKTABLE
    # {("plan_id", "email", "ped", "psd")}
    # set = {("plan" "1stjan", "<EMAIL>"), ("1stmarc", "<EMAIL>")}

    logger.info("Fetch active payees for month end dates")
    total_payees_count, locked_payees_count, not_locked_payees_count = 0, 0, 0
    for period in month_end_dates_for_year:
        plan_payees_for_the_period = PlanPayeeAccessor(
            client_id
        ).get_active_payees_in_plan_as_list(period, plan_ids)
        total_payees_count += len(plan_payees_for_the_period)
        for payee_email in plan_payees_for_the_period:
            key_to_check_locked_or_not_locked = (payee_email, period)
            if (
                key_to_check_locked_or_not_locked
                in comm_lock_email_ped_to_locked_date_map
            ):  # LOCKED
                if period not in locked_payee_map:
                    locked_payee_map[period] = set()
                locked_payee_map[period].add(payee_email)
                locked_payees_count += 1
            else:  # NOT LOCKED
                if period not in not_locked_payee_map:
                    not_locked_payee_map[period] = set()
                not_locked_payee_map[period].add(
                    payee_email
                )  # period will be month end
                not_locked_payees_count += 1
    logger.info(
        f"Fetched active payees across periods: {total_payees_count}. Locked payees count: {locked_payees_count}. Not locked payees count: {not_locked_payees_count}"
    )

    all_period_commission_summary_objects = []
    logger.info("Fetch entries for locked payees")
    comm_summary_obj_for_locked_payees = []

    #  Run the join query and construct records only if there is at least one payee with a commission lock
    #  across the periods for the given fiscal year
    if locked_payees_count:
        logger.info("Fetching entries for locked payees using new version")
        comm_summary_obj_for_locked_payees = (
            entries_for_locked_payees_using_query_builder(
                client_id,
                current_time,
                fiscal_year_we_are_calculating,
                locked_payee_map,
                comm_adjustment_plan_email_psd_ped_to_amount_map,
                payout_payee_psd_ped_to_object_map,
                comm_lock_plan_email_psd_ped_to_locked_date_map,
                client_base_currency,
                email_to_draw_list_map,
                draw_adj_payee_id_fiscal_year_period_to_object_map,
                client_start_month,
                month_end_dates_for_year,
                logger,
            )
        )
    else:
        logger.info("No locked payee present across periods.")

    logger.info(
        f"Fetched entries for locked payees : {len(comm_summary_obj_for_locked_payees)}"
    )

    logger.info("Fetch entries for unlocked payees")
    comm_summary_obj_for_not_locked_payees = []

    if not_locked_payees_count:
        #  Run the join query and construct records only if there is at least one payee without a commission lock
        #  across the periods for the given fiscal year
        logger.info("Fetching entries for unlocked payees using new version")
        comm_summary_obj_for_not_locked_payees = (
            entries_for_not_locked_payees_using_query_builder(
                client_id,
                current_time,
                fiscal_year_we_are_calculating,
                not_locked_payee_map,
                comm_adjustment_plan_email_psd_ped_to_amount_map,
                client_base_currency,
                email_to_draw_list_map,
                draw_adj_payee_id_fiscal_year_period_to_object_map,
                client_start_month,
                month_end_dates_for_year,
                logger,
            )
        )
    else:
        logger.info("No not locked/not paid payee present across periods.")

    logger.info(
        f"Fetched entries for unlocked payees : {len(comm_summary_obj_for_not_locked_payees)}"
    )
    all_period_commission_summary_objects.extend(comm_summary_obj_for_locked_payees)
    all_period_commission_summary_objects.extend(comm_summary_obj_for_not_locked_payees)

    row_keys_list = pydash.map_(all_period_commission_summary_objects, "row_key")
    comm_summary_obj_for_remaining_adjs = entries_for_remaining_adjustments(
        client_id,
        current_time,
        fiscal_year_we_are_calculating,
        comm_adjustment_plan_email_psd_ped_to_amount_map,
        payout_payee_psd_ped_to_object_map,
        comm_lock_plan_email_psd_ped_to_locked_date_map,
        row_keys_list,
    )
    logger.info(
        f"Fetched entries for payees adjustments without any commissions : {len(comm_summary_obj_for_remaining_adjs)}"
    )

    all_period_commission_summary_objects.extend(comm_summary_obj_for_remaining_adjs)
    logger.info(
        f"Invalidate and insert for fiscal year {fiscal_year_we_are_calculating} : {len(all_period_commission_summary_objects)} records"
    )

    table_name = get_report_object_data_table_name(client_id, "commission_summary")
    _write_to_snowflake(
        client_id=client_id,
        ked=current_time,
        fiscal_year=fiscal_year_we_are_calculating,
        report_records=all_period_commission_summary_objects,
        table_name=table_name,
    )


def persist_frozen_payroll(client_id, records, fiscal_year):
    knowledge_date = make_aware(datetime.now())
    fpd_objects = []
    for rec in records:
        email, end_date, variable_pay_base, variable_pay_payee = rec
        fpd_obj = FrozenPayrollData(
            client_id=client_id,
            knowledge_begin_date=knowledge_date,
            period_end_date=make_aware(end_date),
            fiscal_year=fiscal_year,
            payee_email_id=email,
            variable_pay_in_base_currency=variable_pay_base,
            variable_pay_in_payee_currency=variable_pay_payee,
        )
        fpd_objects.append(fpd_obj)

    fpd = FrozenPayrollDataAccessor(client_id)
    fpd.invaldate_for_fiscal_year(fiscal_year, knowledge_date)
    if fpd_objects:
        fpd.insert_new_data(fpd_objects)


def extract_and_persist_frozen_payroll(client_id, fiscal_year):
    client = get_client(client_id)
    client_start_month = client.fiscal_start_month - 1
    client_base_currency = client.base_currency

    end_dates_list = []
    end_dates_list += get_end_dates_list(int(fiscal_year), client_start_month)
    end_dates_list = [x.strftime("%Y-%m-%d") for x in end_dates_list]

    variable_pay_plan_ids = CommissionPlanAccessor(client_id).get_all_plan_ids_as_list()
    variable_pay_plan_ids = list(map(str, variable_pay_plan_ids))
    payees_in_plan_records = PlanPayeeAccessor(client_id).get_payees_in_plan(
        variable_pay_plan_ids
    )

    end_date_plan_payees_map = {}
    end_date_locks_map = {}
    all_payees = []
    end_of_day_aware_end_dates = []
    start_of_day_aware_end_dates = []
    records = []

    for idx, end_date in enumerate(end_dates_list):
        end_of_day_aware_end_dates.append(
            end_of_day(make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d")))
        )
        start_of_day_aware_end_dates.append(
            start_of_day(make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d")))
        )
        payees_list = [
            record["employee_email_id"]
            for record in payees_in_plan_records
            if record["effective_start_date"] <= start_of_day_aware_end_dates[idx]
            and record["effective_end_date"] >= end_of_day_aware_end_dates[idx]
        ]
        end_date_plan_payees_map[end_date] = list(set(payees_list))
        all_payees.extend(end_date_plan_payees_map[end_date])
    all_payees = list(set(all_payees))
    lock_records = CommissionLockAccessor(client_id).get_lock_by_ids(all_payees)

    for idx, end_date in enumerate(end_dates_list):
        locks_list = [
            record
            for record in lock_records
            if record["period_end_date"] == end_of_day_aware_end_dates[idx]
        ]
        end_date_locks_map[end_date] = locks_list

    for idx, end_date in enumerate(end_dates_list):
        payees = end_date_plan_payees_map[end_date]
        all_locks = end_date_locks_map[end_date]
        lock_dict = {x["payee_email_id"]: x for x in all_locks} if all_locks else {}
        for payee in payees:
            payee_lock_record = lock_dict.get(payee, None)
            payroll_end_date = get_payee_payroll_date(
                client_id, payee, end_of_day_aware_end_dates[idx], payee_lock_record
            )
            payroll = EmployeePayrollAccessor(
                client_id
            ).get_payee_variable_pay_record_for_given_date_first(
                payroll_end_date, end_of_day_aware_end_dates[idx], payee
            )
            if payroll:
                variable_pay_payee = payroll.variable_pay
                payee_currency = payroll.pay_currency
                variable_pay_base = (
                    change_to_base_currency(
                        client_id,
                        variable_pay_payee,
                        payee_currency,
                        datetime.strptime(end_date, "%Y-%m-%d"),
                        payroll_end_date,
                    )
                    if client_base_currency != payee_currency
                    else variable_pay_payee
                )
                variable_pay_base_per_month = variable_pay_base / 12
                variable_pay_payee_per_month = variable_pay_payee / 12
                records.append(
                    (
                        payee,
                        datetime.strptime(end_date, "%Y-%m-%d"),
                        variable_pay_base_per_month,
                        variable_pay_payee_per_month,
                    )
                )
    persist_frozen_payroll(client_id, records, fiscal_year)


def extract_and_persist_frozen_payroll_v2(client_id, fiscal_year, logger=None):
    logger = (
        logger
        if logger
        else LogWithContext({"client_id": client_id, "fiscal_year": fiscal_year})
    )
    logger.info(
        "Using extract_and_persist_frozen_payroll_v2 to save frozen payroll data."
    )
    client = get_client(client_id)
    client_start_month = client.fiscal_start_month - 1
    client_base_currency = client.base_currency

    variable_pay_plan_ids = CommissionPlanAccessor(client_id).get_all_plan_ids_as_list()
    variable_pay_plan_ids = list(map(str, variable_pay_plan_ids))

    if len(variable_pay_plan_ids) == 0:
        logger.info(
            "No published commission plan present across fiscal years for client."
        )
        persist_frozen_payroll(client_id, [], fiscal_year)
        return

    payees_in_plan_records = PlanPayeeAccessor(client_id).get_payees_in_plan(
        variable_pay_plan_ids
    )

    end_date_plan_payees_map = {}
    end_dates_list = []
    end_dates_list += get_end_dates_list(int(fiscal_year), client_start_month)
    end_dates_list = [x.strftime("%Y-%m-%d") for x in end_dates_list]

    end_of_day_aware_end_dates = []
    start_of_day_aware_end_dates = []
    records = []

    for idx, end_date in enumerate(end_dates_list):
        end_of_day_aware_end_dates.append(
            end_of_day(make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d")))
        )
        start_of_day_aware_end_dates.append(
            start_of_day(make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d")))
        )
        payees_list = [
            record["employee_email_id"]
            for record in payees_in_plan_records
            if record["effective_start_date"] <= start_of_day_aware_end_dates[idx]
            and record["effective_end_date"] >= end_of_day_aware_end_dates[idx]
        ]
        end_date_plan_payees_map[end_date] = set(payees_list)

    for idx, end_date in enumerate(end_dates_list):
        payees_by_end_date = end_date_plan_payees_map[end_date]
        logger.info(f"Extracting data for period : {end_date}")
        payees_with_commission_entries = fetch_variable_pay_for_payees_with_commission(
            client_id,
            end_of_day_aware_end_dates[idx],
            client_base_currency,
            payees_by_end_date,
            logger,
        )
        logger.info(
            f"Extracted entries for payees with commission: {len(payees_with_commission_entries)}"
        )
        payees_with_commission_set = set(
            [locked_payee[0] for locked_payee in payees_with_commission_entries]
        )

        payees_without_commission = payees_by_end_date - payees_with_commission_set

        payees_without_commission_entries = (
            fetch_variable_pay_for_payees_without_commission(
                client_id,
                end_of_day_aware_end_dates[idx],
                payees_without_commission,
                client_base_currency,
            )
        )
        logger.info(
            f"Extracted entries for payees without commission: {len(payees_without_commission_entries)}"
        )

        records.extend(payees_with_commission_entries)
        records.extend(payees_without_commission_entries)

    persist_frozen_payroll(client_id, records, fiscal_year)


def get_commission_summary_payout_for_year(
    plan_ids, client_id, year, month_list=None, request_id=None
):
    if plan_ids:
        variable_pay_plan_ids = deepcopy(plan_ids)
        # To pull global commission adjustments when plan_id filters present
        # plan_ids.append("None")
    else:
        variable_pay_plan_ids = CommissionPlanAccessor(
            client_id
        ).get_all_plan_ids_as_list()
        variable_pay_plan_ids = list(map(str, variable_pay_plan_ids))

    commission_summary_monthly_payout_map = {}
    variable_pay_map = {}
    log_context = {
        "client_id": client_id,
        "year": year,
        "plan_ids": str(plan_ids),
        "request_id": request_id,
    }
    logger = LogWithContext(log_context)

    if not CommissionAccessor(client_id).commission_entry_exist_for_any_plan_ids(
        variable_pay_plan_ids
    ):
        logger.error(
            "END: COMMISSION PAYOUTS COUNT QUERY - COMMISSIONS NOT AVAILABLE FOR SELECTED PLANS"
        )
        return {
            "error": get_localized_message_utils(
                "COMMISSIONS_NOT_AVAILABLE_FOR_SELECTED_PLANS", client_id
            )
        }

    try:
        client = get_client(client_id)
        client_start_month = client.fiscal_start_month - 1
        end_dates_list = month_list
        if end_dates_list is None:
            end_dates_list = get_end_dates_list(int(year), client_start_month)
            end_dates_list = [x.strftime("%Y-%m-%d") for x in end_dates_list]
        payees_in_plan_records = PlanPayeeAccessor(client_id).get_payees_in_plan(
            variable_pay_plan_ids
        )
        end_date_plan_payees_map = {}
        all_payees = []
        end_of_day_aware_end_dates = []
        start_of_day_aware_end_dates = []
        for idx, end_date in enumerate(end_dates_list):
            end_of_day_aware_end_dates.append(
                end_of_day(make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d")))
            )
            start_of_day_aware_end_dates.append(
                start_of_day(
                    make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d"))
                )
            )
            payees_list = [
                record["employee_email_id"]
                for record in payees_in_plan_records
                if record["effective_start_date"] <= start_of_day_aware_end_dates[idx]
                and record["effective_end_date"] >= end_of_day_aware_end_dates[idx]
            ]
            end_date_plan_payees_map[end_date] = list(set(payees_list))
            all_payees.extend(end_date_plan_payees_map[end_date])
        all_payees = list(set(all_payees))

        logger.info(
            f"Begin Frozen Payroll Data: Fetch Variable Pay in Base Currency for {len(all_payees)} payees"
        )
        period_variable_pay_data = pd.DataFrame(
            FrozenPayrollDataAccessor(
                client_id
            ).get_variable_pay_in_base_currency_by_fiscal_year_for_payees(
                year, all_payees
            )
        )
        logger.info(
            f"End Frozen Payroll Data: Fetch Variable Pay in Base Currency for {len(all_payees)} payees"
        )
        if period_variable_pay_data.empty:
            variable_pay_map = {end_date: Decimal(0) for end_date in end_dates_list}
        else:
            period_variable_pay_data["variable_pay_in_base_currency"] = (
                period_variable_pay_data["variable_pay_in_base_currency"].apply(Decimal)
            )
            period_variable_pay_data["period_end_date"] = period_variable_pay_data[
                "period_end_date"
            ].dt.strftime("%Y-%m-%d")
            period_variable_pay_data = (
                period_variable_pay_data.groupby("period_end_date")[
                    "variable_pay_in_base_currency"
                ]
                .sum()
                .to_dict()
            )
            variable_pay_map = {
                end_date: (
                    period_variable_pay_data[end_date]
                    if end_date in period_variable_pay_data
                    else Decimal(0)
                )
                for end_date in end_dates_list
            }
        logger.info(
            "No. of plans: {}, No. of payees: {}".format(
                len(variable_pay_plan_ids),
                len(all_payees),
            )
        )

        commission_summary_monthly_payout_map = {
            end_date: {
                "commission_amount": Decimal(0),
                "variable_pay": Decimal(0),
            }
            for end_date in end_dates_list
        }

        result_monthly = {}
        logger.info("BEGIN: Get commission summary")
        report_object_id = "commission_summary"
        table_name = get_report_object_data_table_name(client_id, report_object_id)
        report_data = get_data_by_object_id_year_plan_id_payee_id(
            client_id,
            report_object_id,
            year,
            plan_ids,
            None,
            table_name,
            log_context=log_context,
        )
        latest_kd = report_data[0]["knowledge_begin_date"] if report_data else None
        payee_email_id_end_dates = set()
        none_plan_id_record_data = []  # For global adjustments
        variable_pay_draw_adj_added = set()
        for record in report_data:
            end_date = (record["row_key"].split("##::##")[3]).split(" ")[0]
            payee_email_id = record["row_key"].split("##::##")[1]
            plan_id = record["row_key"].split("##::##")[4]
            if plan_id != "None":
                payee_email_id_end_dates.add((payee_email_id, end_date))
            else:
                none_plan_id_record_data.append(record)
            if plan_id != "None" and end_date in commission_summary_monthly_payout_map:
                commission_summary_monthly_payout_map[end_date][
                    "commission_amount"
                ] += Decimal(record["data"]["commission_amount"]) + Decimal(
                    record["data"]["comm_adj_for_plan"]
                )
                # add draw adjustment amount and variable pay only once for a payee for a period
                if not (payee_email_id, end_date) in variable_pay_draw_adj_added:
                    variable_pay_draw_adj_added.add((payee_email_id, end_date))
                    commission_summary_monthly_payout_map[end_date][
                        "commission_amount"
                    ] += Decimal(record["data"]["draw_adjustment_amount"])
                    commission_summary_monthly_payout_map[end_date][
                        "variable_pay"
                    ] += Decimal(record["data"]["variable_pay"])
        # Handling global adjustments for only payee's present for that period
        for record in none_plan_id_record_data:
            payee_email_id = record["row_key"].split("##::##")[1]
            end_date = (record["row_key"].split("##::##")[3]).split(" ")[0]
            if (
                (
                    payee_email_id,
                    end_date,
                )
                in payee_email_id_end_dates
                and end_date in commission_summary_monthly_payout_map
            ):
                commission_summary_monthly_payout_map[end_date][
                    "commission_amount"
                ] += Decimal(record["data"]["comm_adj_for_plan"])

        for end_date in commission_summary_monthly_payout_map:
            if str(
                commission_summary_monthly_payout_map[end_date]["variable_pay"]
            ) != str(variable_pay_map[end_date]):
                commission_summary_monthly_payout_map[end_date]["variable_pay"] = (
                    variable_pay_map[end_date]
                )

        index = 1
        for value in commission_summary_monthly_payout_map.values():
            result_monthly[index] = {
                "commission_amount": str(round(value["commission_amount"], 2)),
                "variable_pay": str(round(value["variable_pay"], 2)),
            }
            index += 1
        result_quarterly = []

        # month_list is provided when commission_summary_payout details is required for current and previous months
        if month_list is None:
            result_quarterly = calculate_qtd_values(result_monthly)
        logger.info("END: Get commission summary")
        latest_knowledge_date_dict = latest_knowledge_date_for_report_objects(
            client_id=client_id,
            report_object_ids=report_object_id,
        )
        latest_kd = latest_knowledge_date_dict.get(report_object_id, None)
        output = {
            "commission_payouts": result_monthly,
            "commission_payouts_qtd": result_quarterly,
            "last_updated": (
                latest_kd.strftime("%d %b %Y, %I:%M %p") if latest_kd else None
            ),
        }
        return output
    except Exception as e:
        error_dict = {"traceback": traceback.print_exc()}
        logger.error(
            f"Error while getting commission summary for client - {client_id} year - {year} ",
            error_dict,
        )
        raise SQLParseError from e


def get_payee_commission_summary_payout_for_year(
    client_id, year, payee_id, request_id=None
):
    commission_summary_monthly_payout_map = {}
    variable_pay_map = {}
    log_context = {
        "client_id": client_id,
        "year": year,
        "payee_id": payee_id,
        "request_id": request_id,
    }
    logger = LogWithContext(log_context)

    report_object_id = "commission_summary"
    table_name = get_report_object_data_table_name(client_id, report_object_id)
    if not does_record_exist_by_object_id_payee_id(
        client_id,
        report_object_id,
        payee_id,
        table_name,
        log_context=log_context,
    ):
        logger.error(
            "END: COMMISSION PAYOUTS COUNT QUERY - COMMISSIONS NOT AVAILABLE FOR SELECTED USER"
        )
        return {
            "error": get_localized_message_utils(
                "COMMISSION_NOT_AVAILABLE_FOR_USER",
                client_id,
            )
        }

    try:
        client = get_client(client_id)
        client_start_month = client.fiscal_start_month - 1
        end_dates_list = get_end_dates_list(int(year), client_start_month)
        end_dates_list = [x.strftime("%Y-%m-%d") for x in end_dates_list]
        end_of_day_aware_end_dates = []
        variable_pay_map = {}
        for idx, end_date in enumerate(end_dates_list):
            end_of_day_aware_end_dates.append(
                end_of_day(make_aware_wrapper(datetime.strptime(end_date, "%Y-%m-%d")))
            )

        logger.info(
            f"Begin Frozen Payroll Data: Fetch Variable Pay in Payee Currency for {payee_id}"
        )
        period_variable_pay_data = FrozenPayrollDataAccessor(
            client_id
        ).get_variable_pay_in_payee_currency_by_fiscal_year_for_payee(year, payee_id)
        logger.info(
            f"End Frozen Payroll Data: Fetch Variable Pay in Payee Currency for {payee_id}"
        )

        variable_pay_map = {end_date: Decimal(0) for end_date in end_dates_list}
        for record in period_variable_pay_data:
            variable_pay_map[record["period_end_date"].strftime("%Y-%m-%d")] = Decimal(
                record["variable_pay_in_payee_currency"]
            )
        commission_summary_monthly_payout_map = {
            end_date: {
                "commission_amount": Decimal(0),
                "variable_pay": Decimal(0),
            }
            for end_date in end_dates_list
        }

        result_monthly = {}
        logger.info("BEGIN: Get commission summary")
        report_data = get_data_by_object_id_year_plan_id_payee_id(
            client_id,
            report_object_id,
            year,
            None,
            payee_id,
            table_name,
            log_context=log_context,
        )
        latest_kd = report_data[0]["knowledge_begin_date"] if report_data else None
        payee_email_id_end_dates = set()
        none_plan_id_record_data = []  # For global adjustments
        variable_pay_draw_adj_added = set()
        for record in report_data:
            end_date = (record["row_key"].split("##::##")[3]).split(" ")[0]
            payee_email_id = record["row_key"].split("##::##")[1]
            plan_id = record["row_key"].split("##::##")[4]
            if plan_id != "None":
                payee_email_id_end_dates.add((payee_email_id, end_date))
            else:
                none_plan_id_record_data.append(record)
            if plan_id != "None" and end_date in commission_summary_monthly_payout_map:
                commission_summary_monthly_payout_map[end_date][
                    "commission_amount"
                ] += Decimal(record["data"]["commission_amount"]) + Decimal(
                    record["data"]["comm_adj_for_plan"]
                )
                # add draw adjustment amount and variable pay only once for a payee for a period
                if not (payee_email_id, end_date) in variable_pay_draw_adj_added:
                    variable_pay_draw_adj_added.add((payee_email_id, end_date))
                    commission_summary_monthly_payout_map[end_date][
                        "commission_amount"
                    ] += Decimal(record["data"]["draw_adjustment_amount"])
                    commission_summary_monthly_payout_map[end_date][
                        "variable_pay"
                    ] += Decimal(record["data"]["variable_pay"])
        # Handling global adjustments for only payee's present for that period
        for record in none_plan_id_record_data:
            payee_email_id = record["row_key"].split("##::##")[1]
            end_date = (record["row_key"].split("##::##")[3]).split(" ")[0]
            if (
                (
                    payee_email_id,
                    end_date,
                )
                in payee_email_id_end_dates
                and end_date in commission_summary_monthly_payout_map
            ):
                commission_summary_monthly_payout_map[end_date][
                    "commission_amount"
                ] += Decimal(record["data"]["comm_adj_for_plan"])

        for end_date in commission_summary_monthly_payout_map:
            if str(
                commission_summary_monthly_payout_map[end_date]["variable_pay"]
            ) != str(variable_pay_map[end_date]):
                commission_summary_monthly_payout_map[end_date]["variable_pay"] = (
                    variable_pay_map[end_date]
                )

        index = 1
        fx_rate_map = PayoutStatusAccessor(client_id).get_fx_rate_for_payee_in_periods(
            payee_id, end_of_day_aware_end_dates
        )
        fx_rate_map = {
            entry["period_end_date"]: entry["fx_rate"] for entry in fx_rate_map
        }
        for idx, end_date in enumerate(end_dates_list):
            commission_amount = commission_summary_monthly_payout_map[end_date][
                "commission_amount"
            ]
            fx_rate = fx_rate_map.get(end_of_day_aware_end_dates[idx], None)
            if fx_rate:
                commission_amount = change_to_payee_currency_by_fx_rate(
                    commission_amount, fx_rate
                )
            result_monthly[index] = {
                "commission_amount": str(round(commission_amount, 2)),
                "variable_pay": str(
                    round(
                        commission_summary_monthly_payout_map[end_date]["variable_pay"],
                        2,
                    )
                ),
            }
            index += 1
        result_quarterly = []

        result_quarterly = calculate_qtd_values(result_monthly)
        logger.info("END: Get commission summary")
        if latest_kd is None:
            latest_knowledge_date_dict = latest_knowledge_date_for_report_objects(
                client_id=client_id,
                report_object_ids=report_object_id,
            )
            latest_kd = latest_knowledge_date_dict.get(report_object_id, None)

        output = {
            "commission_payouts": result_monthly,
            "commission_payouts_qtd": result_quarterly,
            "last_updated": (
                latest_kd.strftime("%d %b %Y, %I:%M %p") if latest_kd else None
            ),
        }
        return output
    except Exception as e:
        error_dict = {"traceback": traceback.print_exc()}
        logger.error(
            f"Error while getting commission summary for client - {client_id} year - {year} payee - {payee_id} ",
            error_dict,
        )
        raise SQLParseError from e


def get_commission_payout_for_curr_and_prev_months(
    client_id, plan_ids, request_id=None
):
    month_list = get_last_2_months_end_date()
    client = get_client(client_id)
    fiscal_year = get_fiscal_year(client.fiscal_start_month)

    result = get_commission_summary_payout_for_year(
        plan_ids, client_id, fiscal_year, month_list, request_id=request_id
    )
    output = {
        "current": {"commission_amount": "0.00", "variable_pay": "0.00"},
        "previous": {"commission_amount": "0.00", "variable_pay": "0.00"},
    }
    if result.get("commission_payouts", {}):
        output = {
            "current": result["commission_payouts"][1],
            "previous": result["commission_payouts"][2],
        }
    logger = logging.getLogger(__name__)
    logger.info("Last 2 month Payout: client id %s %s", client_id, output)
    return output


def get_last_2_months_end_date(curr_month=None, curr_year=None):
    if curr_month is None:
        curr_month = date.today().month
    if curr_year is None:
        curr_year = date.today().year
    current_month_date = get_end_date_of_month(curr_month, curr_year).strftime(
        "%Y-%m-%d"
    )

    if curr_month == 1:
        prev_month_date = get_end_date_of_month(12, curr_year - 1).strftime("%Y-%m-%d")
    else:
        prev_month_date = get_end_date_of_month(curr_month - 1, curr_year).strftime(
            "%Y-%m-%d"
        )
    month_list = []
    month_list.append(current_month_date)
    month_list.append(prev_month_date)
    return month_list


def _write_to_snowflake(
    client_id, ked=None, fiscal_year=None, report_records=None, table_name=None
):
    logger = logging.getLogger(__name__)
    logger.info("Write to snowflake: COMMISSION SUMMARY REPORT")
    invalidate_query = f"""
        update {table_name}
        set knowledge_end_date = '{ked}'
        where client_id='{client_id}'
        and knowledge_end_date is null
        and object_id='commission_summary'
        and row_key like '{str(fiscal_year)}%'
        """

    # TODO Always ensure the test table_name is changed when deployment
    with snowflake_writer_handler(client_id=client_id) as snowpark_session:
        snowflake_run_sql(
            client_id=client_id,
            query=invalidate_query,
            snowpark_session=snowpark_session,
        )

        snowflake_insert_records(
            table_name=table_name,
            records=report_records,
            snowpark_session=snowpark_session,
            client_id=client_id,
        )


def construct_frozen_payroll_data_record_from_sql_result(
    sql_res, end_date, client_base_currency
):
    records = []
    if sql_res:
        for payroll in sql_res:
            payee_email = payroll[0]
            variable_pay_payee = payroll[1]
            payee_currency = payroll[2]
            date_key = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y%m%d")
            fx_rate = None

            fx_values = payroll[4]
            if fx_values:
                fx_values = json.loads(fx_values)
                for value in fx_values:
                    if value["currency"] == payee_currency:
                        fx_rate = (
                            value["fx_rates"][date_key]
                            if date_key in value["fx_rates"]
                            else None
                        )
            variable_pay_base = (
                round(variable_pay_payee / Decimal(fx_rate), 6)
                if fx_rate
                else (
                    variable_pay_payee
                    if client_base_currency != payee_currency
                    else variable_pay_payee
                )
            )
            variable_pay_base_per_month = variable_pay_base / 12
            variable_pay_payee_per_month = variable_pay_payee / 12
            records.append(
                (
                    payee_email,
                    datetime.strptime(end_date, "%Y-%m-%d"),
                    variable_pay_base_per_month,
                    variable_pay_payee_per_month,
                )
            )
    return records


def fetch_variable_pay_for_payees_with_commission(
    client_id,
    end_of_day_aware_end_date,
    client_base_currency,
    payees_in_plan,
    logger,
):
    # get distinct payee email ids from PlanPayees for end date (as result A)
    # get sec kd for all payees from result A: (as result X) - for payroll_end_date
    # get variable pay of payees who have a commission lock by payroll end date: EmployeePayroll inner join result X (as result Z)
    # get corresponding fx rate by payroll end date: ClientConfig right join result Z
    if not payees_in_plan:
        return []
    payees_in_plan = tuple(payees_in_plan)
    formatted_end_of_day = end_of_day_aware_end_date.strftime(
        format="%Y-%m-%d %H:%M:%S.%f%z"
    )
    raw_sql_query = """--frozen_payroll_data_etl
       select
            "payroll_end_date"."employee_email_id",
            "payroll_end_date"."variable_pay_in_pay_currency",
            "payroll_end_date"."pay_currency",
            "payroll_end_date"."payout_frequency",
            "client_config"."value" "fx_value"
        from
            "client_config"
        right join (
            select
                "employee_payroll_details"."employee_email_id",
                "commission_kd"."secondary_kd",
                "employee_payroll_details"."variable_pay" "variable_pay_in_pay_currency",
                "employee_payroll_details"."pay_currency",
                "employee_payroll_details"."payout_frequency"
            from
                "employee_payroll_details"
            inner join (
                    select
                        "csk"."payee_email_id",
                        "csk"."sec_kd" as "secondary_kd"
                    from (
                        select
                            "commission_sec_kd"."payee_email_id",
                            "commission_sec_kd"."sec_kd"
                        from
                            "commission_sec_kd"
                        where
                            "commission_sec_kd"."client_id" =  %(client_id)s
                            and not "commission_sec_kd"."is_deleted"
                            and "commission_sec_kd"."knowledge_end_date" is null
                            and "commission_sec_kd"."period_end_date" = %(formatted_end_of_day)s
                    ) as "csk"
                    inner join (
                        select
                            "payout_status"."payee_email_id"
                        from
                            "payout_status"
                        where
                            "payout_status"."client_id" = %(client_id)s
                            and not "payout_status"."is_deleted"
                            and "payout_status"."knowledge_end_date" is null
                            and "payout_status"."period_end_date" = %(formatted_end_of_day)s
                            and "payout_status"."payee_email_id" in %(payees_in_plan)s
                        ) as "payees_by_end_date"
                    on
                    "csk"."payee_email_id" = "payees_by_end_date"."payee_email_id"
                ) "commission_kd" 
                on (
                    "employee_payroll_details"."client_id" = %(client_id)s
                    and not "employee_payroll_details"."is_deleted"
                    and (
                            "employee_payroll_details"."knowledge_begin_date" <= "commission_kd"."secondary_kd"
                            and 
                            "employee_payroll_details"."effective_start_date" <= %(formatted_end_of_day)s
                    )
                    and ("employee_payroll_details"."knowledge_end_date" > "commission_kd"."secondary_kd"
                        or "employee_payroll_details"."knowledge_end_date" is null)
                    and ("employee_payroll_details"."effective_end_date" >= %(formatted_end_of_day)s
                        or "employee_payroll_details"."effective_end_date" is null)
                    and "employee_payroll_details"."employee_email_id" = "commission_kd"."payee_email_id"
                )
            ) "payroll_end_date"
        on (
            "client_config"."client_id" = %(client_id)s
            and "client_config"."knowledge_begin_date" <= "payroll_end_date"."secondary_kd"
            and (
                        "client_config"."knowledge_end_date" > "payroll_end_date"."secondary_kd"
                or
                        "client_config"."knowledge_end_date" is null
                )
            and "client_config"."type" = 'FxRate'
        )
    """
    params = {
        "client_id": client_id,
        "formatted_end_of_day": formatted_end_of_day,
        "payees_in_plan": payees_in_plan,
    }
    logger.info(
        f"SQL Query to calculate payroll for payees with commission\n{raw_sql_query}"
    )
    with connection.cursor() as cursor:
        cursor.execute(raw_sql_query, params)
        result: list = cursor.fetchall()
    logger.info("Executed query to calculate payroll for payees with commission.")
    return construct_frozen_payroll_data_record_from_sql_result(
        result, end_of_day_aware_end_date.strftime("%Y-%m-%d"), client_base_currency
    )


def fetch_variable_pay_for_payees_without_commission(
    client_id,
    end_of_day_aware_end_date,
    payees_without_commission,
    client_base_currency,
):
    payrolls = EmployeePayrollAccessor(
        client_id
    ).get_variable_pay_record_for_given_date_and_emails_first(
        datetime.now(),
        end_of_day_aware_end_date,
        payees_without_commission,
    )

    fx_values = ClientConfigAccessor(client_id).get_fx_rates()
    fx_values = fx_values.value if fx_values else None
    end_date = end_of_day_aware_end_date.strftime("%Y-%m-%d")

    records = []
    for payroll in payrolls:
        payee_email = payroll["employee_email_id"]
        variable_pay_payee = payroll["variable_pay"]
        payee_currency = payroll["pay_currency"]
        date_key = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y%m%d")
        fx_rate = None
        if fx_values:
            for value in fx_values:
                if value["currency"] == payee_currency:
                    fx_rate = (
                        value["fx_rates"][date_key]
                        if date_key in value["fx_rates"]
                        else None
                    )
        variable_pay_base = (
            round(variable_pay_payee / Decimal(fx_rate), 6)
            if fx_rate
            else (
                variable_pay_payee
                if client_base_currency != payee_currency
                else variable_pay_payee
            )
        )
        variable_pay_base_per_month = variable_pay_base / 12
        variable_pay_payee_per_month = variable_pay_payee / 12
        records.append(
            (
                payee_email,
                datetime.strptime(end_date, "%Y-%m-%d"),
                variable_pay_base_per_month,
                variable_pay_payee_per_month,
            )
        )
    return records
