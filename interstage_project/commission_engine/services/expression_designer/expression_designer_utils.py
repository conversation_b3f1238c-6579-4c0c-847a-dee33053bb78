"""
Expression Designer Utils module.
"""

from spm.services.localization_services import get_localized_message_service

from .data_conversion_models import Datatypes

default_functions = [
    "SUM",
    "SUMIF",
    "MIN",
    "MAX",
    "AVG",
    "COUNTIF",
    "CurrentPayoutPeriod",
    "CountNotNull",
    "GetDate",
    "DistinctCount",
    "IsEmpty",
    "IsNotEmpty",
    "Contains",
    "NotContains",
    "DATEDIFF",
    "Lower",
    "Concat",
    "Find",
    "Round",
    "RoundUp",
    "RoundDown",
    "Config",
    "QuotaAttainment",
    "QuotaErosion",
    "Quota",
    "GetUserProperty",
]

cpq_functions = [
    "GetLineItemValue",
    "MIN",
    "MAX",
    "AVG",
    "SUM",
    "IsEmpty",
    "IsNotEmpty",
    "Contains",
    "NotContains",
    "StartsWith",
    "EndsWith",
    "DateIsIn",
]

tier_functions = ["TieredValue"]

quota_functions = ["TieredValue", "TieredPercentage"]

summation_functions = [
    "SUM",
    "SUMIF",
    "MIN",
    "MAX",
    "AVG",
    "COUNTIF",
    "CountNotNull",
    "DistinctCount",
    "CurrentPayoutPeriod",
    "Config",
    "QuotaAttainment",
    "QuotaErosion",
    "Quota",
    "GetUserProperty",
]

schedule_criteria_functions = [
    "CurrentPayoutPeriod",
    "GetDate",
    "IsEmpty",
    "IsNotEmpty",
    "Contains",
    "NotContains",
    "DATEDIFF",
    "Lower",
    "Concat",
    "Find",
    "Round",
    "RoundUp",
    "RoundDown",
    "Config",
]

team_extra_functions = [
    "TEAM-SUM",
    "TEAM-COUNT",
    "TEAM-MIN",
    "TEAM-MAX",
    "TEAM-AVG",
    "TEAM-SUMIF",
    "TEAM-COUNTIF",
    "TEAM-QuotaAttainment",
    "TEAM-QuotaErosion",
    "TEAM-Quota",
    "TEAM-COUNT-NOT-NULL",
    "TEAM-DISTINCT-COUNT",
]

calculated_fields_functions = [
    "DATEDIFF",
    "StartDate",
    "LastDate",
    "DateAdd",
    "Timezone",
    "GetDate",
    "Round",
    "RoundUp",
    "RoundDown",
    "IsEmpty",
    "IsNotEmpty",
    "Contains",
    "NotContains",
    "Trim",
    "Lower",
    "Concat",
    "Coalesce",
    "Len",
    "Left",
    "Right",
    "Mid",
    "Find",
]

window_functions = [
    "Rank",
    "Hierarchy",
    "Rolling",
]

advanced_filter_transformations_functions = [
    "DATEDIFF",
    "DateAdd",
    "Round",
    "RoundUp",
    "RoundDown",
    "IsEmpty",
    "IsNotEmpty",
    "Contains",
    "NotContains",
    "Lower",
    "Len",
    "Find",
]

ui_filter_transformation_functions = [
    "IsEmpty",
    "IsNotEmpty",
    "Contains",
    "NotContains",
    "StartsWith",
    "EndsWith",
    "DateIsIn",
]


function_label_map = {
    "Hierarchy": "GenerateHierarchy",
    "GetNthLevelNode": "GetValueFromHierarchy",
    "Rolling": "RollingSum",
    "Timezone": "ConvertTimezone",
}

function_datatype_map = {
    "AVG": Datatypes.INTEGER.value,
    "Concat": Datatypes.STRING.value,
    "Contains": Datatypes.BOOLEAN.value,
    "CountNotNull": Datatypes.INTEGER.value,
    "COUNTIF": Datatypes.INTEGER.value,
    "CurrentPayoutPeriod": Datatypes.INTEGER.value,
    "DateAdd": Datatypes.DATE.value,
    "DATEDIFF": Datatypes.INTEGER.value,
    "DistinctCount": Datatypes.INTEGER.value,
    "Find": Datatypes.INTEGER.value,
    "GetDate": Datatypes.INTEGER.value,
    "IsEmpty": Datatypes.BOOLEAN.value,
    "IsNotEmpty": Datatypes.BOOLEAN.value,
    "LastDate": Datatypes.DATE.value,
    "Len": Datatypes.INTEGER.value,
    "Lower": Datatypes.STRING.value,
    "MAX": Datatypes.INTEGER.value,
    "MIN": Datatypes.INTEGER.value,
    "Mid": Datatypes.STRING.value,
    "NotContains": Datatypes.BOOLEAN.value,
    "Quota": Datatypes.INTEGER.value,
    "QuotaAttainment": Datatypes.INTEGER.value,
    "QuotaErosion": Datatypes.INTEGER.value,
    "Rank": Datatypes.INTEGER.value,
    "Left": Datatypes.STRING.value,
    "Right": Datatypes.STRING.value,
    "Rolling": Datatypes.INTEGER.value,
    "Round": Datatypes.INTEGER.value,
    "RoundDown": Datatypes.INTEGER.value,
    "RoundUp": Datatypes.INTEGER.value,
    "StartDate": Datatypes.DATE.value,
    "SUM": Datatypes.INTEGER.value,
    "SUMIF": Datatypes.INTEGER.value,
    "TEAM-AVG": Datatypes.INTEGER.value,
    "TEAM-COUNT": Datatypes.INTEGER.value,
    "TEAM-COUNT-NOT-NULL": Datatypes.INTEGER.value,
    "TEAM-COUNTIF": Datatypes.INTEGER.value,
    "TEAM-DISTINCT-COUNT": Datatypes.INTEGER.value,
    "TEAM-MAX": Datatypes.INTEGER.value,
    "TEAM-MIN": Datatypes.INTEGER.value,
    "TEAM-Quota": Datatypes.INTEGER.value,
    "TEAM-QuotaAttainment": Datatypes.INTEGER.value,
    "TEAM-QuotaErosion": Datatypes.INTEGER.value,
    "TEAM-SUM": Datatypes.INTEGER.value,
    "TEAM-SUMIF": Datatypes.INTEGER.value,
    "TieredPercentage": Datatypes.PERCENTAGE.value,
    "TieredValue": Datatypes.INTEGER.value,
    "Timezone": Datatypes.DATE.value,
    "Trim": Datatypes.STRING.value,
    "Hierarchy": Datatypes.HIERARCHY.value,
    "GetNthLevelNode": Datatypes.STRING.value,
    "StartsWith": Datatypes.BOOLEAN.value,
    "EndsWith": Datatypes.BOOLEAN.value,
    "DateIsIn": Datatypes.BOOLEAN.value,
}


def get_function_details_map(client_id):
    """
    This function returns the localized expression function details that contains information on each function used in Guides

    Args:
        client_id : Client id to apply localization

    Returns:
        Returns a dictionary containing information about each expression function
        expression_function -> label (optional), description, input_format, example
        input_format -> label, columns
        columns -> name, description
    """
    localized_quota_value = get_localized_message_service("$QUOTA", client_id)
    localized_quota_lc_value = get_localized_message_service("$QUOTA_lc", client_id)
    localized_quota_erosion_value = get_localized_message_service(
        "$QUOTA_EROSION", client_id
    )

    function_details_map = {
        "DATEDIFF": {
            "description": "Returns the date difference between the values of two date columns based on the chosen temporal units (e.g., day, month, half-year, year, quarter).",
            "input_format": {
                "label": "DateDiff(Date column 1, Date column 2, Temporal unit)",
                "columns": [
                    {
                        "name": "Date column 1",
                        "description": "Any Date field from the datasheet",
                    },
                    {
                        "name": "Date column 2",
                        "description": "Any Date field from the datasheet",
                    },
                    {
                        "name": "Temporal unit",
                        "description": "The period/duration to be added. Eg: Day, Month, Year",
                    },
                ],
            },
            "example": "DateDiff(Contract Start Date, Contract End Date, Month)",
        },
        "SUM": {
            "description": "Returns the sum of the values in a selected numeric column field.",
            "input_format": {
                "label": "Sum(Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    }
                ],
            },
            "example": "Sum(Opportunity Amount)",
        },
        "SUMIF": {
            "description": "Returns the sum of the values in a selected numeric column field based on the conditions provided.",
            "input_format": {
                "label": "SumIF(Integer Column, IF conditions)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    },
                    {
                        "name": "Condition",
                        "description": "The condition that the column fields are being checked against.",
                    },
                ],
            },
            "example": "SumIF(Opportunity Amount, Opportunity stage == 'Closed Won')",
        },
        "MIN": {
            "description": "Returns the minimum value present in the chosen column.",
            "input_format": {
                "label": "Min(Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any integer column in the datasheet",
                    }
                ],
            },
            "example": "Min(Invoice amount)",
        },
        "MAX": {
            "description": "Returns the maximum value present in the chosen column.",
            "input_format": {
                "label": "Max(Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any integer column in the datasheet",
                    }
                ],
            },
            "example": "Max(Invoice amount)",
        },
        "AVG": {
            "description": "Returns the average value of the selected column.",
            "input_format": {
                "label": "AVG (Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any integer column that’s part of the datasheet.",
                    }
                ],
            },
            "example": "AVG (#Deals per month)",
        },
        "COUNTIF": {
            "description": "Returns the number of records for the chosen column that satisfies the conditions specified.",
            "input_format": {
                "label": "Count(Column name) IF (condition)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any Column Field in the datasheet that would be counted through, to check if the condition is satisfied.",
                    },
                    {
                        "name": "Condition",
                        "description": "The condition that the column fields are being tested against.",
                    },
                ],
            },
            "example": "Count(Opportunity Id) IF (Opp.Stage == Closed-Won)",
        },
        "CurrentPayoutPeriod": {
            "description": "Returns the integer value that corresponds to the selected payout period.",
            "input_format": {
                "label": "CurrentPayoutPeriod(Period)",
                "columns": [
                    {
                        "name": "Period",
                        "description": "Any temporal period. Eg: Month, Quarter, Half Year, Year.",
                    }
                ],
            },
            "example": "CurrentPayoutPeriod(Month)",
        },
        "CountNotNull": {
            "description": "Returns the number of non empty records for the selected column.",
            "input_format": {
                "label": "CountNotNull(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any Column Field from the datasheet that would be counted through, to check for non empty fields.",
                    }
                ],
            },
            "example": "CountNotNull(Invoice Id)",
        },
        "GetDate": {
            "description": "Returns the day, quarter, month, or half year of the record in the selected date column based on fiscal or calendar year.",
            "input_format": {
                "label": "Getdate(Date column, Temporal unit, Calendar/Fiscal year)",
                "columns": [
                    {
                        "name": "Date column",
                        "description": "Any date column from the datasheet that is considered",
                    },
                    {
                        "name": "Temporal unit",
                        "description": "The period/duration to be added. Eg: Day, Month, Year",
                    },
                    {
                        "name": "Calendar/Fiscal Year",
                        "description": "The Fiscal year of the particular business/scenario.",
                    },
                ],
            },
            "example": "Getdate(Contract Start Date,Month, Fiscal Year)",
        },
        "DistinctCount": {
            "description": "Returns the number of distinct values in a column.",
            "input_format": {
                "label": "DistinctCount(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any column field in the datasheet that is to be counted on.",
                    }
                ],
            },
            "example": "DistinctCount(Opportunity Id)",
        },
        "IsEmpty": {
            "description": "Returns a boolean indicating whether the selected field contains empty records.",
            "input_format": {
                "label": "IsEmpty(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any column field in the datasheet that is to be searched on.",
                    }
                ],
            },
            "example": "IsEmpty(Invoice Id)",
        },
        "IsNotEmpty": {
            "description": "Returns a boolean expression indicating whether the selected field contains non-empty records.",
            "input_format": {
                "label": "IsNotEmpty(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any column field in the datasheet that is to be searched on.",
                    }
                ],
            },
            "example": "IsNotEmpty(Invoice Id)",
        },
        "Contains": {
            "description": "Returns a boolean indicating whether the selected column contains the query text or if similar data is present across the two selected columns.",
            "input_format": {
                "label": "Contains(String Column, Query text)",
                "columns": [
                    {
                        "name": "String Column",
                        "description": "Any string column that’s part of the selected datasheet.",
                    },
                    {
                        "name": "Query text Input",
                        "description": "Constant/Datasheet field",
                    },
                    {
                        "name": "Query text",
                        "description": "String that is to be checked for.",
                    },
                ],
            },
            "example": "Contains(Opportunity Name,Constant, USA)",
        },
        "NotContains": {
            "description": "Returns boolean values to verify the absence of query text in a chosen column and to check for the lack of similar data across two different columns.",
            "input_format": {
                "label": "NotContains(Column name, Query text)",
                "columns": [
                    {
                        "name": "String Column",
                        "description": "Any string column that’s part of the selected datasheet.",
                    },
                    {
                        "name": "Query text Input",
                        "description": "Constant/Datasheet field",
                    },
                    {
                        "name": "Query text",
                        "description": "String that is to be checked for.",
                    },
                ],
            },
            "example": "NotContains(Opportunity Owner P&L, US)",
        },
        "Lower": {
            "description": "Returns the lowercase format of any string data type field.",
            "input_format": {
                "label": "Lower(String column name)",
                "columns": [
                    {
                        "name": "String column",
                        "description": "Any column field in the datasheet",
                    }
                ],
            },
            "example": "Lower(P&L TEAM)",
        },
        "Concat": {
            "description": "Returns a new entity formed by combining values from two or more columns along with the preferred delimiter.",
            "input_format": {
                "label": "Concat(Column 1, Column 2,..)",
                "columns": [
                    {
                        "name": "Column *",
                        "description": "Any column within the datasheet, regardless of its datatype, can be chosen.",
                    }
                ],
            },
            "example": "Concat(Invoice Id, Invoice Date, Invoice Status)",
        },
        "Find": {
            "description": "Returns the position (integer) of the starting letter of the entered query text.",
            "input_format": {
                "label": "Find(Column name, Query text)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any column field in the datasheet that is to be searched on.",
                    },
                    {
                        "name": "Find text Input",
                        "description": "Constant/Datasheet field",
                    },
                    {
                        "name": "Query text",
                        "description": "String that is to be checked for.",
                    },
                ],
            },
            "example": "Find(Opportunity Name, US)",
        },
        "Round": {
            "description": "Returns the rounded values of the input column based on the specified number of decimal places.",
            "input_format": {
                "label": "Round(Integer Column, Number of decimal places required)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical column from the datasheet",
                    },
                    {
                        "name": "Number of decimal places required",
                        "description": "The number of decimal places to which the numerical value is to be rounded to.",
                    },
                ],
            },
            "example": f"Round({localized_quota_value} Attainment, 4)",
        },
        "RoundUp": {
            "description": "Returns the input number with the decimal places rounded up to the specified number of decimal places",
            "input_format": {
                "label": "RoundUp(Integer Column, Number of decimal places required)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical column from the datasheet",
                    },
                    {
                        "name": "Number of decimal places required",
                        "description": "The number of decimal places to which the numerical value is to be rounded to.",
                    },
                ],
            },
            "example": f"Round({localized_quota_value} Attainment, 4)",
        },
        "RoundDown": {
            "description": "Returns the input number with the decimal places rounded down to the specified number of decimal places",
            "input_format": {
                "label": "RoundDown(Integer Column, Number of decimal places required)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical column from the datasheet",
                    },
                    {
                        "name": "Number of decimal places required",
                        "description": "The number of decimal places to which the numerical value is to be rounded to.",
                    },
                ],
            },
            "example": f"Round({localized_quota_value} Attainment, 4)",
        },
        "Config": {
            "description": "Returns the value of a Dynamic field set for the payees in the commission plan.",
            "input_format": {
                "label": "Config(Column *)",
                "columns": [
                    {
                        "name": "Column *",
                        "description": "You can refer to any column created as a dynamic field.",
                    }
                ],
            },
            "example": "Config(BCR Rate)",
        },
        "QuotaAttainment": {
            "label": f"{localized_quota_value}Attainment",
            "description": f"Returns the percentage value of cumulative earnings for a given {localized_quota_lc_value} category. It is the ratio of earnings to {localized_quota_lc_value}, converted to a percentage.",
            "input_format": {
                "label": f"{localized_quota_value}Attainment({localized_quota_value} category name, Lookback period)",
                "columns": [
                    {
                        "name": f"{localized_quota_value} category name",
                        "description": f"Can be any applicable {localized_quota_lc_value} category.",
                    },
                    {
                        "name": "Lookback period",
                        "description": f"The value to obtain the {localized_quota_lc_value} value from a past timeframe",
                    },
                ],
            },
            "example": f"{localized_quota_value}Attainment(Account Executive {localized_quota_value}, 1)",
        },
        "QuotaErosion": {
            "label": f"{localized_quota_erosion_value}",
            "description": f"Returns the value eroded against the chosen {localized_quota_lc_value} category, i.e., the cumulative sum of earnings depleted from that category.",
            "input_format": {
                "label": f"{localized_quota_erosion_value}({localized_quota_value} category name, Lookback period)",
                "columns": [
                    {
                        "name": f"{localized_quota_value} category name",
                        "description": f"Can be any applicable {localized_quota_lc_value} category.",
                    },
                    {
                        "name": "Lookback period",
                        "description": f"The value to obtain the {localized_quota_lc_value} value from a past timeframe",
                    },
                ],
            },
            "example": f"{localized_quota_erosion_value}(Account Executive {localized_quota_value}, 1)",
        },
        "Quota": {
            "label": f"{localized_quota_value}",
            "description": f"Returns the {localized_quota_lc_value} value of the selected {localized_quota_lc_value} category.",
            "input_format": {
                "label": f"{localized_quota_value}({localized_quota_value} category name, Lookback period)",
                "columns": [
                    {
                        "name": f"{localized_quota_value} category name",
                        "description": f"Can be any applicable {localized_quota_lc_value} category.",
                    },
                    {
                        "name": "Lookback period",
                        "description": f"The value to obtain the {localized_quota_lc_value} value from a past timeframe",
                    },
                ],
            },
            "example": f"{localized_quota_value}(Account Executive {localized_quota_value}, 1)",
        },
        "TieredValue": {
            "description": "Provides the maximum tiered value of quota retirement, either the value greater than the lowest tier if Every Row configuration is chosen or the entire quota retirement when Overall Sum is selected.",
            "input_format": {
                "label": "TieredValue()",
                "columns": [],
            },
            "example": "2500",
        },
        "TieredPercentage": {
            "description": "Provides the maximum tiered percentage of quota attainment, either the value greater than the lowest tier if Every Row configuration is chosen or the entire quota attainment when Overall Sum is selected.",
            "input_format": {
                "label": "TieredPercentage()",
                "columns": [],
            },
            "example": "31.50%",
        },
        "TEAM-SUM": {
            "description": "Returns the sum of the values in a selected numeric column field for team-based calculations.",
            "input_format": {
                "label": "TEAM-SUM(Integer column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    }
                ],
            },
            "example": "TEAM-SUM(Opportunity Amount)",
        },
        "TEAM-COUNT": {
            "description": "Returns the total number of values in a selected column for team-based calculations.",
            "input_format": {
                "label": "TEAM-COUNT(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any field from the datasheet",
                    }
                ],
            },
            "example": "TEAM-COUNT(Opportunity Id)",
        },
        "TEAM-MIN": {
            "description": "Returns the minimum value present in the chosen column for team-based calculations.",
            "input_format": {
                "label": "TEAM-MIN(Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    }
                ],
            },
            "example": "TEAM-MIN(Opportunity Amount)",
        },
        "TEAM-MAX": {
            "description": "Returns the maximum value present in the chosen column for team-based calculations.",
            "input_format": {
                "label": "TEAM-MAX(Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    }
                ],
            },
            "example": "TEAM-MAX(Opportunity Amount)",
        },
        "TEAM-AVG": {
            "description": "Returns the average value of the selected column for team-based calculations.",
            "input_format": {
                "label": "Team-AVG(Integer Column)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    }
                ],
            },
            "example": "Team-AVG(#Deals per month)",
        },
        "TEAM-SUMIF": {
            "description": "Returns the sum of the values in a selected numeric column field based on the conditions provided for team-based calculations.",
            "input_format": {
                "label": "TEAM-SUM(Integer column, IF condition)",
                "columns": [
                    {
                        "name": "Integer Column",
                        "description": "Any numerical field from the datasheet",
                    },
                    {
                        "name": "IF conditions",
                        "description": "The conditions to be checked over the Numerical column",
                    },
                ],
            },
            "example": "TEAM-SUM(Opportunity Amount, Opportunity Stage == Closed-Won)",
        },
        "TEAM-COUNTIF": {
            "description": "Returns the number of records for the chosen column that satisfies the conditions specified for team-based calculations.",
            "input_format": {
                "label": "TEAM-COUNT(Column name, IF - Conditional Expression)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any field from the datasheet",
                    },
                    {
                        "name": "Condition",
                        "description": "The condition that the column fields are being checked against.",
                    },
                ],
            },
            "example": "TEAM-COUNT(Opportunity Id, Opportunity Stage = Closed-Won)",
        },
        "TEAM-QuotaAttainment": {
            "label": f"TEAM-{localized_quota_value}Attainment",
            "description": f"Returns the percentage value of cumulative earnings for a given {localized_quota_lc_value} category. It is the ratio of earnings to {localized_quota_lc_value}, converted to a percentage for team-based calculations.",
            "input_format": {
                "label": f"Team-{localized_quota_value}Attainment({localized_quota_value} category name, Lookback period)",
                "columns": [
                    {
                        "name": f"{localized_quota_value} category name",
                        "description": f"Can be any {localized_quota_lc_value} category applicable.",
                    },
                    {
                        "name": "Lookback period",
                        "description": f"The value to obtain the {localized_quota_lc_value} value from a past timeframe",
                    },
                ],
            },
            "example": f"Team-{localized_quota_value}Attainment(Account Executive {localized_quota_value}, 1)",
        },
        "TEAM-QuotaErosion": {
            "label": f"TEAM-{localized_quota_erosion_value}",
            "description": f"Returns the value eroded against the chosen {localized_quota_lc_value} category, i.e., the cumulative sum of earnings depleted from that category for team-based calculations.",
            "input_format": {
                "label": f"Team-{localized_quota_erosion_value}({localized_quota_value} category name, Lookback period)",
                "columns": [
                    {
                        "name": f"{localized_quota_value} category name",
                        "description": f"Can be any {localized_quota_lc_value} category applicable.",
                    },
                    {
                        "name": "Lookback period",
                        "description": f"The value to obtain the {localized_quota_lc_value} value from a past timeframe",
                    },
                ],
            },
            "example": f"Team-{localized_quota_erosion_value}(Account Executive {localized_quota_value}, 1)",
        },
        "TEAM-Quota": {
            "label": f"TEAM-{localized_quota_value}",
            "description": f"Returns the {localized_quota_lc_value} value of the selected {localized_quota_lc_value} category for team-based calculations.",
            "input_format": {
                "label": f"Team-{localized_quota_value}({localized_quota_value} category name, Lookback period)",
                "columns": [
                    {
                        "name": f"{localized_quota_value} category name",
                        "description": f"Can be any {localized_quota_lc_value} category applicable.",
                    },
                    {
                        "name": "Lookback period",
                        "description": f"The value to obtain the {localized_quota_lc_value} value from a past timeframe",
                    },
                ],
            },
            "example": f"TEAM-{localized_quota_value}(Account Executive {localized_quota_value}, 1)",
        },
        "TEAM-COUNT-NOT-NULL": {
            "description": "Returns the number of non empty records for the selected column for team-based calculations.",
            "input_format": {
                "label": "TEAM-COUNT-NOT-NULL(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any field from the datasheet",
                    }
                ],
            },
            "example": "TEAM-COUNT-NOT-NULL(Opportunity Id)",
        },
        "TEAM-DISTINCT-COUNT": {
            "description": "Returns the number of distinct values in a column for team-based calculations.",
            "input_format": {
                "label": "TEAM-DISTINCT-COUNT(Column name)",
                "columns": [
                    {
                        "name": "Column name",
                        "description": "Any field from the datasheet",
                    }
                ],
            },
            "example": "TEAM-DISTINCT-COUNT(Opportunity Id)",
        },
        "GetUserProperty": {
            "description": "Returns the value of a User field for a payee as of a specific date from the function",
            "input_format": {
                "label": "GetUserProperty(For whom, As on Date, User Field)",
                "columns": [
                    {
                        "name": "For whom",
                        "description": "Any email column from the datasheet that is considered or choosing 'For whom the commission is calculated' shall reference the user for whom the commission is being calculated",
                    },
                    {
                        "name": "As on date",
                        "description": "Any date column from the datasheet that is considered",
                    },
                    {"name": "User Field", "description": "Any user object Field"},
                ],
            },
            "example": "GetUserProperty(Opp.Owner, Close Date, Currency)",
        },
    }

    return function_details_map


constants_details = [
    {
        "key": "Integer",
        "label": "Integer",
        "description": "Use Integer to enter whole numbers (positive or negative). Enter the number in the expression box. Choose Number from the autosuggestion.",
        "example": "60",
        "meta": {
            "output_data_type": "Integer",
        },
    },
    {
        "key": "String",
        "label": "String",
        "description": "Use String to enter text data. Enter the text in the expression box without any quotes. Choose String from the autosuggestion.",
        "example": "Closed-Won",
        "meta": {
            "output_data_type": "String",
        },
    },
    {
        "key": "IntArray",
        "label": "IntArray",
        "description": "Use IntArry to enter an array of numerical values. Enter the values separated by a comma. Use autosuggestion to quickly choose the array type (text or number) based on your input.",
        "example": "[5, 10, 25, 50]",
        "meta": {
            "output_data_type": "Integer",
        },
    },
    {
        "key": "StringArray",
        "label": "StringArray",
        "description": "Use StringArray to enter an array of text data. Enter the values separated by a comm. Ensure that you enter the text in double quotes (” “). Use autosuggestion to quickly choose the array type (text or number) based on your input.",
        "example": "[“Evaluation”, “Negotiation”, “Closed Won”]",
        "meta": {
            "output_data_type": "String",
        },
    },
    {
        "key": "Percentage",
        "label": "Percentage",
        "description": "Use Percentage to enter a percentage value. Enter a number followed by the % sign, and click on the percentage option in the autosuggestion.",
        "example": "25%",
        "meta": {
            "output_data_type": "Percentage",
        },
    },
]
