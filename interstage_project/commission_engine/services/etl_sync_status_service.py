import datetime
import logging
from typing import Any, Dict, List
from uuid import UUID

import pydash
from celery import chain, shared_task
from django.db.models import Q
from django.utils.timezone import make_aware
from pydantic import BaseModel, Field
from sendgrid import Email, Mail

import interstage_project.utils as iputils


class FailedManualJob(BaseModel):
    """
    Pydantic model representing a failed manual job
    """

    client_id: int
    task: str
    sync_status: str
    sync_start_time: str
    sync_end_time: str
    e2e_sync_run_id: UUID
    additional_info: Dict[str, Any]
    client_name: str
    retry_history: List[Any] = Field(default_factory=list)


from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_subscription_plan,
    is_clear_pending_tasks_enabled,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    CommissionETLStatusAccessor,
    DatabookETLStatusAccessor,
    DatabookETLStatusReaderAccessor,
    ETLLockAccessor,
    ETLSyncStatusAccessor,
    ETLSyncStatusReaderAccessor,
    PayoutSnapshotETLStatusAccessor,
    ReportETLStatusAccessor,
    SettlementETLStatusAccessor,
    SettlementSnapshotETLStatusAccessor,
    UpstreamCsvETLStatusAccessor,
    UpstreamETLStatusAccessor,
)
from commission_engine.models.client_models import Client
from commission_engine.models.etl_housekeeping_models import ETLSyncStatus
from commission_engine.services.databook_etl_sync_status_service import (
    get_failed_sync_run_logs,
)
from commission_engine.utils.general_data import ETL_STATUS
from commission_engine.utils.log_utils import merge_log_context
from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_admin_backend.services.client_audit_service import readable_datetime
from everstage_ddd.upstream.fivetran_webhook import FivetranSyncLogAccessor
from everstage_etl.celery.remove_tasks_in_redis import clear_pending_tasks_redis
from everstage_etl.celery.revoke_tasks_in_worker import clear_pending_tasks_worker
from everstage_etl.services.datasheet_execution_context_service import (
    update_datasheet_execution_context,
)
from everstage_infra.aws_infra.ecs import is_prod_env, is_staging_env
from interstage_project.celery import TaskGroupEnum
from interstage_project.threadlocal_log_context import set_threadlocal_context
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.email_template_accessor import EmailTemplateDetailsAccessor
from spm.accessors.support_email_notification_accessor import (
    SupportEmailNotificationAccessor,
)
from spm.constants.localization_constants import LocalizationTerms, StatementTerms
from spm.custom_exceptions.datasheet_exceptions import DatasheetRowLimitExceeded
from spm.services.email_services.email_services import send_email
from spm.services.localization_services import (
    get_localized_message_service,
    get_localized_words_service,
)

logger = logging.getLogger(__name__)


def is_sync_status_exist(client_id, e2e_sync_run_id):
    return ETLSyncStatusReaderAccessor(client_id).is_e2e_id_exist(e2e_sync_run_id)


def insert_etl_sync_status(
    client_id,
    e2e_sync_run_id,
    task,
    sync_status,
    sync_start_time,
    audit,
    params=None,
    additional_info=None,
):
    if not is_sync_status_exist(client_id, e2e_sync_run_id):
        ETLSyncStatusAccessor(client_id, e2e_sync_run_id).insert_sync_status(
            sync_start_time=sync_start_time,
            sync_status=sync_status,
            task=task,
            sync_end_time=None,
            audit=audit,
            params=params,
            additional_info=additional_info,
        )
    else:
        status = ETLSyncStatusReaderAccessor(client_id).get_sync_status(e2e_sync_run_id)
        if status == ETL_STATUS.NOT_STARTED.value:
            ETLSyncStatusAccessor(
                client_id, e2e_sync_run_id
            ).update_sync_status_to_started(sync_start_time)


def change_status_to_failed(
    client_id,
    e2e_sync_run_id,
    timestamp: datetime.datetime,
    should_update_completion_time=False,
    email_id=None,
):
    ETLSyncStatusAccessor(client_id, e2e_sync_run_id).change_status_to_failed(
        should_update_completion_time,
        timestamp,
    )
    if should_update_completion_time and email_id:
        send_email_notification_for_data_sync(client_id, e2e_sync_run_id, email_id)


def change_status_to_lock_failed(
    client_id, e2e_sync_run_id, should_update_completion_time=False, email_id=None
):
    """
    Change status in etl_sync_status table to lock failed
    """
    ETLSyncStatusAccessor(client_id, e2e_sync_run_id).change_status_to_lock_failed(
        should_update_completion_time
    )
    if should_update_completion_time and email_id:
        send_email_notification_for_data_sync(client_id, e2e_sync_run_id, email_id)


def get_status_object(client_id, e2e_sync_run_id):
    return ETLSyncStatusAccessor(client_id, e2e_sync_run_id).get_object().last()


@shared_task(base=EverCeleryBaseTask)
def update_completion_time_and_send_email_notification(
    client_id, e2e_sync_run_id, email_id, log_context=None
):
    log_context_local = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        "email_id": email_id,
    }
    log_context = merge_log_context(log_context, log_context_local)
    set_threadlocal_context(log_context)
    update_completion_time(client_id, e2e_sync_run_id)
    if email_id:
        send_email_notification_for_data_sync(client_id, e2e_sync_run_id, email_id)


@shared_task(base=EverCeleryBaseTask)
def sync_completion_wrapper(client_id, e2e_sync_run_id):
    subscription_plan = get_client_subscription_plan(client_id)
    misc_queue_name = iputils.get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.MISC.value
    )
    return chain(
        update_completion_time.si(  # type: ignore
            client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
        ).set(queue=misc_queue_name),
    )


@shared_task(base=EverCeleryBaseTask)
def update_completion_time(client_id, e2e_sync_run_id):
    sync_status = ETLSyncStatusReaderAccessor(client_id).get_sync_status(
        e2e_sync_run_id
    )
    log_context = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
    }
    set_threadlocal_context(log_context)
    if sync_status:
        if sync_status == ETL_STATUS.STARTED.value:
            sync_status = ETL_STATUS.COMPLETE.value
        # We want to mark the ETL sync as partially failed if any fivetran sync has failed
        all_fivetran_e2e_sync_status = (
            FivetranSyncLogAccessor(client_id, e2e_sync_run_id)
            .get_records_by_e2e_sync_id_and_status(e2e_sync_run_id)
            .values_list("sync_status", flat=True)
        )
        if (
            ETL_STATUS.FAILED.value in all_fivetran_e2e_sync_status
            or ETL_STATUS.PARTIALLY_FAILED.value in all_fivetran_e2e_sync_status
        ):
            sync_status = ETL_STATUS.PARTIALLY_FAILED.value
        logger.info(f"Updating sync status to - {sync_status}")
        ETLSyncStatusAccessor(
            client_id, e2e_sync_run_id
        ).update_status_and_completion_time(sync_status)


def send_email_notification_for_data_sync(client_id, e2e_sync_run_id, email_id):
    etl_sync_status = ETLSyncStatusReaderAccessor(
        client_id
    ).get_etl_sync_status_record_by_id(e2e_sync_run_id)
    emp_details = EmployeeAccessor(client_id).get_employees_name([email_id])
    name = (
        emp_details[0]["first_name"] + " " + emp_details[0]["last_name"]
        if emp_details
        else email_id
    )

    message = Mail(
        from_email=Email(email="<EMAIL>", name="Everstage"),
        to_emails=email_id,
    )
    client_name = get_client(client_id).name
    status = etl_sync_status["sync_status"]  # type: ignore
    if status == ETL_STATUS.COMPLETE.value:
        message.template_id = "d-0d1f136782dd41a9b763afdbebc64651"
    else:
        message.template_id = "d-3fcf42460dc540d194a648058d7ecce6"
    activity_name = etl_sync_status["task"]  # type: ignore
    # add localisation whenever the task is commission calculation
    if activity_name == "Commission calculation":
        activity_name = get_localized_message_service(
            StatementTerms.COMM_CALCULATION.value, client_id
        )
    keywords_to_localize = {"commission": LocalizationTerms.COMMISSION.value}
    localized_keywords = get_localized_words_service(keywords_to_localize, client_id)
    message.dynamic_template_data = {
        "name": name,
        "activity_name": activity_name,
        "activity_id": str(e2e_sync_run_id),
        "start_time": str(etl_sync_status["sync_start_time"]),  # type: ignore
        "end_time": str(etl_sync_status["sync_end_time"]),  # type: ignore
        "activity_status": status,
        "client_name": str(client_name),
        "commission": localized_keywords["commission"],
    }
    send_email(message=message, type="data_sync_notification_mails")


def send_email_notification_on_crm_error(
    client_id,
    object_name,
    e2e_sync_run_id,
    upcoming_scheduled_sync_time,
    **kwargs,
):
    """
    Send email notification to support team on CRM error
    """
    if is_staging_env() or is_prod_env():
        client_name = get_client(client_id).name
        support_team_record = SupportEmailNotificationAccessor(
            client_id=client_id
        ).get_support_team_members_for_client()
        template_id = EmailTemplateDetailsAccessor().get_template_id_for_event_code(
            "UPSTREAM_SYNC_FAIL_NOTIFICATION"
        )
        if (
            template_id is None
            or support_team_record is None
            or len(support_team_record.support_team_detail) == 0
        ):
            return
        subscription_plan = get_client_subscription_plan(client_id)
        send_email_queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
        )
        current_timestamp = datetime.datetime.now(tz=datetime.timezone.utc).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        for team_member in support_team_record.support_team_detail:
            message = Mail(
                from_email=Email(email="<EMAIL>", name="Everstage"),
                to_emails=team_member["employee_email_id"],
            )
            message.template_id = template_id

            message.dynamic_template_data = {
                "client_name": client_name,
                "admin_name": team_member["employee_name"],
                "timestamp": current_timestamp,
                "sync_id": str(e2e_sync_run_id),
                "error_type": str(kwargs.get("error_type")),
                "impacted_connectors": str(object_name),
                "trigger_source": str(kwargs.get("trigger_source", "CRON")),
                "llm_generated_cause": str(kwargs.get("llm_generated_cause")),
                "fix_step_1": "Step 1",
                "fix_description_1": str(kwargs.get("action_1")),
                "fix_step_2": "Step 2",
                "fix_description_2": str(kwargs.get("action_2")),
                "next_cron_time": upcoming_scheduled_sync_time,
            }
            # send notification email asynchronously
            send_email.si(message=message, type="upstream_sync_fail_notification_mail").set(  # type: ignore
                queue=send_email_queue_name
            ).apply_async()


def send_upstream_deleted_field_notification(
    client_id, e2e_sync_run_id, deleted_fields
):
    """
    Send email notification to support team about deleted fields in upstream sources

    Args:
        client_id (int): The client ID
        e2e_sync_run_id (UUID): The end-to-end sync run ID
        deleted_fields (list): Pre-formatted list of dictionaries with connection_name, object_name, and field_list
    """
    if is_staging_env() or is_prod_env():
        client_name = get_client(client_id).name
        support_team_record = SupportEmailNotificationAccessor(
            client_id=client_id
        ).get_support_team_members_for_client()
        template_id = EmailTemplateDetailsAccessor().get_template_id_for_event_code(
            "UPSTREAM_DELETED_FIELDS_NOTIFICATION"
        )
        if (
            template_id is None
            or support_team_record is None
            or len(support_team_record.support_team_detail) == 0
        ):
            return

        subscription_plan = get_client_subscription_plan(client_id)
        send_email_queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
        )
        for team_member in support_team_record.support_team_detail:
            message = Mail(
                from_email=Email(email="<EMAIL>", name="Everstage"),
                to_emails=team_member["employee_email_id"],
            )
            message.template_id = template_id

            message.dynamic_template_data = {
                "client_name": client_name,
                "admin_name": team_member["employee_name"],
                "sync_id": str(e2e_sync_run_id),
                "deleted_fields": deleted_fields,
            }
            # send notification email asynchronously
            send_email.si(message=message, type="upstream_deleted_field_notification_mail").set(  # type: ignore
                queue=send_email_queue_name
            ).apply_async()


def get_sync_without_end_time(client_id):
    client_name = get_client(client_id).name
    running_objects = list(
        ETLSyncStatus.objects.filter(
            sync_end_time__isnull=True, client_id=client_id
        ).values(
            "client_id",
            "task",
            "sync_status",
            "sync_start_time",
            "e2e_sync_run_id",
            "additional_info__is_cron_job",
        )
    )

    subscription_plan = get_client_subscription_plan(client_id)
    for each_run in running_objects:
        each_run["client_name"] = client_name
        each_run["subscription_plan"] = subscription_plan
        if each_run["additional_info__is_cron_job"]:
            each_run["task"] = each_run["task"] + " (Cron)"
        if each_run["sync_start_time"] is not None:
            each_run["elapsed_time"] = (
                datetime.datetime.now()
                - each_run["sync_start_time"].replace(tzinfo=None)
            ) / 3600
        else:
            each_run["elapsed_time"] = "None"
        each_run["sync_start_time"] = readable_datetime(each_run["sync_start_time"])
    return running_objects


def get_sync_without_end_time_across_clients(
    include_audit_col=False, subscription_plans: list[str] | None = None
):
    """
    Get all running syncs across all clients, filtered by subscription plans.
    If subscription_plans is provided, only returns syncs for clients with those subscription plans.
    If a client has no subscription plan specified, they are considered to be under BASIC plan.

    Args:
        include_audit_col (bool): Whether to include audit column in the response
        subscription_plans (list, optional): List of subscription plans to filter by. If None, returns all syncs.

    Returns:
        list: List of running sync objects with their details
    """
    client_id_name_tuple = tuple(
        Client.objects.all().values("client_id", "name", "client_features")
    )
    client_id_name_map: dict[int, str] = {}
    client_subscription_plans: dict[int, str] = {}
    subscription_plan_lookup_hashset: set[str] = (
        set(subscription_plans) if subscription_plans else set()
    )

    for ele in client_id_name_tuple:
        client_id = ele["client_id"]
        client_id_name_map[client_id] = ele["name"]

        # Get subscription plan from client_features, default to BASIC if not specified
        client_features = ele["client_features"] or {}
        client_subscription_plans[client_id] = client_features.get(
            "subscription_plan", "BASIC"
        )

    query_values = [
        "client_id",
        "task",
        "sync_status",
        "sync_start_time",
        "e2e_sync_run_id",
        "additional_info__is_cron_job",
    ]

    if include_audit_col:
        query_values.append("audit")

    running_objects = list(
        ETLSyncStatus.objects.filter(sync_end_time__isnull=True).values(*query_values)
    )

    filtered_running_objects = []
    for each_run in running_objects:
        client_id = each_run["client_id"]
        subscription_plan = client_subscription_plans[client_id]

        if (
            subscription_plan_lookup_hashset
            and subscription_plan not in subscription_plan_lookup_hashset
        ):
            continue

        each_run["client_name"] = client_id_name_map[client_id]
        each_run["subscription_plan"] = subscription_plan

        if each_run["additional_info__is_cron_job"]:
            each_run["task"] = each_run["task"] + " (Cron)"

        if each_run["sync_start_time"] is not None:
            each_run["elapsed_time"] = (
                datetime.datetime.now()
                - each_run["sync_start_time"].replace(tzinfo=None)
            ) / 3600
        else:
            each_run["elapsed_time"] = "None"

        # Convert the sync start time to readable datetime
        each_run["sync_start_time"] = readable_datetime(each_run["sync_start_time"])
        filtered_running_objects.append(each_run)

    return filtered_running_objects


def get_all_failed_cron_jobs():
    client_id_name_tuple = tuple(Client.objects.all().values("client_id", "name"))
    client_id_name_map = {}
    for ele in client_id_name_tuple:
        client_id_name_map[ele["client_id"]] = ele["name"]

    query_values = [
        "client_id",
        "task",
        "sync_status",
        "sync_start_time",
        "sync_end_time",
        "e2e_sync_run_id",
        "additional_info",
    ]

    failed_cron_jobs = list(
        ETLSyncStatus.objects.filter(
            Q(additional_info__skipped=False)
            | Q(additional_info__skipped__isnull=True),
            sync_status=ETL_STATUS.PARTIALLY_FAILED.value,  # get only the partially failed cron jobs, since we won't retry the failed cron jobs
            additional_info__is_cron_job=True,  # get only the cron jobs
            additional_info__marked_as_failed=False,  # get only the cron jobs that have not been marked as failed by any user
            additional_info__was_retried=False,  # get only the cron jobs that have not been retried
        )
        .values(*query_values)
        .order_by("-sync_start_time")
    )

    logger.info(f"Failed cron jobs: {failed_cron_jobs}")

    failed_cron_jobs_with_info = []
    for each_cron_job in failed_cron_jobs:
        each_cron_job["client_name"] = client_id_name_map[each_cron_job["client_id"]]
        each_cron_job["retry_history"] = each_cron_job["additional_info"].get(
            "retry_signature", []
        )
        each_cron_job["sync_start_time"] = readable_datetime(
            each_cron_job["sync_start_time"]
        )
        each_cron_job["sync_end_time"] = readable_datetime(
            each_cron_job["sync_end_time"]
        )
        failed_cron_jobs_with_info.append(each_cron_job)

    return failed_cron_jobs_with_info


def get_all_failed_manual_jobs() -> List[FailedManualJob]:
    """
    Returns all the failed manual jobs that have not been marked as failed or retried
    """
    client_id_name_tuple = tuple(Client.objects.all().values("client_id", "name"))
    client_id_name_map = {}
    for ele in client_id_name_tuple:
        client_id_name_map[ele["client_id"]] = ele["name"]

    query_values = [
        "client_id",
        "task",
        "sync_status",
        "sync_start_time",
        "sync_end_time",
        "e2e_sync_run_id",
        "additional_info",
    ]

    failed_manual_jobs = list(
        ETLSyncStatus.objects.filter(
            Q(additional_info__marked_as_failed=False)
            | Q(additional_info__marked_as_failed__isnull=True),
            Q(additional_info__was_retried=False)
            | Q(additional_info__was_retried__isnull=True),
            Q(additional_info__skipped=False)
            | Q(additional_info__skipped__isnull=True),
            sync_status=ETL_STATUS.PARTIALLY_FAILED.value,
            additional_info__is_cron_job=False,
        )
        .values(*query_values)
        .order_by("-sync_start_time")
    )
    logger.info(f"Failed manual jobs: {failed_manual_jobs}")

    failed_manual_jobs_with_info = []
    for each_manual_job in failed_manual_jobs:
        logger.info(each_manual_job["client_id"])
        each_manual_job["client_name"] = client_id_name_map[
            each_manual_job["client_id"]
        ]
        each_manual_job["retry_history"] = pydash.get(
            each_manual_job, "additional_info.retry_signature", []
        )
        each_manual_job["sync_start_time"] = readable_datetime(
            each_manual_job["sync_start_time"]
        )
        each_manual_job["sync_end_time"] = readable_datetime(
            each_manual_job["sync_end_time"]
        )
        failed_manual_jobs_with_info.append(each_manual_job)

    return failed_manual_jobs_with_info


def mark_cron_job_as_failed(client_id, e2e_sync_run_id, user_email):
    try:
        ETLSyncStatusAccessor(client_id, e2e_sync_run_id).mark_cron_job_as_failed(
            user_email
        )
    except Exception as e:
        logger.error(
            f"Failed to mark cron job as failed for client {client_id} and e2e sync run id {e2e_sync_run_id}: {e}"
        )
        raise e


def mark_manual_job_as_failed(client_id: int, e2e_sync_run_id: str, user_email: str):
    try:
        ETLSyncStatusAccessor(client_id, e2e_sync_run_id).mark_manual_job_as_failed(
            user_email
        )
        logger.info(f"Manual job {e2e_sync_run_id} marked as failed by {user_email}")
    except Exception as e:
        logger.error(
            f"Failed to mark manual job as failed for client {client_id} and e2e sync run id {e2e_sync_run_id}: {e}"
        )
        raise e


def mark_sync_as_skipped(
    client_id: int, e2e_sync_run_id: str, user_email: str, reason: str
):
    try:
        ETLSyncStatusAccessor(client_id, e2e_sync_run_id).mark_sync_as_skipped(
            reason, user_email
        )
        logger.info(
            f"Sync {e2e_sync_run_id} marked as skipped by {user_email} with reason: {reason}"
        )
    except Exception as e:
        logger.error(
            f"Failed to mark sync as skipped for client {client_id} and e2e sync run id {e2e_sync_run_id}: {e}"
        )
        raise e


def get_recent_data_sync_status(client_id):
    "Return the details of the currently running sync or the details of the latest sync that ran in last 48 hours"
    sync_status_reader = ETLSyncStatusReaderAccessor(client_id)
    sync_status = sync_status_reader.get_current_running_sync_details()
    if sync_status:
        return sync_status
    else:
        last_48_hours = make_aware(
            datetime.datetime.now() + datetime.timedelta(hours=-48)
        )
        return sync_status_reader.get_latest_data_sync_details(last_48_hours)


def get_recent_datasheet_databook_sync_status(
    client_id, databook_id, etl_activity, datasheet_id=None
):
    """
    Returns the latest entry in databook_etl_status for the given datasheet id
    """
    detailed_status = {}
    etl_status_reader = ETLSyncStatusReaderAccessor(client_id=client_id)
    if datasheet_id is None:
        # If datasheet_id is None then it databook sync
        record = etl_status_reader.get_latest_e2e_sync_run_id_for_db_id(
            activity=etl_activity, databook_id=databook_id
        )
    else:
        # If datasheet_id is given then it is datasheet sync
        record = etl_status_reader.get_latest_sync_info_by_ds_id(
            activity=etl_activity, datasheet_id=datasheet_id
        )

    if record:
        detailed_status["e2e_sync_run_id"] = str(record["e2e_sync_run_id"])
        detailed_status["sync_status"] = record["sync_status"]
        detailed_status["sync_end_time"] = str(record["sync_end_time"])
        detailed_status["sync_start_time"] = str(record["sync_start_time"])

    if record and record["sync_end_time"] is not None:
        # If sync_end_time is None, then it means sync is still running
        # If sync_end_time is not None, then only check for error message.
        is_ds_row_limit_error, error_details = _exceeded_row_limit(
            client_id=client_id,
            e2e_sync_run_id=record["e2e_sync_run_id"],
            databook_id=databook_id,
        )
        if is_ds_row_limit_error:
            detailed_status["error_info"] = DatasheetRowLimitExceeded.error_code
            detailed_status["error_details"] = error_details
    return detailed_status


def get_recent_datasheet_sync_status(client_id, databook_id, datasheet_id):
    latest_records = DatabookETLStatusReaderAccessor(
        client_id
    ).get_last_n_sync_for_datasheet(
        databook_id=databook_id, datasheet_id=datasheet_id, n=1
    )

    if latest_records:
        latest_record = latest_records[0]
    else:
        return {}

    etl_status_reader = ETLSyncStatusReaderAccessor(client_id=client_id)

    record = etl_status_reader.get_latest_sync_info_for_datasheet(
        datasheet_id, databook_id, latest_record["e2e_sync_run_id"]
    )
    detailed_status = {}
    if record:
        detailed_status["e2e_sync_run_id"] = str(record["e2e_sync_run_id"])
        detailed_status["sync_status"] = record["sync_status"]
        detailed_status["sync_end_time"] = str(record["sync_end_time"])
        detailed_status["sync_start_time"] = str(record["sync_start_time"])

    if record and record["sync_end_time"] is not None:
        # If sync_end_time is None, then it means sync is still running
        # If sync_end_time is not None, then only check for error message.
        is_ds_row_limit_error, error_details = _exceeded_row_limit(
            client_id=client_id,
            e2e_sync_run_id=record["e2e_sync_run_id"],
            databook_id=databook_id,
        )
        if is_ds_row_limit_error:
            detailed_status["error_info"] = DatasheetRowLimitExceeded.error_code
            detailed_status["error_details"] = error_details
    return detailed_status


def _exceeded_row_limit(client_id, e2e_sync_run_id, databook_id) -> tuple[bool, dict]:
    """
    This function is used to check if any row_limit_exceeded failure occurred for
    the given e2e_sync_run_id.
    """
    sync_run_logs = get_failed_sync_run_logs(
        client_id=client_id,
        databook_id=databook_id,
        e2e_sync_run_id=e2e_sync_run_id,
    )

    # To avoid type error there is a need to check if sync_run_log is not None
    has_failed = any(
        pydash.get(sync_run_log, "sync_run_log.error_details.error_code")
        == DatasheetRowLimitExceeded.error_code
        for sync_run_log in sync_run_logs
    )
    sample_failure = {}
    if has_failed and len(sync_run_logs) > 0:
        sample_failure = sync_run_logs[0]["sync_run_log"].get(
            "error_details",
            {"message": "A datasheet exceeded the configured row limit"},
        )
    return has_failed, sample_failure


def get_report_elt_status_by_e2e_sync_id(client_id, e2e_sync_run_id):
    """
    Get the status of report etl given the task id.
    Returns success if all report etl tasks for the sync id runs successfully.
    Returns failed if even one of the report etl fails.
    """
    query_result = ReportETLStatusAccessor(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
    ).get_status_by_sync_id()
    # convert the query set to list
    query_result = list(query_result.values("sync_status"))

    failed_statuses = [
        ETL_STATUS.FAILED.value,
        ETL_STATUS.PARTIALLY_FAILED.value,
        ETL_STATUS.LOCK_FAILED.value,
    ]
    for sync_info in query_result:
        if sync_info["sync_status"] in failed_statuses:
            return "failed"

    return "success"


def remove_etl_lock_for_e2e_id(client_id, e2e_sync_run_id, user_email):
    """
    Remove etl lock for the given e2e sync run id

    Looks for locks in the following tables:

    - etl_lock

    - fivetran_sync_log

    - upstream_etl_status

    - upstream_csv_etl_status

    - databook_etl_status

    - commission_etl_status

    - settlement_etl_status

    - report_etl_status

    - etl_sync_status
    """
    etl_sync_status_acc = ETLSyncStatusAccessor(client_id, e2e_sync_run_id)
    audit_details = etl_sync_status_acc.get_object()[0].audit
    audit_details = audit_details if audit_details else {}
    audit_details["message"] = f"lock released manually by {user_email}"
    sync_end_time = make_aware(datetime.datetime.now())

    FivetranSyncLogAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    UpstreamETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    UpstreamCsvETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    DatabookETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    CommissionETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    SettlementETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    PayoutSnapshotETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    SettlementSnapshotETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    ReportETLStatusAccessor(
        client_id, e2e_sync_run_id, None
    ).release_lock_for_all_started_tasks_by_e2e_sync_id(e2e_sync_run_id, sync_end_time)

    ETLLockAccessor(client_id, e2e_sync_run_id, None).release_lock_by_e2e_sync_id(
        e2e_sync_run_id, sync_end_time
    )

    record = etl_sync_status_acc.get_object().first()
    should_set_sync_start_time = (
        True if record.sync_status == ETL_STATUS.NOT_STARTED.value else False
    )
    etl_sync_status_acc.release_lock(
        sync_end_time,
        audit_details,
        should_set_sync_start_time=should_set_sync_start_time,
    )
    if is_clear_pending_tasks_enabled(client_id=client_id):
        try:
            clear_pending_tasks_redis(
                client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
            )
            clear_pending_tasks_worker(
                client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
            )
        except Exception as e:
            logger.exception("Failed to clear pending tasks: %s", str(e))

    # Update the datasheet execution context to set is_polling_threshold_breached to True
    update_datasheet_execution_context(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        update_data={"is_polling_threshold_breached": True},
    )


def is_end_to_end_sync_running(client_id):
    """
    Returns True if any end to end sync is running for the given client id
    """
    return ETLSyncStatusReaderAccessor(client_id).is_end_to_end_sync_running()


def is_upstream_sync_running(client_id):
    """
    Returns True if any upstream sync is running for the given client id
    """
    return ETLSyncStatusReaderAccessor(client_id).is_upstream_sync_running()


def get_etl_params(client_id: int, e2e_sync_run_id: str) -> dict | None:
    """
    Returns the params for the given e2e_sync_run_id
    """
    logger.info(f"BEGIN: Getting etl params for e2e_sync_run_id: {e2e_sync_run_id}")
    result = ETLSyncStatusReaderAccessor(client_id=client_id).get_params(
        e2e_sync_run_id=e2e_sync_run_id
    )
    logger.info(
        f"END: Getting etl params for e2e_sync_run_id: {e2e_sync_run_id} - result: {result}"
    )
    return result


def get_all_failed_syncs_that_havent_been_notified() -> list:
    """
    Returns all failed syncs that haven't been notified yet
    """
    logger.info("BEGIN: Getting all failed syncs that haven't been notified yet")
    accessor = ETLSyncStatusReaderAccessor()
    result = accessor.get_all_failed_syncs_that_havent_been_notified()
    logger.info("END: Getting all failed syncs that haven't been notified yet")
    return result


def update_params_for_e2e_sync_run_ids(
    e2e_sync_run_ids: list[str], params: dict
) -> None:
    """
    Update the params for the given list of e2e_sync_run_ids in one go.
    """
    logger.info(f"BEGIN: Bulk updating params for e2e_sync_run_ids: {e2e_sync_run_ids}")
    accessor = ETLSyncStatusReaderAccessor()
    accessor.bulk_update_params_for_e2e_sync_run_ids(
        e2e_sync_run_ids=e2e_sync_run_ids, params=params
    )
    logger.info(f"END: Bulk updating params for e2e_sync_run_ids: {e2e_sync_run_ids}")


def is_sync_triggered_from_datasheet_ui(client_id: int, e2e_sync_run_id: str) -> bool:
    """
    Check if the given e2e_sync_run_id is a sync triggered from the datasheet ui.
    """
    logger.info(
        f"BEGIN: Checking if e2e_sync_run_id: {e2e_sync_run_id} is a sync triggered from the datasheet ui"
    )
    result = ETLSyncStatusReaderAccessor(client_id).is_sync_triggered_from_datasheet_ui(
        e2e_sync_run_id
    )
    logger.info(
        f"END: Checking if e2e_sync_run_id: {e2e_sync_run_id} is a sync triggered from the datasheet ui - result: {result}"
    )
    return result


def is_sync_lock_active(client_id: int, e2e_sync_run_id: str) -> bool:
    """
    Check if the given sync lock is active for the client.

    If the sync_end_time is null, then the sync lock is active.
    """
    return ETLSyncStatusReaderAccessor(client_id).is_sync_lock_active(e2e_sync_run_id)
