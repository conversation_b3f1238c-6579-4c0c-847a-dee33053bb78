import json
import random
import xml.etree.ElementTree as ET
from base64 import b64encode, urlsafe_b64encode
from datetime import datetime, timedelta
from http import HTTPStatus
from time import sleep
from urllib.parse import parse_qs, urlparse

import pydash
import requests
from Crypto.Hash import SHA256
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from django.core.cache import cache

import interstage_project.utils as iputils
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    IntegrationAccessor,
)
from commission_engine.custom_exceptions.request_exceptions import (
    ThirdPartyAPIException,
    ThirdPartyTokenException,
)
from commission_engine.utils.general_data import Services
from commission_engine.utils.redis_lock import RedisLockManager
from commission_engine.utils.third_party_api_utils import CRM_ERROR_MAP

from .exceptions.custom_exceptions import TokenRefreshFailedError

MAX_RETRY_LIMIT = 5


class ThirdPartyApi:
    log = iputils.LogWithContext()

    def get_jwt_bearer_flow_token(self, access_config_obj):
        jwt_data = access_config_obj.jwt_data
        now = datetime.now()
        now_exp = now + timedelta(minutes=3)
        epoch = now_exp.timestamp()
        payload_params = {"epoch": epoch}
        header = pydash.get(jwt_data, "header")
        payload = {}
        payload_body = pydash.get(jwt_data, "payload")
        for key, val in payload_body.items():
            payload[key] = val.format(**payload_params)
        private_key_str = pydash.get(jwt_data, "signature")
        # print(f"SIG... {private_key_str}")
        header_encoded = urlsafe_b64encode(json.dumps(header).encode("utf-8"))
        payload_encoded = urlsafe_b64encode(json.dumps(payload).encode("utf-8"))
        token_bs = header_encoded + b"." + payload_encoded
        self.log.info("JWT Token Bs - {}".format(token_bs))
        # print("%" * 100)
        private_key = RSA.importKey(private_key_str)
        digest = SHA256.new(token_bs)
        signer = PKCS1_v1_5.new(private_key)
        sig = signer.sign(digest)
        self.log.info("JWT Sig - {}".format(sig))
        # print("-" * 100)
        token = urlsafe_b64encode(sig).decode()
        result = token_bs + b"." + token.encode("utf-8")
        return result.decode("utf-8")

    def get_access_token(self, client_id, integration, access_config_obj):
        try:
            cache_key = f"{str(client_id)}_{access_config_obj.access_token_config_id}_{integration}_access_token"
            access_url = access_config_obj.access_token_url
            # params = access_config_obj.api_access_body
            self.log.info("Access Token Url - {}".format(access_url))
            access_type = access_config_obj.access_type
            req_body = access_config_obj.access_request_body
            args = {}
            if access_type.upper() == "PUB-PRI":
                body = {}
                body_params = {}
                body_params["JWT"] = self.get_jwt_bearer_flow_token(access_config_obj)
                for key, val in req_body.items():
                    body[key] = val.format(**body_params)
            elif access_type.upper() == "UN-PWD":
                body = req_body
            args[access_config_obj.payload_type.lower()] = body
            r = requests.post(access_url, **args, timeout=120)
            response_data = r.json()
            access_token = response_data.get("access_token")
            timeout = response_data.get("expires_in", 3600)
            cache.set(cache_key, access_token, timeout)
            return access_token
        except Exception as e:
            raise e

    def get_bullhorn_rest_token(self, access_config_obj, access_token):
        """
        Retrieves the Bullhorn REST token using the access token and access configuration object.

        Args:
            access_config_obj: The access token config obj containing the Bullhorn REST URL.
            access_token: The access token to be used for authentication.

        Returns:
            str: The Bullhorn REST token obtained from the request or None if the request fails.
        """
        bh_rest_url = (access_config_obj.additional_data or {}).get("bh_rest_url", "")
        params = {
            "version": "*",
            "access_token": access_token,
        }
        response = requests.post(bh_rest_url, params=params, timeout=120)
        return response.json().get("BhRestToken")

    def generate_fresh_bullhorn_tokens(
        self, access_config_obj, cache_key
    ) -> str | None:
        """
        Generates a fresh Bullhorn refresh token using OAuth flow
        when the current refresh token is invalid.

        Args:
            access_config_obj: Access token config object containing OAuth credentials and URLs

        Returns:
            str | None: The new refresh token
        """

        def _get_auth_code(url: str) -> str | None:
            """Extract authorization code from OAuth redirect URL"""
            query = urlparse(url).query
            params = parse_qs(query)
            return params.get("code", [None])[0]

        # Extract required OAuth config
        additional_data = access_config_obj.additional_data or {}
        request_body = access_config_obj.access_request_body or {}

        credentials = {
            "client_id": pydash.get(request_body, "client_id"),
            "client_secret": pydash.get(request_body, "client_secret"),
            "api_username": pydash.get(additional_data, "api_username"),
            "api_password": pydash.get(additional_data, "api_password"),
        }

        if not all(credentials.values()):
            self.log.error("Missing required OAuth credentials")
            return None

        try:
            # Get Authorization Code
            self.log.info("Generating Authorization Code")
            auth_url = (
                f"{access_config_obj.access_token_url}/authorize?"
                f"client_id={credentials['client_id']}&"
                f"response_type=code&"
                f"action=Login&"
                f"username={credentials['api_username']}&"
                f"password={credentials['api_password']}"
            )

            response = requests.get(auth_url, timeout=120)
            response.raise_for_status()

            auth_code = _get_auth_code(response.url)
            if not auth_code:
                self.log.error("Failed to obtain authorization code")
                return None

            self.log.info("Exchanging Authorization Code for Refresh Token")
            token_url = (
                f"{access_config_obj.access_token_url}/token?"
                f"grant_type=authorization_code&"
                f"code={auth_code}&"
                f"client_id={credentials['client_id']}&"
                f"client_secret={credentials['client_secret']}"
            )
            response = requests.post(token_url, timeout=120)
            response.raise_for_status()

            # Generate bh_rest_token and save it in
            self.log.info("Setting Bullhorn REST Token in Cache")
            bullhorn_rest_token = self.get_bullhorn_rest_token(
                access_config_obj, response.json().get("access_token")
            )
            timeout = response.json().get("expires_in", 600)
            cache.set(cache_key, bullhorn_rest_token, timeout)

            return response.json().get("refresh_token")

        except Exception as err:
            self.log.exception("Failed to generate fresh Bullhorn refresh token...")
            raise ThirdPartyTokenException(response=response) from err

    def get_access_token_from_refresh_token(
        self, client_id, integration, access_config_obj, cache_key=None
    ):
        access_url = access_config_obj.access_token_url
        req_body = access_config_obj.access_request_body

        def _set_access_token_in_cache(access_token, timeout, cache_key):
            if cache_key is None:
                cache_key = f"{str(client_id)}_{access_config_obj.access_token_config_id}_{access_config_obj.service_name}_access_token"
            cache.set(cache_key, access_token, timeout)

        def _handle_response(response):
            response_data = response.json()
            access_token = response_data.get("access_token")
            if not access_token:
                raise ThirdPartyTokenException(response=response)
            return (
                access_token,
                response_data.get("refresh_token"),
                response_data.get("expires_in", 3600),
            )

        if integration == "salesforce":
            retry = 3
            while retry:
                try:
                    response = requests.post(access_url, data=req_body, timeout=120)
                    self.log.info(
                        f"Got response: {response} with data: {response.json()}"
                    )
                    response_refresh_token = pydash.get(req_body, "refresh_token")
                    access_token, _, timeout = _handle_response(response)
                    _set_access_token_in_cache(access_token, timeout, cache_key)
                    break  # Exit the loop
                except (
                    json.JSONDecodeError,
                    requests.exceptions.JSONDecodeError,
                ) as error:
                    self.log.error(  # noqa: TRY400
                        f"JSON decode error: {error}. Response text: {response.text}"
                    )
                    sleep_time = random.uniform(1, 5)  # noqa: S311
                    self.log.info(f"Sleeping for {sleep_time} seconds before retry")
                    sleep(sleep_time)
                retry -= 1
                if retry == 0:
                    raise ThirdPartyTokenException(response=response)

        elif integration == "bullhorn":
            response = requests.post(
                f"{access_url}/token", params=req_body, timeout=120
            )
            access_token, response_refresh_token, timeout = _handle_response(response)
            bullhorn_rest_token = self.get_bullhorn_rest_token(
                access_config_obj, access_token
            )
            _set_access_token_in_cache(bullhorn_rest_token, timeout, cache_key)

        else:
            auth_header = f"{pydash.get(req_body, 'app_client_id')}:{pydash.get(req_body, 'app_secret_key')}"
            auth_header = "Basic " + b64encode(auth_header.encode("utf-8")).decode(
                "utf-8"
            )
            token_headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": auth_header,
            }
            token_body = {
                "grant_type": "refresh_token",
                "refresh_token": pydash.get(req_body, "refresh_token"),
            }
            response = requests.post(
                access_url, headers=token_headers, data=token_body, timeout=120
            )
            access_token, response_refresh_token, timeout = _handle_response(response)
            _set_access_token_in_cache(access_token, timeout, cache_key)

        return access_token, response_refresh_token

    # curl -v https://everstage-dev-ed.lightning.force.com/services/data/v50.0/sobjects/Opportunity/deleted/?start=2020-10-21T09:57:28Z&end=2020-11-09T06:26:31Z -H 'Content-Type: application/json' -H 'Authorization: Bearer 00D4W000007Q5kR!AQYAQNPoVN8rhVu1popeg63ulJx6ghmnndCHbH6vzSSdowEY8wd1BwSqc7EnRF6dHk.5YkTY_2LAbwEQothUvtfw4WTOryTw'

    def make_call(
        self,
        client_id,
        url,
        access_token,
        api_config_object,
        access_config,
        parameters=None,
        method="get",
        header_data=None,
        **_,
    ):
        if parameters is None:
            parameters = {}
        try:
            config_additional_data = api_config_object.additional_data or {}
            invalid_token = config_additional_data.get("invalid_token", {})
            error_code_key = invalid_token.get("key")
            error_code_value = invalid_token.get("value")
            http_status = invalid_token.get("http_status")
            integration_retry_limit = config_additional_data.get(
                "retry_limit", MAX_RETRY_LIMIT
            )
            retry = integration_retry_limit
            integration_object = IntegrationAccessor(
                client_id
            ).get_integration_by_integration_id(api_config_object.integration_id)
            is_csv_integration = False
            if integration_object.additional_data is not None:
                is_csv_integration = integration_object.additional_data.get(
                    "is_csv_integration"
                )
            while retry:
                params = {"access_token": access_token}
                if header_data:
                    params.update(header_data)
                header_params = api_config_object.request_header
                headers = {}
                for key, val in header_params.items():
                    headers[key] = val.format(**params)

                if method == "get":
                    r = requests.request(
                        method,
                        url,
                        headers=headers,
                        params=parameters,
                        timeout=900,
                    )
                elif method in ["post", "patch"]:
                    r = requests.request(
                        method,
                        url,
                        headers=headers,
                        data=json.dumps(parameters),
                        timeout=900,
                    )
                else:
                    # other methods not implemented in this example
                    raise ValueError("Method should be get or post or patch.")
                self.log.info(
                    "API Method - {0}, URL - {1}, Status Code: {2}".format(
                        method, r.url, r.status_code
                    )
                )
                if r.status_code < 300:
                    if method == "patch":
                        return None
                    elif is_csv_integration:
                        return r.text
                    else:
                        return r.json()
                elif r.status_code == 304:  # No modified content
                    return {}
                # If the request fails because of expired access token, then retry by passing a new access token
                elif (
                    invalid_token
                    and r.status_code == http_status
                    and r.json().get(error_code_key) == error_code_value
                ):
                    self.log.info(
                        "Access token expired. Generating new access token......"
                    )
                    access_token = self.get_access_token(
                        client_id, api_config_object.integration, access_config
                    )
                    self.log.info(f"New access token {access_token}")
                # If the request fails because of timeout error
                elif r.status_code in (
                    HTTPStatus.BAD_GATEWAY,
                    HTTPStatus.SERVICE_UNAVAILABLE,
                    HTTPStatus.INTERNAL_SERVER_ERROR,
                ):
                    sleep_time = random.uniform(2, 4)
                    self.log.info(
                        f"TIMEOUT ERROR - Status Code: {r.status_code} SLEEPING FOR {sleep_time} SECONDS"
                    )
                    sleep(sleep_time)
                else:
                    raise ThirdPartyAPIException(response=r)
                retry -= 1
                if retry == 0:
                    self.log.error(f"Max retries reached for the API call - {url}")
                    raise ThirdPartyAPIException(response=r)  # noqa: TRY301
        except ThirdPartyAPIException as e:
            raise e
        except Exception as e:
            raise e

    def get_cached_access_token(
        self,
        client_id,
        api_config_object,
        access_config,
        cache_key,
        refresh_token_callback=None,
    ):
        """
        Get access token from cache or generate new token if not found.
        Handles token generation with distributed locking and deadlock recovery.
        Ensures only once process generates token while others wait for it to appear in cache.
        If the process generating token fails, other processes will repeat the algorithm till
        the first process succeeds in generating the token and sets it in cache.

        Args:
            client_id: Client ID
            api_config_object: API Access Config object
            access_config: Access Token Config object
            cache_key: Cache key for storing the token
            refresh_token_callback: Optional callback function to handle refresh token updates.
                                  Should accept (client_id, access_token_config_id, new_refresh_token) as parameters.
        """
        redis_lock = RedisLockManager(lock_timeout=20)
        lock_key = f"{cache_key}_lock"
        max_retries = MAX_RETRY_LIMIT

        use_locking = False
        default_locking_flag = access_config.service_name.lower() in [
            Services.SALESFORCE.value,
            "quickbooks",
        ]
        if not access_config.additional_data:
            use_locking = default_locking_flag
        elif access_config.additional_data and isinstance(  # type:ignore
            access_config.additional_data, dict  # type:ignore
        ):
            use_locking = access_config.additional_data.get(  # type:ignore
                "use_locking", default_locking_flag
            )
            max_retries = access_config.additional_data.get(
                "retry_limit", MAX_RETRY_LIMIT
            )

        attempt = 1
        base_delay = 1
        while attempt <= max_retries:
            # Try to get cached token first
            access_token = cache.get(cache_key)
            if access_token:
                self.log.info(
                    f"Cache Hit: Access Token - {access_token}",
                    {"cache_event": "CACHE_HIT"},
                )
                return access_token

            # Access Token not found in cache, Try to acquire lock if use_locking is enabled to generate token
            if not use_locking or redis_lock.acquire_lock(lock_key):
                try:
                    # Double-check cache after acquiring lock
                    if use_locking:
                        access_token = cache.get(cache_key)
                        if access_token:
                            return access_token

                    # Generate new token based on config
                    if not access_config.api_access_key:
                        if access_config.access_type == "REFRESH":
                            access_token, response_refresh_token = (
                                self.get_access_token_from_refresh_token(
                                    client_id,
                                    api_config_object.integration,
                                    access_config,
                                    cache_key,
                                )
                            )
                            # Handle refresh token update via callback if provided
                            if refresh_token_callback and response_refresh_token:
                                refresh_token_callback(
                                    client_id=client_id,
                                    access_token_config_id=access_config.access_token_config_id,
                                    new_refresh_token=response_refresh_token,
                                )
                        else:
                            access_token = self.get_access_token(
                                client_id,
                                api_config_object.integration,
                                access_config,
                            )
                    elif access_config.access_type == "API Key":
                        access_token = access_config.api_access_key
                    elif access_config.access_type.upper() == "BASIC_AUTH":
                        access_token = b64encode(
                            access_config.api_access_key.encode("utf-8")
                        ).decode("utf-8")

                    self.log.info(
                        f"Cache Miss: Generated Access Token - {access_token}",
                        {"cache_event": "CACHE_MISS"},
                    )
                    return access_token

                finally:
                    if use_locking:
                        redis_lock.release_lock(lock_key)
            else:
                # Acquire Lock failed, Wait for token to appear in cache or lock to expire
                access_token, should_retry = redis_lock.wait_for_token(
                    lock_key, cache_key
                )
                if access_token:
                    self.log.info(
                        f"Cache Hit: Access Token appeared in cache while waiting - {access_token}",
                        {"cache_event": "CACHE_HIT"},
                    )
                    return access_token
                self.log.info(
                    f"Timeout waiting for access token, attempt {attempt} of {max_retries}"
                )
                if should_retry:
                    if attempt <= max_retries:
                        # Retry with exponential backoff
                        delay = base_delay * (
                            2 ** (attempt - 1)
                        ) + random.uniform(  # noqa: S311
                            -0.5, 0.5
                        )
                        self.log.info(f"Retrying in {delay:.2f} seconds")
                        sleep(delay)
                        attempt += 1
                        continue
                else:
                    raise TokenRefreshFailedError(
                        api_config_object.source_object_id,
                        "Timeout waiting for access token",
                    )

        raise TokenRefreshFailedError(
            api_config_object.source_object_id,
            f"Failed to acquire access token after {max_retries} retries",
        )

    def api_call(
        self,
        client_id,
        url,
        body,
        api_config_object,
        header=None,
        cache_key=None,
        is_validation=False,
        refresh_token_callback=None,
    ):
        """
        Helper function to make calls to Salesforce REST API.
        Parameters: action (the URL), URL params, method (get, post or patch), data for POST/PATCH.
        """
        try:
            if cache_key is None:
                cache_key = f"{str(client_id)}_{api_config_object.access_token_config_id}_{api_config_object.integration}_access_token"

            atc = AccessTokenConfigAccessor(client_id)
            access_config = atc.get_object_by_id(
                api_config_object.access_token_config_id
            )

            access_token = None
            if access_config.api_access_key:  # type:ignore
                if access_config.access_type == "API Key":  # type:ignore
                    access_token = access_config.api_access_key  # type:ignore
                elif access_config.access_type.upper() == "BASIC_AUTH":  # type:ignore
                    access_token = b64encode(
                        access_config.api_access_key.encode("utf-8")  # type:ignore
                    ).decode("utf-8")
                self.log.info(f"Access Token fetched from DB - {access_token}")
            else:
                # Generate access token with refresh token/Get access token from cache if already generated
                if cache_key is None:
                    cache_key = f"{str(client_id)}_{api_config_object.access_token_config_id}_{api_config_object.integration}_access_token"
                self.log.info(f"Getting Access Token with cache key - {cache_key}")

                skip_cache = False
                if access_config.additional_data and isinstance(  # type:ignore
                    access_config.additional_data, dict  # type:ignore
                ):
                    skip_cache = access_config.additional_data.get(  # type:ignore
                        "skip_cache", False
                    )

                if skip_cache:
                    self.log.info("Skipping Cache Check")
                    if access_config.access_type == "REFRESH":  # type: ignore
                        access_token, _ = self.get_access_token_from_refresh_token(
                            client_id,
                            api_config_object.integration,
                            access_config,
                            cache_key,
                        )
                    else:
                        access_token = self.get_access_token(
                            client_id, api_config_object.integration, access_config
                        )
                else:
                    access_token = self.get_cached_access_token(
                        client_id,
                        api_config_object,
                        access_config,
                        cache_key,
                        refresh_token_callback,
                    )

            method = api_config_object.request_type
            r = self.make_call(
                client_id=client_id,
                url=url,
                access_token=access_token,
                api_config_object=api_config_object,
                access_config=access_config,
                parameters=body,
                method=method,
                header_data=header,
                is_validation=is_validation,
            )
            return r
        except ThirdPartyAPIException as e:
            raise e
        except Exception as e:
            print("TPA EXCEPTION.. {}".format(e))
            raise e


def get_error_message(response_json, service_name):
    """
    Helper function to erxtract error message from response for different CRM
    """
    error_code = None
    if service_name == "salesforce":
        error_code = response_json[0].get("errorCode")
    elif service_name == "stripe":
        error_code = response_json.get("error").get("type")
    elif service_name == "hubspot":
        error_code = response_json.get("category")
    elif service_name == "chargebee":
        error_code = response_json.get("type")
        if not error_code:
            error_code = response_json.get("api_error_code")
    elif service_name == "quickbooks":
        response_str = response_json.decode("utf-8")
        # Parse the XML response
        root = ET.fromstring(response_str)

        # Find the error message
        namespace = "{http://schema.intuit.com/finance/v3}"
        error_code = root.find(f".//{namespace}Message").text
    if error_code:
        err_msg = CRM_ERROR_MAP.get(service_name, {}).get(error_code.upper())
        if err_msg:
            return err_msg
    return None
