import logging
from copy import deepcopy
from datetime import datetime, timedelta, timezone
from importlib import import_module
from time import sleep
from typing import List, Optional, Tuple
from uuid import UUID

import pydash
from django.forms import model_to_dict

from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    IntegrationAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusAccessor,
)
from commission_engine.models.etl_config_models import ApiAccessConfig
from commission_engine.third_party_connections.third_party_api import ThirdPartyApi
from commission_engine.third_party_connections.utils import process_lines
from commission_engine.utils import make_aware, parse
from commission_engine.utils.general_data import (
    QuickbooksReportTypes,
    UpstreamETLVersions,
)
from commission_engine.utils.log_utils import merge_log_context
from everstage_ddd.upstream import UpstreamChangesWriter
from interstage_project.threadlocal_log_context import (
    get_threadlocal_context,
    set_threadlocal_context,
)

from .exceptions.custom_exceptions import AccessTokenConfigObjectNotFoundError

logger = logging.getLogger(__name__)


class Quickbooks(ThirdPartyApi):
    def update_refresh_token(
        self,
        client_id: int,
        access_token_config_id: int,
        new_refresh_token: str,
    ):
        """
        Helper method to update refresh token in the database when existing refresh token is expired.
        """
        atca = AccessTokenConfigAccessor(client_id)
        current_atc_obj = atca.get_object_by_id(access_token_config_id)

        if not current_atc_obj:
            logger.info(f"Newly acquired refresh_token - {new_refresh_token}")
            raise AccessTokenConfigObjectNotFoundError(
                client_id, access_token_config_id
            )

        if (
            current_atc_obj.access_request_body.get("refresh_token")
            != new_refresh_token
        ):
            logger.info(
                f"Updating newly acquired refresh_token {new_refresh_token} in "
                f"access_token_config"
            )
            atc_data = model_to_dict(current_atc_obj)
            access_request_body = atc_data["access_request_body"]
            access_request_body["refresh_token"] = new_refresh_token
            atca.update_access_request_body_and_status(
                access_token_config_id, access_request_body
            )

    def _get_formatted_changes_start_time(
        self, changes_start_time: datetime
    ) -> Tuple[str, datetime]:
        # changes_start_time should be in utc timezone and iso format
        if changes_start_time:
            if isinstance(changes_start_time, str):
                changes_start_time = parse(changes_start_time)
            changes_start_time = changes_start_time.astimezone(tz=timezone.utc)
        else:
            changes_start_time = make_aware(
                datetime.now(), timezone=timezone.utc
            ) - timedelta(days=7)

        return changes_start_time.strftime("%Y-%m-%dT%H:%M:%SZ"), changes_start_time

    def _get_extractor_params(
        self,
        request_body: dict,
        changes_start_time: datetime,
        updated_body_params_val: dict,
        is_report_integration: bool,
        additional_data: dict,
    ) -> dict:
        if not is_report_integration:
            changes_start_time, _ = self._get_formatted_changes_start_time(
                changes_start_time
            )
            updated_body_params_val["previous_time"] = f"'{changes_start_time}'"
            request_body["query"] = request_body["query"].format(
                **updated_body_params_val
            )
        else:
            if not additional_data:
                raise ValueError("Additional data not provided")

            report_type = additional_data.get("report_type", "").lower()
            date_macro = additional_data.get("date_macro")
            start_date = additional_data.get("start_date")
            end_date = additional_data.get("end_date")
            summarize_column_by = additional_data.get("summarize_column_by")

            if not report_type:
                raise ValueError("Report type is required")
            if not date_macro and not start_date and not end_date:
                raise ValueError(
                    "start_date and end_date, or a date macro, must be provided."
                )

            if summarize_column_by:
                request_body["summarize_column_by"] = summarize_column_by

            if date_macro:
                request_body["date_macro"] = date_macro
            else:
                request_body["start_date"] = start_date
                request_body["end_date"] = end_date

        return request_body

    def _parse_list_report_data(self, raw_data: dict) -> list:
        """
        A helper method to parse the `list` type report data.

        Args:
            raw_data (dict): The raw report data to parse.

        Returns:
            list: The parsed list report data.
        """
        rows = raw_data["Rows"]["Row"]
        parsed_data_list = []

        # Iterate through each row
        for row in rows:
            # Get parent columns and child rows for each row
            parent_columns = raw_data["Columns"]["Column"]
            child_rows = row.get("Rows", {}).get("Row", [])

            # Process each child row
            for child_row_indx, child_row in enumerate(child_rows):
                data_dict = {}
                curr_data_id = ""
                # Match parent columns with child row data
                for column, col_data in zip(
                    parent_columns, child_row.get("ColData", [])
                ):
                    column_title = column.get("ColTitle", "").strip()
                    column_value = col_data.get("value", "")

                    if "id" in col_data:
                        col_id = col_data.get("id")
                        if curr_data_id:
                            curr_data_id += f"##{col_id}"
                        else:
                            curr_data_id = col_id
                        data_dict[column_title] = {
                            "id": col_id,
                            "object_value": column_value,
                        }
                    else:
                        data_dict[column_title] = column_value

                # Add processed data to the list
                data_dict["Id"] = f"{child_row_indx}##{curr_data_id}"
                parsed_data_list.append(data_dict)

        return parsed_data_list

    def _process_row_level_data(self, rows, index, current_data):
        """
        A helper method to process row-level data recursively to populate the current_data dictionary.
        Used to parse `Transaction` type report data.

        Args:
            rows (list): List of rows containing data to be processed.
            index (int): Index indicating the column to process in each row.
            current_data (dict): Dictionary to populate with processed data.

        Returns:
            None
        """
        for row in rows:
            header_col_data = row.get("Header", {}).get("ColData", [])
            summary_col_data = row.get("Summary", {}).get("ColData", [])
            row_col_data = row.get("ColData", [])

            # Process header column data if available
            if header_col_data:
                section_name = header_col_data[0].get("value", "")
                section_value = header_col_data[index + 1].get("value", "")
                current_data[section_name] = {"object_value": section_value}

            # Process regular row column data
            if row_col_data:
                col_name = row_col_data[0].get("value", "")
                col_value = row_col_data[index + 1].get("value", "")
                current_data[col_name] = col_value

            # Recursively process sub-rows if they exist
            if "Rows" in row and header_col_data:
                parent_name = header_col_data[0].get("value", "")
                self._process_row_level_data(
                    row["Rows"]["Row"], index, current_data[parent_name]
                )

            # Process summary column data
            if summary_col_data:
                parent_name = (
                    header_col_data[0].get("value", "") if header_col_data else None
                )  # type-ignore
                section_name = summary_col_data[0].get("value", "")
                section_value = summary_col_data[index + 1].get("value", "")

                # Attach summary data to appropriate parent or directly to current_data
                if parent_name:
                    current_data[parent_name][section_name] = section_value
                else:
                    current_data[section_name] = section_value

    def _parse_transaction_report_data(self, raw_data, processed_data):
        """
        Parses the transaction report data and populates the processed_data list.

        Args:
            raw_data (dict): The raw data containing rows and columns.
            processed_data (list): The list to store the processed data.

        Returns:
            None
        """

        rows = raw_data["Rows"]["Row"]
        columns = raw_data["Columns"]["Column"]
        header = raw_data["Header"]
        ## Create top level schema
        for i, col in enumerate(columns):
            if i > 0 and "MetaData" in col:
                start_date = next(
                    (
                        item["Value"]
                        for item in col["MetaData"]
                        if item["Name"] == "StartDate"
                    ),
                    None,
                )
                end_date = next(
                    (
                        item["Value"]
                        for item in col["MetaData"]
                        if item["Name"] == "EndDate"
                    ),
                    None,
                )
                col_title = col.get("ColTitle", "").strip()
                if col_title.lower() == "total":
                    processed_data.append(
                        {
                            "start_date": header.get("StartPeriod"),
                            "end_date": header.get("EndPeriod"),
                            "col_title": col_title,
                        }
                    )
                if start_date and end_date:
                    processed_data.append(
                        {
                            "start_date": start_date,
                            "end_date": end_date,
                            "col_title": col_title,
                        }
                    )

        # Call the recursive method to generate schema
        for index, current_data in enumerate(processed_data):
            self._process_row_level_data(rows, index, current_data)

    def _parse_report_data(self, data: dict, additional_data: dict) -> list:
        """
        Parses the report data based on the report type.

        Args:
            data (dict): The report data to parse.
            additional_data (dict): A dictionary containing additional information about the report.

        Returns:
            list: The parsed report data.
        """
        report_type = additional_data.get("report_type", "").lower()
        processed_data = []
        if report_type == QuickbooksReportTypes.LIST.value:
            processed_data = self._parse_list_report_data(data)
        elif report_type == QuickbooksReportTypes.TRANSACTION.value:
            self._parse_transaction_report_data(data, processed_data)

        return processed_data

    def get_changed_records(
        self,
        client_id: int,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        object_id: str,
        primary_kd: datetime,
        changes_start_time: datetime,
        _unused_sync_mode: str,
        config_object: ApiAccessConfig,
        _unused_snapshot_key: str,
        _unused_destination_object_type: str,
        integration_id: UUID,
        is_validation: bool,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
    ):
        """
        Fetch records using quickbooks api

        Args:
            client_id: customer's unique id
            object_id: the source object to fetch from the CRM
            primary_kd: Knowledge start date used for snowflake table meta data
            changes_start_time: modified/created time from which we want to get the records
            sync_mode: contains mode of string e.g. "all", "changes" ...
            config_object: contains the URL to access the resource
            _unused_snapshot_key: parameter unused
            _unused_destination_object_type: parameter unused
            integration_id: Id used to get run_snowflake_sync flag

        Returns: list of data fetched for an object
        """
        module_name = "everstage_etl.tasks.extraction"
        module = import_module(module_name)
        insert_extract_sync_log = getattr(module, "insert_extract_sync_log")

        if source_primary_keys is None:
            source_primary_keys = []
        # pylint: disable=unused-variable
        old_log_context = get_threadlocal_context()
        new_log_context = {
            "client_id": client_id,
            "object_id": object_id,
            "changes_start_time": changes_start_time,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": sync_run_id,
        }

        merged_log_context = merge_log_context(old_log_context, new_log_context)

        set_threadlocal_context(merged_log_context)

        logger.info(f"RUNNING CHANGES SYNC.. {object_id}")

        id_attr = pydash.get(config_object.additional_data, "result_key")
        composite_result_key: List[str] = pydash.get(config_object.additional_data, "composite_result_key", [])  # type: ignore

        if id_attr is None and len(composite_result_key) == 0:
            raise Exception(
                f"No result key / composite result key present for {object_id} for {client_id}"
            )

        # Handle Report
        integration_object = IntegrationAccessor(
            client_id
        ).get_integration_by_integration_id(config_object.integration_id)
        is_report_integration = False
        if integration_object.additional_data is not None:
            is_report_integration = integration_object.additional_data.get(
                "is_report_integration", False
            )

        integration_record = IntegrationAccessor(
            client_id=client_id
        ).get_object_by_integration_id(integration_id=integration_id)
        additional_data = integration_record.additional_data  # type: ignore
        run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)

        if run_snowflake_sync:
            snowflake_uploader = UpstreamChangesWriter(
                client_id=client_id,
                integration_id=integration_id,
                is_validation=is_validation,
            )
            snowflake_uploader.set_meta(is_deleted=False, updated_on=primary_kd)

        curr_page = 0
        visited = set()
        start_position = 1
        total_so_far = 0
        final_result = []
        response_key = config_object.response_key
        additional_data = config_object.additional_data or {}
        request_body = self._get_extractor_params(
            deepcopy(config_object.request_body),
            changes_start_time,
            {"start_position": 1},
            is_report_integration,
            additional_data,
        )

        logger.info(f"BEGIN: IMPORT DATA FROM QUICKBOOKS {object_id}")
        while True:
            logger.info(f"REQUEST BODY... {request_body}")
            page_records = []
            curr_page += 1
            part_result = self.api_call(
                client_id=client_id,
                url=config_object.request_url,
                body=request_body,
                api_config_object=config_object,
                is_validation=is_validation,
                refresh_token_callback=self.update_refresh_token,
            )

            # extract data from response
            if is_report_integration:
                data = self._parse_report_data(part_result, additional_data)
            else:
                data = part_result.get("QueryResponse", {}).get(response_key, [])
            if not data:
                break

            # ignore duplicate records and process lists
            for _data in data:
                if len(composite_result_key) != 0:
                    _id = "##::##".join(
                        [_data.get(crk) for crk in composite_result_key]
                    )
                else:
                    _id = _data.get(id_attr)

                if _id not in visited:
                    visited.add(_id)
                    if config_object.additional_data.get("process_list") == "True":
                        processed = process_lines(_data, config_object.additional_data)
                        page_records.extend(processed)
                        total_so_far += len(processed)
                    else:
                        page_records.append(_data)
                        total_so_far += 1

            # added sleep to avoid secondly rate limit
            sleep(0.5)

            if run_snowflake_sync and page_records:
                snowflake_uploader.save_in_temp_table(page_records)
            elif upstream_etl_version == UpstreamETLVersions.V1.value:
                final_result.extend(page_records)
            else:
                logger.info(
                    f"BEGIN: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                )
                insert_extract_sync_log(
                    client_id,
                    e2e_sync_run_id,
                    sync_run_id,
                    object_id,
                    page_records,
                    "change",
                    source_primary_keys=source_primary_keys,
                )
                logger.info(
                    f"END: Batch insert current page ({curr_page}) records of length {len(page_records)} to extraction_sync_log"
                )

            # length of data < 1000 implies there are no more pages to be fetched
            # is_validation is True then exit the loop to avoid unnecessary fetching of records.
            # is_report_integration is True then exit the loop as there won't be any more records to be fetched.
            if len(data) < 1000 or is_validation or is_report_integration:
                break

            start_position += 1000
            request_body = self._get_extractor_params(
                deepcopy(config_object.request_body),
                changes_start_time,
                {"start_position": start_position},
                is_report_integration,
                additional_data,
            )

            logger.info(
                f"No of records fetched so far - {total_so_far}, page - {curr_page}"
            )

        logger.info(
            f"Done making Api Calls for {object_id} with total pages {curr_page} and {total_so_far} records"
        )

        if run_snowflake_sync:
            snowflake_uploader.save()

        UpstreamETLStatusAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        ).update_object({"extracted_records_count": total_so_far})

        logger.info("END: IMPORT DATA FROM QUICKBOOKS")

        return final_result
