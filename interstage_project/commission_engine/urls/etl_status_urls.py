from django.urls import path

from commission_engine.views.etl_status_views import (
    AllFailedCronJobs,
    AllFailedManualJobs,
    AllRunningSync,
    MarkCronJobAsFailed,
    MarkManualJobAsFailed,
    MarkSyncAsSkipped,
    RemoveEtlLock,
    RetryCronJob,
)

urlpatterns = [
    path(
        "all_running_syncs/",
        AllRunningSync.as_view(),
        name="all_running_syncs",
    ),
    path(
        "remove_etl_lock",
        RemoveEtlLock.as_view(),
        name="remove_etl_lock",
    ),
    path(
        "cron_jobs/failed",
        AllFailedCronJobs.as_view(),
        name="all_failed_cron_jobs",
    ),
    path(
        "cron_jobs/mark_as_failed/",
        MarkCronJobAsFailed.as_view(),
        name="mark_cron_job_as_failed",
    ),
    path(
        "cron_jobs/retry/",
        RetryCronJob.as_view(),
        name="retry_cron_job",
    ),
    path(
        "manual_jobs/failed",
        AllFailedManualJobs.as_view(),
        name="all_failed_manual_jobs",
    ),
    path(
        "manual_jobs/mark_as_failed/",
        MarkManualJobAsFailed.as_view(),
        name="mark_manual_job_as_failed",
    ),
    path(
        "syncs/mark_as_skipped/",
        MarkSyncAsSkipped.as_view(),
        name="mark_sync_as_skipped",
    ),
]
