import uuid

import pytest

from commission_engine.accessors.client_accessor import get_client_subscription_plan
from commission_engine.accessors.etl_housekeeping_accessor import ETLSyncStatusAccessor
from commission_engine.models.etl_housekeeping_models import ETLSyncStatus
from commission_engine.services.etl_sync_status_service import (
    get_all_failed_cron_jobs,
    get_all_failed_manual_jobs,
    get_etl_params,
    get_sync_without_end_time,
    get_sync_without_end_time_across_clients,
    is_end_to_end_sync_running,
    is_upstream_sync_running,
    mark_cron_job_as_failed,
    mark_manual_job_as_failed,
    mark_sync_as_skipped,
)
from commission_engine.utils.general_data import ETL_STATUS


@pytest.mark.django_db
class TestETLSyncStatusService:
    @pytest.mark.parametrize(
        "client_id, expected_end_to_end_sync_status",
        [
            (4005, True),
            (4, False),
        ],
    )
    def test_is_end_to_end_sync_running(
        self, client_id, expected_end_to_end_sync_status
    ):
        """
        Test is_end_to_end_sync_running.
        client_id, 4 - `Crystal - Customer` & 4005 - `sync_client`.
        """
        end_to_end_sync_status = is_end_to_end_sync_running(client_id=client_id)
        assert end_to_end_sync_status == expected_end_to_end_sync_status

    @pytest.mark.parametrize(
        "client_id, expected_upstream_sync_status",
        [
            (4005, True),
            (4, False),
        ],
    )
    def test_is_upstream_sync_running(self, client_id, expected_upstream_sync_status):
        """
        Test is_upstream_sync_running.
        client_id, 4 - `Crystal - Customer` & 4005 - `sync_client`.
        """
        upstream_sync_status = is_upstream_sync_running(client_id=client_id)
        assert upstream_sync_status == expected_upstream_sync_status


@pytest.mark.django_db
def test_get_etl_params():
    """
    Test get_etl_params.
    """
    client_id = 3001
    e2e_sync_run_id = "26683f98-0cc8-45e2-918e-9d5462f65875"
    etl_params = get_etl_params(client_id=client_id, e2e_sync_run_id=e2e_sync_run_id)
    assert etl_params == {"datasheet_id": "a43c1d4f-865f-42d9-a652-fab5664f0e5c"}


@pytest.mark.django_db
def test_get_sync_without_end_time():
    """
    Test get_sync_without_end_time.
    """
    client_id = 3001
    running_syncs = get_sync_without_end_time(client_id)
    subscription_plan = get_client_subscription_plan(client_id)

    # check if the sync is running
    for each_sync in running_syncs:
        if each_sync["additional_info__is_cron_job"]:
            assert each_sync["task"].endswith("(Cron)")

        assert each_sync["subscription_plan"] == subscription_plan


@pytest.mark.django_db
def test_get_sync_without_end_time_across_clients():
    """
    Test get_sync_without_end_time_across_clients.
    """
    running_syncs = get_sync_without_end_time_across_clients()
    for each_sync in running_syncs:
        client_id = each_sync["client_id"]
        subscription_plan = get_client_subscription_plan(client_id)
        assert each_sync["subscription_plan"] == subscription_plan

        if each_sync["additional_info__is_cron_job"]:
            assert each_sync["task"].endswith("(Cron)")


@pytest.mark.django_db
def test_get_all_failed_cron_jobs():
    """
    Test get_all_failed_cron_jobs.
    """
    failed_cron_jobs = get_all_failed_cron_jobs()

    # check if they are cron jobs
    for each_cron_job in failed_cron_jobs:
        assert each_cron_job["additional_info"]["is_cron_job"] is True

    # check if they are partially failed
    for each_cron_job in failed_cron_jobs:
        assert each_cron_job["sync_status"] == ETL_STATUS.PARTIALLY_FAILED.value

    # check if they are not marked as failed by any user
    for each_cron_job in failed_cron_jobs:
        assert each_cron_job["marked_as_failed_by"] is None


@pytest.mark.django_db
def test_mark_cron_job_as_failed():
    """
    Test mark_cron_job_as_failed.
    """
    client_id = 3001
    e2e_sync_run_id = str(uuid.uuid4())

    ETLSyncStatus.objects.create(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        additional_info={"is_cron_job": True, "marked_as_failed_by": None},
    )

    mark_cron_job_as_failed(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, user_email="<EMAIL>"
    )

    accessor = ETLSyncStatusAccessor(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
    )
    obj = accessor.get_object()[0]
    assert obj.additional_info["marked_as_failed"] is True
    assert obj.additional_info["marked_as_failed_by"] == "<EMAIL>"


@pytest.mark.django_db
def test_mark_sync_as_skipped_success():
    """Test successfully marking a manual job as skipped."""
    client_id = 3001
    e2e_sync_run_id = str(uuid.uuid4())
    user_email = "<EMAIL>"
    reason = "Manual skip for testing."

    # Create a manual job
    sync = ETLSyncStatus.objects.create(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        additional_info={"is_cron_job": False},
    )

    mark_sync_as_skipped(client_id, e2e_sync_run_id, user_email, reason)
    sync.refresh_from_db()
    assert sync.additional_info["skipped"] is True
    assert sync.additional_info["skipped_by"] == user_email
    assert sync.additional_info["skipped_reason"] == reason


@pytest.mark.django_db
def test_mark_sync_as_skipped_missing_reason():
    """Test skipping a sync with missing reason should raise Exception."""
    client_id = 3001
    e2e_sync_run_id = str(uuid.uuid4())
    user_email = "<EMAIL>"
    reason = ""

    # Create a manual job
    ETLSyncStatus.objects.create(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        additional_info={"is_cron_job": False},
    )

    with pytest.raises(Exception) as exc_info:
        mark_sync_as_skipped(client_id, e2e_sync_run_id, user_email, reason)
    assert "Reason is required" in str(exc_info.value)


@pytest.mark.django_db
def test_mark_manual_job_as_failed_success():
    """Test successfully marking a manual job as failed."""
    client_id = 3001
    e2e_sync_run_id = str(uuid.uuid4())
    user_email = "<EMAIL>"

    # Create a manual job
    sync = ETLSyncStatus.objects.create(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        additional_info={"is_cron_job": False},
    )

    mark_manual_job_as_failed(client_id, e2e_sync_run_id, user_email)
    sync.refresh_from_db()
    assert sync.additional_info["marked_as_failed"] is True
    assert sync.additional_info["marked_as_failed_by"] == user_email


@pytest.mark.django_db
def test_mark_manual_job_as_failed_cron_job():
    """Test marking a cron job as failed using mark_manual_job_as_failed should raise Exception."""
    client_id = 3001
    e2e_sync_run_id = str(uuid.uuid4())
    user_email = "<EMAIL>"

    # Create a cron job
    ETLSyncStatus.objects.create(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        additional_info={"is_cron_job": True},
    )

    with pytest.raises(Exception) as exc_info:
        mark_manual_job_as_failed(client_id, e2e_sync_run_id, user_email)
    assert "Sync is not a manual job" in str(exc_info.value)


@pytest.mark.django_db
def test_get_all_failed_manual_jobs():
    """Test getting all failed manual jobs."""
    all_failed_manual_jobs = get_all_failed_manual_jobs()

    for each_failed_manual_job in all_failed_manual_jobs:
        assert each_failed_manual_job.additional_info["is_cron_job"] is False
        assert each_failed_manual_job.additional_info["marked_as_failed"] is True
        assert each_failed_manual_job.additional_info["marked_as_failed_by"] is not None
