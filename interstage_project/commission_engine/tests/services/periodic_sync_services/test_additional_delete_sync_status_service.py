from datetime import datetime, timezone
from unittest.mock import ANY, MagicMock, patch

import pytest
from django.utils import timezone
from django_celery_beat.models import CrontabSchedule

from commission_engine.models.hard_delete_models import HardDeletePeriodicTask
from commission_engine.services.etl_tasks_service import should_run_in_delete_sync_mode
from commission_engine.services.periodic_sync_services.hard_delete_sync_status_service import (
    add_new_hard_delete_sync,
    delete_hard_delete_sync,
    get_hard_delete_sync_status_for_client,
    update_hard_delete_sync_status,
)


@pytest.mark.django_db
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.LogWithContext"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.get_client"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.HardDeleteAccessor"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.CrontabScheduleAccessor"
)
def test_get_hard_delete_sync_status_for_client(
    mock_CrontabScheduleAccessor,
    mock_HardDeleteAccessor,
    mock_get_client,
    mock_LogWithContext,
):
    # Arrange
    client_id = 1
    client_name = "Test Client"

    mock_client = MagicMock()
    mock_client.name = client_name
    mock_get_client.return_value = mock_client

    current_time = timezone.now()

    task1 = MagicMock()
    task1.crontab_id = 101
    task1.knowledge_begin_date = current_time
    task1.enabled = True
    task1.date_changed = current_time

    task2 = MagicMock()
    task2.crontab_id = 102
    task2.knowledge_begin_date = current_time
    task2.enabled = False
    task2.date_changed = current_time

    mock_HardDeleteAccessor.return_value.get_task_by_client_id.return_value = [task1]

    schedules = {
        101: {
            "minute": "0",
            "hour": "0",
            "day_of_week": "*",
            "day_of_month": "*",
        },
        102: {
            "minute": "0",
            "hour": "0",
            "day_of_week": "1",
            "day_of_month": "2",
        },
    }
    mock_CrontabScheduleAccessor.return_value.get_cron_schedule_by_id.return_value = [
        {"id": 101, **schedules[101]},
    ]

    # Act
    result1 = get_hard_delete_sync_status_for_client(client_id)

    mock_HardDeleteAccessor.return_value.get_task_by_client_id.return_value = [task2]

    mock_CrontabScheduleAccessor.return_value.get_cron_schedule_by_id.return_value = [
        {"id": 102, **schedules[102]},
    ]

    result2 = get_hard_delete_sync_status_for_client(client_id)
    # Assert
    expected_result1 = {
        "additional_delete_syncs": [
            {
                "cron_expression": {
                    "minute": "0",
                    "hour": "0",
                    "day_of_week": "*",
                    "day_of_month": "*",
                },
                "client_name": client_name,
                "client_id": client_id,
                "enabled": True,
                "date_changed": current_time,
            }
        ]
    }
    expected_result2 = {
        "additional_delete_syncs": [
            {
                "cron_expression": {
                    "minute": "0",
                    "hour": "0",
                    "day_of_week": "1",
                    "day_of_month": "2",
                },
                "client_name": client_name,
                "client_id": client_id,
                "enabled": False,
                "date_changed": current_time,
            },
        ]
    }

    assert result1 == expected_result1
    assert result2 == expected_result2
    mock_LogWithContext.assert_called_with({"client_id": client_id})
    mock_LogWithContext().info.assert_any_call(
        f"Fetching all additional delete sync status for client ID {client_id}"
    )
    # mock_LogWithContext().info.assert_any_call(
    #     f"Retrieved {len(tasks)} additional delete sync tasks"
    # )
    # mock_LogWithContext().info.assert_any_call(
    #     f"Compiled {len(expected_result['additional_delete_syncs'])} additional delete syncs"
    # )


@pytest.mark.django_db
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.LogWithContext"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.CrontabScheduleAccessor"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.HardDeleteAccessor"
)
def test_update_hard_delete_sync_status(
    mock_HardDeleteAccessor,
    mock_CrontabScheduleAccessor,
    mock_LogWithContext,
):
    # Arrange
    client_id = 1
    status = "enabled"
    cron_expression = {
        "minute": "0",
        "hour": "0",
        "day_of_week": "*",
        "day_of_month": "*",
    }

    mock_cron_tab_schedule = MagicMock()
    mock_CrontabScheduleAccessor.return_value.get_or_create_cron_expression.return_value = (
        mock_cron_tab_schedule
    )

    # Act
    update_hard_delete_sync_status(
        client_id=client_id, status=status, cron_expression=cron_expression, audit={}
    )

    # Assert
    mock_LogWithContext.assert_called_with({"client_id": client_id})
    mock_LogWithContext().info.assert_any_call(
        f"Updating additional delete sync status for client ID {client_id}"
    )

    mock_CrontabScheduleAccessor.return_value.get_or_create_cron_expression.assert_called_once_with(
        minute=cron_expression["minute"],
        hour=cron_expression["hour"],
        day_of_week=cron_expression["day_of_week"],
        day_of_month=cron_expression["day_of_month"],
        month_of_year="*",
    )
    mock_HardDeleteAccessor.return_value.update_task_details.assert_called_once_with(
        status=status, cron_tab=mock_cron_tab_schedule, audit={}
    )


@pytest.mark.django_db
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.LogWithContext"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.HardDeleteAccessor"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.CrontabScheduleAccessor"
)
def test_add_new_hard_delete_sync(
    mock_CrontabScheduleAccessor,
    mock_HardDeleteAccessor,
    mock_LogWithContext,
):
    # Arrange
    client_id = 1
    cron_expression = {
        "minute": "0",
        "hour": "0",
        "day_of_week": "*",
        "day_of_month": "*",
    }

    mock_HardDeleteAccessor.return_value.get_additional_delete_upstream_sync_tasks_for_client.return_value = (
        []
    )

    mock_cron_tab_schedule = MagicMock()
    mock_CrontabScheduleAccessor.return_value.get_or_create_cron_expression.return_value = (
        mock_cron_tab_schedule
    )

    periodic_task_acc_obj = mock_HardDeleteAccessor.return_value

    # Act
    add_new_hard_delete_sync(
        client_id=client_id, cron_expression=cron_expression, audit={}
    )

    # Assert
    mock_LogWithContext.assert_called_with({"client_id": client_id})
    mock_LogWithContext().info.assert_any_call(
        f"Adding new additional delete sync for client ID {client_id}"
    )

    mock_CrontabScheduleAccessor.return_value.get_or_create_cron_expression.assert_called_once_with(
        minute=cron_expression["minute"],
        hour=cron_expression["hour"],
        day_of_week=cron_expression["day_of_week"],
        day_of_month=cron_expression["day_of_month"],
        month_of_year="*",
    )

    periodic_task_acc_obj.get_or_create_hard_delete_task.assert_called_once_with(
        mock_cron_tab_schedule, {}
    )

    mock_LogWithContext().info.assert_any_call(f"Task created for client: {client_id}")
    add_new_hard_delete_sync(
        client_id=client_id, cron_expression=cron_expression, audit={}
    )


@pytest.mark.django_db
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.LogWithContext"
)
@patch(
    "commission_engine.services.periodic_sync_services.hard_delete_sync_status_service.HardDeleteAccessor"
)
def test_delete_hard_delete_sync(mock_HardDeleteAccessor, mock_LogWithContext):
    # Arrange
    client_id = 1

    periodic_task_acc_obj = mock_HardDeleteAccessor.return_value

    # Act
    delete_hard_delete_sync(client_id=client_id, audit={})

    # Assert
    mock_LogWithContext.assert_called_with({"client_id": client_id})
    mock_LogWithContext().info.assert_any_call(
        f"Deleting Additional delete sync for client {client_id}"
    )
    periodic_task_acc_obj.remove_task_by_client_id.assert_called_once_with({})
    mock_LogWithContext().info.assert_any_call("Task deleted successfully")


@pytest.mark.django_db
@pytest.mark.parametrize(
    "test_scenario, current_date, schedule_configs, expected_result",
    [
        # No tasks scenario
        (
            "no_tasks",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # Sunday, 12th
            [],
            False,
        ),
        # Daily schedule
        (
            "daily_schedule",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),
            [{"day_of_week": "*", "day_of_month": "*", "enabled": True}],  # Every day
            True,
        ),
        # Weekly schedule with current day match
        (
            "weekly_match",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # Sunday
            [{"day_of_week": "7", "day_of_month": "*", "enabled": True}],  # Sunday
            True,
        ),
        # Weekly schedule with no match
        (
            "weekly_no_match",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # Sunday
            [{"day_of_week": "1", "day_of_month": "*", "enabled": True}],  # Monday
            False,
        ),
        # Monthly schedule with current day match
        (
            "monthly_match",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # 12th day
            [{"day_of_week": "*", "day_of_month": "12", "enabled": True}],  # 12th
            True,
        ),
        # Monthly schedule with no match
        (
            "monthly_no_match",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # 12th
            [{"day_of_week": "*", "day_of_month": "1", "enabled": True}],  # 1st
            False,
        ),
        # Multiple tasks with one match
        (
            "multiple_tasks_one_match",
            datetime(2025, 1, 13, 10, 30, tzinfo=timezone.utc),  # Monday 13th
            [
                {"day_of_week": "3", "day_of_month": "*", "enabled": True},  # Wednesday
                {"day_of_week": "*", "day_of_month": "10", "enabled": True},  # 10th
                {"day_of_week": "1", "day_of_month": "*", "enabled": True},  # Monday
            ],
            True,
        ),
        # Multiple tasks with no matches
        (
            "multiple_tasks_no_match",
            datetime(2025, 1, 13, 10, 30, tzinfo=timezone.utc),  # Monday 13th
            [
                {"day_of_week": "3", "day_of_month": "*", "enabled": True},  # Wednesday
                {"day_of_week": "*", "day_of_month": "10", "enabled": True},  # 10th
            ],
            False,
        ),
        # Multiple tasks with one enabled match and one disabled match
        (
            "mixed_enabled_disabled_tasks",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # Sunday
            [
                {
                    "day_of_week": "7",
                    "day_of_month": "*",
                    "enabled": False,
                },  # Sunday, disabled
                {
                    "day_of_week": "7",
                    "day_of_month": "*",
                    "enabled": True,
                },  # Sunday, enabled
            ],
            True,
        ),
        # Disabled tasks with otherwise matching schedules
        (
            "all_disabled_tasks",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # Sunday
            [
                {"day_of_week": "7", "day_of_month": "*", "enabled": False},  # Sunday
                {"day_of_week": "*", "day_of_month": "*", "enabled": False},  # Daily
                {"day_of_week": "*", "day_of_month": "12", "enabled": False},  # 12th
            ],
            False,
        ),
        # Weekly-sunday
        (
            "sunday_specific",
            datetime(2025, 1, 12, 10, 30, tzinfo=timezone.utc),  # Sunday
            [
                {
                    "day_of_week": "0",
                    "day_of_month": "*",
                    "enabled": True,
                },  # Sunday (0 or 7)
            ],
            True,
        ),
    ],
)
@patch("commission_engine.services.etl_tasks_service.now")
def test_should_run_in_delete_sync_mode(
    mock_now, test_scenario, current_date, schedule_configs, expected_result
):
    """
    Test whether current daily sync runs in hard delete mode based on configured schedule
    """
    # Mock current time
    mock_now.return_value = current_date

    client_id = 1

    # Create CrontabSchedule and HardDeletePeriodicTask records
    for config in schedule_configs:
        crontab = CrontabSchedule.objects.create(
            minute="*",
            hour="*",
            day_of_week=config["day_of_week"],
            day_of_month=config["day_of_month"],
            month_of_year="*",
        )

        HardDeletePeriodicTask.objects.create(
            client_id=client_id,
            crontab=crontab,
            enabled=config["enabled"],
            knowledge_begin_date=timezone.now(),
            is_deleted=False,
            knowledge_end_date=None,
        )

    # Test and assert the result
    result = should_run_in_delete_sync_mode(client_id)
    assert result is expected_result, (
        f"{test_scenario.upper()}: Expected sync to be run with hard delete "
        f"sync flag: {expected_result}, but got: {result}"
    )
