from datetime import datetime
from unittest.mock import MagicMock

import pytest

from commission_engine.services.maestro import (
    run_commission_for_multiple_period_postwork,
)
from everstage_ddd.settlement_v3.common import ExecutionMode


@pytest.mark.commission_engine
@pytest.mark.django_db
class TestMaestro:
    @pytest.mark.parametrize(
        "params, final_map, payees_without_plan",
        [
            (
                {
                    "client_id": 123,
                    "e2e_sync_run_id": "test-run-id",
                    "sync_date": "01/01/2024",
                    "sync_end_date": "31/01/2024",
                    "secondary_kd": "2024-01-01",
                    "payee_list": ["<EMAIL>"],
                    "notification_email_id": "<EMAIL>",
                    "databook_refresh_params": {
                        "system_report_objects": [],
                        "databook_ids_to_refresh": [],
                    },
                },
                {
                    "15/01/2024": ["<EMAIL>"],
                    "31/01/2024": ["<EMAIL>"],
                },
                [],
            ),
        ],
    )
    def test_run_commission_for_multiple_period_postwork_with_settlement_v3_dry_run(
        self, monkeypatch, params, final_map, payees_without_plan
    ):
        # Mock get_settlement_v3_execution_mode to return DRY_RUN
        mock_get_execution_mode = MagicMock(return_value=ExecutionMode.DRY_RUN)
        mock_dry_run_settlement_v3_for_multiple_periods = MagicMock()
        monkeypatch.setattr(
            "commission_engine.services.maestro.get_settlement_v3_execution_mode",
            mock_get_execution_mode,
        )
        monkeypatch.setattr(
            "commission_engine.services.maestro.dry_run_settlement_v3_for_multiple_periods",
            mock_dry_run_settlement_v3_for_multiple_periods,
        )

        # Mock run_sync_stage to track calls
        mock_run_sync = MagicMock()
        monkeypatch.setattr(
            "commission_engine.services.maestro.run_sync_stage", mock_run_sync
        )

        # Run the function
        run_commission_for_multiple_period_postwork(
            params, final_map, payees_without_plan
        )

        # Calculate expected number of calls
        # 1 for pre-work stages
        # len(final_map) for commission stages (one per period)
        # 1 for post-work stages
        expected_calls = 1 + len(final_map) + 1

        # Verify run_sync_stage was called correct number of times
        assert mock_run_sync.call_count == expected_calls

        # Verify pre-work stages
        pre_work_call = mock_run_sync.call_args_list[0]
        assert "e2e_prework" in pre_work_call[0][0]
        assert "system_report_sync" in pre_work_call[0][0]
        assert "system_custom_databook_sync" in pre_work_call[0][0]
        assert "plan_modification" in pre_work_call[0][0]

        # Verify commission stages for each period
        for i in range(len(final_map)):
            commission_call = mock_run_sync.call_args_list[i + 1]
            assert "l1_payee_sync" in commission_call[0][0]
            assert "l1_team_sync" in commission_call[0][0]
            assert "l1_payout_snapshot" in commission_call[0][0]
            assert "l1_report_sync" in commission_call[0][0]
            assert "l1_databook_sync" in commission_call[0][0]
            assert "l2_payee_sync" in commission_call[0][0]
            assert "l2_team_sync" in commission_call[0][0]
            assert "invalidate_cache_for_tasks" in commission_call[0][0]
            assert "notify_attainment" in commission_call[0][0]
            assert "l2_payout_snapshot_sync" in commission_call[0][0]

            # Verify the sync_date in params matches the period date
            period_date = sorted(final_map.keys())[i]
            commission_params = commission_call[0][1]
            assert commission_params["sync_date"] == period_date

        # Verify post-work stages
        post_work_call = mock_run_sync.call_args_list[-1]
        assert "settlement_sync" in post_work_call[0][0]
        assert "payout_status_sync" in post_work_call[0][0]
        assert "settlement_snapshot_sync" in post_work_call[0][0]
        assert "l2_report_sync" in post_work_call[0][0]
        assert "l2_databook_etl" in post_work_call[0][0]
        assert "e2e_postwork" in post_work_call[0][0]

        # Verify the order of stages remains consistent
        commission_stages = commission_call[0][0].split(",")
        expected_order = [
            "l1_payee_sync",
            "l1_team_sync",
            "l1_payout_snapshot",
            "l1_report_sync",
            "l1_databook_sync",
            "l2_payee_sync",
            "l2_team_sync",
            "invalidate_cache_for_tasks",
            "notify_attainment",
            "l2_payout_snapshot_sync",
        ]
        assert commission_stages == expected_order
        assert mock_dry_run_settlement_v3_for_multiple_periods.call_count == 1

        # Assert the params passed to mock_dry_run_settlement_v3_for_multiple_periods
        dry_run_call_args, dry_run_call_kwargs = (
            mock_dry_run_settlement_v3_for_multiple_periods.call_args
        )

        sorted_dict = {
            key: final_map[key]
            for key in sorted(
                final_map,
                key=lambda x: datetime.strptime(x, "%d/%m/%Y"),  # noqa: DTZ007
            )
        }

        assert dry_run_call_args[0] == params
        assert dry_run_call_args[1] == sorted_dict

    @pytest.mark.parametrize(
        "params, final_map, payees_without_plan",
        [
            (
                {
                    "client_id": 123,
                    "e2e_sync_run_id": "test-run-id",
                    "sync_date": "01/01/2024",
                    "sync_end_date": "31/01/2024",
                    "secondary_kd": "2024-01-01",
                    "payee_list": ["<EMAIL>"],
                    "notification_email_id": "<EMAIL>",
                    "databook_refresh_params": {
                        "system_report_objects": ["user", "quota"],
                        "databook_ids_to_refresh": [1, 2, 3],
                    },
                },
                {
                    "15/01/2024": ["<EMAIL>"],
                    "31/01/2024": ["<EMAIL>"],
                },
                [],
            ),
        ],
    )
    def test_run_commission_for_multiple_period_postwork_with_settlement_v3_actual_run(
        self, monkeypatch, params, final_map, payees_without_plan
    ):
        # Mock get_settlement_v3_execution_mode to return ACTUAL_RUN
        mock_get_execution_mode = MagicMock(return_value=ExecutionMode.ACTUAL_RUN)
        mock_dry_run_settlement_v3_for_multiple_periods = MagicMock()
        monkeypatch.setattr(
            "commission_engine.services.maestro.get_settlement_v3_execution_mode",
            mock_get_execution_mode,
        )
        monkeypatch.setattr(
            "commission_engine.services.maestro.dry_run_settlement_v3_for_multiple_periods",
            mock_dry_run_settlement_v3_for_multiple_periods,
        )
        # Mock run_sync_stage to track calls
        mock_run_sync = MagicMock()
        monkeypatch.setattr(
            "commission_engine.services.maestro.run_sync_stage", mock_run_sync
        )

        # Run the function
        run_commission_for_multiple_period_postwork(
            params, final_map, payees_without_plan
        )

        # Calculate expected number of calls
        # 1 for pre-work stages
        # len(final_map) for commission stages (one per period)
        # 1 for post-work stages
        expected_calls = 1 + len(final_map) + 1

        # Verify run_sync_stage was called correct number of times
        assert mock_run_sync.call_count == expected_calls

        # Verify pre-work stages
        pre_work_call = mock_run_sync.call_args_list[0]
        assert "e2e_prework" in pre_work_call[0][0]
        assert "system_report_sync" in pre_work_call[0][0]
        assert "system_custom_databook_sync" in pre_work_call[0][0]
        assert "plan_modification" in pre_work_call[0][0]

        # Verify commission stages for each period
        for i in range(len(final_map)):
            commission_call = mock_run_sync.call_args_list[i + 1]
            assert "l1_payee_sync" in commission_call[0][0]
            assert "l1_team_sync" in commission_call[0][0]
            assert "l1_payout_snapshot" in commission_call[0][0]
            assert "l1_report_sync" in commission_call[0][0]
            assert "l1_databook_sync" in commission_call[0][0]
            assert "l2_payee_sync" in commission_call[0][0]
            assert "l2_team_sync" in commission_call[0][0]
            assert "invalidate_cache_for_tasks" in commission_call[0][0]
            assert "notify_attainment" in commission_call[0][0]
            assert "l2_payout_snapshot_sync" in commission_call[0][0]
            assert (
                "settlement_sync" in commission_call[0][0]
            )  # Settlement v3 runs for each period

            # Verify the sync_date in params matches the period date
            period_date = sorted(final_map.keys())[i]
            commission_params = commission_call[0][1]
            assert commission_params["sync_date"] == period_date

        # Verify post-work stages
        post_work_call = mock_run_sync.call_args_list[-1]
        assert "payout_status_sync" in post_work_call[0][0]
        assert "settlement_snapshot_sync" in post_work_call[0][0]
        assert "l2_report_sync" in post_work_call[0][0]
        assert "l2_databook_etl" in post_work_call[0][0]
        assert "e2e_postwork" in post_work_call[0][0]

        # Verify the order of stages remains consistent
        commission_stages = commission_call[0][0].split(",")
        expected_order = [
            "l1_payee_sync",
            "l1_team_sync",
            "l1_payout_snapshot",
            "l1_report_sync",
            "l1_databook_sync",
            "l2_payee_sync",
            "l2_team_sync",
            "invalidate_cache_for_tasks",
            "notify_attainment",
            "l2_payout_snapshot_sync",
            "settlement_sync",
        ]
        assert commission_stages == expected_order
        assert mock_dry_run_settlement_v3_for_multiple_periods.call_count == 0

    @pytest.mark.parametrize(
        "params, final_map, payees_without_plan",
        [
            (
                {
                    "client_id": 123,
                    "e2e_sync_run_id": "test-run-id",
                    "sync_date": "01/01/2024",
                    "sync_end_date": "31/01/2024",
                    "secondary_kd": "2024-01-01",
                    "payee_list": ["<EMAIL>"],
                    "notification_email_id": "<EMAIL>",
                    "databook_refresh_params": {
                        "system_report_objects": ["user", "quota"],
                        "databook_ids_to_refresh": [1, 2, 3],
                    },
                },
                {
                    "15/01/2024": ["<EMAIL>"],
                    "31/01/2024": ["<EMAIL>"],
                },
                [],
            ),
        ],
    )
    def test_run_commission_for_multiple_period_postwork_with_settlement_v3_disabled(
        self, monkeypatch, params, final_map, payees_without_plan
    ):
        # Mock get_settlement_v3_execution_mode to return DISABLED
        mock_get_execution_mode = MagicMock(return_value=ExecutionMode.DISABLED)
        mock_dry_run_settlement_v3_for_multiple_periods = MagicMock()
        monkeypatch.setattr(
            "commission_engine.services.maestro.get_settlement_v3_execution_mode",
            mock_get_execution_mode,
        )
        monkeypatch.setattr(
            "commission_engine.services.maestro.dry_run_settlement_v3_for_multiple_periods",
            mock_dry_run_settlement_v3_for_multiple_periods,
        )

        # Mock run_sync_stage to track calls
        mock_run_sync = MagicMock()
        monkeypatch.setattr(
            "commission_engine.services.maestro.run_sync_stage", mock_run_sync
        )

        # Run the function
        run_commission_for_multiple_period_postwork(
            params, final_map, payees_without_plan
        )

        # Calculate expected number of calls
        # 1 for pre-work stages
        # len(final_map) for commission stages (one per period)
        # 1 for post-work stages
        expected_calls = 1 + len(final_map) + 1

        # Verify run_sync_stage was called correct number of times
        assert mock_run_sync.call_count == expected_calls

        # Verify pre-work stages
        pre_work_call = mock_run_sync.call_args_list[0]
        assert "e2e_prework" in pre_work_call[0][0]
        assert "system_report_sync" in pre_work_call[0][0]
        assert "system_custom_databook_sync" in pre_work_call[0][0]
        assert "plan_modification" in pre_work_call[0][0]

        # Verify commission stages for each period
        for i in range(len(final_map)):
            commission_call = mock_run_sync.call_args_list[i + 1]
            assert "l1_payee_sync" in commission_call[0][0]
            assert "l1_team_sync" in commission_call[0][0]
            assert "l1_payout_snapshot" in commission_call[0][0]
            assert "l1_report_sync" in commission_call[0][0]
            assert "l1_databook_sync" in commission_call[0][0]
            assert "l2_payee_sync" in commission_call[0][0]
            assert "l2_team_sync" in commission_call[0][0]
            assert "invalidate_cache_for_tasks" in commission_call[0][0]
            assert "notify_attainment" in commission_call[0][0]
            assert "l2_payout_snapshot_sync" in commission_call[0][0]
            assert (
                "settlement_sync" not in commission_call[0][0]
            )  # Settlement runs only in post-work

            # Verify the sync_date in params matches the period date
            period_date = sorted(final_map.keys())[i]
            commission_params = commission_call[0][1]
            assert commission_params["sync_date"] == period_date

        # Verify post-work stages
        post_work_call = mock_run_sync.call_args_list[-1]
        assert (
            "settlement_sync" in post_work_call[0][0]
        )  # Old settlement runs once in post-work
        assert "payout_status_sync" in post_work_call[0][0]
        assert "settlement_snapshot_sync" in post_work_call[0][0]
        assert "l2_report_sync" in post_work_call[0][0]
        assert "l2_databook_etl" in post_work_call[0][0]
        assert "e2e_postwork" in post_work_call[0][0]

        # Verify the order of stages remains consistent
        commission_stages = commission_call[0][0].split(",")
        expected_order = [
            "l1_payee_sync",
            "l1_team_sync",
            "l1_payout_snapshot",
            "l1_report_sync",
            "l1_databook_sync",
            "l2_payee_sync",
            "l2_team_sync",
            "invalidate_cache_for_tasks",
            "notify_attainment",
            "l2_payout_snapshot_sync",
        ]
        assert commission_stages == expected_order
        assert mock_dry_run_settlement_v3_for_multiple_periods.call_count == 0
