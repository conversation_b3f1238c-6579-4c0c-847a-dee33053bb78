"""
Util script to bootstrap a freshly created database, that has been migrated, with the required minimal data on which
Selenium tests can be run.
In specific, this script does the following:
- Create default email templates for invites
- Create Crickinfo client
- Create superadmin user
- Insert data types, variables, operators
- Create countries: India, USA, Canada, Netherlands, Great Britain, France, Australia
This script is to be executed as:
~interstage_project % python manage.py runscript commission_engine.tests.create_bootstrap_data
"""

from django.db import connections

from commission_engine.accessors.client_accessor import (
    CRYSTAL_CUSTOM_PERIODS_DEFAULT,
    fiscal_start_month_number_map,
)
from commission_engine.management.commands.bootstrap_instance import (
    insert_data_types,
    insert_datasheet_filter_operators,
    insert_operators,
    insert_variables,
)
from commission_engine.tests.ever_object_data import (
    add_ever_objects,
    add_ever_variables,
)
from commission_engine.tests.primary_data_loader import (
    CLIENT_DETAILS,
    CLIENT_ESPNCRICKINFO,
    CLIENT_EVERSTAGE,
)
from commission_engine.utils.report_enums import RunSettlementReport
from everstage_admin_backend.services.create_client_service import (
    create_client_with_properties,
)
from interstage_project.utils import log_me
from spm.models.config_models.countries_models import Countries
from spm.models.config_models.employee_models import Employee
from spm.models.email_template_model import EmailTemplateDetails
from spm.rbac.permission_instance import bootstrap_default_roles, insert_permission_data

COUNTRIES = {
    "INDIA": ("IND", "India", "INR", "₹", "true", "en-IN", "false", "{}"),
    "USA": (
        "USA",
        "United States Of America",
        "USD",
        "$",
        "true",
        "en-US",
        "false",
        "{}",
    ),
    "CANADA": ("CAN", "Canada", "CAD", "CA$", "true", "en-CA", "false", "{}"),
    "NETHERLANDS": ("NLD", "Netherlands", "EUR", "€", "true", "nl-NL", "false", "{}"),
    "GBR": ("GBR", "United Kingdom", "GBP", "£", "true", "en-GB", "false", "{}"),
    "FRANCE": ("FRA", "France", "EUR", "€", "true", "fr-FR", "false", "{}"),
    "AUSTRALIA": ("AUS", "Australia", "AUD", "AU$", "true", "en-AU", "false", "{}"),
}
INSERT_COUNTRY_QUERY = "INSERT INTO countries(country_code, country_name, currency_code, currency_symbol, is_active, locale_id, is_client_specific, client_ids) VALUES"


EMAIL_TEMPLATES = {
    "email-password-invite": (
        "d-3a4f3fd41b734f12857a98bb3b5cf06e",
        "email-password-invite",
        "SEND_INVITE",
        "{}",
    ),
    "everstage-social-invite": (
        "d-de18807722c8459ba069f06ab5802df6",
        "everstage-social-invite",
        "SEND_INVITE",
        "{}",
    ),
}
INSERT_EMAIL_TEMPLATE_QUERY = "INSERT INTO email_template_details(template_id, template_name, email_event_code, client_ids) VALUES"


DEFAULT_DELETE_CLIENT_APPROVERS = [
    "<EMAIL>",
    "<EMAIL>",
]


def insert_countries(cursor):
    if Countries.objects.all().count() > 0:
        log_me("Countries already inserted")
        return

    for country, data in COUNTRIES.items():
        query = INSERT_COUNTRY_QUERY + " " + str(data)
        cursor.execute(query)


def insert_email_templates(cursor):
    if EmailTemplateDetails.objects.all().count() > 0:
        log_me("Email templates already inserted")
        return

    for template_name, data in EMAIL_TEMPLATES.items():
        query = INSERT_EMAIL_TEMPLATE_QUERY + " " + str(data)
        cursor.execute(query)


def insert_admin_users(cursor):
    email_ids = [
        "<EMAIL>",
        "<EMAIL>",
    ]
    role_map = bootstrap_default_roles(1)
    role = role_map["Power Admin"]
    INSERT_SUPERADMIN_CRICK_USER = f"""
        INSERT INTO employee(employee_email_id, first_name, last_name, user_role, client_id,
                             knowledge_begin_date, created_date, is_deleted, send_notification, status)
        VALUES ('<EMAIL>', 'super', 'admin', '["{role}"]',1,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'false', 'false', 'Active')
        """
    INSERT_SUPPORT_EVERSTAGE_USER = f"""
        INSERT INTO employee(employee_email_id, first_name, last_name, user_role, client_id,
                             knowledge_begin_date, created_date, is_deleted, send_notification, status)
        VALUES ('<EMAIL>', 'super', 'admin', '["{role}"]', 1,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'false', 'false', 'Active')
        """

    super_admin_count = Employee.objects.filter(
        employee_email_id="<EMAIL>"
    ).count()
    if super_admin_count == 0:
        cursor.execute(INSERT_SUPERADMIN_CRICK_USER)
    else:
        log_me("Super user already inserted")

    everstage_support_count = Employee.objects.filter(
        employee_email_id__in=email_ids
    ).count()

    if everstage_support_count == 0:
        cursor.execute(INSERT_SUPPORT_EVERSTAGE_USER)
    else:
        log_me("Everstage support user already inserted")


def run():
    # create client
    log_me("create client")
    cursor = connections["default"].cursor()
    month_number_name_map = {v: k for (k, v) in fiscal_start_month_number_map.items()}

    log_me("insert email templates")
    insert_email_templates(cursor)

    for client_name in (CLIENT_ESPNCRICKINFO, CLIENT_EVERSTAGE):
        client_params = CLIENT_DETAILS[client_name]
        client_params["connection_name"] = client_params["auth_connection_name"]
        client_params["connection_type"] = client_params["auth_connection_name"]
        client_params["client_notification"] = "false"
        client_params["payee_notification"] = "false"
        if client_params["fiscal_start_month"] in month_number_name_map.keys():
            client_params["fiscal_start_month"] = month_number_name_map[
                client_params["fiscal_start_month"]
            ]
        elif client_params["fiscal_start_month"] in month_number_name_map.values():
            client_params["fiscal_start_month"] = client_params["fiscal_start_month"]
        else:
            raise f"Invalid param fiscal_start_month: {client_params['fiscal_start_month']}"

        client_params["timezone"] = "UTC"
        client_params["status"] = "false"
        client_params["type"] = "false"
        client_params["hide_categories"] = ""
        client_params["show_commission_percent"] = "true"
        client_params["show_commission_buddy"] = "true"
        client_params["show_territory_plan"] = "false"
        client_params["upstream_etl"] = "true"
        client_params["commission_etl"] = "true"
        client_params["etl_technique"] = "snowflake"
        client_params["show_salesforce_integration"] = "true"
        client_params["show_superset_dashboard"] = "true"
        client_params["show_return_v1_button"] = "true"
        client_params["show_statements_v2"] = "true"
        client_params["crystal_version"] = "3"
        client_params["enable_ever_comparison"] = "true"
        client_params["manager_rollup_ed"] = "false"
        client_params["show_approval_feature"] = "true"
        client_params["show_roles"] = "true"
        client_params["show_datasheet_permission"] = "true"
        client_params["show_custom_object_permission"] = "true"
        client_params["delete_approvers"] = DEFAULT_DELETE_CLIENT_APPROVERS
        client_params["is_new_frozen_payroll_etl"] = "true"
        client_params["show_simulation_v2"] = "true"
        client_params["enable_concurrent_sessions"] = "false"
        client_params["datasheet_v2"] = "false"
        client_params["show_datasheet_v2"] = "false"
        client_params["datasheet_builder"] = "false"
        client_params["enable_support_user_access"] = "false"
        client_params["enable_tsar_webapp_custom_roles"] = "false"
        client_params["is_secure_admin_ui_auth0_user_mgmt"] = "false"
        client_params["allow_adjustments_to_frozen_commission"] = "false"
        client_params["salesforce_env"] = "production"
        client_params["subscription_plan"] = "BASIC"
        client_params["expose_comm_reports_in_plan"] = "false"
        client_params["show_chatgpt"] = "false"
        client_params["show_payout_table_breakdown"] = "false"
        client_params["isolated_snowflake_database"] = "false"
        client_params["plan_summary_model"] = "gpt-4-1106-preview"
        client_params["show_advanced_filter"] = "false"
        client_params["profile_picture_permission"] = "ALL"
        client_params["warn_on_unlock"] = "NONE"
        client_params["email_invite_template_id"] = "d-3a4f3fd41b734f12857a98bb3b5cf06e"
        client_params["show_statements_pdf"] = "true"
        client_params["custom_calendar"] = "false"
        client_params["commission_plan_version"] = "v2"
        client_params["show_forecast"] = "false"
        client_params["quota_effective_dated"] = "false"
        client_params["allow_quota_settings_override"] = "false"
        client_params["allow_annual_quota_effective_dated"] = "false"
        client_params["expressionbox_version"] = "v2"
        client_params["databook_expressionbox_version"] = "v2"
        client_params["calc_fields_strategy"] = "udf"
        client_params["calc_fields_lambda_chunk_size"] = 100000
        client_params["payout_snapshot_etl"] = "true"
        client_params["crm_hyperlinks"] = "false"
        client_params["enable_hris_integration"] = "false"
        client_params["evaluation_mode"] = "serial"
        client_params["split_summation_to_li"] = "false"
        client_params["show_metrics"] = "false"
        client_params["avoid_iframe_in_contracts"] = "false"
        client_params["take_ds_snapshot"] = "false"
        client_params["use_multi_engine_stormbreaker"] = "false"
        client_params["enable_custom_workflows"] = "false"
        client_params["enable_custom_theme"] = "false"
        client_params["enable_sidebar_v3"] = "false"
        client_params["enable_multi_language_support"] = "false"
        client_params["enable_rounding_in_tier_functions"] = "false"
        client_params["show_data_sources_v2"] = "false"
        client_params["created_by"] = "bootstrap script"
        client_params["upstream_etl_version"] = "v1"
        client_params["chrome_extension_enabled"] = "false"
        client_params["object_knowledge_date_query_strategy"] = "postgres"
        client_params["show_get_user_property_commission"] = "true"
        client_params["settlement_v2"] = "true"
        client_params["edit_locked_quota"] = "false"
        client_params["is_auto_enrich_report"] = "false"
        client_params["async_export_datasheet"] = "false"
        client_params["use_aggrid_for_pdf_export"] = "true"
        client_params["crystal_custom_calendar_future_periods"] = str(
            CRYSTAL_CUSTOM_PERIODS_DEFAULT
        )
        client_params["run_settlement_report"] = RunSettlementReport.IF_NEEDED.value
        client_params["enable_everai"] = "false"
        client_params["insert_meta_data_to_vec_db"] = "false"
        client_params["crystal_custom_calendar_future_periods"] = str(
            CRYSTAL_CUSTOM_PERIODS_DEFAULT
        )
        client_params["show_g2_review_form"] = "Off"
        client_params["allow_only_admins_to_modify_user_name"] = "false"
        client_params["modules"] = "ICM"
        client_params["allow_csv_upload_bulk_payment_register"] = "false"

        log_me("insert data types")
        insert_data_types()
        log_me("insert variables")
        insert_variables()
        log_me("insert operators")
        insert_operators()

        create_client_with_properties(
            client_params, create_super_user=False, create_datasheet_data=False
        )

    log_me("insert datasheet filter operators")
    insert_datasheet_filter_operators()
    log_me("insert admin users")
    insert_admin_users(cursor)
    log_me("insert countries")
    insert_countries(cursor)

    log_me("Populate ever_objects table")
    add_ever_objects()
    log_me("Populate ever_objects_variables table")
    add_ever_variables()
    log_me("Populate permission table")
    insert_permission_data(1)
    insert_permission_data(2)
