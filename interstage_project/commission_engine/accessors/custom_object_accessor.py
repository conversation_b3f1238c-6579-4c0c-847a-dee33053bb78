# pylint: disable=pointless-string-statement, unused-argument
import datetime
import logging
from typing import Any, Dict, List, Optional

import pytz
from django.db import transaction
from django.db.models import Count, Max, Q, QuerySet
from django.db.models.functions import Lower
from django.utils import timezone
from pydash import snake_case

from commission_engine.accessors.client_accessor import (
    get_object_knowledge_date_query_strategy,
)
from commission_engine.accessors.skd_pkd_map_accessor import CustomObjectPkdMapAccessor
from commission_engine.custom_types.self_service_integration_types import (
    CustomObjectVariableType,
)
from commission_engine.models.custom_object_models import (
    COPermissions,
    CustomObject,
    CustomObjectData,
    CustomObjectVariable,
)
from commission_engine.serializers.custom_object_serializers import (
    CustomObjectSerializer,
    CustomObjectVariableSerializer,
)
from commission_engine.utils.general_utils import log_time_taken
from interstage_project.utils import log_me
from spm.utils import clone_object

logger = logging.getLogger(__name__)


class CustomObjectAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return CustomObject.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def persist_custom_object(self, data):
        data.save()

    def get_all_objects(self):
        return list(self.client_kd_aware().order_by(Lower("name")).values())

    def get_object_for_id(self, custom_object_id: int) -> Optional[CustomObject]:
        return self.client_kd_aware().filter(custom_object_id=custom_object_id).get()

    def get_object_by_ids(self, custom_object_ids=None, as_dicts=False):
        """
        Returns a list of custom objects for this client.  If custom_object_ids is None, then return all objects
        """
        query_set = None
        if custom_object_ids is None:
            query_set = self.client_kd_aware()
        else:
            if not isinstance(custom_object_ids, list):
                custom_object_ids = [custom_object_ids]
            query_set = self.client_kd_aware().filter(
                custom_object_id__in=custom_object_ids
            )

        custom_objects = list(query_set.values()) if as_dicts else list(query_set)
        return custom_objects

    def get_objects_excluding_ids(
        self, custom_object_ids=None
    ) -> QuerySet[CustomObject]:
        """
        Returns a list of custom objects for this client.  If custom_object_ids is None, then return all objects
        Else return all objects excluding the ids in the list
        """
        query_set = None
        if not custom_object_ids:
            query_set = self.client_kd_aware()
        else:
            query_set = self.client_kd_aware().exclude(
                custom_object_id__in=custom_object_ids
            )
        return query_set

    def get_object_by_id(self, custom_object_id: int, as_queryset=True):
        custom_object = self.client_kd_aware().filter(custom_object_id=custom_object_id)

        if as_queryset:
            return custom_object

        return None if len(custom_object) == 0 else custom_object.get()

    def get_object_by_kd(self, custom_object_id, knowledge_date):
        qs = self.client_kd_deleted_aware(knowledge_date).filter(
            custom_object_id=custom_object_id
        )
        return list(qs)

    def get_max_id(self) -> int:
        """
        Returns the ID of the most recently created object.
        Note: Post PR #7464, this function will also take invalidated / deleted objects into account.

        Returns:
            int: The ID of the most recently created object.
        """
        max_id = self.client_aware().aggregate(
            Max("custom_object_id")
        )  # returns {'custom_object_id__max': value}
        max_id_value = max_id.get("custom_object_id__max")
        if max_id_value is not None:
            return max_id_value
        return 0

    def get_primary_keys(self, object_id, knowledge_date=None):
        if knowledge_date:
            return (
                self.client_kd_deleted_aware(knowledge_date=knowledge_date)
                .filter(custom_object_id=object_id)
                .values("primary_key")
                .get()
            )
        else:
            return (
                self.client_kd_aware()
                .filter(custom_object_id=object_id)
                .values("primary_key")
                .get()
            )

    def get_object_by_tag(self, knowledge_date, tags):
        # tags = [{'category':'object_label', 'value': 'opportunity'}]
        if not isinstance(tags, list):
            tags = [tags]
        qs = self.client_kd_deleted_aware(knowledge_date).filter(tags__contains=tags)
        return list(qs)

    def get_snapshot_keys(self, object_id):
        return (
            self.client_kd_aware()
            .filter(custom_object_id=object_id)
            .values("snapshot_key")
            .get()
        )

    def invalidate_record(
        self,
        custom_object_id: int,
        curr_time: datetime.datetime,
        audit_log: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Utility function to invalidate a single TransformationConfig record.

        Args:
            transormation_config_id (int): Id of the record to be invalidated.
            integration_id (UUID): Integration id of the record to be invalidated.
            curr_time (datetime.datetime): Timestamp of invalidation.
        """
        record = self.get_object_by_id(custom_object_id=custom_object_id)

        if len(record) == 0:
            raise Exception("Record not found")

        record.update(knowledge_end_date=curr_time, additional_details=audit_log)

    def update_record(self, custom_object_id: int, updated_fields: dict) -> None:
        """
        Utility function to update a single TransformationConfig record.

        Args:
            integration_id (UUID): Integration ID
            source_field (str): Source field
            updated_fields (dict): Record's fields to be updated.

        Raises:
            Exception: If record with given id is not found.

        Returns:
            None
        """
        current_time = timezone.now()

        existing_record = self.get_object_by_id(
            custom_object_id=custom_object_id
        ).first()

        if existing_record is None:
            raise Exception("Record not found")

        updated_record = clone_object(
            current_object=existing_record, curr_time=current_time, request_audit=None
        )  # type: ignore

        self.invalidate_record(
            custom_object_id=custom_object_id,
            curr_time=current_time,
        )

        ser = CustomObjectSerializer(data=updated_record.__dict__)

        ser.update(instance=updated_record, validated_data=updated_fields)

    def does_custom_object_exist(self, custom_object_id):
        return self.client_kd_aware().filter(custom_object_id=custom_object_id).exists()

    def does_custom_object_name_exists(self, custom_object_name):
        return self.client_kd_aware().filter(name__iexact=custom_object_name).exists()

    def get_custom_object_id_by_name(self, custom_object_name: str) -> Optional[int]:
        if not self.does_custom_object_name_exists(custom_object_name):
            return None
        custom_object_record = (
            self.client_kd_aware()
            .filter(name__iexact=custom_object_name)
            .values_list("custom_object_id", flat=True)
            .first()
        )
        return int(custom_object_record) if custom_object_record else None

    def get_custom_object_csv_header_mapping(self, custom_object_id):
        return (
            self.client_kd_aware()
            .filter(custom_object_id=custom_object_id)
            .values("csv_header_map")
            .get()
        )

    def get_custom_object_ids(self) -> list[int]:
        """
        Returns all custom object ids for this client
        """
        custom_object_ids = self.client_kd_aware().values_list(
            "custom_object_id", flat=True
        )
        return list(custom_object_ids)

    def get_custom_object_id_to_name_map(self) -> dict[int, str]:
        """
        Returns a dict of custom object id to custom object name
        """
        records = self.client_kd_aware().values("custom_object_id", "name")
        return {int(record["custom_object_id"]): record["name"] for record in records}

    def get_custom_object_variable_order(self, custom_object_id):
        """
        Returns the stored order of the variables
        """

        custom_object_order = (
            self.client_kd_aware()
            .filter(custom_object_id=custom_object_id)
            .values("ordered_columns")
            .first()
        )
        return custom_object_order

    def delete_record(
        self,
        custom_object_id: int,
        timestamp: datetime.datetime,
        audit_log: Optional[Dict[str, Any]] = None,
    ) -> None:
        object_data = list(
            self.get_object_by_id(custom_object_id=custom_object_id).values()
        )

        if len(object_data) != 1:
            raise Exception("Invalid Custom Object ID")

        delete_data = {
            **object_data[0],
            **{
                "temporal_id": None,
                "knowledge_begin_date": timestamp,
                "knowledge_end_date": None,
                "is_deleted": True,
                "additional_details": audit_log,
            },
        }

        self.invalidate_record(
            custom_object_id=custom_object_id, curr_time=timestamp, audit_log=audit_log
        )

        CustomObject.objects.create(**delete_data)

    def get_object_fields_by_id(
        self, custom_object_id: int, projections: List[str]
    ) -> list[dict]:
        query_set = self.client_kd_aware().filter(custom_object_id=custom_object_id)
        if projections:
            return list(query_set.values(*projections))
        return list(query_set.values())


class CustomObjectVariableAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return CustomObjectVariable.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def all_variables_in_object(self, object_ids=None, as_dicts=False):
        if object_ids is None:
            query_set = self.client_kd_aware().order_by("temporal_id")
        else:
            if not isinstance(object_ids, list):
                object_ids = [object_ids]
            query_set = (
                self.client_kd_aware()
                .filter(custom_object_id__in=object_ids)
                .order_by("temporal_id")
            )
        variables = list(query_set.values()) if as_dicts else list(query_set)
        return variables

    def get_display_names_for_object(self, object_id: int) -> list[str]:
        return list(
            self.client_kd_aware()
            .filter(custom_object_id=object_id)
            .values_list("display_name", flat=True)
        )

    def get_variable_by_system_name(self, object_id, system_name):
        return (
            self.client_kd_aware()
            .filter(custom_object_id=object_id, system_name=system_name)
            .first()
        )

    def invalidate(self, object_id, system_name, knowledge_date):
        self.client_kd_aware().filter(
            custom_object_id=object_id, system_name=system_name
        ).update(knowledge_end_date=knowledge_date)

    def persist_variables(self, variable):
        variable.save()

    def get_variable_last_updated_at_per_custom_object_id(
        self, object_ids: Optional[list[int]] = None
    ) -> list[dict]:
        qs = self.client_kd_aware()
        if object_ids:
            qs = qs.filter(custom_object_id__in=object_ids)
        return list(qs.values("custom_object_id").annotate(Max("knowledge_begin_date")))

    def get_total_variables_per_custom_object_id(
        self, object_ids: Optional[list[int]] = None
    ) -> list[dict]:
        qs = self.client_kd_aware()
        if object_ids:
            qs = qs.filter(custom_object_id__in=object_ids)
        return list(
            qs.values("custom_object_id").annotate(total=Count("custom_object_id"))
        )

    def get_variable_by_display_name(self, object_id, display_name):
        return (
            self.client_kd_aware()
            .filter(custom_object_id=object_id, display_name__iexact=display_name)
            .first()
        )

    def get_latest_copy_with_new_kd(self, object_id, system_name, kd):
        prev = (
            self.client_kd_aware()
            .filter(custom_object_id=object_id, system_name=system_name)
            .get()
        )
        if prev:
            prev.pk = None
            prev.knowledge_begin_date = kd
            return prev
        else:
            return None

    def get_variables_in_obj_by_data_type(self, object_id, data_type_id):
        return list(
            self.client_kd_aware().filter(
                custom_object_id=object_id, data_type_id=data_type_id
            )
        )

    def get_variable_by_system_name_and_id(self, object_id, system_name):
        return self.client_kd_aware().filter(
            custom_object_id=object_id, system_name=system_name
        )

    def get_system_names_by_object_id(self, object_id):
        return list(
            self.client_kd_aware()
            .filter(custom_object_id=object_id)
            .values_list("system_name", flat=True)
        )

    def get_all_system_names_by_object_id(self, object_id):
        return list(
            self.client_aware()
            .filter(custom_object_id=object_id)
            .values_list("system_name", flat=True)
        )

    def get_variables_by_object_id(self, object_id, as_dicts=False):
        qs = self.client_kd_aware().filter(custom_object_id=object_id)
        return list(qs.values()) if as_dicts else list(qs)

    def get_display_names_from_system_names(self, custom_object_id, system_names):
        queryset = self.client_kd_aware().filter(custom_object_id=custom_object_id)

        if system_names:
            queryset = queryset.filter(system_name__in=system_names)

        return tuple(queryset.values_list("system_name", "display_name"))

    def get_display_names_from_system_names_with_data_type(
        self, custom_object_id, system_names
    ):
        return tuple(
            self.client_kd_aware()
            .filter(custom_object_id=custom_object_id)
            .filter(system_name__in=system_names)
            .values("system_name", "display_name", "data_type")
        )

    def get_variables_by_id(self, object_id):
        return list(self.client_kd_aware().filter(custom_object_id=object_id))

    def get_ds_var_system_name_and_dtype(self, custom_object_id, knowledge_date):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_kd_aware()
        qs = qs.filter(custom_object_id=custom_object_id)
        return tuple(qs.values("system_name", "data_type_id"))

    def get_system_name_to_dtype(self, custom_object_id, knowledge_date) -> list:
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_kd_aware()
        query_result = qs.filter(custom_object_id=custom_object_id).values(
            "system_name", "data_type"
        )
        return [(item["system_name"], item["data_type"]) for item in query_result]

    def create_and_persist_record(self, fields: dict) -> None:
        current_timestamp = timezone.now()

        fields["client"] = str(self.client_id)
        fields["knowledge_begin_date"] = current_timestamp

        ser = CustomObjectVariableSerializer(data=fields)

        if not ser.is_valid():
            print(f"ERROR: {ser.errors}")
            raise Exception("Invalid data")

        ser.save()

    @transaction.atomic
    def bulk_create_and_persist_records(
        self, custom_object_id: int, variables: list[CustomObjectVariableType]
    ) -> None:
        for var in variables:
            fields = {
                "custom_object_id": custom_object_id,
                "system_name": "co_{0}_{1}".format(
                    custom_object_id, snake_case(var.get("display_name"))
                ),
                "display_name": var.get("display_name"),
                "data_type": var.get("id"),
            }

            self.create_and_persist_record(fields=fields)

    def get_variables_query_set_for_objects(
        self, ids: List[int]
    ) -> QuerySet[CustomObjectVariable]:
        return (
            self.client_kd_aware()
            .filter(custom_object_id__in=ids)
            .order_by("custom_object_id")
        )

    def invalidate_variables_for_custom_object(
        self,
        custom_object_id: int,
        timestamp: datetime.datetime,
        audit_log: Optional[Dict[str, Any]] = None,
    ) -> None:
        variable_objects = self.get_variables_for_custom_object(
            custom_object_id=custom_object_id
        )

        variable_objects.update(
            knowledge_end_date=timestamp, additional_details=audit_log
        )

    def delete_variables_for_custom_object(
        self,
        custom_object_id: int,
        timestamp: datetime.datetime,
        audit_log: Optional[Dict[str, Any]] = None,
    ) -> None:
        variable_data = list(
            self.get_variables_for_custom_object(
                custom_object_id=custom_object_id
            ).values()
        )

        delete_data = [
            {
                **vd,
                **{
                    "temporal_id": None,
                    "knowledge_begin_date": timestamp,
                    "knowledge_end_date": None,
                    "is_deleted": True,
                    "additional_details": audit_log,
                },
            }
            for vd in variable_data
        ]

        self.invalidate_variables_for_custom_object(
            custom_object_id=custom_object_id, timestamp=timestamp, audit_log=audit_log
        )

        CustomObjectVariable.objects.bulk_create(
            [CustomObjectVariable(**data) for data in delete_data]
        )

    def get_variables_for_custom_object(
        self, custom_object_id: int
    ) -> QuerySet[CustomObjectVariable]:
        return self.client_kd_aware().filter(custom_object_id=custom_object_id)

    def get_data_types_for_system_names(
        self, custom_object_id: int, system_names: List[str]
    ):
        return (
            self.get_variables_for_custom_object(custom_object_id=custom_object_id)
            .filter(system_name__in=system_names)
            .select_related("data_type")
            .values_list("system_name", "data_type__data_type")
        )

    def get_custom_variables_by_id(
        self, custom_object_id: int, projections: List[str]
    ) -> list[dict]:
        query_set = self.client_kd_aware().filter(custom_object_id=custom_object_id)
        if projections:
            return list(query_set.values(*projections))
        return list(query_set.values())


class CustomObjectDataAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return CustomObjectData.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_any_kd_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    """ def client_kd_end_date_neq_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(Q(knowledge_end_date__gt=knowledge_date) | Q(knowledge_end_date__isnull=True))
        )"""

    def get_all_records_in_object(self, object_id, knowledge_date, as_dicts=False):
        query_set = self.client_any_kd_aware(knowledge_date).filter(
            custom_object_id=object_id
        )
        records = list(query_set.values()) if as_dicts else list(query_set)
        return records

    def invalidate(self, object_id, row_key, knowledge_date, is_deleted=False):
        query_set = self.client_kd_aware().filter(custom_object_id=object_id)
        if isinstance(row_key, list):
            query_set = query_set.filter(row_key__in=row_key)
        else:
            query_set = query_set.filter(row_key=row_key)
        if is_deleted:
            query_set.update(is_deleted=True, knowledge_end_date=knowledge_date)
        else:
            query_set.update(knowledge_end_date=knowledge_date)

    def invalidate_with_snapshot_key(self, object_id, snapshot_key, knowledge_date):
        query_set = self.client_kd_aware().filter(custom_object_id=object_id)
        if isinstance(snapshot_key, list):
            query_set = query_set.filter(snapshot_value__in=snapshot_key)
        else:
            query_set = query_set.filter(snapshot_value=snapshot_key)
        query_set.update(knowledge_end_date=knowledge_date)

    def persist_data(self, data):
        data.save()

    def persist_update(self, record):
        record.pk = None
        record.save()

    def update_custom_object_data(self, object_id, row_key, curr_data, knowledge_date):
        prev_record = (
            self.client_kd_aware()
            .filter(custom_object_id=object_id, row_key=row_key)
            .first()
        )
        if prev_record:
            self.invalidate(object_id, row_key, knowledge_date)
            prev_record.knowledge_begin_date = knowledge_date
            prev_record.data.update(curr_data)
            self.persist_update(prev_record)

    def get_all_objects(self, object_id, as_dicts=True, options=None):
        if options is None:
            options = {}
        query_set = self.client_kd_aware().filter(custom_object_id=object_id)
        if options:
            for attr, value in options.items():
                if value and isinstance(value, list) and "__" not in attr:
                    attr = attr + "__in"
                query_set = query_set.filter(**{attr: value})
        data_list = list(query_set.values()) if as_dicts else list(query_set)
        return data_list

    def create_objects(self, records):  # each record is a dict
        custom_object_records = []
        if isinstance(records, list):
            for record in records:
                if isinstance(record, dict):
                    record = CustomObjectData(**record)
                record.pk = None
                custom_object_records.append(record)
        try:
            CustomObjectData.objects.bulk_create(custom_object_records, batch_size=1000)
        except Exception as e:
            log_me("CUSTOM OBJ BULK INSERT EXP - {}".format(e))
            raise e

    def update_objects(self, object_id, row_key, knowledge_date, data):
        try:
            self.invalidate(object_id, row_key, knowledge_date)
            self.create_objects(data)
        except Exception as e:
            log_me("ERROR WHILE UPDATING CUSTOM OBJECTS DATA - {}".format(e))
            raise e

    def get_custom_object_data_object_by_kd(self, object_id, knowledge_date):
        return (
            self.client_any_kd_aware(knowledge_date)
            .filter(custom_object_id=object_id)
            .order_by("temporal_id")
        )

    def get_qualifying_objects(
        self,
        object_id,
        knowledge_date,
        filters,
        as_dicts=False,
    ):
        """
        This is a deprecated method
        """
        # qs = self.client_any_kd_aware(knowledge_date).filter(custom_object_id=object_id)
        # if filters:
        #     log_me("Filters present")
        #     qs = ju.get_custom_object_query_set(self.client_id, filters, qs, object_id)
        # if (not as_dicts) or (filters and filters[0]["type"] == "GROUP_BY"):
        #     return list(qs)
        # else:
        #     return list(qs.values())
        return []

    """     def get_qualifying_objects_with_new_kd(self, object_id, knowledge_date, filters, as_dicts=False):
        qs = self.client_any_kd_aware(knowledge_date).filter(custom_object_id=object_id)
        if filters:
            qs = ju.get_custom_object_query_set(self.client_id, filters, qs, object_id)
        if (not as_dicts) or (filters and filters[0]["type"] == "GROUP_BY"):
            return list(qs)
        else:
            return list(qs.values()) """

    def get_objects_with_snapshot_value(self, snapshot_value):
        query_set = self.client_kd_aware()
        if isinstance(snapshot_value, list):
            query_set = query_set.filter(snapshot_value__in=snapshot_value)
        else:
            query_set = query_set.filter(snapshot_value=snapshot_value)
        return list(query_set.values())

    def get_latest_record_in_custom_object(self, custom_object_id):
        query_set = self.client_kd_aware().filter(custom_object_id=custom_object_id)
        if query_set.exists():
            return query_set.latest("knowledge_begin_date")
        return None

    def get_max_knowledge_date(self, object_id):
        max_knowledge_date = (
            self.client_kd_aware()
            .filter(custom_object_id=object_id)
            .aggregate(Max("knowledge_begin_date"))
        )
        max_knowledge_date_value = max_knowledge_date.get("knowledge_begin_date__max")
        return max_knowledge_date_value


class COPermissionsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return COPermissions.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def object_aware(self, object_id):
        return self.client_kd_aware().filter(object_id=object_id)

    def get_all_permissions_of_object(self, object_id):
        return self.client_kd_aware().filter(object_id=object_id).values()

    def get_all_permission_of_target(self, target_list, object_type=None):
        queryset = (
            self.client_kd_aware()
            .filter(target__in=target_list)
            .values("object_id", "is_include")
        )
        if object_type is not None:
            queryset = queryset.filter(object_type=object_type)
        return tuple(queryset)

    def get_include_objects_excluding_ids(self, object_ids, object_type=None):
        queryset = (
            self.client_kd_aware()
            .exclude(object_id__in=object_ids)
            .filter(is_include=True)
        )
        if object_type is not None:
            queryset = queryset.filter(object_type=object_type)
        return set(queryset.values_list("object_id", flat=True).distinct())

    def persist_permissions(self, data):
        return data.save()

    def invalidate(self, object_id, knowledge_date):
        query_set = self.client_kd_aware().filter(object_id=object_id)
        query_set.update(knowledge_end_date=knowledge_date)

    def does_permissions_with_given_user_group_id_exist(self, user_group_id):
        return (
            self.client_kd_aware()
            .filter(target_type="user_group", target=user_group_id)
            .exists()
        )


@log_time_taken()
def latest_knowledge_date_for_custom_objects(
    client_id, custom_object_ids: Optional[list[str]] = None
) -> dict[str, datetime.datetime]:
    """
    Finds the time when each of the custom object ids was last modified - this can
    be when it was last updated or last deleted

    Returns a dictionary of custom object id to its last modified time
    """

    # Importing here to avoid circular import
    object_kd_strategy = get_object_knowledge_date_query_strategy(client_id)

    logger.info(
        f"Querying custom object latest kd for client {client_id} from {object_kd_strategy}"
    )

    if object_kd_strategy == "snowflake":
        from commission_engine.snowflake_accessors.custom_object_data_accessor import (
            latest_knowledge_date_for_custom_objects as latest_knowledge_date_for_custom_objects_snowflake,
        )

        return latest_knowledge_date_for_custom_objects_snowflake(
            client_id, custom_object_ids
        )

    # Fetch all custom object for the client if it None
    if custom_object_ids is None:
        custom_object_ids_set = set(
            str(co_id)
            for co_id in CustomObjectAccessor(
                client_id=client_id
            ).get_custom_object_ids()
        )
    else:
        custom_object_ids_set = set(custom_object_ids)

    co_pkd_map_accessor = CustomObjectPkdMapAccessor(client_id)

    result = co_pkd_map_accessor.get_custom_objects_primary_kd(custom_object_ids_set)

    for custom_object_id in custom_object_ids_set:
        if str(custom_object_id) not in result:
            result[str(custom_object_id)] = timezone.make_aware(
                datetime.datetime.min, pytz.utc
            )

    return result
