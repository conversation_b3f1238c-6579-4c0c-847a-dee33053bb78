import datetime
from functools import reduce

from django.db.models import Max, Min, Q

from commission_engine.models import PayoutStatus, PayoutStatusChanges


class PayoutStatusAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return PayoutStatus.objects.filter(client=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def invalidate_period_records(self, ped, time):
        self.client_kd_aware().filter(period_end_date=ped).update(
            knowledge_end_date=time
        )

    def invalidate_payee_period_record(self, payee_ids, ped, time):
        self.client_kd_aware().filter(
            payee_email_id__in=payee_ids, period_end_date=ped
        ).update(knowledge_end_date=time)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_paid_kd_aware(self):
        return self.client_kd_aware().filter(payment_status="Paid")

    def client_any_kd_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def get_records_for_period(self, ped, fields=None):
        if fields:
            return list(
                self.client_kd_aware().filter(period_end_date=ped).values(*fields)
            )
        return list(self.client_kd_aware().filter(period_end_date=ped).values())

    def get_payee_period_records(self, payee_ids, ped, as_dict=True) -> list:
        if as_dict:
            return list(
                self.client_kd_aware()
                .filter(payee_email_id__in=payee_ids, period_end_date=ped)
                .values()
            )
        else:
            return list(
                self.client_kd_aware().filter(
                    payee_email_id__in=payee_ids, period_end_date=ped
                )
            )

    def get_invalidated_payee_period_records(self, payee_ids, ped, ked):
        return list(
            self.client_aware().filter(
                payee_email_id__in=payee_ids,
                period_end_date=ped,
                is_deleted=False,
                knowledge_end_date=ked,
            )
        )

    def insert_new_data(self, objs):
        try:
            PayoutStatus.objects.bulk_create(objs, batch_size=10000)
        except Exception as e:
            raise e

    def get_distinct_periods_for_payee_ed(self, email_id, psd):
        q = list(
            self.client_kd_aware()
            .filter(payee_email_id=email_id, period_start_date__gte=psd)
            .values("period_end_date")
            .distinct()
        )
        return q

    def get_existing_ped_between_dates_payee_ids(self, payee_ids, psd, ped):
        return list(
            self.client_kd_aware()
            .filter(
                payee_email_id__in=payee_ids,
                period_end_date__gte=psd,
                period_end_date__lte=ped,
            )
            .values("period_end_date")
            .distinct()
        )

    def get_payout_status_for_payee_in_periods(self, payee, period_end_dates):
        return list(
            self.client_kd_aware().filter(
                payee_email_id=payee, period_end_date__in=period_end_dates
            )
        )

    def get_payout_status_for_payees_in_period(self, payees, period_end_date):
        return list(
            self.client_kd_aware()
            .filter(payee_email_id__in=payees, period_end_date=period_end_date)
            .values()
        )

    def get_payees_in_period(self, period_start_date, period_end_date, payees):
        return list(
            self.client_kd_aware()
            .filter(
                period_start_date=period_start_date, period_end_date=period_end_date
            )
            .filter(payee_email_id__in=payees)
            .values_list("payee_email_id")
        )

    def get_payout_status_for_payees_in_period_qs(
        self, payees, period_end_date, projection=None
    ):
        projection = projection or []
        return (
            self.client_kd_aware()
            .filter(payee_email_id__in=payees, period_end_date=period_end_date)
            .values(*projection)
        )

    def count_of_paid_payees_for_end_date(self, ped):
        return (
            self.client_paid_kd_aware()
            .filter(period_end_date=ped)
            .values("payee_email_id")
            .count()
        )

    def get_periods_for_a_payee(self, payee_email):
        qs = (
            self.client_kd_aware()
            .filter(payee_email_id=payee_email)
            .values("period_start_date", "period_end_date", "payout_frequency")
        )
        return qs

    def get_periods_for_payees(self, payee_emails: list):
        qs = (
            self.client_kd_aware()
            .filter(payee_email_id__in=payee_emails)
            .values(
                "period_start_date",
                "period_end_date",
                "payout_frequency",
                "payee_email_id",
            )
        )
        return qs

    def get_periods_for_a_payees(self, payee_email=None):
        qs = self.client_kd_aware()
        if payee_email:
            qs.filter(payee_email_id__in=payee_email)
        return qs.values(
            "payee_email_id",
            "period_start_date",
            "period_end_date",
            "payout_frequency",
        )

    def count_of_active_payees_for_end_date(self, ped):
        return (
            self.client_kd_aware()
            .filter(period_end_date=ped)
            .values("payee_email_id")
            .count()
        )

    def get_period_end_dates_for_year(self, year):
        qs = (
            self.client_kd_aware()
            .filter(period_end_date__year=year)
            .values("period_end_date")
            .distinct()
            .order_by("period_end_date")
        )
        return list(qs)

    def get_fx_rate_for_payee_in_periods(self, payee, period_end_dates):
        return list(
            self.client_kd_aware()
            .filter(payee_email_id=payee, period_end_date__in=period_end_dates)
            .values("period_end_date", "fx_rate")
        )

    def get_payouts_history_for_payee(self, payee_email):
        return list(
            self.client_kd_aware()
            .filter(payee_email_id=payee_email)
            .values()
            .order_by("-period_end_date")
        )

    def get_payout_entries_after_date(
        self,
        payee_email: str,
        date: datetime.datetime,
        projection: list[str] | None = None,
    ):
        """Get payout entries for a payee after a given date"""
        projection = projection or []
        return list(
            self.client_kd_aware()
            .filter(payee_email_id=payee_email, period_end_date__gt=date)
            .values(*projection)
        )

    def get_payee_psd_ped_for_payee(self, payee_details: list) -> list:
        """
        The payee_details contains the payee_email, effective_start_date, effective_end_date in a plan

        This function returns the period_start_date and period_end_date for the payee
        In the given period
        """
        query_set = self.client_kd_aware()
        filter_conditions = []
        for payee in payee_details:
            filter_conditions.append(
                Q(
                    payee_email_id=payee["employee_email_id"],
                    period_start_date__gte=payee["effective_start_date"],
                    period_end_date__lte=payee["effective_end_date"],
                )
            )
        if filter_conditions:
            query_set = query_set.filter(reduce(lambda x, y: x | y, filter_conditions))
            return list(
                query_set.values(
                    "payee_email_id", "period_start_date", "period_end_date"
                )
            )
        return []

    def get_mid_month_period_on_date(self, payee_details: list) -> list:
        """
        Get the first period after a mid month ped or psd to cover mid month payee removal.

        The effective_date will be the effective_start or effective_end date of payee in a plan.

        Get the first period after the mid month ped. This will be checked with
        whether period_start_date < effective_end_date < period_end_date to confirm.
        This is a strict greater than and lesser then check to avoid any overlap.
        """
        query_set = self.client_kd_aware()
        filter_conditions = []

        for payee in payee_details:
            filter_conditions.append(
                Q(
                    payee_email_id=payee["employee_email_id"],
                    period_start_date__lt=payee["effective_start_date"],
                    period_end_date__gt=payee["effective_start_date"],
                )
            )
        for payee in payee_details:
            filter_conditions.append(
                Q(
                    payee_email_id=payee["employee_email_id"],
                    period_start_date__lt=payee["effective_end_date"],
                    period_end_date__gt=payee["effective_end_date"],
                )
            )
        if filter_conditions:
            query_set = query_set.filter(reduce(lambda x, y: x | y, filter_conditions))
            return list(
                query_set.values(
                    "payee_email_id", "period_start_date", "period_end_date"
                ).order_by("period_end_date")
            )
        return []

    def get_psd_ped_with_plan_payee_effective(self, payee_details: list) -> list:
        """
        The payee_details contains the payee_email, psd_limit, ped_limit in a plan

        This function returns the period_start_date and period_end_date for the payee
        In the given period

        The payee_details contains either psd_limit or ped_limit
        1. psd_limit: when ped is trimmed (since ped can be mid-month)
        2. ped_limit: when psd is pushed forward (since psd can be mid-month)
        """
        query_set = self.client_kd_aware()
        filter_conditions = []
        for payee in payee_details:
            if payee.get("psd_limit"):
                filter_conditions.append(
                    Q(
                        payee_email_id=payee["employee_email_id"],
                        period_start_date__gte=payee["psd_limit"],
                    )
                )
            else:
                filter_conditions.append(
                    Q(
                        payee_email_id=payee["employee_email_id"],
                        period_end_date__lte=payee["ped_limit"],
                    )
                )
        if filter_conditions:
            query_set = query_set.filter(reduce(lambda x, y: x | y, filter_conditions))
            return list(
                query_set.values(
                    "payee_email_id", "period_start_date", "period_end_date"
                )
            )
        return []

    def get_details_for_end_date_payees(self, ped, payee_emails):
        return list(
            self.client_kd_aware()
            .filter(payee_email_id__in=payee_emails, period_end_date=ped)
            .values("payee_email_id", "period_start_date", "period_end_date")
        )

    def get_all_periods(self):
        return list(
            self.client_kd_aware()
            .values("period_end_date")
            .distinct()
            .order_by("period_end_date")
        )

    def get_active_payee_payout_periods(
        self,
        payees: list[str] | None = None,
        peds: list[datetime.datetime] | None = None,
    ):
        """
        get the list of active payee payout periods for the client
        """
        query_set = self.client_kd_aware()
        if payees:
            query_set = query_set.filter(payee_email_id__in=payees)
        if peds:
            query_set = query_set.filter(period_end_date__in=peds)
        return list(
            query_set.distinct("payee_email_id", "period_start_date", "period_end_date")
            .order_by("payee_email_id", "period_start_date", "period_end_date")
            .values("payee_email_id", "period_start_date", "period_end_date")
        )

    def get_min_max_period_dates(self):
        return self.client_kd_aware().aggregate(
            min_period_start_date=Min("period_start_date"),
            max_period_end_date=Max("period_end_date"),
        )

    def get_periods_after_date(self, payee_email: str, as_of_date: datetime.datetime):
        return list(
            self.client_kd_aware()
            .filter(payee_email_id=payee_email, period_start_date__gt=as_of_date)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .order_by("-period_start_date")
        )

    def get_all_modified_entries_after_kd(self, kd: datetime.datetime):
        qs = (
            self.client_aware()
            .filter(Q(knowledge_end_date__gte=kd) | Q(knowledge_begin_date__gte=kd))
            .values("payee_email_id", "period_end_date")
            .distinct()
        )
        return list(qs)

    def get_payees_with_ped(self, period_end_date: datetime.datetime):
        return list(
            self.client_kd_aware()
            .filter(period_end_date=period_end_date)
            .values("payee_email_id")
            .distinct()
        )


class PayoutStatusChangesAccessor:
    def __init__(self, client_id: int):
        self.client_id = client_id

    def client_aware(self):
        return PayoutStatusChanges.objects.filter(client=self.client_id)

    def bulk_create_payout_status_changes(self, payout_status_changes):
        PayoutStatusChanges.objects.bulk_create(payout_status_changes, batch_size=3000)

    def get_payout_status_changes_after_last_sync(
        self, last_sync_time: datetime.datetime
    ):
        return list(
            self.client_aware()
            .filter(created_at__gte=last_sync_time)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .distinct()
        )

    def get_changes_by_types_after_last_sync(
        self, last_sync_time: datetime.datetime, change_types: list
    ):
        qs = (
            self.client_aware()
            .filter(created_at__gte=last_sync_time, change_type__in=change_types)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .distinct()
        )
        return list(qs)

    def does_record_exist_after_date(self, last_sync_time: datetime.datetime):
        return self.client_aware().filter(created_at__gte=last_sync_time).exists()
