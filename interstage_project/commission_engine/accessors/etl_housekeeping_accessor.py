import datetime
import logging
import time
from datetime import date
from typing import Any, List, Optional
from uuid import UUID

import pydash
from django.db import IntegrityError, transaction
from django.db.models import (
    <PERSON><PERSON>anField,
    Case,
    Count,
    F,
    Max,
    OuterRef,
    Q,
    QuerySet,
    Subquery,
    Sum,
    Value,
    When,
)
from django.utils import timezone
from django.utils.timezone import make_aware

from commission_engine.accessors.accessor_templates.commission_etl_status_accessor_template import (
    CommissionETLStatusAccessorTemplate,
    CommissionETLStatusReaderAccessorTemplate,
)
from commission_engine.accessors.databook_accessor import DatasheetAccessor
from commission_engine.custom_exceptions.etl_exceptions import ETLConcurrencyException
from commission_engine.custom_types.general_types import SyncRunLogObject
from commission_engine.models.etl_housekeeping_models import (
    CommissionETLStatus,
    DatabookETLStatus,
    ETLLock,
    ETLSyncStatus,
    ExtractionSyncLog,
    ForecastETLStatus,
    LoadSyncLog,
    PayoutSnapshotETLStatus,
    PlanModificationSyncStatus,
    ReportETLStatus,
    SettlementETLStatus,
    SettlementSnapshotETLStatus,
    TransformationSyncLog,
    UpstreamCsvETLStatus,
    UpstreamETLStatus,
)
from commission_engine.serializers.upstream_serializers import (
    UpstreamCsvETLStatusSerializer,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.utils.general_data import ETL_ACTIVITY, ETL_STATUS, SYNC_OBJECT

logger = logging.getLogger(__name__)


class UpstreamETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return UpstreamETLStatus.objects.filter(client_id=self.client_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_object(self):
        obj = UpstreamETLStatus.objects.filter(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        # obj = UpstreamETLStatus.objects.filter(client_id=self.client_id).filter(
        #     e2e_sync_run_id=self.e2e_sync_run_id)
        return obj

    def insert_sync_status(self, params):
        object_id = pydash.get(params, "object_id")
        integration_id = pydash.get(params, "integration_id")

        sync_start_time = (
            params["sync_start_time"] if "sync_start_time" in params else None
        )
        sync_completion_time = (
            params["sync_completion_time"] if "sync_completion_time" in params else None
        )
        sync_status = params["sync_status"] if "sync_status" in params else None
        sync_mode = params["sync_mode"] if "sync_mode" in params else None
        changes_start_time = (
            params["changes_start_time"] if "changes_start_time" in params else None
        )
        changes_end_time = (
            params["changes_end_time"] if "changes_end_time" in params else None
        )
        extraction_start_time = (
            params["extraction_start_time"]
            if "extraction_start_time" in params
            else None
        )
        extraction_status = (
            params["extraction_status"] if "extraction_status" in params else None
        )
        extraction_end_time = (
            params["extraction_end_time"] if "extraction_end_time" in params else None
        )
        transformation_start_time = (
            params["transformation_start_time"]
            if "transformation_start_time" in params
            else None
        )
        transformation_status = (
            params["transformation_status"]
            if "transformation_status" in params
            else None
        )
        transformation_end_time = (
            params["transformation_end_time"]
            if "transformation_end_time" in params
            else None
        )
        load_start_time = (
            params["load_start_time"] if "load_start_time" in params else None
        )
        load_status = params["load_status"] if "load_status" in params else None
        load_end_time = params["load_end_time"] if "load_end_time" in params else None
        filter_query = params["filter_query"] if "filter_query" in params else None
        source = params["source"] if "source" in params else None
        audit = params["audit"] if "audit" in params else None
        sync_run_log = params["sync_run_log"] if "sync_run_log" in params else None
        trigger = params["trigger"] if "trigger" in params else None
        try:
            sync_obj = UpstreamETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                object_id=object_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                sync_mode=sync_mode,
                changes_start_time=changes_start_time,
                changes_end_time=changes_end_time,
                extraction_start_time=extraction_start_time,
                extraction_status=extraction_status,
                extraction_end_time=extraction_end_time,
                transformation_start_time=transformation_start_time,
                transformation_status=transformation_status,
                transformation_end_time=transformation_end_time,
                load_start_time=load_start_time,
                load_status=load_status,
                load_end_time=load_end_time,
                filter_query=filter_query,
                source=source,
                audit=audit,
                integration_id=integration_id,
                sync_run_log=sync_run_log,
                trigger=trigger,
            )
            return sync_obj
        except Exception as e:
            raise e

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_skipped(
        self,
        timestamp: datetime.datetime,
        sync_run_log: SyncRunLogObject,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.SKIPPED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_extraction(self):
        return self.update_object({"sync_status": ETL_STATUS.EXTRACTION.value})

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_transformation(self):
        return self.update_object({"sync_status": ETL_STATUS.TRANSFORMATION.value})

    def change_status_to_loading(self):
        return self.update_object({"sync_status": ETL_STATUS.LOADING.value})

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def update_extraction_details(self, params):
        details = {}
        details["extraction_start_time"] = params["start_time"]
        details["extraction_status"] = params["status"]
        details["extraction_end_time"] = params["end_time"]
        return self.update_object(details)

    def update_transformation_details(self, params):
        details = {}
        details["transformation_start_time"] = params["start_time"]
        details["transformation_status"] = params["status"]
        details["transformation_end_time"] = params["end_time"]
        return self.update_object(details)

    def update_loading_details(self, params):
        details = {}
        details["load_start_time"] = params["start_time"]
        details["load_status"] = params["status"]
        details["load_end_time"] = params["end_time"]
        return self.update_object(details)

    def update_extraction_end_time(self, end_time):
        details = {}
        details["changes_end_time"] = end_time
        return self.update_object(details)

    def update_delete_details(self, params):
        details = {}
        details["delete_start_time"] = params["delete_start_time"]
        details["delete_end_time"] = params["delete_end_time"]
        return self.update_object(details)

    def get_last_success_obj(self, object_id, mode=False):
        qs = UpstreamETLStatus.objects.filter(
            client_id=self.client_id,
            object_id=object_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )
        if mode:
            qs = qs.filter(sync_mode=mode)
        if qs.exists():
            return qs.latest("sync_start_time")
        else:
            return None

    def get_last_successful_run_record_for_integration(
        self, integration_id: UUID
    ) -> Optional[UpstreamETLStatus]:
        """
        Retrieve the last successful run record for a specific integration.

        This function queries the database for the most recent successfully completed
        Upstream ETL run status record associated with a given integration ID and the
        client ID associated with the instance.

        Parameters:
            self (object): The instance of the class calling this method, typically
                        representing a client or user.
            integration_id (UUID): The unique identifier of the integration for which
                                to retrieve the last successful run record.

        Returns:
            Optional[UpstreamETLStatus]: An instance of the UpstreamETLStatus model
            representing the last successful run record, or None if no such record
            is found.
        """
        queryset = UpstreamETLStatus.objects.filter(
            client_id=self.client_id,
            integration_id=integration_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )

        if queryset.exists():
            return queryset.latest("sync_start_time")

        return None

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_start_time": make_aware(kd),
                "sync_completion_time": make_aware(kd),
            }
        )


class UpstreamCsvETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return UpstreamCsvETLStatus.objects.filter(client_id=self.client_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_object(self):
        obj = UpstreamCsvETLStatus.objects.filter(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=self.sync_run_id,
        )
        return obj

    def insert_sync_status(self, params):
        object_id = pydash.get(params, "object_id")
        sync_start_time = (
            params["sync_start_time"] if "sync_start_time" in params else None
        )
        sync_completion_time = (
            params["sync_completion_time"] if "sync_completion_time" in params else None
        )
        sync_status = params["sync_status"] if "sync_status" in params else None
        sync_mode = params["sync_mode"] if "sync_mode" in params else None
        changes_start_time = (
            params["changes_start_time"] if "changes_start_time" in params else None
        )
        changes_end_time = (
            params["changes_end_time"] if "changes_end_time" in params else None
        )
        extraction_start_time = (
            params["extraction_start_time"]
            if "extraction_start_time" in params
            else None
        )
        extraction_status = (
            params["extraction_status"] if "extraction_status" in params else None
        )
        extraction_end_time = (
            params["extraction_end_time"] if "extraction_end_time" in params else None
        )
        filter_query = params["filter_query"] if "filter_query" in params else None
        source = params["source"] if "source" in params else None
        audit = params["audit"] if "audit" in params else None
        try:
            sync_obj = UpstreamCsvETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                object_id=object_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                sync_mode=sync_mode,
                changes_start_time=changes_start_time,
                changes_end_time=changes_end_time,
                extraction_start_time=extraction_start_time,
                extraction_status=extraction_status,
                extraction_end_time=extraction_end_time,
                filter_query=filter_query,
                source=source,
                audit=audit,
            )
            return sync_obj
        except Exception as e:
            raise e

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def change_status_to_complete(
        self,
        sync_run_log: Optional[SyncRunLogObject] = None,
        timestamp: datetime.datetime = make_aware(datetime.datetime.now()),
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def change_status_to_extraction(self):
        return self.update_object({"sync_status": ETL_STATUS.EXTRACTION.value})

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime = make_aware(datetime.datetime.now()),
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_transformation(self):
        return self.update_object({"sync_status": ETL_STATUS.TRANSFORMATION.value})

    def change_status_to_loading(self):
        return self.update_object({"sync_status": ETL_STATUS.LOADING.value})

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: Optional[SyncRunLogObject] = None,
        timestamp: datetime.datetime = make_aware(datetime.datetime.now()),
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def update_extraction_details(self, params):
        details = {}
        details["extraction_start_time"] = params["start_time"]
        details["extraction_status"] = params["status"]
        details["extraction_end_time"] = params["end_time"]
        return self.update_object(details)

    def get_last_success_obj(self, object_id, mode=False):
        qs = UpstreamCsvETLStatus.objects.filter(
            client_id=self.client_id,
            object_id=object_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )
        if mode:
            qs = qs.filter(sync_mode=mode)
        if qs.exists():
            return qs.latest("sync_start_time")
        else:
            return None

    def get_record_by_e2e_id_and_object_id(self, e2e_sync_run_id, object_id):
        qs = UpstreamCsvETLStatus.objects.filter(
            client_id=self.client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            object_id=object_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )
        if qs.exists():
            return qs.first()
        else:
            return None

    def reset_initial_sync_line(self, object_id: str) -> None:
        current_timestamp = timezone.now()

        record = (
            self.client_aware()
            .filter(object_id=object_id, sync_status="complete")
            .order_by("changes_end_time")
            .first()
        )

        record_data = record.__dict__

        record_data["sync_start_time"] = current_timestamp
        record_data["sync_completion_time"] = current_timestamp

        ser = UpstreamCsvETLStatusSerializer(data=record_data)

        if ser.is_valid():
            ser.save()
        else:
            raise Exception("Something went wrong")


class UpstreamCsvETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return UpstreamCsvETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[UpstreamCsvETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)


class UpstreamETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return UpstreamETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[UpstreamETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)

    def get_records_by_integration_id(
        self, integration_id: UUID
    ) -> QuerySet[UpstreamETLStatus]:
        return self.client_aware().filter(integration_id=integration_id)

    def get_all_locked_started_tasks(self):
        return tuple(
            self.client_aware()
            .filter(Q(sync_status="started") | Q(sync_status="locked"))
            .values()
        )

    def is_upstream_sync_running_for_object(self, object_id):
        if isinstance(object_id, str):
            return (
                self.client_aware()
                .filter(
                    object_id=object_id,
                    sync_completion_time__isnull=True,
                )
                .order_by("-sync_start_time")
                .exclude(sync_status=ETL_STATUS.COMPLETE.value)
                .exists()
            )
        return (
            self.client_aware()
            .filter(
                object_id__in=object_id,
                sync_completion_time__isnull=True,
            )
            .order_by("-sync_start_time")
            .exclude(sync_status=ETL_STATUS.COMPLETE.value)
            .exists()
        )

    def is_sync_running(self, integration_id: UUID) -> bool:
        return (
            self.client_aware()
            .filter(
                integration_id=integration_id,
                sync_completion_time__isnull=True,
            )
            .order_by("-sync_start_time")
            .exclude(sync_status=ETL_STATUS.COMPLETE.value)
            .exists()
        )

    def get_all_last_success_sync_for_tasks(self, tasks):
        latest_id = (
            self.client_aware()
            .filter(
                sync_status=ETL_STATUS.COMPLETE.value,
                object_id__in=tasks,
            )
            .values("object_id")
            .annotate(latest_id=Max("id"))
            .values("latest_id")
        )

        return list(
            self.client_aware()
            .filter(
                id__in=latest_id,
            )
            .order_by("object_id")
            .values("object_id", "changes_end_time")
        )

    def get_last_n_e2e_syncs(self, n):
        return list(
            self.client_aware()
            .filter(object_id="UPSTREAM_WRAPPER_SYNC")
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id",
                "sync_run_id",
                "sync_start_time",
                "sync_status",
            )[:n]
        )

    def get_records_for_e2e_sync_run_id(self, e2e_sync_run_id, values=None):
        result = (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .order_by("-sync_start_time")
        )
        if isinstance(values, list) and values:
            return list(result.values(*values))
        return list(result)

    def get_records_for_e2e_sync_run_id_and_object_id(
        self, e2e_sync_run_id, object_id, values=None
    ):
        result = self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id, object_id=object_id
        )
        if isinstance(values, list) and values:
            return result.values(*values)
        return result

    def get_last_success_obj(self, object_id):
        qs = UpstreamETLStatus.objects.filter(
            client_id=self.client_id,
            object_id=object_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )
        if qs.exists():
            return qs.latest("sync_start_time")
        else:
            return None

    def get_latest_run_records_for_integrations(
        self, integration_ids: List[UUID]
    ) -> QuerySet[UpstreamETLStatus]:
        queryset = UpstreamETLStatus.objects.none()

        for integration_id in integration_ids:
            curr_record_qs = self.client_aware().filter(
                client_id=self.client_id, integration_id=integration_id
            )

            if curr_record_qs.exists():
                queryset = queryset.union(
                    curr_record_qs.order_by("-sync_start_time")[:1]
                )

        return queryset

    def get_last_successful_run_records_for_integrations(
        self, integration_ids: List[UUID]
    ) -> QuerySet[UpstreamETLStatus]:
        queryset = UpstreamETLStatus.objects.none()

        for integration_id in integration_ids:
            curr_record_qs = self.client_aware().filter(
                client_id=self.client_id,
                integration_id=integration_id,
                sync_status=ETL_STATUS.COMPLETE.value,
            )

            if curr_record_qs.exists():
                queryset = queryset.union(
                    curr_record_qs.order_by("-sync_start_time")[:1]
                )

        return queryset

    def get_initial_sync_lines_for_integrations(
        self, integration_ids: List[UUID]
    ) -> QuerySet[UpstreamETLStatus]:
        queryset = UpstreamETLStatus.objects.none()

        for integration_id in integration_ids:
            curr_record_qs = self.client_aware().filter(
                client_id=self.client_id,
                integration_id=integration_id,
                sync_status=ETL_STATUS.COMPLETE.value,
            )

            if curr_record_qs.exists():
                queryset = queryset.union(
                    curr_record_qs.order_by("changes_end_time")[:1]
                )

        return queryset

    def get_last_successful_run_record_for_integration(
        self, integration_id: UUID
    ) -> Optional[UpstreamETLStatus]:
        """
        Retrieve the last successful run record for a specific integration.

        This function queries the database for the most recent successfully completed
        Upstream ETL run status record associated with a given integration ID and the
        client ID associated with the instance.

        Parameters:
            self (object): The instance of the class calling this method, typically
                        representing a client or user.
            integration_id (UUID): The unique identifier of the integration for which
                                to retrieve the last successful run record.

        Returns:
            Optional[UpstreamETLStatus]: An instance of the UpstreamETLStatus model
            representing the last successful run record, or None if no such record
            is found.
        """
        queryset = UpstreamETLStatus.objects.filter(
            client_id=self.client_id,
            integration_id=integration_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )

        if queryset.exists():
            return queryset.latest("sync_start_time")

        return None

    def get_intial_sync_line_record(
        self, integration_id: str
    ) -> Optional[UpstreamETLStatus]:
        """
        Retrieve the initial sync line record for a specific integration.
        """
        return (
            self.client_aware()
            .filter(integration_id=integration_id, sync_status="complete")
            .order_by("changes_end_time")
            .first()
        )

    def get_complete_status_of_past_days_by_integration_ids(
        self, integration_ids, days
    ) -> Optional[UpstreamETLStatus]:
        """
        Retrieve the first record with sync_status as 'complete' for the given integration_ids
        within the last x days.
        """
        time_threshold = timezone.now() - timezone.timedelta(days=days)

        return (
            self.client_aware()
            .filter(
                integration_id__in=integration_ids,
                sync_status=ETL_STATUS.COMPLETE.value,
                sync_start_time__gte=time_threshold,
            )
            .first()
        )


class ETLLockAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return ETLLock.objects.filter(client_id=self.client_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_object(self):
        obj = ETLLock.objects.filter(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        return obj

    def insert_lock(self, params):
        object_id = params["object_id"] if "object_id" in params else None
        lock_acquired_time = make_aware(datetime.datetime.now())
        lock_name = params["lock_name"] if "lock_name" in params else None
        is_active = params["is_active"] if "is_active" in params else None
        sync_mode = params["sync_mode"] if "sync_mode" in params else None
        trigger = params["trigger"] if "trigger" in params else None
        try:
            lock_obj = ETLLock.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                object_id=object_id,
                sync_mode=sync_mode,
                lock_acquired_time=lock_acquired_time,
                is_active=is_active,
                lock_name=lock_name,
                trigger=trigger,
            )
            return lock_obj
        except Exception as e:
            raise e

    def get_lock_by_lock_name(self, lock_name):
        try:
            obj = ETLLock.objects.filter(
                client_id=self.client_id,
                lock_name=lock_name,
                is_active=True,
            )

            return obj
        except Exception as e:
            raise e

    def acquire_lock(self, lock_name, object_id, cnt=3, sleeptime=10, trigger=None):
        try:
            locked = True
            lock_obj = None
            while cnt >= 0 and locked:
                obj = self.get_lock_by_lock_name(lock_name)

                if obj:  # Can't acquire lock
                    print("Can't acquire Lock CNT - {0} - {1}".format(lock_name, cnt))
                    if cnt == 0:
                        break
                    if isinstance(sleeptime, list):
                        st = sleeptime[3 - cnt]
                    else:
                        st = sleeptime
                    cnt -= 1
                    time.sleep(st)
                else:
                    params = {
                        "object_id": object_id,
                        "lock_name": lock_name,
                        "is_active": True,
                        "trigger": trigger,
                    }
                    lock_obj = self.insert_lock(params)
                    break
            return lock_obj
        except Exception as e:
            raise e

    def release_lock(self):
        try:
            params = {
                "is_active": False,
                "lock_released_time": make_aware(datetime.datetime.now()),
            }
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def release_lock_by_lock_name(self, lock_name):
        try:
            params = {
                "is_active": False,
                "lock_released_time": make_aware(datetime.datetime.now()),
            }
            self.get_object().filter(lock_name=lock_name).update(**params)
            return True
        except Exception as e:
            raise e

    def release_lock_by_e2e_sync_id(self, e2e_sync_run_id, sync_end_time):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id, is_active=True
        ).update(is_active=False, lock_released_time=sync_end_time)

    def is_lock_active(self, tasks, lock_names=None):
        query_set = (
            self.client_aware().filter(object_id__in=tasks).filter(is_active=True)
        )
        if lock_names:
            query_set = query_set.filter(lock_names__in=lock_names)
        if query_set.exists():
            return True
        return False

    def fetch_records_for_specified_columns(self, condition=None, columns=None) -> list:
        """
        This function is used to get the records for the given columns and given condition
        based on e2e_sync_run_id.

        If columns is None, fetch all columns.
        """
        query = self.client_aware().filter(e2e_sync_run_id=self.e2e_sync_run_id)

        if condition:
            query = query.filter(**condition)

        if columns is not None:
            query_result = query.values(*columns)
        else:
            query_result = query.values()

        return list(query_result)


class ExtractionSyncLogAccessor:
    def __init__(
        self, client_id: int, e2e_sync_run_id: UUID, sync_run_id: UUID
    ) -> None:
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return ExtractionSyncLog.objects.filter(client_id=self.client_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_sync_run_aware(self):
        return self.client_aware().filter(
            e2e_sync_run_id=self.e2e_sync_run_id, sync_run_id=self.sync_run_id
        )

    def delete_records_older_than(self, given_time):
        """
        Deletes all records from ExtractionSyncLog
        where the updated_at time is older than the given time.

        Args:
            given_time (datetime): The time before which records should be deleted.
        """
        records_to_delete = ExtractionSyncLog.objects.filter(updated_at__lt=given_time)
        records_to_delete.delete()

    def insert_bulk_record(self, ext_objs):
        try:
            ExtractionSyncLog.objects.bulk_create(ext_objs, batch_size=1000)
            return True
        except Exception as e:
            raise e

    def get_records_for_object_name(self, object_name: str) -> List[ExtractionSyncLog]:
        qs = self.client_sync_run_aware().filter(object_name=object_name)
        return list(qs)

    def get_query_set_for_object_name(
        self, object_name: str, operation: str
    ) -> QuerySet[ExtractionSyncLog]:
        if operation != "delete":
            qs = self.client_sync_run_aware().filter(
                object_name=object_name, operation=operation
            )

        else:
            qs = (
                self.client_sync_run_aware()
                .filter(object_name=object_name)
                .filter(Q(operation="delete") | Q(operation="secondary_delete"))
            )

        qs = qs.order_by("id")

        return qs


class TransformationSyncLogAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return TransformationSyncLog.objects.filter(client_id=self.client_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_sync_run_aware(self):
        return self.client_aware().filter(
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=self.sync_run_id,
        )

    def delete_records_older_than(self, given_time):
        """
        Deletes all records from TransformationSyncLog
        where the updated_at time is older than the given time.

        Args:
            given_time (datetime): The time before which records should be deleted.
        """
        records_to_delete = TransformationSyncLog.objects.filter(
            updated_at__lt=given_time
        )
        records_to_delete.delete()

    def insert_bulk_record(self, trans_objs):
        try:
            TransformationSyncLog.objects.bulk_create(trans_objs, batch_size=1000)
            return True
        except Exception as e:
            raise e

    def get_records_for_object_name(self, object_name):
        qs = self.client_sync_run_aware().filter(object_name=object_name)
        return list(qs)

    def get_transformed_objects_for_object_name(
        self, custom_object_id: str
    ) -> List[dict[str, Any]]:
        qs = (
            self.client_sync_run_aware()
            .filter(object_name=custom_object_id)
            .values_list("transformed_objects", flat=True)
        )

        return list(qs)

    def get_query_set_for_object_name(
        self, object_name: str
    ) -> QuerySet[TransformationSyncLog]:
        return (
            self.client_sync_run_aware().filter(object_name=object_name).order_by("id")
        )


class LoadSyncLogAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return LoadSyncLog.objects.filter(client_id=self.client_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_sync_run_aware(self):
        return self.client_aware().filter(
            e2e_sync_run_id=self.e2e_sync_run_id, sync_run_id=self.sync_run_id
        )

    def insert_bulk_record(self, load_objs):
        try:
            LoadSyncLog.objects.bulk_create(load_objs, batch_size=1000)
            return True
        except Exception as e:
            raise e

    def get_records_for_object_name(self, object_name):
        qs = self.client_sync_run_aware().filter(object_name=object_name)
        return list(qs)


class CommissionETLStatusAccessor(CommissionETLStatusAccessorTemplate):
    def client_aware(self):
        return CommissionETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        period_start_date = pydash.get(params, "period_start_date")
        period_end_date = pydash.get(params, "period_end_date")
        payee_email_id = pydash.get(params, "payee_email_id")
        plan_id = pydash.get(params, "plan_id")
        criteria_id = pydash.get(params, "criteria_id")
        primary_kd = pydash.get(params, "primary_kd")
        secondary_kd = pydash.get(params, "secondary_kd")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_completion_time = pydash.get(params, "sync_completion_time")
        sync_status = pydash.get(params, "sync_status")
        audit = pydash.get(params, "audit")
        try:
            sync_obj = CommissionETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                audit=audit,
                task=task,
                period_start_date=period_start_date,
                period_end_date=period_end_date,
                payee_email_id=payee_email_id,
                primary_kd=primary_kd,
                secondary_kd=secondary_kd,
                plan_id=plan_id,
                criteria_id=criteria_id,
            )
            return sync_obj
        except Exception as e:
            raise e

    def insert_bulk_objs(self, objs):
        try:
            CommissionETLStatus.objects.bulk_create(objs, batch_size=1000)
        except Exception as e:
            raise e


class CommissionETLStatusReaderAccessor(CommissionETLStatusReaderAccessorTemplate):
    def client_aware(self):
        return CommissionETLStatus.objects.filter(client_id=self.client_id)


class PayoutSnapshotETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return PayoutSnapshotETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        # snapshot_id = pydash.get(params, "snapshot_id")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_completion_time = pydash.get(params, "sync_completion_time")
        sync_status = pydash.get(params, "sync_status")
        primary_kd = pydash.get(params, "primary_kd")
        audit = pydash.get(params, "audit")
        try:
            sync_obj = PayoutSnapshotETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                primary_kd=primary_kd,
                audit=audit,
                task=task,
            )
            return sync_obj
        except Exception as e:
            raise e

    def get_object(self):
        obj = PayoutSnapshotETLStatus.objects.filter(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def update_payees(self, payees):
        return self.update_object({"payees": payees})

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def set_sync_start_time_and_status(self, start_time, status):
        record = self.get_object().first()
        if record and not record.sync_start_time:
            return self.update_object(
                {
                    "sync_start_time": start_time,
                    "sync_status": status,
                }
            )

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_start_time": make_aware(kd),
                "sync_completion_time": make_aware(kd),
            }
        )

    def get_sync_status_for_e2e(self, task=None):
        qs = self.client_aware().filter(e2e_sync_run_id=self.e2e_sync_run_id)
        if task:
            qs = qs.filter(task=task)
        return qs.first()


class PayoutSnapshotETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return PayoutSnapshotETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id_and_task(self, e2e_sync_run_id, task_list):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .filter(task__in=task_list)
            .values_list("task", "sync_status")
        )

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[PayoutSnapshotETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_last_n_e2e_syncs(self, n):
        return list(
            self.client_aware()
            .filter(task="PAYOUT_SNAPSHOT_WRAPPER_SYNC")
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id",
                "sync_run_id",
                "sync_start_time",
                "sync_status",
            )[:n]
        )

    def get_all_locked_started_tasks(self):
        return tuple(
            self.client_aware()
            .filter(Q(sync_status="started") | Q(sync_status="locked"))
            .values()
        )

    def job_count_sum_for_e2e(self, e2e_sync_run_id, tasks):
        return (
            self.client_aware()
            .filter(task__in=tasks)
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .aggregate(
                Sum("job_count"),
            )["job_count__sum"]
        )

    def job_count_sum_for_inter_comm_snapshot(self, e2e_sync_run_id, task):
        return (
            self.client_aware()
            .filter(task=task)
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .count()
        )

    def get_sync_time_for_e2e(self, e2e_sync_run_id, task):
        qs = self.client_aware().filter(task=task, e2e_sync_run_id=e2e_sync_run_id)
        return qs.first()

    def get_latest_sucessful_sync(self, task: str):
        qs = self.client_aware()

        return (
            qs.filter(
                sync_status=ETL_STATUS.COMPLETE.value,
                sync_completion_time__isnull=False,
                task=task,
            )
            .order_by("-sync_start_time")
            .values_list("sync_start_time", flat=True)
            .first()
        )


class SettlementSnapshotETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return SettlementSnapshotETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        # snapshot_id = pydash.get(params, "snapshot_id")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_completion_time = pydash.get(params, "sync_completion_time")
        sync_status = pydash.get(params, "sync_status")
        primary_kd = pydash.get(params, "primary_kd")
        audit = pydash.get(params, "audit")
        job_count = params.get("job_count", 0)
        try:
            sync_obj = SettlementSnapshotETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                primary_kd=primary_kd,
                audit=audit,
                task=task,
                job_count=job_count,
            )
            return sync_obj
        except Exception as e:
            raise e

    def get_object(self):
        obj = SettlementSnapshotETLStatus.objects.filter(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def update_payees(self, payees):
        return self.update_object({"payees": payees})

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def set_sync_start_time_and_status(self, start_time, status):
        record = self.get_object().first()
        if record and not record.sync_start_time:
            return self.update_object(
                {
                    "sync_start_time": start_time,
                    "sync_status": status,
                }
            )

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_start_time": make_aware(kd),
                "sync_completion_time": make_aware(kd),
            }
        )

    def delete_records(self, e2e_sync_run_id):
        self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id).delete()

    def get_latest_end_time_for_success_sync(self):
        """
        Returns:
            successfull_runs - Dict
            {
                "e2e_sync_run_id":"3cd1c551-12f7-4a50-ada5-edb7878495ca",
                "sync_completion_time":"2024-08-14 07:51:48.437567+00:00"
            }
            Here a settlement snapshot sync is considered successful if all the
            settlement snapshot sync tasks for the e2e_sync_run_id are completed.
            This function returns the e2e_sync_run_id and sync_completion_time of the
            latest successful run.
        """

        # Define the subquery to count completed SETTLEMENT_SNAPSHOT_SYNC tasks
        completed_count_subquery = (
            self.client_aware()
            .filter(
                e2e_sync_run_id=OuterRef("e2e_sync_run_id"),
                task=SYNC_OBJECT.SETTLEMENT_SNAPSHOT_SYNC.value,
                sync_status=ETL_STATUS.COMPLETE.value,
            )
            .values("e2e_sync_run_id")
            .annotate(completed_count=Count("sync_run_id"))
            .values("completed_count")
        )
        # Filter by task and sort by sync_start_time
        queryset = (
            self.client_aware()
            .filter(task=SYNC_OBJECT.SETTLEMENT_SNAPSHOT_WRAPPER_SYNC.value)
            .order_by("-sync_start_time")
        )
        # Define the main query to use the subquery - returns e2e_sync_run_id and sync_completion_time for successful runs
        successfull_runs = (
            queryset.annotate(completed_count=Subquery(completed_count_subquery))
            .filter(job_count=F("completed_count"))
            .values("e2e_sync_run_id", "sync_completion_time")
            .first()
        )
        return successfull_runs


class SettlementSnapshotETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return SettlementSnapshotETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id_and_task(self, e2e_sync_run_id, task_list):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .filter(task__in=task_list)
            .values_list("task", "sync_status")
        )

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[SettlementSnapshotETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_last_n_e2e_syncs(self, n):
        return list(
            self.client_aware()
            .filter(task="SETTLEMENT_SNAPSHOT_WRAPPER_SYNC")
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id",
                "sync_run_id",
                "sync_start_time",
                "sync_status",
            )[:n]
        )

    def get_all_locked_started_tasks(self):
        return tuple(
            self.client_aware()
            .filter(Q(sync_status="started") | Q(sync_status="locked"))
            .values()
        )

    def job_count_sum_for_e2e(self, e2e_sync_run_id, tasks):
        return (
            self.client_aware()
            .filter(task__in=tasks)
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .aggregate(
                Sum("job_count"),
            )["job_count__sum"]
        )

    def get_sync_time_for_e2e(self, e2e_sync_run_id, task):
        qs = self.client_aware().filter(task=task, e2e_sync_run_id=e2e_sync_run_id)
        return qs.first()

    # This method will only work if only one snapshot sync is triggered from wrapper sync
    # For settlement snapshot in snowflake this method will work. For postgres this method will not work
    def get_latest_sucessful_sync(
        self, task=SYNC_OBJECT.SETTLEMENT_SNAPSHOT_SYNC.value
    ):
        qs = self.client_aware()

        return (
            qs.filter(
                sync_status=ETL_STATUS.COMPLETE.value,
                sync_completion_time__isnull=False,
                task=task,
            )
            .order_by("-sync_start_time")
            .values_list("sync_start_time", flat=True)
            .first()
        )


class DatabookETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return DatabookETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        databook_id = pydash.get(params, "databook_id")
        datasheet_id = pydash.get(params, "datasheet_id")
        primary_kd = pydash.get(params, "primary_kd")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_completion_time = pydash.get(params, "sync_completion_time")
        sync_status = pydash.get(params, "sync_status")
        sync_type = pydash.get(params, "sync_type")
        audit = pydash.get(params, "audit")
        try:
            sync_obj = DatabookETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_type=sync_type,
                sync_completion_time=sync_completion_time,
                audit=audit,
                task=task,
                primary_kd=primary_kd,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
            )
            return sync_obj
        except Exception as e:
            raise e

    def get_object(self):
        obj = DatabookETLStatus.objects.filter(
            client_id=self.client_id, e2e_sync_run_id=self.e2e_sync_run_id
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
                ETL_STATUS.IN_PROGRESS.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def get_last_success_obj(self, databook_id, datasheet_id):
        qs = DatabookETLStatus.objects.filter(client_id=self.client_id).filter(
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            sync_status=ETL_STATUS.COMPLETE.value,
        )
        if qs.exists():
            qs = qs.latest("sync_start_time")
            return qs
        else:
            return None

    def set_sync_start_time_and_status(self, start_time, status):
        record = self.get_object().first()
        if record and not record.sync_start_time:
            return self.update_object(
                {
                    "sync_start_time": start_time,
                    "sync_status": status,
                }
            )

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_start_time": make_aware(kd),
                "sync_completion_time": make_aware(kd),
            }
        )

    def get_completed_failed_datasheet_ids(self) -> tuple[list[UUID], list[UUID]]:
        """
        This method is used to get completed and failed datasheet ids
        """
        query = (
            self.client_aware()
            .filter(e2e_sync_run_id=self.e2e_sync_run_id)
            .filter(task=SYNC_OBJECT.DATASHEET_SYNC.value)
            .values_list("datasheet_id", "sync_status")
        )

        completed_datasheet_ids = [
            datasheet_id
            for datasheet_id, status in query
            if status == ETL_STATUS.COMPLETE.value
        ]
        failed_datasheet_ids = [
            datasheet_id
            for datasheet_id, status in query
            if status == ETL_STATUS.FAILED.value
        ]

        return completed_datasheet_ids, failed_datasheet_ids

    def bulk_datasheet_lock(self, datasheet_records) -> None:
        """
        This method is used to create bulk lock for given records
        """
        logger.info("BEGIN: bulk_datasheet_lock")
        datasheet_records = [
            DatabookETLStatus(**record) for record in datasheet_records
        ]
        DatabookETLStatus.objects.bulk_create(datasheet_records)
        logger.info("END: bulk_datasheet_lock")

    def get_task_start_time(self):
        """
        This method is used to get the start time of the task
        """
        return (
            self.client_aware()
            .filter(
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
            )
            .values_list("sync_start_time", flat=True)
            .first()
        )

    def make_bulk_fail_datasheets(
        self, sync_run_ids, parent_datasheet_detail, error_info=None, error_details=None
    ) -> None:
        """
        This function is used to make bulk datasheets failed for given sync_run_ids
        error_info and error_details are similar sounding keys - we initially used
        error_info as a string and to keep backward compatibility, we introduced error_details which
        can be a dictionary
        """
        logger.info("BEGIN: make_bulk_fail_datasheets")

        start_time = self.get_task_start_time()
        parent_datasheet_name = parent_datasheet_detail.datasheet_name
        parent_sync_run_id = parent_datasheet_detail.sync_run_id
        if error_info is None:
            # If error_info is None put the geneal error message
            error_info = (
                f"The datasheet '{parent_datasheet_name}' and its dependent datasheets have failed.",
            )
            # Add datasheet link only when show_data_sources_v2 feature is enabled
            if has_feature(self.client_id, "show_data_sources_v2"):
                datasheet_id = (
                    parent_datasheet_detail.details_for_snowflake_transformation.datasheet_id
                )
                datasheet_url = f"/datasheet?id={datasheet_id}"
                # the parent datasheet name should be a link to the datasheet url
                error_info = (
                    f"The datasheet '<a href={datasheet_url}>{parent_datasheet_name}</a>' and its dependent datasheets have failed.",
                )
        sync_run_log = {
            "task": SYNC_OBJECT.DATASHEET_SYNC.value,
            "start_time": str(start_time),
            "end_time": str(make_aware(datetime.datetime.now())),
            "status": ETL_STATUS.FAILED.value,
            "error_info": error_info,
            "error_details": error_details,
        }

        self.client_aware().filter(
            e2e_sync_run_id=self.e2e_sync_run_id, sync_run_id=parent_sync_run_id
        ).update(sync_run_log=sync_run_log)

        self.client_aware().filter(
            e2e_sync_run_id=self.e2e_sync_run_id, sync_run_id__in=sync_run_ids
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=str(make_aware(datetime.datetime.now())),
        )
        logger.info("END: make_bulk_fail_datasheets for sync_run_ids")

    def get_record_sync_status(self) -> str:
        """
        Returns the sync_status of the datasheet for the given client_id ,e2e_sync_run_id
        and sync_run_id
        """
        sync_status = self.get_object().values("sync_status")[0]
        logger.info(
            "sync_status for the sync_run_id %s is %s", self.sync_run_id, sync_status
        )
        return sync_status["sync_status"]

    def current_all_locked_datasheet_ids(
        self, task=None, databook_ids=None
    ) -> set[UUID]:
        """
        Returns a set of datasheet ids that have sync_status of "started", "locked", or "not_started"
        for the task "DATASHEET_SYNC".

        Returns:
            {
                "datasheet_id_1",
                "datasheet_id_2",
                "datasheet_id_3",
            }
        """

        query = self.client_aware().filter(
            sync_status__in=[
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.NOT_STARTED.value,
            ],
        )

        if task is not None:
            if not isinstance(task, list):
                # Convert to list if task is not a list
                task = [task]
            query = query.filter(task__in=task)

        if databook_ids is not None:
            if not isinstance(databook_ids, list):
                # Convert to list if databook_ids is not a list
                databook_ids = [databook_ids]

            query = query.filter(databook_id__in=databook_ids)

        datasheet_ids = query.values_list("datasheet_id", flat=True)
        return set(datasheet_ids)

    def set_primary_kd(self, knowledge_date):
        """
        This method is used to set the primary_kd for the datasheet
        """
        logger.info("set_primary_kd -> %s", knowledge_date)
        return self.update_object({"primary_kd": knowledge_date})

    def fetch_records_for_specified_columns(self, columns=None) -> list:
        """
        This function is used to get the records for the given columns
        based on e2e_sync_run_id.

        If columns is None, fetch all columns.
        """
        query = self.client_aware().filter(e2e_sync_run_id=self.e2e_sync_run_id)

        if columns is not None:
            query_result = query.values(*columns)
        else:
            query_result = query.values()

        return list(query_result)

    def get_failed_sync_run_logs(self, databook_id) -> list:
        """
        This method is used to get the failed sync_run_log for the given e2e_sync_run_id
        """
        query = (
            self.client_aware()
            .filter(
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_status=ETL_STATUS.FAILED.value,
                databook_id=databook_id,
            )
            .values("databook_id", "datasheet_id", "sync_run_log")
        )
        return list(query)

    def has_pending_datasheet_generation_accessor(self, datasheet_ids: list) -> bool:
        """
        Checks if any of the given datasheet_ids has pending datasheet generation
        """
        result = (
            self.client_aware().filter(
                e2e_sync_run_id=self.e2e_sync_run_id,
                datasheet_id__in=datasheet_ids,
                sync_status__in=[
                    ETL_STATUS.LOCKED.value,
                    ETL_STATUS.STARTED.value,
                    ETL_STATUS.IN_PROGRESS.value,
                ],
            )
        ).exists()
        return result

    def change_status(self, status: str) -> None:
        """
        Changes the status of the datasheet to the given input status.
        """
        self.update_object({"sync_status": status})

    def change_all_pending_datasheets_to_failed(
        self, sync_type: str | None = None
    ) -> None:
        """
        Changes the status of all the pending datasheets to failed for the given e2e_sync_run_id.
        If sync_type is provided, it will filter the datasheets by the given sync_type.
        """
        query = self.client_aware().filter(
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_status__in=[
                ETL_STATUS.IN_PROGRESS.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.STARTED.value,
            ],
        )
        if sync_type:
            query = query.filter(sync_type=sync_type)

        timestamp = make_aware(datetime.datetime.now())
        query.update(
            sync_status=ETL_STATUS.FAILED.value, sync_completion_time=timestamp
        )


class DatabookETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DatabookETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[DatabookETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_last_n_e2e_syncs(self, n):
        return list(
            self.client_aware()
            .filter(task="DATABOOK_WRAPPER_SYNC")
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id",
                "sync_run_id",
                "sync_start_time",
                "sync_status",
            )[:n]
        )

    def get_datasheet_id_and_status_for_task(self, e2e_sync_run_id, task):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id, task=task)
            .values_list("datasheet_id", "sync_status")
        )

    def get_all_locked_started_tasks(self):
        return tuple(
            self.client_aware()
            .filter(Q(sync_status="started") | Q(sync_status="locked"))
            .values()
        )

    def get_records_for_e2e_task_sync_type(
        self, e2e_sync_run_id, task, sync_type, values=None
    ):
        result = self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id, task=task)
        if sync_type:
            result = result.filter(sync_type=sync_type)
        result = result.order_by("-sync_start_time")
        if isinstance(values, list) and values:
            return list(result.values(*values))
        return list(result)

    def get_sync_time_for_e2e(self, e2e_sync_run_id, task, sync_type):
        return (
            self.client_aware()
            .filter(task=task, e2e_sync_run_id=e2e_sync_run_id, sync_type=sync_type)
            .order_by("-sync_start_time")
            .first()
        )

    def get_latest_sync_info_by_ds_id(self, activity, databook_id, datasheet_id):
        """
        Gets latest entry in the table for a sync of type activity. If sync is already running,
        it returns entry with completion_time as null
        """
        data = (
            self.client_aware()
            .filter(task=activity, databook_id=databook_id, datasheet_id=datasheet_id)
            .order_by("-sync_completion_time")
            .values(
                "e2e_sync_run_id",
                "sync_status",
                "sync_start_time",
                "sync_completion_time",
            )
            .first()
        )
        return data

    def get_unique_databook_ids(self, e2e_sync_run_id: UUID, sync_type) -> int:
        """
        Get the number of unique databook IDs for a given e2e_sync_run_id and sync_type
        """
        unique_databook_ids_count = (
            self.client_aware()
            .filter(
                e2e_sync_run_id=e2e_sync_run_id,
                task=SYNC_OBJECT.DATASHEET_SYNC.value,
                sync_type=sync_type,
            )
            .values("databook_id")
            .distinct()
            .count()
        )

        return unique_databook_ids_count

    def get_datasheet_sync_status(
        self, e2e_sync_run_id: UUID, sync_type
    ) -> dict[UUID, str]:
        """
        This method returns the dict of datasheet id with the corresponding sync status.
        {
            datasheet_id_1: 'complete',
            datasheet_id_2: 'failed',
            datasheet_id_2: 'lock_failed,
        }
        """
        return dict(
            self.client_aware()
            .filter(
                e2e_sync_run_id=e2e_sync_run_id,
                task=SYNC_OBJECT.DATASHEET_SYNC.value,
                sync_type=sync_type,
            )
            .values_list("datasheet_id", "sync_status")
        )

    def datasheet_records_by_sync_type(self, e2e_sync_run_id):
        """
        This method is used to get the generated datasheet records from the databook_etl_status table
        by sync_type for the given e2e_sync_run_id.

        {
            "SYSTEM_CUSTOM_DB_SYNC": [
                'f89c23c1-5c2b-4e78-85ff-12a07c32fcdc',
                '9e20e805-6beb-4e3f-9be1-fa640dfb496b',
                '1593da94-f3d8-4fc2-b6e9-d0303eb3d3b4',
                '9dd132fd-8eeb-4475-be04-74745ddc190e',
                '108bb6e7-971b-4866-ba3e-429a36c9bee4'
            ]
            "INTER_REPORT_OBJECT_DB_SYNC": [
                'f89c23c1-5c2b-4e78-85ff-12a07c32fcd3',
                '108bb6e7-971b-4866-ba3e-429a36c9bee1'
            ]
        }

        """
        records = self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id, task="DATASHEET_SYNC"
        )

        result_dict = {}
        for record in records:
            sync_type = record.sync_type
            datasheet_id = record.datasheet_id
            result_dict.setdefault(sync_type, []).append(str(datasheet_id))

        return result_dict

    def get_records_for_given_date(self, target_date=None):
        """
        This method is used to get the records for the given date.
        If target_date is None, it defaults to today's date.
        """
        if target_date is None:
            target_date = date.today()

        target_date_start = make_aware(
            datetime.datetime.combine(target_date, datetime.time.min)
        )
        target_date_end = make_aware(
            datetime.datetime.combine(target_date, datetime.time.max)
        )

        return list(
            DatabookETLStatus.objects.filter(
                sync_start_time__gte=target_date_start,
                sync_completion_time__lte=target_date_end,
            )
            .values("e2e_sync_run_id", "client_id")
            .distinct()
        )

    def get_last_n_sync_for_datasheet(self, databook_id, datasheet_id, n):
        return list(
            self.client_aware()
            .filter(
                task="DATASHEET_SYNC",
                databook_id=databook_id,
                datasheet_id=datasheet_id,
            )
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id",
                "primary_kd",
                "sync_run_id",
                "sync_start_time",
                "sync_status",
            )[:n]
        )

    def is_datasheet_sync_in_progress(self, databook_id, datasheet_id):
        """
        This method is used to check if the datasheet sync is in progress
        """
        return (
            self.client_aware()
            .filter(
                task="DATASHEET_SYNC",
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                sync_status__in=[ETL_STATUS.STARTED.value, ETL_STATUS.LOCKED.value],
            )
            .exists()
        )

    def is_databook_sync_in_progress(self, databook_id):
        """
        This method is used to check if the databook sync is in progress
        """
        return (
            self.client_aware()
            .filter(
                task=SYNC_OBJECT.DATASHEET_SYNC.value,
                databook_id=databook_id,
                sync_status__in=[ETL_STATUS.STARTED.value, ETL_STATUS.LOCKED.value],
            )
            .exists()
        )

    def sync_type_to_primary_kd(self, e2e_sync_run_id) -> dict:
        """
        This method is used to get the sync_type to primary_kd mapping for the given e2e_sync_run_id.
        {
            "SYSTEM_CUSTOM_DB_SYNC": "primary_kd_1",
            "INTER_REPORT_OBJECT_DB_SYNC": "primary_kd_2",
        }
        """
        result = dict(
            self.client_aware()
            .filter(
                e2e_sync_run_id=e2e_sync_run_id,
                task=SYNC_OBJECT.DATASHEET_WRAPPER_SYNC.value,
            )
            .values_list("sync_type", "primary_kd")
        )
        return result

    def get_record_sync_type(self, e2e_sync_run_id, sync_run_id) -> str | None:
        """
        Returns the sync_type for a given sync_run_id
        """
        return (
            self.client_aware()
            .get(e2e_sync_run_id=e2e_sync_run_id, sync_run_id=sync_run_id)
            .sync_type
        )

    def get_wrapper_sync_run_id(self, e2e_sync_run_id, sync_type) -> UUID | None:
        """
        Returns the sync_run_id for the DATASHEET_WRAPPER_SYNC entry, given a e2e_sync_run_id and sync_type
        """
        wrapper_sync_object = (
            self.client_aware()
            .filter(
                e2e_sync_run_id=e2e_sync_run_id,
                task=SYNC_OBJECT.DATASHEET_WRAPPER_SYNC.value,
                sync_type=sync_type,
            )
            .first()
        )
        if wrapper_sync_object is None:
            return None
        return wrapper_sync_object.sync_run_id


class ReportETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id=None, sync_run_id=None):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return ReportETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, **kwargs):
        task = kwargs.get("task")
        object_id = kwargs.get("object_id")
        sync_start_time = kwargs.get("sync_start_time")
        sync_completion_time = kwargs.get("sync_completion_time")
        sync_status = kwargs.get("sync_status")
        changes_start_time = kwargs.get("changes_start_time")
        changes_end_time = kwargs.get("changes_end_time")
        audit = kwargs.get("audit")
        sync_mode = kwargs.get("sync_mode")
        try:
            sync_obj = ReportETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                changes_start_time=changes_start_time,
                changes_end_time=changes_end_time,
                audit=audit,
                task=task,
                object_id=object_id,
                sync_mode=sync_mode,
            )
            return sync_obj
        except Exception as e:
            raise e

    def get_object(self):
        obj = ReportETLStatus.objects.filter(client_id=self.client_id).filter(
            e2e_sync_run_id=self.e2e_sync_run_id
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def set_sync_start_time_and_status(self, start_time, status):
        record = self.get_object().first()
        if record and not record.sync_start_time:
            return self.update_object(
                {
                    "sync_start_time": start_time,
                    "sync_status": status,
                }
            )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def get_last_success_obj(self, object_id, task=None):
        qs = (
            ReportETLStatus.objects.filter(client_id=self.client_id)
            .filter(object_id=object_id)
            .filter(sync_mode__in=["all", "changes"])
            .filter(sync_status=ETL_STATUS.COMPLETE.value)
        )
        if task:
            qs.filter(task=task)
        if qs.exists():
            qs = qs.latest("sync_start_time")
            return qs
        else:
            return None

    def change_status_to_extraction(self):
        return self.update_object({"sync_status": ETL_STATUS.EXTRACTION.value})

    def change_status_to_transformation(self):
        return self.update_object({"sync_status": ETL_STATUS.TRANSFORMATION.value})

    def change_status_to_loading(self):
        return self.update_object({"sync_status": ETL_STATUS.LOADING.value})

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_start_time": make_aware(kd),
                "sync_completion_time": make_aware(kd),
            }
        )

    def get_status_by_sync_id(self):
        """
        Gets the status of report etl
        """
        qs = ReportETLStatus.objects.filter(
            client_id=self.client_id, e2e_sync_run_id=self.e2e_sync_run_id
        )
        return qs

    def get_last_successful_run_records_for_objects(
        self, object_ids: List[str]
    ) -> QuerySet[ReportETLStatus]:
        queryset = ReportETLStatus.objects.none()

        for ro_id in object_ids:
            curr_record_qs = self.client_aware().filter(
                client_id=self.client_id,
                object_id=ro_id,
                sync_status=ETL_STATUS.COMPLETE.value,
            )

            if curr_record_qs.exists():
                queryset = queryset.union(
                    curr_record_qs.order_by("-sync_start_time")[:1]
                )

        return queryset


class SettlementETLStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return SettlementETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        period_start_date = pydash.get(params, "period_start_date")
        period_end_date = pydash.get(params, "period_end_date")
        payee_email_id = pydash.get(params, "payee_email_id")
        primary_kd = pydash.get(params, "primary_kd")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_completion_time = pydash.get(params, "sync_completion_time")
        sync_status = pydash.get(params, "sync_status")
        audit = pydash.get(params, "audit")
        datasheet_id = pydash.get(params, "datasheet_id")
        plan_id = pydash.get(params, "plan_id")
        criteria_id = pydash.get(params, "criteria_id")
        sync_run_log = pydash.get(params, "sync_run_log")
        try:
            sync_obj = SettlementETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                audit=audit,
                task=task,
                period_start_date=period_start_date,
                period_end_date=period_end_date,
                payee_email_id=payee_email_id,
                primary_kd=primary_kd,
                datasheet_id=datasheet_id,
                plan_id=plan_id,
                criteria_id=criteria_id,
                sync_run_log=sync_run_log,
            )
            return sync_obj
        except Exception as e:
            raise e

    def get_object(self):
        obj = SettlementETLStatus.objects.filter(client_id=self.client_id).filter(
            e2e_sync_run_id=self.e2e_sync_run_id
        )
        if self.sync_run_id:
            obj = obj.filter(sync_run_id=self.sync_run_id)
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def update_job_count(self, task_count: int):
        return self.update_object({"job_count": task_count})

    def update_primary_kd(self, primary_kd: datetime.datetime):
        return self.update_object({"primary_kd": primary_kd})

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def change_status_to_in_progress(self):
        return self.update_object({"sync_status": ETL_STATUS.IN_PROGRESS.value})

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def release_lock_for_all_started_tasks_by_e2e_sync_id(
        self, e2e_sync_run_id, sync_end_time
    ):
        self.client_aware().filter(
            e2e_sync_run_id=e2e_sync_run_id,
            sync_completion_time__isnull=True,
            sync_status__in=[
                ETL_STATUS.NOT_STARTED.value,
                ETL_STATUS.STARTED.value,
                ETL_STATUS.LOCKED.value,
                ETL_STATUS.EXTRACTION.value,
                ETL_STATUS.TRANSFORMATION.value,
                ETL_STATUS.LOADING.value,
            ],
        ).update(
            sync_status=ETL_STATUS.FAILED.value,
            sync_completion_time=sync_end_time,
        )

    def get_last_success_obj(self, datasheet_id):
        query_set = (
            SettlementETLStatus.objects.filter(client_id=self.client_id)
            .filter(datasheet_id=datasheet_id)
            .filter(sync_status=ETL_STATUS.COMPLETE.value)
        )
        return query_set.latest("sync_start_time") if query_set else None

    def set_sync_start_time_and_status(self, start_time, status):
        record = self.get_object().first()
        if record and not record.sync_start_time:
            return self.update_object(
                {
                    "sync_start_time": start_time,
                    "sync_status": status,
                }
            )

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_start_time": make_aware(kd),
                "sync_completion_time": make_aware(kd),
            }
        )


class SettlementETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return SettlementETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[SettlementETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)

    def get_primary_kd(self, e2e_sync_run_id: UUID, sync_run_id: UUID):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id, sync_run_id=sync_run_id)
            .values_list("primary_kd", flat=True)
            .first()
        )

    def get_sync_run_log_by_e2e_and_sync_run_id(
        self, e2e_sync_run_id: UUID, sync_run_id: UUID
    ) -> dict:
        res = list(
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .filter(sync_run_id=sync_run_id)
            .values_list("sync_run_log", flat=True)
        )
        return res[0] if res else {}

    def all_tasks_completed(self, e2e_sync_run_id) -> bool:
        incomplete_or_failed_sync_exists = (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .exclude(sync_status=ETL_STATUS.COMPLETE.value)
            .exclude(task=SYNC_OBJECT.SETTLEMENT_WRAPPER_SYNC.value)
            .exists()
        )
        return not incomplete_or_failed_sync_exists

    def job_count_sum_for_e2e(self, e2e_sync_run_id, tasks):
        return (
            self.client_aware()
            .filter(task__in=tasks)
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .aggregate(
                Sum("job_count"),
            )["job_count__sum"]
        )

    def get_task_and_status_for_task_list(self, e2e_sync_run_id, task_list):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .filter(task__in=task_list)
            .values_list("task", "sync_status")
        )

    # TODO: Remove this method it's not used anywhere
    def get_latest_end_time_for_success_sync(self, e2e_sync_run_id, payees=None):
        qs = self.client_aware().exclude(e2e_sync_run_id=e2e_sync_run_id)
        if payees:
            qs = qs.filter(payee_email_id__in=payees)
        return (
            qs.filter(
                sync_status=ETL_STATUS.COMPLETE.value,
                sync_completion_time__isnull=False,
            )
            .order_by("-sync_completion_time")
            .values_list("sync_completion_time", flat=True)
            .first()
        )


class ReportETLStatusReaderAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ReportETLStatus.objects.filter(client_id=self.client_id)

    def get_records_by_e2e_sync_run_id(
        self, e2e_sync_run_id: UUID
    ) -> QuerySet[ReportETLStatus]:
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id)

    def get_all_locked_started_tasks(self):
        return tuple(
            self.client_aware()
            .filter(Q(sync_status="started") | Q(sync_status="locked"))
            .values()
        )

    def get_records_for_e2e_sync_run_id_and_task_list(
        self, e2e_sync_run_id, task_list, values=None
    ):
        result = (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .filter(object_id__in=task_list)
            .order_by("-sync_start_time")
        )
        if isinstance(values, list) and values:
            return list(result.values(*values))
        return list(result)

    def get_sync_time_for_e2e(self, e2e_sync_run_id, task):
        try:
            return (
                self.client_aware()
                .filter(task=task, e2e_sync_run_id=e2e_sync_run_id)
                .order_by("-sync_start_time")
                .first()
            )
        except ReportETLStatus.DoesNotExist:
            return None


class ETLSyncStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id

    def get_object(self):
        obj = ETLSyncStatus.objects.filter(client_id=self.client_id).filter(
            e2e_sync_run_id=self.e2e_sync_run_id
        )
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def get_sync_start_time(self) -> datetime.datetime:
        """
        This method is used to get the start time of the sync.

        Returns:
            datetime.datetime: The start time of the sync.
        """
        return self.get_object().values_list("sync_start_time", flat=True).first()  # type: ignore

    @transaction.atomic
    def change_status_to_failed(
        self,
        should_update_completion_time=False,
        timestamp: datetime.datetime = make_aware(datetime.datetime.now()),
    ):
        qs = self.get_object()

        if qs.exists():
            if should_update_completion_time:
                qs.update(
                    sync_status=ETL_STATUS.PARTIALLY_FAILED.value,
                    sync_end_time=timestamp,
                )
            else:
                qs.update(
                    sync_status=ETL_STATUS.PARTIALLY_FAILED.value,
                )

    def change_status_to_lock_failed(self, should_update_completion_time=False):
        qs = self.get_object()

        if qs.exists():
            if should_update_completion_time:
                qs.update(
                    sync_status=ETL_STATUS.LOCK_FAILED.value,
                    sync_end_time=make_aware(datetime.datetime.now()),
                )
            else:
                qs.update(sync_status=ETL_STATUS.LOCK_FAILED.value)

    def release_lock(
        self, sync_end_time, audit_details, should_set_sync_start_time=False
    ):
        qs = self.get_object()
        if should_set_sync_start_time:
            qs.update(
                sync_status=ETL_STATUS.FAILED.value,
                sync_start_time=sync_end_time,
                sync_end_time=sync_end_time,
                audit=audit_details,
            )
        else:
            qs.update(
                sync_status=ETL_STATUS.PARTIALLY_FAILED.value,
                sync_end_time=sync_end_time,
                audit=audit_details,
            )

    def update_status_and_completion_time(self, status):
        qs = self.get_object()
        if qs.exists():
            qs.update(
                sync_status=status, sync_end_time=make_aware(datetime.datetime.now())
            )

    def _get_datasheet_names(self, datasheet_ids: list[str]) -> str:
        datasheet_names = DatasheetAccessor(self.client_id).get_datasheet_name_by_id(
            datasheet_ids
        )
        formatted_datasheet_names = ", ".join(datasheet_names)
        return formatted_datasheet_names

    def _is_datasheet_generation_possible(
        self, params: dict | None
    ) -> tuple[bool, set[str]]:
        """
        Checks if datasheet generation is possible by comparing locked datasheets
        with the stale datasheet list.
        """
        # Validate input
        if not params or not params.get("datasheet_id"):
            raise ValueError("datasheet_id is required for datasheet generation.")

        # Import dependencies
        from everstage_etl.tasks.datasheet_generation.pre_generate import (
            current_all_locked_datasheets,
        )
        from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph

        # Initialize graph and fetch locked datasheets
        datasheet_graph = DataSheetGraph(
            client_id=self.client_id,
            include_stale_information_query=True,
        )
        current_all_locked_ds = {
            str(ds)
            for ds in current_all_locked_datasheets(
                client_id=self.client_id,
                task=[SYNC_OBJECT.DATASHEET_SYNC.value],
            )
        }

        # Fetch stale datasheet details
        stale_details = datasheet_graph.get_stale_datasheets_to_generate(
            datasheet_ids=[params["datasheet_id"]]
        )

        logger.info(f"stale_details: {stale_details}")

        stale_datasheet_list = stale_details.stale_datasheet_list

        logger.info(f"Locked datasheets: {current_all_locked_ds}")
        logger.info(f"Stale datasheets: {stale_datasheet_list}")

        # Check for intersection
        if stale_datasheet_list:
            intersection = current_all_locked_ds.intersection(stale_datasheet_list)
            if intersection:
                return False, intersection

        return True, set()

    def _is_databook_already_running(self, databook_ids: list[str]) -> bool:
        """
        Checks if any of the given databooks are already running.
        """
        if not databook_ids:
            return False

        existing_syncs = ETLSyncStatus.objects.filter(
            client_id=self.client_id,
            task=ETL_ACTIVITY.REFRESH_DATABOOK.value,
            sync_end_time__isnull=True,
            params__databook_ids__contains=databook_ids,
        )
        logger.info(f"existing_syncs: {existing_syncs}")
        logger.info(f"Checking databooks {databook_ids} for active syncs.")
        return existing_syncs.exists()

    def _handle_refresh_databook(self, params: dict | None) -> None:
        if not params:
            raise ValueError("params is required for databook check.")

        databook_ids = params.get("databook_ids")

        # if the databook is already being refreshed/locked by another sync, then raise an error
        if databook_ids and self._is_databook_already_running(databook_ids):
            logger.info(f"Conflicting databooks with existing syncs: {databook_ids}")
            raise ETLConcurrencyException(
                "Another sync is already running for specified databooks. Please wait for it to complete."
            )

    def _handle_generate_datasheet(self, params: dict | None) -> None:
        is_generation_possible, conflicted_ids = self._is_datasheet_generation_possible(
            params
        )

        # if the datasheet is already being generated/locked by another sync, then raise an error
        if not is_generation_possible:
            datasheet_names = self._get_datasheet_names(list(conflicted_ids))
            logger.info(
                f"Conflicting datasheets with existing syncs: {datasheet_names}"
            )
            raise ETLConcurrencyException(
                f"Cannot perform this action since sheets {datasheet_names} are already being refreshed. Please wait for it to complete."
            )

    def _insert_sync_status_with_concurrency_checks(self, **kwargs):
        task = kwargs.get("task")
        sync_start_time = kwargs.get("sync_start_time")
        sync_end_time = kwargs.get("sync_end_time")
        sync_status = kwargs.get("sync_status")
        audit = kwargs.get("audit")
        params = kwargs.get("params")
        additional_info = kwargs.get("additional_info")

        match task:
            # if the task is refresh_databook, then we need to check if the databook_ids overlap with other databook_ids already locked
            case ETL_ACTIVITY.REFRESH_DATABOOK.value:
                self._handle_refresh_databook(params)

            # if the task is generate_datasheet, then we need to check if the datasheet_id, or
            # realted databook_ids overlap with other databook_ids already locked
            case ETL_ACTIVITY.GENERATE_DATASHEET.value:
                self._handle_generate_datasheet(params)

            case _:
                pass

        # if additional_info is not a dict, then it means that the sync is not a cron job
        # and we need to set the is_cron_job to False and the retry_signature to contain the e2e_sync_run_id
        # retry signature is not required for non cron jobs
        if not isinstance(additional_info, dict):
            additional_info = dict()
            additional_info["is_cron_job"] = False

        # try to insert the sync status,
        # if this create fails, then it means that the sync is already running
        # the failure will raise a Django IntegrityError.
        # which is handled by the `insert_sync_status` method
        try:
            obj = ETLSyncStatus.objects.create(
                client_id=self.client_id,
                task=task,
                sync_status=sync_status,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_start_time=sync_start_time,
                sync_end_time=sync_end_time,
                audit=audit,
                params=params,
                additional_info=additional_info,
            )

            logger.info("Sync status successfully inserted for task %s", task)
            return obj

        except IntegrityError as e:
            logger.error(
                f"IntegrityError during sync status creation for task {task}: {e}"
            )
            raise ETLConcurrencyException(
                f"Another {task.lower() if task else task} operation is in progress. Please wait for it to complete."
            ) from e

    def insert_sync_status(self, **kwargs):
        task = kwargs.get("task")

        try:
            self._insert_sync_status_with_concurrency_checks(**kwargs)

        except ETLConcurrencyException as e:
            logger.warning(
                f"[ETL CONCURRENCY ERROR] Sync already running for task {task} and client {self.client_id}"
            )
            raise e

        except Exception as e:
            logger.error(
                "[UNEXPECTED ERROR] Failed to insert sync status for task %s: %s",
                task,
                e,
            )
            raise e

    def update_sync_status_to_started(self, sync_start_time):
        qs = self.get_object()
        if qs.exists():
            qs.update(
                sync_status=ETL_STATUS.STARTED.value, sync_start_time=sync_start_time
            )

    def mark_cron_job_as_failed(self, user_email=None):
        # check if the sync is a cron sync and it is partially failed
        qs = self.get_object()
        if not qs.exists():
            return

        obj = qs.first()

        additional_info = obj.additional_info or {}
        additional_info["marked_as_failed"] = True
        additional_info["marked_as_failed_by"] = str(user_email or "")

        if additional_info.get("is_cron_job", False) == False:
            logger.error(f"Sync {self.e2e_sync_run_id} is not a cron job")
            raise Exception("Sync is not a cron job")

        self.update_object(
            {
                "additional_info": additional_info,
            }
        )

    def mark_manual_job_as_failed(self, user_email: str | None = None):
        # check if the sync is a manual sync and it is partially failed
        qs = self.get_object()
        if not qs.exists():
            return

        obj = qs.first()

        additional_info = obj.additional_info or {}
        additional_info["marked_as_failed"] = True
        additional_info["marked_as_failed_by"] = str(user_email or "")

        if additional_info.get("is_cron_job", True) == True:
            logger.error(f"Sync {self.e2e_sync_run_id} is not a manual job")
            raise Exception("Sync is not a manual job")

        self.update_object(
            {
                "additional_info": additional_info,
            }
        )

    def mark_sync_as_skipped(self, reason: str, user_email: str | None = None):
        # check if the sync is a manual sync
        qs = self.get_object()
        if not qs.exists():
            return

        obj = qs.first()
        additional_info = obj.additional_info or {}
        additional_info["skipped"] = True
        additional_info["skipped_by"] = str(user_email or "")
        additional_info["skipped_reason"] = reason

        if not reason:
            raise Exception("Failed to skip sync. Reason is required.")

        self.update_object(
            {
                "additional_info": additional_info,
            }
        )

    def mark_cron_job_as_retried(self):
        # check if the sync is a cron job and it is partially failed
        qs = self.get_object()
        if not qs.exists():
            return

        obj = qs.first()
        additional_info = obj.additional_info or {}
        additional_info["was_retried"] = True

        if additional_info.get("is_cron_job", False) == False:
            logger.error(f"Sync {self.e2e_sync_run_id} is not a cron job")
            raise Exception("Sync is not a cron job")

        self.update_object(
            {
                "additional_info": additional_info,
            }
        )

        logger.info(f"Sync {self.e2e_sync_run_id} marked as retried")

    def handle_etl_failure(self, kd):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_end_time": make_aware(kd),
            }
        )


class ETLSyncStatusReaderAccessor:
    def __init__(self, client_id=None):
        self.client_id = client_id

    def client_aware(self):
        return ETLSyncStatus.objects.filter(client_id=self.client_id)

    def is_e2e_id_exist(self, e2e_sync_run_id):
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id).exists()

    def is_e2e_id_and_task_exist(self, e2e_sync_run_id, task):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .filter(task=task)
            .exists()
        )

    def get_sync_status(self, e2e_sync_run_id):
        status = (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .values_list("sync_status", flat=True)
            .first()
        )
        return status

    def get_latest_data_sync_details(self, date_to_compare):
        details = (
            self.client_aware()
            .filter(sync_end_time__date__gt=date_to_compare)
            .order_by("-sync_start_time")
            .first()
        )
        return details

    def get_current_running_sync_details(self):
        status = (
            self.client_aware()
            .filter(sync_end_time__isnull=True)
            .order_by("sync_start_time")
            .first()
        )
        return status

    def get_etl_sync_status_records(self, data_from, data_to):
        return list(
            self.client_aware()
            .filter(sync_end_time__isnull=False)
            .order_by("-sync_start_time")[data_from:data_to]
        )

    def get_etl_sync_status_records_with_cron_marked_as_failed(
        self, data_from, data_to
    ):
        queryset = self.client_aware().filter(sync_end_time__isnull=False)

        # Define the conditions for excluding records
        exclude_conditions = Q(
            sync_status=ETL_STATUS.PARTIALLY_FAILED.value,  # Sync status is partially_failed
            additional_info__is_cron_job=True,  # Only cron jobs
            additional_info__marked_as_failed=False,  # Exclude records where marked_as_failed is False
        )

        # Annotate the queryset with a flag to indicate if the record should be excluded
        queryset = queryset.annotate(
            should_exclude=Case(
                When(exclude_conditions, then=Value(True)),
                default=Value(False),
                output_field=BooleanField(),
            )
        )

        # Filter out the records that should be excluded
        queryset = queryset.filter(should_exclude=False)

        # Order the results by sync_start_time (descending order)
        queryset = queryset.order_by("-sync_start_time")

        # Apply pagination
        result = queryset[data_from:data_to]

        return result

    def get_total_record_count(self):
        return self.client_aware().filter(sync_end_time__isnull=False).count()

    def get_total_record_count_with_cron_marked_as_failed(self):
        queryset = self.client_aware().filter(sync_end_time__isnull=False)

        exclude_conditions = Q(
            sync_status=ETL_STATUS.PARTIALLY_FAILED.value,  # Sync status is partially_failed
            additional_info__is_cron_job=True,  # Only cron jobs
            additional_info__marked_as_failed=False,  # Exclude records where marked_as_failed is False
        )

        queryset = queryset.annotate(
            should_exclude=Case(
                When(exclude_conditions, then=Value(True)),
                default=Value(False),
                output_field=BooleanField(),
            )
        )

        queryset = queryset.filter(should_exclude=False)

        return queryset.count()

    def get_latest_e2e_sync_run_id_and_params(self, activity):
        data = (
            self.client_aware()
            .filter(task=activity)
            .order_by("-sync_start_time")
            .values("e2e_sync_run_id", "params")
            .first()
        )
        if data:
            return [data["e2e_sync_run_id"], data["params"]]
        return [None, None]

    def get_latest_e2e_sync_run_id_for_db_id(self, activity, databook_id):
        data = (
            self.client_aware()
            .filter(task=activity)
            .filter(params__databook_ids__contains=databook_id)
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id", "sync_status", "sync_start_time", "sync_end_time"
            )
            .first()
        )
        if data:
            return data
        return None

    def get_etl_sync_status_record_by_id(self, e2e_sync_run_id):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id)
            .values("task", "sync_start_time", "sync_end_time", "sync_status")
            .first()
        )

    def get_latest_sync_info_for_datasheet(
        self, datasheet_id, databook_id, e2e_sync_run_id=None
    ):
        return (
            self.client_aware()
            .filter(
                Q(params__databook_ids__contains=str(databook_id))
                | Q(params__datasheet_id=str(datasheet_id))
                | Q(e2e_sync_run_id=e2e_sync_run_id)
            )
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id", "sync_status", "sync_start_time", "sync_end_time"
            )
            .first()
        )

    def get_etl_record_by_id(self, e2e_sync_run_id):
        return self.client_aware().filter(e2e_sync_run_id=e2e_sync_run_id).first()

    def get_latest_sync_info_by_ds_id(self, activity, datasheet_id):
        """
        Gets latest entry in the table for a sync of type activity. If sync is already running,
        it returns entry with completion_time as null
        """
        data = (
            self.client_aware()
            .filter(task=activity, params__datasheet_id=datasheet_id)
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id", "sync_status", "sync_start_time", "sync_end_time"
            )
            .first()
        )
        if data:
            return data
        return None

    def get_latest_sync_info_by_db_id(self, activity, databook_id):
        """
        Gets latest entry in the table for a sync of type activity. If sync is already running,
        it returns entry with completion_time as null
        """
        data = (
            self.client_aware()
            .filter(task=activity, params__databook_id=databook_id)
            .order_by("-sync_start_time")
            .values(
                "e2e_sync_run_id", "sync_status", "sync_start_time", "sync_end_time"
            )
            .first()
        )
        if data:
            return data
        return None

    def is_end_to_end_sync_running(self):
        """
        Checks if any end to end sync is running for the client.
        """
        return (
            self.client_aware()
            .filter(
                task=ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value, sync_end_time__isnull=True
            )
            .exists()
        )

    def is_end_to_end_sync_partially_failed(self, e2e_sync_run_id):
        """
        Checks if the end to end sync is partially failed for the client.
        """
        return (
            self.client_aware()
            .filter(
                e2e_sync_run_id=e2e_sync_run_id,
                sync_end_time__isnull=False,
                sync_status=ETL_STATUS.PARTIALLY_FAILED.value,
            )
            .exists()
        )

    def is_upstream_sync_running(self):
        """
        Checks if any upstream sync is running for the client.
        """
        return (
            self.client_aware()
            .filter(
                task=ETL_ACTIVITY.CONNECTOR_UPSTREAM_SYNC.value,
                sync_end_time__isnull=True,
            )
            .exists()
        )

    def is_any_sync_running(self, activity: list) -> bool:
        """
        Checks if any sync is running for the client.
        """
        return (
            self.client_aware()
            .filter(
                task__in=activity,
                sync_end_time__isnull=True,
            )
            .exists()
        )

    def get_params(self, e2e_sync_run_id: str) -> dict | None:
        """
        Returns the params for the given e2e_sync_run_id
        """
        try:
            result = (
                self.client_aware()
                .filter(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("params", flat=True)
                .get()
            )
            return result
        except ETLSyncStatus.DoesNotExist:
            logger.info(
                f"No params found for e2e_sync_run_id: {e2e_sync_run_id} and client_id: {self.client_id}"
            )
            return None

    def get_all_failed_syncs_that_havent_been_notified(self) -> list:
        """
        Query to get all failed syncs that haven't been notified yet.
        """
        failed_syncs = (
            ETLSyncStatus.objects.filter(
                Q(params__is_alert_notified__isnull=True)
                | Q(params__is_alert_notified=False),
                sync_status__in=[
                    ETL_STATUS.PARTIALLY_FAILED.value,
                    ETL_STATUS.FAILED.value,
                ],
            )
            .order_by("sync_start_time")
            .values_list(
                "e2e_sync_run_id", "client_id", "task", "sync_start_time", "audit"
            )
        )

        # Construct a list of dictionaries from the query results
        failed_syncs_list = [
            {
                "e2e_sync_run_id": e2e_sync_run_id,
                "client_id": client_id,
                "task": task,
                "sync_start_time": sync_start_time,
                "audit": audit,
            }
            for e2e_sync_run_id, client_id, task, sync_start_time, audit in failed_syncs
        ]

        return failed_syncs_list

    def bulk_update_params_for_e2e_sync_run_ids(
        self, e2e_sync_run_ids: list[str], params: dict
    ) -> None:
        """
        Bulk update the params for the given list of e2e_sync_run_ids.
        """
        # Fetch the existing records for the given e2e_sync_run_ids
        existing_records = ETLSyncStatus.objects.filter(
            e2e_sync_run_id__in=e2e_sync_run_ids
        )

        # Prepare a list to hold updated records
        updated_records = []

        # Iterate over each record and update the params
        for record in existing_records:
            # Merge existing params with new params
            updated_params = {**record.params, **params} if record.params else params
            record.params = updated_params
            updated_records.append(record)

        # Bulk update the records
        if updated_records:
            logger.info(f"Bulk updating {len(updated_records)} records")
            ETLSyncStatus.objects.bulk_update(updated_records, ["params"])
            logger.info("Bulk update completed successfully")
        else:
            logger.info("No records to update")

    def get_latest_successfull_sync_record(self, activity):
        return (
            self.client_aware()
            .filter(task=activity, sync_status=ETL_STATUS.COMPLETE.value)
            .order_by("-id")
            .first()
        )

    def get_latest_successful_sync_start_time_for_activities(
        self, e2e_sync_run_id: UUID, activities: list
    ):
        # Get the latest sync start time for the given activities
        latest_sync_start_time = (
            self.client_aware()
            .filter(task__in=activities, sync_status=ETL_STATUS.COMPLETE.value)
            .order_by("-sync_start_time")
            .values_list("sync_start_time", flat=True)
            .first()
        )

        # If no latest sync start time, consider the first recorded activities sync start time
        if not latest_sync_start_time:
            latest_sync_start_time = (
                self.client_aware()
                .filter(task__in=activities)
                .order_by("sync_start_time")
                .values_list("sync_start_time", flat=True)
                .first()
            )

        # If still no sync start time, consider the current e2e sync id start time
        if not latest_sync_start_time:
            latest_sync_start_time = (
                self.client_aware()
                .filter(e2e_sync_run_id=e2e_sync_run_id)
                .values_list("sync_start_time", flat=True)
                .first()
            )

        return latest_sync_start_time

    def is_sync_triggered_from_datasheet_ui(self, e2e_sync_run_id: str) -> bool:
        """
        Check if the given e2e_sync_run_id is a sync triggered from the datasheet ui.
        """
        result = (
            self.client_aware()
            .filter(
                e2e_sync_run_id=e2e_sync_run_id,
                task__in=[
                    ETL_ACTIVITY.REFRESH_DATABOOK.value,
                    ETL_ACTIVITY.GENERATE_DATASHEET.value,
                ],
                additional_info__is_triggered_from_datasheet_ui=True,
            )
            .exists()
        )
        return result

    def is_sync_lock_active(self, e2e_sync_run_id: str):
        """
        Checks if the given sync lock is active for the client.

        If the sync_end_time is null, then the sync lock is active.
        """
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=e2e_sync_run_id, sync_end_time__isnull=True)
            .exists()
        )


class ForecastETLStatusAccessor(CommissionETLStatusAccessorTemplate):
    def client_aware(self):
        return ForecastETLStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        period_start_date = pydash.get(params, "period_start_date")
        period_end_date = pydash.get(params, "period_end_date")
        payee_email_id = pydash.get(params, "payee_email_id")
        plan_id = pydash.get(params, "plan_id")
        criteria_id = pydash.get(params, "criteria_id")
        primary_kd = pydash.get(params, "primary_kd")
        secondary_kd = pydash.get(params, "secondary_kd")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_completion_time = pydash.get(params, "sync_completion_time")
        sync_status = pydash.get(params, "sync_status")
        audit = pydash.get(params, "audit")
        try:
            sync_obj = ForecastETLStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_status=sync_status,
                sync_completion_time=sync_completion_time,
                audit=audit,
                task=task,
                period_start_date=period_start_date,
                period_end_date=period_end_date,
                payee_email_id=payee_email_id,
                primary_kd=primary_kd,
                secondary_kd=secondary_kd,
                plan_id=plan_id,
                criteria_id=criteria_id,
            )
            return sync_obj
        except Exception as e:
            raise e

    def insert_bulk_objs(self, objs):
        try:
            ForecastETLStatus.objects.bulk_create(objs, batch_size=1000)
        except Exception as e:
            raise e


class ForecastETLStatusReaderAccessor(CommissionETLStatusReaderAccessorTemplate):
    def client_aware(self):
        return ForecastETLStatus.objects.filter(client_id=self.client_id)


class PlanModificationSyncStatusAccessor:
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id

    def client_aware(self):
        return PlanModificationSyncStatus.objects.filter(client_id=self.client_id)

    def insert_sync_status(self, params):
        task = pydash.get(params, "task")
        sync_start_time = pydash.get(params, "sync_start_time")
        sync_end_time = pydash.get(params, "sync_end_time")
        sync_status = pydash.get(params, "sync_status")
        try:
            sync_obj = PlanModificationSyncStatus.objects.create(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                sync_start_time=sync_start_time,
                sync_completion_time=sync_end_time,
                sync_status=sync_status,
                task=task,
            )
            return sync_obj
        except Exception as e:
            raise e

    def get_object(self):
        obj = PlanModificationSyncStatus.objects.filter(
            client_id=self.client_id
        ).filter(e2e_sync_run_id=self.e2e_sync_run_id)
        return obj

    def update_object(self, params):
        try:
            self.get_object().update(**params)
            return True
        except Exception as e:
            raise e

    def change_status_to_failed(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def change_status_to_lock_failed(self, should_update_completion_time=False):
        qs = self.get_object()

        if qs.exists():
            if should_update_completion_time:
                qs.update(
                    sync_status=ETL_STATUS.LOCK_FAILED.value,
                    sync_end_time=make_aware(datetime.datetime.now()),
                )
            else:
                qs.update(sync_status=ETL_STATUS.LOCK_FAILED.value)

    def change_status_to_complete(
        self,
        timestamp: datetime.datetime,
        sync_run_log: Optional[SyncRunLogObject] = None,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.COMPLETE.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def get_latest_sucessful_sync(self):
        qs = self.client_aware().exclude(e2e_sync_run_id=self.e2e_sync_run_id)

        return (
            qs.filter(
                sync_status=ETL_STATUS.COMPLETE.value,
                sync_completion_time__isnull=False,
            )
            .order_by("-sync_start_time")
            .values_list("sync_start_time", flat=True)
            .first()
        )

    def change_status_to_lock_acquired(self):
        return self.update_object({"sync_status": ETL_STATUS.LOCKED.value})

    def change_status_failed_to_acquire_lock(
        self,
        sync_run_log: SyncRunLogObject,
        timestamp: datetime.datetime,
    ):
        return self.update_object(
            {
                "sync_status": ETL_STATUS.LOCK_FAILED.value,
                "sync_completion_time": timestamp,
                "sync_run_log": sync_run_log,
            }
        )

    def get_total_record_count(self):
        return self.client_aware().filter(e2e_sync_run_id=self.e2e_sync_run_id).count()

    def get_records_by_e2e_sync_run_id_and_task(self, task_list):
        return (
            self.client_aware()
            .filter(e2e_sync_run_id=self.e2e_sync_run_id)
            .filter(task__in=task_list)
            .values_list("task", "sync_status")
        )
