from abc import ABC, abstractmethod
from typing import List

from django.db.models import Max, Q, QuerySet


class CommissionSecondaryKdAccessorTemplate(ABC):
    """
    Abstract base class for SecondaryKd Accessors.
    Implements the template method pattern and defines a common interface for subclasses.
    SecondaryKd objects should not be used directly in this template.
    """

    @abstractmethod
    def client_aware(self) -> QuerySet:
        pass

    @abstractmethod
    def update_sec_kd_for_period_payees(
        self, period_start_date, period_end_date, payee_email_id, sec_kd
    ):
        pass

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def __init__(self, client_id):
        self.client_id = client_id

    def get_sec_kd_for_period_payees(self, period_end_date, payee_email_ids=None):
        qs = self.client_kd_aware().filter(period_end_date=period_end_date)
        if payee_email_ids:
            qs = qs.filter(payee_email_id__in=payee_email_ids)

        return list(qs.values("payee_email_id", "sec_kd"))

    def get_sec_kd_for_periods_payees(self, period_end_dates, payee_email_ids=None):
        qs = self.client_kd_aware().filter(period_end_date__in=period_end_dates)
        if payee_email_ids:
            qs = qs.filter(payee_email_id__in=payee_email_ids)

        return list(qs.values("period_end_date", "payee_email_id", "sec_kd"))

    def get_record(self, period_end_date, payee_email_id):
        return (
            self.client_kd_aware()
            .filter(
                payee_email_id=payee_email_id,
                period_end_date=period_end_date,
            )
            .first()
        )

    def get_records_for_payees(self, period_end_date, payee_email_ids):
        return self.client_kd_aware().filter(
            payee_email_id__in=payee_email_ids,
            period_end_date=period_end_date,
        )

    def invalidate_record(self, period_end_date, payee_email_id, curr_time):
        self.client_kd_aware().filter(
            payee_email_id=payee_email_id,
            period_end_date=period_end_date,
        ).update(knowledge_end_date=curr_time)

    def get_payee_sec_kd_payee_in_peds(self, ped_payee_list_map: dict):
        """
        ped_payee_list_map = { period_end_date: [payee_ids] }
        e.g.
        ped_payee_list_map = {
            datetime(2023, 4, 30, 23, 59, 59, 999999):
            ["<EMAIL>", "<EMAIL>"],
            datetime(2023, 5, 30, 23, 59, 59, 999999):
            ["<EMAIL>", "<EMAIL>"]
        }

        For each ped and payees of the ped, we add a filter clause in the query
        """
        if not ped_payee_list_map:
            return []
        query_set = self.client_kd_aware()
        filter_clauses = Q()
        for ped, payees in ped_payee_list_map.items():
            filter_clauses = filter_clauses | (
                Q(period_end_date=ped) & Q(payee_email_id__in=payees)
            )
        if filter_clauses:
            query_set = query_set.filter(filter_clauses)
        else:
            return []
        return list(query_set.values("period_end_date", "payee_email_id", "sec_kd"))

    def get_sec_kd_payee_periods(self, payee_period: list[dict]):
        """ "
        The payee_period is a list of dictionaries with the following keys:
        - payee_email_id
        - period_start_date
        - period_end_date
        """
        query = self.client_kd_aware()
        filters = Q()
        for period in payee_period:
            filters |= Q(
                payee_email_id=period["payee_email_id"],
                period_start_date=period["period_start_date"],
                period_end_date=period["period_end_date"],
            )
        return list(
            query.filter(filters).values(
                "payee_email_id", "period_start_date", "period_end_date"
            )
        )

    def get_max_sec_kd_for_payees_across_periods_during_lock(self, payees: List[str]):
        """
        Find the Max sec_kd for a payee
        """
        query_set = (
            self.client_kd_aware()
            .filter(
                payee_email_id__in=payees,
                knowledge_end_date__isnull=True,
                is_deleted=False,
            )
            .values("payee_email_id")
            .annotate(max_sec_kd=Max("sec_kd"))
            .values("payee_email_id", "max_sec_kd")
        )
        return list(query_set)
