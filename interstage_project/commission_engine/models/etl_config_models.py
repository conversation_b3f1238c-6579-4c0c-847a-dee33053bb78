import uuid

from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.db.models import Index
from django.utils import timezone

from interstage_project.db.models import (
    EsAutoField,
    EsBooleanField,
    EsCharField,
    EsDateTimeField,
    EsIntegerField,
    EsJSONField,
    EsTextField,
    EsUUIDField,
)


class ApiAccessConfig(models.Model):
    client_id = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    source_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    request_url = EsCharField(max_length=254, null=True, is_sensitive=False)
    request_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    request_body = EsJSONField(null=True, is_sensitive=False)
    request_header = EsJSONField(null=True, is_sensitive=False)
    access_token_config_id = EsIntegerField(
        null=True, is_sensitive=False
    )  # Connection ID
    integration = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # Service name
    response_key = EsCharField(max_length=254, null=True, is_sensitive=False)
    additional_data = EsJSONField(null=True, is_sensitive=False)
    integration_id = EsUUIDField(null=True, is_sensitive=False)
    # payload_type = EsCharField(max_length=254, null=True)  # PARAMS/BODY

    class Meta:
        db_table = "api_access_config"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "source_object_id",
                ],
                name="aac_ked_soi_idx",
            )
        ]


class AccessTokenConfig(models.Model):
    service_name = EsCharField(max_length=254, null=True, is_sensitive=False)
    client_id = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    access_token_config_id = EsAutoField(primary_key=True, is_sensitive=False)
    access_type = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # UN-PWD/PUB-PRI
    payload_type = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # PARAMS/BODY
    api_access_key = EsCharField(max_length=254, null=True)
    access_token_url = EsCharField(max_length=254, null=True)
    access_request_body = EsJSONField(null=True, is_sensitive=False)
    jwt_data = EsJSONField(null=True, is_sensitive=False)
    service_name = EsCharField(max_length=254, null=True, is_sensitive=False)
    domain = EsCharField(max_length=254, null=True, is_sensitive=False)
    connection_name = EsCharField(max_length=254, null=True, is_sensitive=False)
    connection_status = EsCharField(
        max_length=32, default="CONNECTED", is_sensitive=False
    )
    connection_type = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # hris/upstream
    created_on = EsDateTimeField(default=timezone.now, is_sensitive=False)
    additional_data = EsJSONField(null=True)

    class Meta:
        db_table = "access_token_config"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "access_token_config_id",
                ],
                name="atc_ked_soi_idx",
            )
        ]


# have priority suppose Contract data comes 4m 2 source objects. Objs with same destination Id, will be run  to priority
# grp by priority.( grp all P1 objs, P2 objs) and then use chord
class ExtractionConfig(models.Model):
    client_id = EsIntegerField(null=True, is_sensitive=False)
    priority = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    is_disabled = EsBooleanField(default=False, is_sensitive=False)
    task_group = EsCharField(max_length=254, null=True, is_sensitive=False)
    task = EsCharField(max_length=254, null=True, is_sensitive=False)
    source_object_id = EsCharField(max_length=254, null=True)
    destination_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    sync_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    destination_object_type = EsCharField(
        max_length=254, default="common", is_sensitive=False
    )
    integration_id = EsUUIDField(null=True, is_sensitive=False)
    access_token_config_id = EsIntegerField(null=False, is_sensitive=False)
    additional_data = EsJSONField(
        null=True, encoder=DjangoJSONEncoder, is_sensitive=False
    )

    class Meta:
        db_table = "extraction_config"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "task_group",
                ],
                name="ec_ked_tg_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "source_object_id",
                ],
                name="ec_ked_soi_idx",
            ),
        ]


class TransformationConfig(models.Model):
    client_id = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    source_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    destination_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    source_field = EsCharField(max_length=254, null=True, is_sensitive=False)
    destination_field = EsCharField(max_length=254, null=True, is_sensitive=False)
    field_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    transformation_logic_id = EsIntegerField(null=True, is_sensitive=False)
    integration_id = EsUUIDField(null=True, is_sensitive=False)
    additional_config = EsJSONField(null=True, encoder=DjangoJSONEncoder)

    class Meta:
        db_table = "transformation_config"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "source_object_id",
                    "destination_object_id",
                ],
                name="tc_ked_soi_doi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "integration_id",
                ],
                name="tc_ked_ini_idx",
            ),
        ]


class TransformationLogic(models.Model):
    client_id = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    source_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    logic = EsTextField(null=True)

    class Meta:
        db_table = "transformation_logic"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                ],
                name="tl_ked_idx",
            )
        ]


class EnrichmentConfig(models.Model):
    client_id = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    source_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    ref_object_key = EsCharField(max_length=254, null=True, is_sensitive=False)
    enrichment_resp_key = EsCharField(max_length=254, null=True, is_sensitive=False)
    enrichment_id_key = EsCharField(max_length=254, null=True, is_sensitive=False)
    source_new_key = EsCharField(max_length=254, null=True, is_sensitive=False)
    enrichment_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    ref_api_config_obj = EsCharField(max_length=254, null=True, is_sensitive=False)
    integration_id = EsUUIDField(null=True, is_sensitive=False)
    additional_data = EsJSONField(
        null=True, encoder=DjangoJSONEncoder, is_sensitive=False
    )

    class Meta:
        db_table = "enrichment_config"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "enrichment_type",
                    "source_object_id",
                ],
                name="ec_ked_et_soi_idx",
            )
        ]


class Integration(models.Model):
    client_id = EsIntegerField(null=True, is_sensitive=False)
    knowledge_begin_date = EsDateTimeField(null=True, is_sensitive=False)
    knowledge_end_date = EsDateTimeField(null=True, is_sensitive=False)
    integration_id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    name = EsCharField(max_length=254, null=True, is_sensitive=False)
    service_name = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # s3, freshworks
    desc = EsCharField(max_length=254, null=True, is_sensitive=False)
    logo_url = EsCharField(max_length=254, null=True, is_sensitive=False)
    source_object_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    properties = EsJSONField(null=True, is_sensitive=False)
    additional_data = EsJSONField(null=True, is_sensitive=False)
    is_api = EsBooleanField(null=True, is_sensitive=False)
    destination_object_id = EsIntegerField(null=False, is_sensitive=False)
    hyperlink_info = EsJSONField(null=True)
    batch_etl_page_size = EsIntegerField(
        null=True,
        is_sensitive=False,
    )  # Validations at interstage_project/everstage_ddd/upstream/preprocessors/validations.py
    preprocessing_metadata = EsJSONField(null=True, is_sensitive=False)
    transformation_logic = EsTextField(null=True, is_sensitive=False)
    # indicates whether saleforce cdc is enabled for this integration
    sf_cdc_enabled = EsBooleanField(default=False, is_sensitive=False)

    class Meta:
        db_table = "integration"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-knowledge_end_date",
                    "integration_id",
                ],
                name="in_ked_ini_idx",
            )
        ]
