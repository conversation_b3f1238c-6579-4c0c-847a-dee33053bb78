import logging
from operator import itemgetter

from django.core.cache import cache
from requests import Response, request

from commission_engine.accessors.etl_config_accessor import AccessTokenConfigAccessor
from commission_engine.custom_types import SourceObjectFieldType, SourceObjectType
from commission_engine.utils.general_data import SalesforceGrantTypes

logger = logging.getLogger(__name__)


class SalesforceApiClient:
    grant_type = SalesforceGrantTypes.PASSWORD.value
    client_id: str
    client_secret: str
    access_request_body: dict[str, str]
    domain: str
    access_token_config_id: int
    salesforce_env: str
    __cache_key: str

    def __init__(  # noqa: PLR0913
        self,
        client_id: str,
        client_secret: str,
        access_request_body: dict[str, str],
        domain: str,
        access_token_config_id: int,
        salesforce_env: str,
    ) -> None:
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_request_body = access_request_body
        self.domain = domain
        self.access_token_config_id = access_token_config_id
        self.salesforce_env = salesforce_env
        self.__cache_key = (
            f"salesforce_{self.access_token_config_id}_{self.client_id}_access_token"
        )

    def __make_request(  # noqa: PLR0913
        self,
        method: str,
        data_endpoint: str | None = None,
        params: dict[str, str] | None = None,
        data: dict[str, str] | None = None,
        auth_endpoint: str | None = None,
    ) -> Response:
        if params is None:
            params = {}
        if data is None:
            data = {}

        connection = AccessTokenConfigAccessor(int()).get_record_by_id(
            access_token_config_id=self.access_token_config_id, client_aware=False
        )

        version = (connection.additional_data or {}).get("version", "v54.0")

        if auth_endpoint:
            url = f"{self.domain}/services/oauth2{auth_endpoint}"
        else:
            url = f"{self.domain}/services/data/{version}{data_endpoint}"
        res = request(
            method=method,
            url=url,
            params=params,
            data=data,
            headers={"Authorization": f"Bearer {cache.get(self.__cache_key)}"},
        )
        if res.status_code in {401, 403}:
            self.__refresh_access_token()

            return self.__make_request(
                method=method,
                data_endpoint=data_endpoint,
                params=params,
                data=data,
                auth_endpoint=auth_endpoint,
            )

        return res

    def get_access_token(self) -> str:
        if not cache.get(self.__cache_key):
            self.__refresh_access_token()
        return cache.get(self.__cache_key)

    def __refresh_access_token(self) -> None:
        res = request(
            method="post",
            url=f"https://{'test' if self.salesforce_env == 'sandbox' else 'login'}.salesforce.com/services/oauth2/token",
            data=self.access_request_body,
        )

        if res.status_code != 200:
            raise Exception("Connection failed. Check your credentials and try again.")

        access_token: str = res.json()["access_token"]

        cache.set(self.__cache_key, access_token, 86400)

    def get_all_objects(self) -> list[SourceObjectType]:
        res = self.__make_request(
            method="get",
            data_endpoint="/sobjects",
        )

        objects: list[SourceObjectType] = []

        for s_object in res.json()["sobjects"]:
            label, name = itemgetter("label", "name")(s_object)

            object_record: SourceObjectType = {
                "label": label.strip(),
                "name": name.strip(),
            }

            objects.append(object_record)

        return objects

    def get_all_fields_in_object(self, object_id: str) -> list[SourceObjectFieldType]:
        object_cache_key = f"salesforce_{self.access_token_config_id}_{self.client_id}_{object_id.lower()}"

        if not cache.get(object_cache_key):
            res = self.__make_request(
                method="get",
                data_endpoint=f"/sobjects/{object_id}/describe",
            )
            cache.set(object_cache_key, res, 86400)
        else:
            res = cache.get(object_cache_key)

        fields: list[SourceObjectFieldType] = []

        for field in res.json()["fields"]:
            label, name, field_type = itemgetter("label", "name", "type")(field)

            field_record: SourceObjectFieldType = {
                "label": label.strip(),
                "name": name.strip(),
                "type": field_type,
                "is_association": False,
                "is_function": False,
            }

            fields.append(field_record)

        return fields

    def get_object_meta_info(self, object_id: str) -> SourceObjectType:
        object_cache_key = f"salesforce_{self.access_token_config_id}_{self.client_id}_{object_id.lower()}"

        if not cache.get(object_cache_key):
            res = self.__make_request(
                method="get",
                data_endpoint=f"/sobjects/{object_id}/describe",
            )
            cache.set(object_cache_key, res, 86400)
        else:
            res = cache.get(object_cache_key)

        object_mf: SourceObjectType = {"label": "", "name": ""}

        object_mf["label"] = res.json()["label"].strip()
        object_mf["name"] = res.json()["name"].strip()

        return object_mf

    def clear_cached_fields_for_object(self, object_id: str) -> None:
        object_cache_key = f"salesforce_{self.access_token_config_id}_{self.client_id}_{object_id.lower()}"
        print(f"Clearing cache: {object_cache_key}")
        cache.delete(object_cache_key)

    def is_queryall_api_supported(self, object_id: str) -> bool:
        res = self.__make_request(
            method="get",
            data_endpoint="/queryAll",
            params={"q": f"select Id from {object_id} where isDeleted = true limit 1"},
        )

        if res.status_code != 200:
            return False

        return True

    def get_user_info(self):
        res = self.__make_request(
            method="get",
            auth_endpoint="/userinfo",
        )
        return res.json()

    def is_sobjects_deleted_api_supported(self, object_id: str) -> bool:
        res = self.__make_request(
            method="get",
            data_endpoint=f"/sobjects/{object_id.lower()}/describe",
        )
        return res.json()["replicateable"]
