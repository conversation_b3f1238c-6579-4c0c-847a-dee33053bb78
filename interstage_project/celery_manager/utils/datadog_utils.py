import logging
import os
from datetime import datetime, timezone

import pydash
import requests

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class Datadog:
    """
    Datadog helper class to fetch scalar metric data via the v2 API.
    """

    def __init__(self):
        self.api_url: str = ""
        self.api_headers: dict = {}
        self._set_api_essentials()

    def _set_api_essentials(self):
        """
        Prepare the API endpoint and authentication headers.
        """
        site = os.getenv("DD_SITE", "datadoghq.com")
        self.api_url = f"https://api.{site}/api/v2/query"
        self.api_headers = {
            "Content-Type": "application/json",
            "DD-API-KEY": os.getenv("DD_API_KEY"),
            "DD-APPLICATION-KEY": os.getenv("DD_APP_KEY"),
        }

    def _build_query_body(self, container_name: str, query_metric: str) -> dict:
        """
        Construct the POST body for a scalar_request of container.memory.rss.
        query_metric is the metric to query.
        eg. container.memory.rss, container.memory.limit, etc.
        """
        # format unix timestamp in milliseconds
        now_ms = int(datetime.now(timezone.utc).timestamp() * 1000)
        five_min_ago_ms = now_ms - 5 * 60 * 1000

        return {
            "data": {
                "type": "scalar_request",
                "attributes": {
                    "from": five_min_ago_ms,
                    "to": now_ms,
                    "queries": [
                        {
                            "query": (
                                f"{query_metric}" f"{{container_name:{container_name}}}"
                            ),
                            "data_source": "metrics",
                            "name": "query1",
                            "aggregator": "max",
                        }
                    ],
                    "response_format": "scalar",
                },
            }
        }

    def query_memory_usage(
        self, container_name: str, query_metric: str
    ) -> float | None:
        """
        Queries Datadog for the most recent avg:container.memory.rss for the given container.
        Returns the memory in MB.
        Raises RuntimeError on failure.
        """
        body = self._build_query_body(container_name, query_metric)

        try:
            resp = requests.post(
                self.api_url + "/scalar",
                json=body,
                headers=self.api_headers,
                timeout=10,
            )
        except requests.RequestException as e:
            raise RuntimeError(f"Network error querying Datadog: {e}") from e

        if not resp.ok:
            raise RuntimeError(f"Datadog API returned {resp.status_code}: {resp.text}")

        data = resp.json()
        logger.debug("Datadog response JSON: %s", data)

        value = pydash.get(data, "data.attributes.columns[0].values[0]")

        if value is None:
            logger.warning(
                f"No memory usage returned for container '{container_name}' for metric {query_metric}"
            )
            return None

        # Convert from bytes to MB
        return float(value) / (1024 * 1024)
