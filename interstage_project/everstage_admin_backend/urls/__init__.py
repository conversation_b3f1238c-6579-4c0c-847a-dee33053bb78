"""
This module defines the URL patterns for the Everstage admin backend.

The `urlpatterns` variable is a list that contains the URL patterns for the customer URLs and session management URLs.
These URL patterns are combined using the `+` operator and assigned to the `urlpatterns` variable.

Example usage:
    from everstage_admin_backend.urls import urlpatterns

    urlpatterns += [
        # Add additional URL patterns here
    ]
"""

from everstage_admin_backend.qa_reports.urls import urlpatterns as qa_report_urls
from everstage_admin_backend.tsar.urls import urlpatterns as tsar_urls
from everstage_admin_backend.user_management.urls import (
    urlpatterns as user_management_urls,
)

from .customer_urls import urlpatterns as customer_urls
from .etl_config_urls import urlpatterns as etl_config_urls
from .feature_flags_urls import urlpatterns as feature_flags_urls
from .session_management_urls import urlpatterns as session_management_urls

urlpatterns = (
    customer_urls
    + session_management_urls
    + tsar_urls
    + user_management_urls
    + qa_report_urls
    + feature_flags_urls
    + etl_config_urls
)
