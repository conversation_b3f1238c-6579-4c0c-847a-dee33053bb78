from django.urls import path

from everstage_admin_backend.views import (
    add_user_to_oauth_view,
    check_multi_client_domain_view,
    client_audit_view,
    client_delete_notify_views,
    create_customer_views,
    default_dashboard_view,
    delete_mapping_views,
    email_template_views,
    get_access_token_views,
    get_oauth_user_views,
    global_sync_views,
    hard_delete_customer_views,
    invalidate_cache_views,
    salesforce_cdc_views,
    save_connection_views,
    save_integration_views,
    save_mapping_views,
    save_object_api_views,
    soft_delete_customer_views,
    test_connection_views,
    test_object_api_views,
    update_customer_views,
    update_mapping_views,
    update_sort_column_view,
    validate_expose_comm_views,
)

urlpatterns = [
    path(
        "create_customer",
        create_customer_views.CreateCustomer.as_view(),
        name="create_customer",
    ),
    path(
        "update_customer",
        update_customer_views.UpdateCustomer.as_view(),
        name="update_customer",
    ),
    path(
        "multi-clients/domain",
        check_multi_client_domain_view.CheckMultiClientDomain.as_view(),
        name="check_multi_client_domain",
    ),
    path(
        "hard_delete_customer",
        hard_delete_customer_views.HardDeleteCustomer.as_view(),
        name="hard_delete_customer",
    ),
    path(
        "soft_delete_customer",
        soft_delete_customer_views.SoftDeleteCustomer.as_view(),
        name="soft_delete_customer",
    ),
    path(
        "add_user_oauth",
        add_user_to_oauth_view.CreateUserInOauth.as_view(),
        name="create_user_oauth",
    ),
    path(
        "get_oauth_user",
        get_oauth_user_views.GetOAuthUser.as_view(),
        name="get_oauth_user",
    ),
    path(
        "invalidate_cache",
        invalidate_cache_views.InvalidateCache.as_view(),
        name="invalidate_cache",
    ),
    path(
        "test_connection",
        test_connection_views.TestConnection.as_view(),
        name="test_connection",
    ),
    path(
        "get_access_token",
        get_access_token_views.GetAccessTokenView.as_view(),
        name="get_access_token",
    ),
    path(
        "save_connection",
        save_connection_views.SaveConnection.as_view(),
        name="save_connection",
    ),
    path(
        "save_integration",
        save_integration_views.SaveIntegration.as_view(),
        name="save_integration",
    ),
    path(
        "save_mapping",
        save_mapping_views.SaveMapping.as_view(),
        name="save_mapping",
    ),
    path(
        "update_mapping",
        update_mapping_views.UpdateMapping.as_view(),
        name="update_mapping",
    ),
    path(
        "test_object_api",
        test_object_api_views.TestObjectApi.as_view(),
        name="test_object_api",
    ),
    path(
        "save_object_api",
        save_object_api_views.SaveObjectApi.as_view(),
        name="save_object_api",
    ),
    path(
        "delete_mapping",
        delete_mapping_views.DeleteMapping.as_view(),
        name="delete_mapping",
    ),
    path(
        "client_delete_notify",
        client_delete_notify_views.ClientDeleteNotify.as_view(),
        name="client_delete_notify",
    ),
    path(
        "set_global_sync",
        global_sync_views.SetGlobalSync.as_view(),
        name="set_global_sync",
    ),
    path(
        "reset_global_sync",
        global_sync_views.ResetGlobalSync.as_view(),
        name="reset_global_sync",
    ),
    path(
        "sync_enable_or_disable",
        global_sync_views.SyncEnableOrDisable.as_view(),
        name="sync_enable_or_disable",
    ),
    path(
        "validate_expose_comm_flag",
        validate_expose_comm_views.ValidateExposeCommFlag.as_view(),
        name="validate_expose_comm_flag",
    ),
    path(
        "get_all_email_templates",
        email_template_views.GetEmailTemplates.as_view(),
        name="get_all_email_templates",
    ),
    path(
        "get_invite_templates",
        email_template_views.GetInviteTemplates.as_view(),
        name="get_invite_templates",
    ),
    path(
        "add_email_template",
        email_template_views.AddEmailTemplate.as_view(),
        name="add_email_template",
    ),
    path(
        "update_email_template",
        email_template_views.UpdateEmailTemplate.as_view(),
        name="update_email_template",
    ),
    path(
        "update_sort_columns",
        update_sort_column_view.UpdateSortColumns.as_view(),
        name="update_sort_columns",
    ),
    path(
        "get_client_audit_trail",
        client_audit_view.ClientAuditTrailView.as_view(),
        name="get_client_audit_trail",
    ),
    path(
        "get_client_audit_trail_info",
        client_audit_view.ClientAuditTrailInfo.as_view(),
        name="get_client_audit_trail_info",
    ),
    path(
        "setup_default_dashboard",
        default_dashboard_view.SetupDefaultDashboard.as_view(),
        name="setup_default_dashboard",
    ),
    path(
        "get_default_dashboard_link",
        default_dashboard_view.GetDefaultDashboardLink.as_view(),
        name="get_default_dashboard_link",
    ),
    path(
        "instruction_shutdown_worker",
        salesforce_cdc_views.InstructionShutdownWorkerView.as_view(),
        name="instruction_shutdown_worker",
    ),
    path(
        "object_subscription",
        salesforce_cdc_views.ObjectSubscriptionView.as_view(),
        name="object_subscription",
    ),
]
