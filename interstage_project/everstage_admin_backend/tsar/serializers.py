# pylint: disable=abstract-method
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from django.utils import timezone
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from commission_engine.accessors.client_accessor import (
    is_tsar_enabled_for_client,
    is_tsar_webapp_custom_roles_enabled_for_client,
)
from interstage_project.auth_utils import is_staff_member
from spm.accessors.rbac_accessors import RolePermissionsAccessor

from .accessors import TsarMembershipAccessor
from .constants import (
    COMMENT_PLACEHOLDER_FOR_REVOKE_MEMBERSHIP,
    QuickFilter,
    SortByField,
    SortOrder,
)
from .models import (
    POSTGRESQL_CLIENT_SPECIFIC_ROLES,
    SNOWFLAKE_CLIENT_SPECIFIC_ROLES,
    DatabaseAccessScope,
    PostgresqlAccessScope,
    SnowflakeAccessScope,
    TsarApplication,
    TsarMembership,
)
from .utils import durations_config


def validate_everstage_domain(value: str) -> None:
    """Validates that the email domain belongs to a staff member."""
    if not is_staff_member(value, case_sensitive=False):
        raise ValidationError(
            f"Email not matching staff member domain(s). | Email: {value}"
        )


def validate_time_window_availability(
    application: TsarApplication,
    client_id: Optional[int],
    members: List[str],
    starts_at: datetime,
    ends_at: datetime,
    is_duration_flexible: bool,
    is_client_specific: bool,
    is_role_specific: bool,
    is_scheduling_enabled: bool,
    roles: List[DatabaseAccessScope | UUID],
    current_membership_id: Optional[UUID] = None,
) -> bool:
    """
    Checks if the time window is available for the user(s).
    Rules:
    1. `ends_at` must be in the future. We need not check for `starts_at` as it can be in the past (a few milliseconds for a new membership OR much more for an update of a live membership).
    2. Time window must be between 30 minutes and 30 days (or 1000 days for non-prods).
    3. The time window should not intersect with existing memberships for the user for the same client same application.

    Args:
        members: The staff members.
        application: The application.
        client_id: The client ID.
        starts_at: The start time. Format e.g.: 2024-04-15 04:10
        ends_at: The end time.
        current_membership_id: The current membership ID. This is used to exclude the current membership while checking for intersection.
    Returns:
        bool: True if the time window is available, raises ValidationError otherwise.
    """
    now = timezone.now()

    # Check if ends_at is in the future
    if ends_at <= now:
        raise ValidationError(
            f"End time must be in the future. | Ends At: {ends_at} | Now: {now}"
        )

    # Check if scheduling is enabled
    if starts_at > now and not is_scheduling_enabled:
        raise ValidationError(
            f"Scheduling is not enabled for this application. | Starts At: {starts_at} | Now: {now}"
        )

    # Check if the time window is between 30 minutes and 30 days (or 1000 days for non-prods)
    lower_limit, upper_limit, _ = durations_config(is_duration_flexible)
    if not lower_limit <= ends_at - starts_at <= upper_limit:
        raise ValidationError(
            f"Time window must be between {lower_limit} and {upper_limit}. | Starts At: {starts_at} | Ends At: {ends_at}"
        )

    exclude_memberships = [current_membership_id] if current_membership_id else None
    does_intersect = TsarMembershipAccessor(application).does_time_window_intersect(
        client_id=client_id,
        members=members,
        starts_at=starts_at,
        ends_at=ends_at,
        is_client_specific=is_client_specific,
        is_role_specific=is_role_specific,
        roles=roles,
        exclude_memberships=exclude_memberships,
    )
    if does_intersect:
        raise ValidationError(
            f"Time window not available. Intersection with existing memberships found. | Staff Members: {members} | Application: {application} | Client ID: {client_id} | Starts At: {starts_at} | Ends At: {ends_at}"
        )

    return True


def validate_updated_time_window_legitimacy(
    application: TsarApplication,
    client_id: Optional[int],
    members: List[str],
    old_starts_at: datetime,
    old_ends_at: datetime,
    new_starts_at: datetime,
    new_ends_at: datetime,
    is_duration_flexible: bool,
    is_client_specific: bool,
    is_role_specific: bool,
    is_scheduling_enabled: bool,
    roles: List[DatabaseAccessScope | UUID],
    current_membership_id: UUID,
) -> bool:
    """
    Checks if the updated time window is legitimate and is aligned with the existing staff members.
    Rules:
    1. If membership is already live (`starts_at` <= now), then `starts_at` cannot be updated.
    2. If membership is expired (`ends_at` <= now), then `ends_at` cannot be updated.
    3. `new_ends_at` must be in the future.
    4. New time window must be between 30 minutes and 30 days (or 1000 days for non-prods).
    5. The new time window should not intersect with other existing memberships (except current membership) for the users for the same client same application.

    Args:
        members: The staff members.
        application: The application.
        client_id: The client ID.
        old_starts_at: The old start time.
        old_ends_at: The old end time.
        new_starts_at: The new start time.
        new_ends_at: The new end time.
        current_membership_id: The current membership ID.

    Returns:
        bool: True if the updated time window is legitimate, raises ValidationError otherwise.
    """
    now = timezone.now()

    # Check if the membership is expired
    if old_ends_at <= now:
        raise ValidationError(
            f"Time window cannot be updated for expired membership. | Ended At: {old_ends_at} | Now: {now}"
        )

    # Check if the membership is live
    if old_starts_at != new_starts_at and old_starts_at <= now:
        raise ValidationError(
            f"Start time cannot be updated for live membership. | Started At: {old_starts_at} | Now: {now}"
        )

    # Other time window checks
    validate_time_window_availability(
        application=application,
        client_id=client_id,
        members=members,
        starts_at=new_starts_at,
        ends_at=new_ends_at,
        is_duration_flexible=is_duration_flexible,
        is_client_specific=is_client_specific,
        is_role_specific=is_role_specific,
        is_scheduling_enabled=is_scheduling_enabled,
        roles=roles,
        current_membership_id=current_membership_id,
    )

    return True


def validate_if_support_access_enabled_for_client(value: int | None) -> None:
    """
    Checks if support access is enabled for the client.

    Args:
        value: The client ID.

    Raises:
        ValidationError: If support access is not enabled for the client.
    """
    if not value:
        raise ValidationError("Client ID is required.")

    # Check if the client exists and has support access enabled
    if not is_tsar_enabled_for_client(value):
        raise ValidationError(
            f"Support access is not enabled for client. | Client ID: {value}"
        )


def get_application_specific_converted_roles(
    application: TsarApplication, roles: List[str]
) -> List[DatabaseAccessScope | str]:
    """
    Converts the roles to the appropriate types based on the application value.

    Args:
        application: The application.
        roles: The roles.

    Returns:
        List[DatabaseAccessScope | str]: The converted roles.
    """
    if not roles:
        return []

    converted_roles = []
    if application == TsarApplication.DESKTOP_WEB:
        # Convert roles to str objects; as UUID is not JSON Serializable
        for role in roles:
            try:
                converted_roles.append(str(role))
            except ValueError as exc:
                raise ValidationError(f"Invalid roles: {role}") from exc

    elif application == TsarApplication.POSTGRESQL:
        # Convert roles to PostgresqlAccessScope enum values
        for role in roles:
            try:
                converted_roles.append(PostgresqlAccessScope(role))
            except ValueError as exc:
                raise ValidationError(
                    f"Invalid role '{role}' for application '{application}'. Allowed values: {PostgresqlAccessScope.values}"
                ) from exc
    elif application == TsarApplication.SNOWFLAKE:
        # Convert roles to SnowflakeAccessScope enum values
        for role in roles:
            try:
                converted_roles.append(SnowflakeAccessScope(role))
            except ValueError as exc:
                raise ValidationError(
                    f"Invalid role '{role}' for application '{application}'. Allowed values: {SnowflakeAccessScope.values}"
                ) from exc
    else:
        raise ValidationError(f"Unsupported application: {application}")

    return converted_roles


def validate_client_id(
    application: TsarApplication,
    client_id: Optional[int],
    roles: List[DatabaseAccessScope | UUID],  # pylint: disable=unused-argument
) -> None:
    """
    Validates the client ID based on the application.

    Args:
        application: The application.
        client_id: The client ID.

    Raises:
        ValidationError: If the client ID is not provided for the application.
    """
    if client_id is not None:
        client_id = int(client_id)
        if client_id < 1:
            raise ValidationError(f"Invalid client ID: {client_id}")

    if application == TsarApplication.DESKTOP_WEB and client_id is None:
        raise ValidationError(
            f"Client ID is required for application '{application}'. | Client ID: {client_id}"
        )
    elif (
        application == TsarApplication.POSTGRESQL
        and POSTGRESQL_CLIENT_SPECIFIC_ROLES.intersection(roles)
        and client_id is None
    ):
        raise ValidationError(
            f"Client ID is required for application '{application}' with client specific roles. | Client ID: {client_id}"
        )
    elif (
        application == TsarApplication.SNOWFLAKE
        and SNOWFLAKE_CLIENT_SPECIFIC_ROLES.intersection(roles)
        and client_id is None
    ):
        raise ValidationError(
            f"Client ID is required for application '{application}' with client specific roles. | Client ID: {client_id}"
        )


def validate_user_roles_for_desktop_web(client_id: int, roles: List[str]) -> None:
    """
    check if the given roles belong to the client or not
    """
    if not RolePermissionsAccessor(client_id).does_all_given_role_permissions_exist(
        roles
    ):
        raise ValidationError(
            f"The selected role(s) don't exist in the client. | Client : {client_id} | Roles: {roles}"
        )

    # check the flag `enable_tsar_webapp_custom_roles`
    if not is_tsar_webapp_custom_roles_enabled_for_client(
        client_id
    ) and not RolePermissionsAccessor(client_id).is_role_power_admin(roles[0]):
        raise ValidationError(
            "`enable_tsar_webapp_custom_roles` is disabled; only Power Admin role is allowed. | Roles : {roles}"
        )


class TsarMembershipListSerializer(serializers.ListSerializer):
    """List serializer for TsarMembership."""

    # pylint: disable=abstract-method
    def create(self, validated_data):
        tsar_memberships = [TsarMembership(**item) for item in validated_data]
        return TsarMembership.objects.bulk_create(  # pylint: disable=no-member
            tsar_memberships, batch_size=1000
        )


class TsarMembershipSerializer(serializers.ModelSerializer):
    """Serializer for revoking TsarMembership."""

    class Meta:
        model = TsarMembership
        fields = "__all__"
        list_serializer_class = TsarMembershipListSerializer


#########################################
# Serializers for Membership Creation and Updation
#########################################


class TsarMembershipCreationSerializer(serializers.ModelSerializer):
    """Serializer for creating TsarMembership."""

    members = serializers.ListField(
        child=serializers.EmailField(validators=[validate_everstage_domain]),
        allow_empty=False,
        required=False,
    )

    # additional logic fields
    is_duration_flexible = serializers.BooleanField(default=False)
    honour_tsar_flag = serializers.BooleanField(default=True)
    is_client_specific = serializers.BooleanField(default=True)
    is_role_specific = serializers.BooleanField(default=False)
    is_scheduling_enabled = serializers.BooleanField(default=True)

    def to_internal_value(self, data):
        """
        Convert the roles field to the appropriate types based on the application value.
        """
        # Call the parent method to handle standard fields
        data = super().to_internal_value(data)
        data["roles"] = get_application_specific_converted_roles(
            data["application"], data["roles"]
        )
        return data

    def validate(self, attrs):
        # validate client id
        client_id: Optional[int] = (
            attrs["client"].client_id if attrs["client"] else None
        )
        validate_client_id(
            application=attrs["application"],
            client_id=client_id,
            roles=attrs["roles"],
        )

        # validate tsar flag
        if attrs["honour_tsar_flag"] is True:
            validate_if_support_access_enabled_for_client(client_id)

        # validate time window
        attrs["members"] = sorted(list(set(attrs["members"])))
        validate_time_window_availability(
            application=attrs["application"],
            client_id=client_id,
            members=attrs["members"],
            starts_at=attrs["starts_at"],
            ends_at=attrs["ends_at"],
            is_duration_flexible=attrs["is_duration_flexible"],
            is_client_specific=attrs["is_client_specific"],
            is_role_specific=attrs["is_role_specific"],
            is_scheduling_enabled=attrs["is_scheduling_enabled"],
            roles=attrs["roles"],
        )

        # validate user roles
        if attrs["application"] == TsarApplication.DESKTOP_WEB:
            validate_user_roles_for_desktop_web(
                client_id=client_id,
                roles=attrs["roles"],
            )

        attrs.pop("honour_tsar_flag")
        attrs.pop("is_duration_flexible")
        attrs.pop("is_client_specific")
        attrs.pop("is_role_specific")
        attrs.pop("is_scheduling_enabled")
        return attrs

    class Meta:
        model = TsarMembership
        fields = "__all__"
        list_serializer_class = TsarMembershipListSerializer


class TsarMembershipUpdationSerializer(serializers.ModelSerializer):
    """Serializer for updating TsarMembership."""

    members = serializers.ListField(
        child=serializers.EmailField(validators=[validate_everstage_domain]),
        allow_empty=False,
    )
    new_starts_at = serializers.DateTimeField(required=False)
    new_ends_at = serializers.DateTimeField(required=False)

    # Additional logic fields
    honour_tsar_flag = serializers.BooleanField(default=True)
    is_duration_flexible = serializers.BooleanField(default=False)
    is_client_specific = serializers.BooleanField(default=True)
    is_role_specific = serializers.BooleanField(default=False)
    is_scheduling_enabled = serializers.BooleanField(default=True)

    def validate(self, attrs):
        client_id: Optional[int] = (
            attrs["client"].client_id if attrs["client"] else None
        )
        validate_client_id(
            application=attrs["application"],
            client_id=client_id,
            roles=attrs["roles"],
        )

        if attrs["honour_tsar_flag"] is True:
            validate_if_support_access_enabled_for_client(client_id)

        attrs["members"] = sorted(list(set(attrs["members"])))
        validate_updated_time_window_legitimacy(
            application=attrs["application"],
            client_id=client_id,
            members=attrs["members"],
            old_starts_at=attrs["starts_at"],
            old_ends_at=attrs["ends_at"],
            new_starts_at=attrs["new_starts_at"],
            new_ends_at=attrs["new_ends_at"],
            is_duration_flexible=attrs["is_duration_flexible"],
            is_client_specific=attrs["is_client_specific"],
            is_role_specific=attrs["is_role_specific"],
            is_scheduling_enabled=attrs["is_scheduling_enabled"],
            roles=attrs["roles"],
            current_membership_id=attrs["membership_id"],
        )
        attrs["starts_at"] = attrs["new_starts_at"]
        attrs["ends_at"] = attrs["new_ends_at"]

        # Remove unnecessary fields from data
        attrs.pop("new_starts_at")
        attrs.pop("new_ends_at")
        attrs.pop("honour_tsar_flag")
        attrs.pop("is_duration_flexible")
        attrs.pop("is_client_specific")
        attrs.pop("is_role_specific")
        attrs.pop("is_scheduling_enabled")
        return attrs

    class Meta:
        model = TsarMembership
        fields = "__all__"


#########################################
# Serializers for Membership Time Window Availability
#########################################


class MembershipNewTimeWindowAvailabilitySerializer(serializers.Serializer):
    """Serializer for checking new time window availability."""

    application = serializers.ChoiceField(
        choices=TsarApplication.choices,
        default=TsarApplication.DESKTOP_WEB,
    )
    client_id = serializers.IntegerField(required=False, default=None, allow_null=True)
    # TODO: Make 'roles' as required and non-empty for postgresql and snowflake apps
    roles = serializers.ListField(default=[])
    members = serializers.ListField(
        child=serializers.EmailField(validators=[validate_everstage_domain]),
        allow_empty=False,
    )
    starts_at = serializers.DateTimeField()
    ends_at = serializers.DateTimeField()

    # Additional logic fields
    honour_tsar_flag = serializers.BooleanField(default=True)
    is_duration_flexible = serializers.BooleanField(default=False)
    is_client_specific = serializers.BooleanField(default=True)
    is_role_specific = serializers.BooleanField(default=False)
    is_scheduling_enabled = serializers.BooleanField(default=True)

    def to_internal_value(self, data):
        """
        Convert the roles field to the appropriate types based on the application value.
        """
        # Call the parent method to handle standard fields
        data = super().to_internal_value(data)
        data["roles"] = get_application_specific_converted_roles(
            data["application"], data["roles"]
        )
        return data

    def validate(self, attrs):
        client_id: Optional[int] = attrs["client_id"]
        validate_client_id(
            application=attrs["application"],
            client_id=client_id,
            roles=attrs["roles"],
        )
        attrs["members"] = list(set(attrs["members"]))
        if attrs["honour_tsar_flag"] is True:
            validate_if_support_access_enabled_for_client(client_id)
        validate_time_window_availability(
            application=attrs["application"],
            client_id=client_id,
            members=attrs["members"],
            starts_at=attrs["starts_at"],
            ends_at=attrs["ends_at"],
            is_duration_flexible=attrs["is_duration_flexible"],
            is_client_specific=attrs["is_client_specific"],
            is_role_specific=attrs["is_role_specific"],
            is_scheduling_enabled=attrs["is_scheduling_enabled"],
            roles=attrs["roles"],
        )
        return attrs


class MembershipUpdatedTimeWindowAvailabilitySerializer(serializers.Serializer):
    """Serializer for checking updated time window availability."""

    membership_id = serializers.UUIDField()
    members = serializers.ListField(
        child=serializers.EmailField(validators=[validate_everstage_domain]),
        allow_empty=False,
    )
    new_starts_at = serializers.DateTimeField()
    new_ends_at = serializers.DateTimeField()
    honor_tsar_flag = serializers.BooleanField(default=True)
    is_duration_flexible = serializers.BooleanField(default=False)
    is_client_specific = serializers.BooleanField(default=True)
    is_role_specific = serializers.BooleanField(default=False)
    is_scheduling_enabled = serializers.BooleanField(default=True)

    def validate(self, attrs):
        membership_id = attrs["membership_id"]
        new_members = attrs["members"]
        new_starts_at = attrs["new_starts_at"]
        new_ends_at = attrs["new_ends_at"]

        # fetch membership details
        membership = TsarMembershipAccessor().get_active_membership_by_id(membership_id)

        if not membership:
            raise ValidationError(
                f"Membership either doesn't exist or is obsolete now. | Membership ID: {membership_id}"
            )

        client_id: Optional[int] = (
            membership.client.client_id if membership.client else None
        )
        if attrs["honor_tsar_flag"] is True:
            validate_if_support_access_enabled_for_client(client_id)

        validate_updated_time_window_legitimacy(
            application=TsarApplication(membership.application),
            client_id=client_id,
            members=new_members,
            old_starts_at=membership.starts_at,
            old_ends_at=membership.ends_at,
            new_starts_at=new_starts_at,
            new_ends_at=new_ends_at,
            is_duration_flexible=attrs["is_duration_flexible"],
            is_client_specific=attrs["is_client_specific"],
            is_role_specific=attrs["is_role_specific"],
            is_scheduling_enabled=attrs["is_scheduling_enabled"],
            roles=[],  # roles are not required for this serializer
            current_membership_id=membership_id,
        )
        return attrs


class StaffMembersInputSerializer(serializers.Serializer):
    """Serializer for staff members input."""

    emails = serializers.ListField(
        child=serializers.EmailField(validators=[validate_everstage_domain]),
        required=False,
    )
    search_text = serializers.CharField(required=False)


class RevokeTsarMembershipsInputSerializer(serializers.Serializer):
    """Serializer for revoking TsarMemberships."""

    memberships = serializers.ListField(
        child=serializers.UUIDField(), required=False, default=[]
    )
    revoke_all = serializers.BooleanField(required=False, default=False)  # type: ignore
    revoke_for_clients = serializers.ListField(
        child=serializers.IntegerField(min_value=1), required=False, default=[]
    )
    revoke_the_expired = serializers.BooleanField(required=False, default=False)  # type: ignore
    comment = serializers.CharField(default=COMMENT_PLACEHOLDER_FOR_REVOKE_MEMBERSHIP)

    def validate(self, attrs):
        memberships = attrs["memberships"]
        revoke_all = attrs["revoke_all"]
        revoke_for_clients = attrs["revoke_for_clients"]
        revoke_the_expired = attrs["revoke_the_expired"]

        if not (memberships or revoke_all or revoke_for_clients or revoke_the_expired):
            raise ValidationError(
                "Either 'memberships' or 'revoke_all' or 'revoke_for_clients' or 'revoke_the_expired' must be provided."
            )

        if memberships and revoke_all and revoke_for_clients and revoke_the_expired:
            raise ValidationError(
                "Multiple values - 'memberships', 'revoke_all' and `revoke_for_clients` cannot be provided altogether."
            )

        return attrs


class TsarMembershipsRetrievalInputSerializer(serializers.Serializer):
    """Serializer for retrieving TsarMemberships."""

    page = serializers.IntegerField(min_value=0, default=0)
    page_size = serializers.IntegerField(min_value=10, max_value=100, default=20)
    sort_by = serializers.ChoiceField(
        choices=SortByField.values,
        default=SortByField.DEFAULT.value,
    )
    sort_order = serializers.ChoiceField(
        choices=SortOrder.values, default=SortOrder.DESC.value
    )
    quick_filter = serializers.ChoiceField(
        choices=QuickFilter.values,
        default=QuickFilter.LIVE.value,
    )
    search = serializers.CharField(required=False, default="")


class DatabaseUserCreationInputSerializer(serializers.Serializer):
    users = serializers.ListField(
        child=serializers.EmailField(validators=[validate_everstage_domain]),
        allow_empty=False,
    )


class UnutilizedCode:
    """
    ##########################################################################
    The following functions are not used in the current codebase.
    But can be used in the future.
    ##########################################################################
    """

    # def validate_membership_obsolescence(membership_id: UUID) -> None:
    #     """
    #     Checks if the membership is active.

    #     Args:
    #         membership_id: The membership ID.

    #     Raises:
    #         ValidationError: If the membership is obsolete.
    #     """
    #     # Check if the membership is active if exists
    #     if TsarMembershipAccessor().is_membership_obsolete(membership_id):
    #         raise ValidationError(
    #             f"Membership is obsolete. | Membership ID: {membership_id}"
    #         )

    # def validate_if_approver_is_support_admin(approver: str) -> None:
    #     """
    #     Checks if the approver is a SupportAdmin.

    #     Args:
    #         approver: The approver email.

    #     Raises:
    #         ValidationError: If the approver is not a SupportAdmin.
    #     """
    #     # Check if the approver is a SupportAdmin
    #     from .services import is_support_admin

    #     if not is_support_admin(approver):
    #         raise ValidationError(f"Approver must be a SupportAdmin. | Email: {approver}")
