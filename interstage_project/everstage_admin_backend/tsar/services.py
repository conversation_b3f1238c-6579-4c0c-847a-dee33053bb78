# pylint: disable=broad-except disable=broad-exception-raised
# pylint: disable=missing-function-docstring disable=missing-module-docstring disable=missing-class-docstring
import copy
import json
import logging
import os
import secrets
import string
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple, Union
from uuid import UUID, uuid4

import boto3
import snowflake.connector
from botocore.exceptions import ClientError
from django.core.cache import cache
from django.db import connections
from django.db.models import F
from django.db.models.query import QuerySet
from django.utils import timezone
from psycopg2 import sql
from pydash import camel_case
from rest_framework.exceptions import ValidationError
from sendgrid.helpers.mail import Personalization, To

from commission_engine.accessors.client_accessor import (
    get_client,
    is_tsar_webapp_custom_roles_enabled_for_client,
)
from commission_engine.models.client_models import Client
from commission_engine.utils.general_data import RbacPermissions
from everstage_admin_backend.services.create_client_service import extract_domain_name
from everstage_admin_backend.tsar.accessors import Tsar<PERSON>embershipAccessor
from everstage_admin_backend.tsar.constants import (
    CACHE_STAFF_MEMBERS_MAP_KEY,
    CACHE_TIMEOUT,
    COMMENT_PLACEHOLDER_FOR_NEW_MEMBERSHIP,
    COMMENT_PLACEHOLDER_FOR_REVOKE_MEMBERSHIP,
    DESKTOP_WEB_SPECIFIC_RBAC_PERMISSIONS_ADMINUI,
    ENVIRONMENT_REGION,
    NULL_VALUES,
    POSTGRES_SECRET_ROTATION_FREQUENCY_DAYS,
    POSTGRESQL_SPECIFIC_RBAC_PERMISSIONS_ADMINUI,
    RDS_ENGINE,
    SECRET_ENVIRONMENT_PREFIX,
    SECRET_TAG_KEYS,
    SENDGRID_TEMPLATE_ID,
    SNOWFLAKE_ROLE_ENVIRONMENT_PREFIX,
    SNOWFLAKE_SPECIFIC_RBAC_PERMISSIONS_ADMINUI,
    Application,
    Server,
)
from everstage_admin_backend.tsar.models import (
    POSTGRESQL_READER_ROLES,
    POSTGRESQL_WRITER_ROLES,
    DatabaseAccessScope,
    MembershipStatus,
    PostgresqlAccessScope,
    SnowflakeAccessScope,
    TsarApplication,
    TsarMembership,
)
from everstage_admin_backend.tsar.postgres_connection_config import (
    SERVER_CONNECTION_DETAILS,
)
from everstage_admin_backend.tsar.serializers import (
    MembershipNewTimeWindowAvailabilitySerializer,
    MembershipUpdatedTimeWindowAvailabilitySerializer,
    RevokeTsarMembershipsInputSerializer,
    TsarMembershipCreationSerializer,
    TsarMembershipSerializer,
    TsarMembershipUpdationSerializer,
)
from everstage_admin_backend.tsar.utils import durations_config, timedelta_to_readable
from interstage_project.auth_management_api import (
    create_oauth_user,
    fetch_staff_members,
)
from interstage_project.auth_utils import is_verified_staff_member
from interstage_project.celery import CommonQueues
from interstage_project.session_utils import restricted_usage___get_login_user
from interstage_project.utils import get_common_queue_name, is_local_env
from spm.accessors.employee_accessor_v2 import (
    EmployeeReadAccessor,
    EmployeeWriteAccessor,
)
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.models.config_models.employee_models import Employee
from spm.models.rbac_models import RolePermissions
from spm.services.email_services.email_services import bulk_send_email

logger = logging.getLogger(__name__)
queue_name = get_common_queue_name(CommonQueues.OTHERS.value)


def is_tsar_accessible_at_frontend(auth0_permissions: List) -> bool:
    """
    Returns: True if the user has access to the TSAR at the frontend, False otherwise.
    """
    if is_local_env():
        return True

    return bool(
        set(auth0_permissions).intersection(
            DESKTOP_WEB_SPECIFIC_RBAC_PERMISSIONS_ADMINUI
            | POSTGRESQL_SPECIFIC_RBAC_PERMISSIONS_ADMINUI
            | SNOWFLAKE_SPECIFIC_RBAC_PERMISSIONS_ADMINUI
        )
    )


def get_duration_config(is_duration_flexible: bool) -> Dict:
    """
    Returns: Dict containing the following keys:
        - relative_durations: List of relative durations for time windows.
        - duration_limits: Dict containing the following keys:
            - lower_limit_in_seconds: Lower limit in seconds.
            - upper_limit_in_seconds: Upper limit in seconds.
            - lower_limit_readable_text: Lower limit in human-readable format.
            - upper_limit_readable_text: Upper limit in human-readable format.
    """
    lower_limit, upper_limit, relative_durations = durations_config(
        is_duration_flexible
    )
    return {
        "relative_durations": list(relative_durations.keys()) + ["Custom"],
        "duration_limits": {
            "lower_limit_in_seconds": int(lower_limit.total_seconds()),
            "upper_limit_in_seconds": int(upper_limit.total_seconds()),
            "lower_limit_readable_text": timedelta_to_readable(lower_limit),
            "upper_limit_readable_text": timedelta_to_readable(upper_limit),
        },
    }


def get_client_roles(client_id: int) -> List:
    """
    If `enable_tsar_webapp_custom_roles` is True, return all roles for the client, else just return the Power Admin role.
    """
    qs: QuerySet = (
        RolePermissionsAccessor(client_id).client_kd_aware()
        if is_tsar_webapp_custom_roles_enabled_for_client(client_id)
        else RolePermissionsAccessor(client_id).get_power_admins_roles()
    )
    return list(
        qs.order_by("show_to_user").values(
            "role_permission_id", "display_name", "show_to_user"
        )
    )


def get_role_by_role_permission_ids_client_agnostic(
    role_permission_ids: List,
) -> List[Dict]:
    """
    !!!!!!!!! NOTE !!!!!!!!
    This method is client agnostic and that's why we haven't put this in regular RolePermissionsAccessor.
    USE THIS CAREFULLY!
    """
    return list(
        RolePermissions.objects.filter(
            is_deleted=False,
            knowledge_end_date__isnull=True,
            role_permission_id__in=role_permission_ids,
        ).values("role_permission_id", "display_name")
    )


def revoke_memberships_for_client(client: int) -> List:
    """
    Revoke memberships by invalidating active memberships for the given clients.

    Args:
        client: The client ID.

    Returns:
        List: The list of revoked membership IDs.
    """
    now = timezone.now()
    login_user = restricted_usage___get_login_user().username  # type: ignore
    data = {
        "revoke_for_clients": [client],
        "comment": "Support Memberships disabled for this client.",
    }
    audit = {
        "server": "everstage_admin_backend",
        "updated_by": login_user,
        "comment": "Triggered by system internally; as Support Memberships were disabled for this client.",
    }

    # serialize data
    ser = RevokeTsarMembershipsInputSerializer(data=data)
    ser.is_valid(raise_exception=True)
    data: Dict = ser.data  # type: ignore

    # Revoke memberships
    revoked_memberships: List[Dict] = TsarDesktopWebService(
        now=now,
        login_user=login_user,
        permissions=[],
        audit=audit,
    ).revoke_memberships(data)

    revoke_memberships_ids = [
        str(membership["membership_id"]) for membership in revoked_memberships
    ]

    # Trigger email notifications
    TsarNotificationService(
        application=TsarApplication.DESKTOP_WEB
    ).notify_membership_revocation(login_user, revoked_memberships)

    return revoke_memberships_ids


def get_live_memberships_for_member(email: str, con_provider: str) -> List[Dict]:
    """
    Get live memberships for the member email.

    Args:
        email: The member email.
        con_provider: The connection provider.

    Returns:
        List: The list of live memberships.

    """
    is_staff_member: bool = is_verified_staff_member(email, con_provider)
    if not is_staff_member:
        return []

    memberships = list(
        TsarMembershipAccessor(TsarApplication.DESKTOP_WEB)
        .get_live_memberships_for_member(email)
        .annotate(client_name=F("client__name"), src=F("client__logo_url"))
        .order_by("ends_at")
        .values(
            "client_id",
            "client_name",
            "src",
            "membership_id",
            "starts_at",
            "ends_at",
            "roles",
        )
    )

    # fetch roles present in the memberships
    role_ids: Set = set()
    for membership in memberships:
        role_ids.update(membership["roles"])

    # get roles information
    roles: List[Dict] = get_role_by_role_permission_ids_client_agnostic(role_ids)

    roles_map: Dict = {
        str(role["role_permission_id"]): role["display_name"] for role in roles
    }

    # replace role_id with role display_name in memberships
    for membership in memberships:
        membership["role"] = (
            roles_map.get(str(membership["roles"][0]))
            if len(membership["roles"]) > 0
            else None
        ) or "Power Admin"

    return memberships


def get_requested_live_membership_for_member(
    membership_id: UUID, email: str
) -> Optional[TsarMembership]:
    """
    Get the live support membership for the given membership_id and user email if it exists.

    Args:
        membership_id: The membership ID.
        email: The member.

    Returns:
        Optional[TsarMembership]: The live support membership if it exists, None otherwise.

    """
    return TsarMembershipAccessor(
        TsarApplication.DESKTOP_WEB
    ).get_requested_live_membership_for_member(membership_id, email)


# <<<<< NOTE: TO BE IMPLEMENTED >>>>>
# class TaskManager:
#     """
#     Task Manager customized for TSAR.
#     """

#     def schedule_task(self, *args, **kwargs):
#         """
#         Schedule a task.
#         """

#     def cancel_task(self, *args, **kwargs):
#         """
#         Cancel a task.
#         """

#     def cancel_all_tasks_for_membership(self, membership_id: str | UUID):
#         """
#         Cancel all the tasks for the membership.
#         """
#         self.cancel_task(membership_id=membership_id)

#     def reschedule(self, *args, **kwargs):
#         """
#         Reschedule a task.
#         """
#         self.cancel_task(*args, **kwargs)
#         self.schedule_task(*args, **kwargs)

#     def list_scheduled_tasks(self, *args, **kwargs):
#         """
#         List the scheduled tasks.
#         """
#         return []

#     def list_scheduled_tasks_for_membership(self, membership_id: str):
#         """
#         List the scheduled tasks for the membership.
#         """
#         return self.list_scheduled_tasks(membership_id=membership_id)


class TsarNotificationService:
    def __init__(
        self,
        application: TsarApplication = TsarApplication.DESKTOP_WEB,
    ):
        self.application = application

    def send_notifications(
        self, personalizations: List[Personalization], message_type: str
    ) -> None:
        bulk_send_email.si(
            personolizations=personalizations,
            template_id=SENDGRID_TEMPLATE_ID,
            message_type=message_type,
        ).set(queue=queue_name).apply_async()

    def notify_membership_creation(self, login_user: str, membership: Dict) -> None:
        """
        Notify the users about the membership creation.

        Types of notifications:
            1. Notify the creator about the successful creation of the membership.
            2. Notify the members about the new membership assigned to them.
            If the creator is also a member, then the creator will receive only one notification.

        Args:
            login_user: The login user.
            membership: dict of TsarMembership model instance.

        """
        if not membership:
            return

        personalizations: List[Personalization] = []
        role_name = self.get_role_display_value(
            membership["roles"], membership["client"]
        )
        client_name = self.get_client_display_value(membership["client"])

        # notify creator
        personalization = Personalization()
        personalization.add_email(To(login_user))
        personalization.dynamic_template_data = {
            "subject": f"Pre-approved {self.application.label} Membership Created",
            "name": self.get_name(login_user),
            "introduction": f"You've successfully created a pre-approved {self.application.label} membership.",
            "details_header": "Details",
            "details": [
                {"label": "Membership ID", "value": str(membership["membership_id"])},
                {
                    "label": "Application",
                    "value": self.application.label,
                },
                {
                    "label": "Client",
                    "value": client_name,
                },
                {
                    "label": "Role",
                    "value": role_name,
                },
                {
                    "label": "Members",
                    "value": self.emails_list_to_names_str(membership["members"]),
                },
                {"label": "Starts At", "value": str(membership["starts_at"])},
                {"label": "Ends At", "value": str(membership["ends_at"])},
                {"label": "Description", "value": membership["description"]},
                {
                    "label": "Jira URL",
                    "value": membership["jira_url"] or "Not provided",
                },
            ],
            "conclusion": None,
        }
        personalizations.append(personalization)

        # notify members
        for member in self.filtered_elements(membership["members"], [login_user]):
            member_personalization = Personalization()
            member_personalization.add_email(To(member))
            member_personalization.dynamic_template_data = {
                "subject": f"New {self.application.label} Membership Assigned",
                "name": self.get_name(member),
                "introduction": f"You have been approved a {self.application.label} membership to access the account of below mentioned client.",
                "details_header": "Details",
                "details": [
                    {
                        "label": "Membership ID",
                        "value": str(membership["membership_id"]),
                    },
                    {
                        "label": "Application",
                        "value": self.application.label,
                    },
                    {
                        "label": "Client",
                        "value": client_name,
                    },
                    {
                        "label": "Role",
                        "value": role_name,
                    },
                    {"label": "Starts At", "value": str(membership["starts_at"])},
                    {"label": "Ends At", "value": str(membership["ends_at"])},
                    {"label": "Description", "value": membership["description"]},
                    {
                        "label": "Jira URL",
                        "value": membership["jira_url"] or "Not provided",
                    },
                    {"label": "Assigned By", "value": self.get_name(login_user)},
                ],
                "conclusion": "Please ensure that you use this access responsibly and within the specified time frame. If you have any questions, feel free to reach out.",
            }
            personalizations.append(member_personalization)

        # send emails
        self.send_notifications(personalizations, "SUPPORT_MEMBERSHIP_CREATION")

    def notify_membership_revocation(
        self, login_user: str, memberships: List[Dict]
    ) -> None:
        """
        Notify the users about the membership revocation.

        Types of notifications:
            1. Notify the revoker about the successful revocation of the memberships.
            2. Notify the members about the revoked memberships.
            3. Notify others about the revoked memberships.
            Others = (creator + approver + past udpaters)  - (revoker + members)

            If the revoker is also a member, then the revoker will receive only one notification.
        Args:
            login_user: The login user.
            memberships: The list of `TsarMembership` model instances' dict representation.

        """
        if not memberships:
            return

        personalizations: List[Personalization] = []

        # notify revoker
        personalization = Personalization()
        personalization.add_email(To(login_user))
        personalization.dynamic_template_data = {
            "subject": f"{self.application.label} Membership Revoked",
            "name": self.get_name(login_user),
            "introduction": f"You have successfully revoked the below mentioned {self.application.label} memberships.",
            "details_header": "Details",
            "details": [
                {
                    "label": "Membership IDs",
                    "value": ", ".join(str(m["membership_id"]) for m in memberships),
                },
                {
                    "label": "Comment",
                    "value": memberships[0]["comment"] or "Not provided",
                },
            ],
            "conclusion": None,
        }
        personalizations.append(personalization)

        for membership in memberships:
            # notify members
            for member in self.filtered_elements(membership["members"], [login_user]):
                member_personalization = Personalization()
                member_personalization.add_email(To(member))
                member_personalization.dynamic_template_data = {
                    "subject": f"{self.application.label} Membership Revoked",
                    "name": self.get_name(member),
                    "introduction": f"Your below mentioned {self.application.label} membership has been revoked.",
                    "details_header": "Details",
                    "details": [
                        {
                            "label": "Membership ID",
                            "value": str(membership["membership_id"]),
                        },
                        {
                            "label": "Application",
                            "value": self.application.label,
                        },
                        {
                            "label": "Client",
                            "value": self.get_client_display_value(
                                membership["client"]
                            ),
                        },
                        {"label": "Revoked By", "value": self.get_name(login_user)},
                        {
                            "label": "Revoked At",
                            "value": str(membership["knowledge_begin_date"]),
                        },
                        {
                            "label": "Comment",
                            "value": membership["comment"] or "Not provided",
                        },
                    ],
                    "conclusion": "You will no longer have access to the resources.",
                }
                personalizations.append(member_personalization)

            # notify (creator and approver) minus revoker
            # TODO: Below operation is not optimal currently. Can be optimized; we can look-up the associated people for all memberships in one go.
            associated_people: set = TsarMembershipAccessor(
                self.application
            ).get_associated_people_with_membership(membership["membership_id"])
            others = associated_people - set(membership["members"]) - {login_user}
            for other in others:
                other_personalization = Personalization()
                other_personalization.add_email(To(other))
                other_personalization.dynamic_template_data = {
                    "subject": f"{self.application.label} Membership Revoked",
                    "name": self.get_name(other),
                    "introduction": f"Please be informed that below-mentioned {self.application.label} membership has been revoked.",
                    "details_header": "Details",
                    "details": [
                        {
                            "label": "Membership ID",
                            "value": str(membership["membership_id"]),
                        },
                        {
                            "label": "Application",
                            "value": self.application.label,
                        },
                        {
                            "label": "Client",
                            "value": self.get_client_display_value(
                                membership["client"]
                            ),
                        },
                        {
                            "label": "Members",
                            "value": self.emails_list_to_names_str(
                                membership["members"]
                            ),
                        },
                        {"label": "Description", "value": membership["description"]},
                        {
                            "label": "Jira URL",
                            "value": membership["jira_url"] or "Not provided",
                        },
                        {"label": "Revoked By", "value": self.get_name(login_user)},
                        {
                            "label": "Revoked At",
                            "value": str(membership["knowledge_begin_date"]),
                        },
                        {
                            "label": "Comment",
                            "value": membership["comment"] or "Not provided",
                        },
                    ],
                }
                personalizations.append(other_personalization)

        # send emails
        self.send_notifications(personalizations, "SUPPORT_MEMBERSHIP_REVOCATION")

    def notify_membership_update(
        self, login_user, membership: Dict, old_membership: Dict
    ) -> None:
        """
        Notify the users about the membership update.

        Types of notifications:
            1. Notify the updater about the successful update of the membership.
            2. Notify the creator, approver and other '3rd persons' about the updated membership.
            3. Notify the unchanged members about the updated membership.
            4. Notify the new members about the updated membership.
            5. Notify the removed members about the updated membership.

            If the updater is also a member, then the updater will receive only one notification.

        Args:
            login_user: The login user.
            membership: The updated membership. (dict of `TsarMembership` model instance)
            old_membership: The old state of membership. (dict of `TsarMembership` model instance)

        """
        if not membership:
            return

        login_name = self.get_name(login_user)
        personalizations: List[Personalization] = []
        role_name = self.get_role_display_value(
            membership["roles"], membership["client"]
        )
        client_name = self.get_client_display_value(membership["client"])

        # notify updater
        personalization = Personalization()
        personalization.add_email(To(login_user))
        personalization.dynamic_template_data = {
            "subject": f"{self.application.label} Membership Updated",
            "name": login_name,
            "introduction": f"You have successfully updated the below mentioned {self.application.label} membership.",
            "details_header": "Updated Details",
            "details": [
                {"label": "Membership ID", "value": str(membership["membership_id"])},
                {
                    "label": "Application",
                    "value": self.application.label,
                },
                {
                    "label": "Client",
                    "value": client_name,
                },
                {
                    "label": "Role",
                    "value": role_name,
                },
                {
                    "label": "Members",
                    "value": self.emails_list_to_names_str(membership["members"]),
                },
                {"label": "Starts At", "value": str(membership["starts_at"])},
                {"label": "Ends At", "value": str(membership["ends_at"])},
                {"label": "Description", "value": membership["description"]},
                {
                    "label": "Jira URL",
                    "value": membership["jira_url"] or "Not provided",
                },
                {"label": "Comment", "value": membership["comment"] or "Not provided"},
            ],
            "conclusion": None,
        }
        personalizations.append(personalization)

        # notify creator, approver and other '3rd persons'
        associated_people: set = TsarMembershipAccessor(
            self.application
        ).get_associated_people_with_membership(membership["membership_id"])
        others = associated_people - set(membership["members"]) - {login_user}
        for other in others:
            other_personalization = Personalization()
            other_personalization.add_email(To(other))
            other_personalization.dynamic_template_data = {
                "subject": f"{self.application.label} Membership Updated",
                "name": self.get_name(other),
                "introduction": f"Please be informed that below-mentioned {self.application.label} membership has been updated.",
                "details_header": "Updated Details",
                "details": [
                    {
                        "label": "Membership ID",
                        "value": str(membership["membership_id"]),
                    },
                    {
                        "label": "Application",
                        "value": self.application.label,
                    },
                    {
                        "label": "Client",
                        "value": client_name,
                    },
                    {
                        "label": "Role",
                        "value": role_name,
                    },
                    {
                        "label": "Members",
                        "value": self.emails_list_to_names_str(membership["members"]),
                    },
                    {"label": "Starts At", "value": str(membership["starts_at"])},
                    {"label": "Ends At", "value": str(membership["ends_at"])},
                    {"label": "Description", "value": membership["description"]},
                    {
                        "label": "Jira URL",
                        "value": membership["jira_url"] or "Not provided",
                    },
                    {"label": "Updated By", "value": self.get_name(login_user)},
                    {
                        "label": "Comment",
                        "value": membership["comment"] or "Not provided",
                    },
                ],
            }
            personalizations.append(other_personalization)

        # notify unchanged members
        unchanged_members = list(
            set(old_membership["members"]) - set(membership["members"])
        )
        for member in self.filtered_elements(unchanged_members, [login_user]):
            member_personalization = Personalization()
            member_personalization.add_email(To(member))
            member_personalization.dynamic_template_data = {
                "subject": f"{self.application.label} Membership Updated",
                "name": self.get_name(member),
                "introduction": f"Please be informed that your below-mentioned {self.application.label} membership has been updated.",
                "details_header": "Updated Details",
                "details": [
                    {
                        "label": "Membership ID",
                        "value": str(membership["membership_id"]),
                    },
                    {
                        "label": "Application",
                        "value": self.application.label,
                    },
                    {
                        "label": "Client",
                        "value": client_name,
                    },
                    {
                        "label": "Role",
                        "value": role_name,
                    },
                    {
                        "label": "Members",
                        "value": self.emails_list_to_names_str(membership["members"]),
                    },
                    {"label": "Starts At", "value": str(membership["starts_at"])},
                    {"label": "Ends At", "value": str(membership["ends_at"])},
                    {"label": "Description", "value": membership["description"]},
                    {
                        "label": "Jira URL",
                        "value": membership["jira_url"] or "Not provided",
                    },
                    {"label": "Updated By", "value": self.get_name(login_user)},
                    {
                        "label": "Comment",
                        "value": membership["comment"] or "Not provided",
                    },
                ],
                "conclusion": "Please review the updated information and ensure that you use this access responsibly. If you have any questions, feel free to reach out.",
            }
            personalizations.append(member_personalization)

        # notify new members
        new_members = list(set(membership["members"]) - set(old_membership["members"]))
        for member in self.filtered_elements(new_members, [login_user]):
            member_personalization = Personalization()
            member_personalization.add_email(To(member))
            member_personalization.dynamic_template_data = {
                "subject": f"New {self.application.label} Membership Assigned",
                "name": self.get_name(member),
                "introduction": f"You have been approved a {self.application.label} membership to access the account of below mentioned client.",
                "details_header": "Details",
                "details": [
                    {
                        "label": "Membership ID",
                        "value": str(membership["membership_id"]),
                    },
                    {
                        "label": "Application",
                        "value": self.application.label,
                    },
                    {
                        "label": "Client",
                        "value": client_name,
                    },
                    {
                        "label": "Role",
                        "value": role_name,
                    },
                    {"label": "Starts At", "value": str(membership["starts_at"])},
                    {"label": "Ends At", "value": str(membership["ends_at"])},
                    {"label": "Description", "value": membership["description"]},
                    {
                        "label": "Jira URL",
                        "value": membership["jira_url"] or "Not provided",
                    },
                    {"label": "Assigned By", "value": self.get_name(login_user)},
                ],
                "conclusion": "Please ensure that you use this access responsibly and within the specified time frame. If you have any questions, feel free to reach out.",
            }
            personalizations.append(member_personalization)

        # notify removed members
        removed_members = list(
            set(old_membership["members"]) - set(membership["members"])
        )
        for member in self.filtered_elements(removed_members, [login_user]):
            member_personalization = Personalization()
            member_personalization.add_email(To(member))
            member_personalization.dynamic_template_data = {
                "subject": f"{self.application.label} Membership Revoked",
                "name": self.get_name(member),
                "introduction": f"Your below mentioned {self.application.label} membership has been revoked.",
                "details_header": "Details",
                "details": [
                    {
                        "label": "Membership ID",
                        "value": str(membership["membership_id"]),
                    },
                    {
                        "label": "Application",
                        "value": self.application.label,
                    },
                    {
                        "label": "Client",
                        "value": client_name,
                    },
                    {
                        "label": "Role",
                        "value": role_name,
                    },
                    {"label": "Revoked By", "value": self.get_name(login_user)},
                    {
                        "label": "Revoked At",
                        "value": str(membership["knowledge_begin_date"]),
                    },
                    {
                        "label": "Comment",
                        "value": membership["comment"] or "Not provided",
                    },
                ],
                "conclusion": "You will no longer have access to the client account.",
            }
            personalizations.append(member_personalization)

        # send emails
        self.send_notifications(personalizations, "SUPPORT_MEMBERSHIP_UPDATE")

    def get_role_display_value(
        self, roles: List[Union[UUID, DatabaseAccessScope]], client: Optional[Client]
    ) -> str:
        if self.application in [TsarApplication.POSTGRESQL, TsarApplication.SNOWFLAKE]:
            if not roles:
                return "Not Selected"
            return roles[0].label
        elif self.application == TsarApplication.DESKTOP_WEB:
            if not roles:
                return "Power Admin"
            return RolePermissionsAccessor(
                client.client_id
            ).get_role_by_role_permission_id(roles[0], ["display_name"])["display_name"]

    def get_client_display_value(self, client: Optional[Client]) -> str:
        if not client:
            return "Not Selected"

        return client.name

    @classmethod
    def get_staff_members_map(cls) -> Dict:
        """
        Fetches staff members from Auth0 and returns a map of email to name.

        Args:
            None

        Returns:
            Dict: The map of email to name.
                { <email>: <name>, ...}
        """
        staff_members_map = cache.get(CACHE_STAFF_MEMBERS_MAP_KEY)
        if staff_members_map:
            return staff_members_map

        staff_members = fetch_staff_members()
        staff_members_map = {staff["email"]: staff["name"] for staff in staff_members}
        cache.set(CACHE_STAFF_MEMBERS_MAP_KEY, staff_members_map, CACHE_TIMEOUT)
        return staff_members_map

    @classmethod
    def get_name(cls, email: str) -> str:
        """
        Get the name of the staff member from the email.

        Args:
            email: The email.

        Returns:
            str: The name.

        """
        staff_members_map = cls.get_staff_members_map()
        return staff_members_map.get(email, email)

    @classmethod
    def emails_list_to_names_str(cls, emails: List[str]) -> str:
        """
        Convert a list of emails to a comma separated string of names.

        Args:
            emails: The list of emails.

        Returns:
            str: The comma separated string of names. e.g. - "Ankur Gupta, John Doe"

        """
        return ", ".join([cls.get_name(email) for email in emails])

    @classmethod
    def filtered_elements(cls, data: List, to_remove: List) -> List:
        """
        Filter the elements from the data.

        Args:
            data: The data.
            to_remove: The list of elements to be removed.

        Returns:
            List: The filtered elements.

        """
        return [item for item in data if item not in to_remove]


class AwsIdentityCenterManager:
    """
    A class to manage AWS Identity Center operations including user management, application assignments,
    and permission set assignments.
    """

    def __init__(self):
        self.identity_store_id = os.getenv("IDENTITY_STORE_ID")

    def _get_temp_creds_from_assumed_role(self):
        """
        Assume an IAM role in another AWS account and return temporary credentials.

        Returns:
            Temporary credentials (access_key, secret_key, session_token).
        """

        if os.getenv("TSAR_ENV") is None:
            raise ValueError("Environment variable TSAR_ENV is not set.")
        else:
            tsar_env = os.getenv("TSAR_ENV", "LOCALDEV").lower()

        if tsar_env not in ENVIRONMENT_REGION:
            raise ValueError(f"Unsupported environment: {tsar_env}")

        sts_client = boto3.client("sts", region_name=ENVIRONMENT_REGION[tsar_env])
        role_arn = os.getenv("AWS_SSO_ACCOUNT_ROLE_ARN")

        if role_arn:
            try:
                assumed_role = sts_client.assume_role(
                    RoleArn=role_arn,
                    RoleSessionName="CrossAccountAccessSessionForAWSIdentityCenter",
                )
                credentials = assumed_role["Credentials"]
                return (
                    credentials["AccessKeyId"],
                    credentials["SecretAccessKey"],
                    credentials["SessionToken"],
                )
            except Exception as exc:
                logger.error("Failed to assume role with ARN %s: %s", role_arn, exc)
                raise RuntimeError(
                    f"Failed to assume role with ARN {role_arn}: {exc}"
                ) from exc
        else:
            logger.error("Environment variable AWS_SSO_ACCOUNT_ROLE_ARN is not set.")
            raise ValueError(
                "Environment variable AWS_SSO_ACCOUNT_ROLE_ARN is not set."
            )

    def _get_sso_admin_client(self):
        """
        Get the SSO Admin client, considering cross-account scenarios if necessary.

        Returns:
            A boto3 SSO Admin client.
        """

        identity_center_region = os.getenv("IDENTITY_CENTER_REGION", "us-west-2")

        (
            access_key,
            secret_key,
            session_token,
        ) = self._get_temp_creds_from_assumed_role()
        return boto3.client(
            "sso-admin",
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            aws_session_token=session_token,
            region_name=identity_center_region,
        )

    def _get_identitystore_client(self):
        """
        Get the Identity Store client, considering cross-account scenarios if necessary.

        Returns:
            A boto3 Identity Store client.
        """

        identity_center_region = os.getenv("IDENTITY_CENTER_REGION", "us-west-2")

        (
            access_key,
            secret_key,
            session_token,
        ) = self._get_temp_creds_from_assumed_role()
        return boto3.client(
            "identitystore",
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            aws_session_token=session_token,
            region_name=identity_center_region,
        )

    def get_user_id(self, email: str) -> Optional[str]:
        """
        Get the UserId for a given email address.

        Args:
            email: The email address of the user.

        Returns:
            Optional[str]: The UserId if found, None otherwise.
        """
        # TODO: TO REVIEW AGAIN: Check if we need to raise Exceptions
        try:
            response = self._get_identitystore_client().list_users(
                IdentityStoreId=self.identity_store_id,
                Filters=[{"AttributePath": "UserName", "AttributeValue": email}],
            )
            if response.get("Users"):
                return response["Users"][0]["UserId"]
            return None
        except Exception as exc:
            logger.error(f"Error fetching user ID for email {email}: {exc}")
            return None

    def get_unassigned_users_in_aws_sso_application(
        self, emails_to_check: List[str], sso_application: str
    ) -> List[str]:
        """
        Identify users (by email) who are not assigned to a specific application in AWS SSO.

        This function checks the provided list of email addresses against the users assigned
        to the specified application in AWS SSO and returns a list of emails that are not assigned.

        Args:
            emails_to_check: List of email addresses to check.
            sso_application: The ARN of the application to check assignments for.

        Returns:
            List of email addresses that are not assigned to the specified application in AWS SSO.
        """
        try:
            identity_store_id = os.getenv("IDENTITY_STORE_ID")

            if not identity_store_id:
                logger.error("Environment variable IDENTITY_STORE_ID is not set.")
                raise ValueError("Environment variable IDENTITY_STORE_ID is not set.")

            sso_admin_client = self._get_sso_admin_client()
            identitystore_client = self._get_identitystore_client()

            # List application assignments
            response = sso_admin_client.list_application_assignments(
                ApplicationArn=sso_application
            )
            application_assignments = response.get("ApplicationAssignments", [])
            assigned_principal_ids = {
                assignment["PrincipalId"] for assignment in application_assignments
            }

            # Map emails to PrincipalIds
            email_to_principal_id = {}
            unassigned_emails = []

            for email in emails_to_check:
                try:
                    # Search for user by email
                    users = identitystore_client.list_users(
                        IdentityStoreId=identity_store_id,
                        Filters=[
                            {"AttributePath": "UserName", "AttributeValue": email}
                        ],
                    )
                    if users["Users"]:
                        principal_id = users["Users"][0]["UserId"]
                        email_to_principal_id[email] = principal_id
                    else:
                        unassigned_emails.append(email)
                except Exception as exc:
                    logger.error(f"Error fetching PrincipalId for email {email}: {exc}")

            # Find unassigned PrincipalIds
            for email, principal_id in email_to_principal_id.items():
                if principal_id not in assigned_principal_ids:
                    unassigned_emails.append(email)

            return unassigned_emails

        except Exception as exc:
            logger.error(f"Error finding unassigned users: {exc}")
            raise

    def assign_user_to_aws_sso_application(self, email: str, application_arn: str):
        """
        Assign a user to a custom application in AWS SSO.

        Args:
            email: The email address of the user to assign.
            application_arn: The ARN of the AWS SSO application to assign the user to.

        Returns:
            None
        """
        try:
            # Retrieve identity store ID from environment variables
            identity_store_id = os.getenv("IDENTITY_STORE_ID")

            if not identity_store_id:
                logger.error("Environment variable IDENTITY_STORE_ID is not set.")
                raise ValueError("Environment variable IDENTITY_STORE_ID is not set.")

            sso_admin_client = self._get_sso_admin_client()
            identitystore_client = self._get_identitystore_client()

            # Search for user by email
            users = identitystore_client.list_users(
                IdentityStoreId=identity_store_id,
                Filters=[{"AttributePath": "UserName", "AttributeValue": email}],
            )

            if not users["Users"]:
                logger.error(f"User with email {email} not found in Identity Store.")
                raise ValueError(
                    f"User with email {email} not found in Identity Store."
                )

            principal_id = users["Users"][0]["UserId"]

            # Assign user to the application
            sso_admin_client.create_application_assignment(
                ApplicationArn=application_arn,
                PrincipalType="USER",
                PrincipalId=principal_id,
            )

            logger.info(f"Successfully assigned user {email} to the application.")

        except Exception as exc:
            logger.error("Error assigning user %s to the application: %s", email, exc)
            raise RuntimeError(
                f"Error assigning user {email} to the application: {exc}"
            ) from exc


class DatabaseUserRoleManager(ABC):
    """
    Database User Role Manager.
    """

    def _construct_db_user_name(self, email: str) -> str:
        """
        Construct the database user name out of the email.

        Args:
            email: The email.

        Returns: The database user name.
        """
        return email

    @abstractmethod
    def _get_db_user(self, email: str) -> str:
        """
        Args:
            email: Everstage staff member email.

        Responsibilities:
            1. Get the database user.
            2. If the user does not exist, create a new user.

        Returns: The database user name.
        """

    @abstractmethod
    def get_db_username_alone(self, email: str) -> str:
        """
        Args:
            email: Everstage staff member email.

        Responsibilities:
            1. Get the database user.

        Returns: The database user name.
        """

    @abstractmethod
    def get_db_role(
        self, scope: DatabaseAccessScope, client_id: Optional[int]
    ) -> Tuple[str] | str:
        """
        Args:
            scope: The scope; i.e. `PostgresqlAccessScope` or `SnowflakeAccessScope`

        Responsibilities:
            1. Construct the role name out of the client_id and scope.
            2. Check if the constructed role exists. If not, create it.

        Returns: The database constructed role name.
        """

    @abstractmethod
    def map_db_role_to_db_users(self, db_users: List[str], db_role: str) -> None:
        """
        Args:
            db_user: The database users.
            db_role: The database role.

        Responsibilities:
            1. Assign the role to the user.
        """

    @abstractmethod
    def unmap_db_role_from_db_users(self, db_users: List[str], db_role: str) -> None:
        """
        Args:
            db_users: The database users.
            db_role: The database role.

        Responsibilities:
            1. Remove the role from the user.
        """

    @abstractmethod
    def list_users_with_their_roles(self) -> List[Dict]:
        """
        List the users with their roles.

        Return format:
        [
            {
                "user": "user1",
                "roles": ["role1", "role2"]
            },
            {
                "user": "user2",
                "roles": ["role1", "role3"]
            }
        ]
        """

    @abstractmethod
    def facilitate_db_users(self, emails: List[str]) -> List[str]:
        """
        Facilitate the users.

        Args:
            emails: The emails.

        Responsibilities:
            1. Get the database users.
            2. If the user does not exist, create a new user.

        Returns: The database user names.
        """

    def execute_role_allocation_to_users(
        self, emails: List[str], scope: DatabaseAccessScope, client_id: Optional[int]
    ):
        """
        Allocate a role to the users.
        """
        logger.info(
            "Inside execute_role_allocation_to_users, emails: %s, scope: %s, client_id: %s",
            emails,
            scope,
            client_id,
        )
        db_roles: Tuple[str] | str = self.get_db_role(scope, client_id)
        db_users: List[str] = self.facilitate_db_users(emails)

        logger.info(
            "Inside execute_role_allocation_to_users, db_roles: %s, db_users: %s",
            db_roles,
            db_users,
        )
        if isinstance(db_roles, str):
            self.map_db_role_to_db_users(db_users, db_roles)
        if isinstance(db_roles, Tuple):
            for db_role in db_roles:
                self.map_db_role_to_db_users(db_users, db_role)

    def execute_role_withdrawal_from_users(
        self, users: List[str], scope: DatabaseAccessScope, client_id: Optional[int]
    ):
        """
        Withdraw a role from the users.
        """
        db_roles: Tuple[str] | str = self.get_db_role(scope, client_id)
        db_users: List[str] = [self.get_db_username_alone(email) for email in users]
        if isinstance(db_roles, str):
            self.unmap_db_role_from_db_users(db_users, db_roles)
        if isinstance(db_roles, Tuple):
            for db_role in db_roles:
                self.unmap_db_role_from_db_users(db_users, db_role)

    # <<<<< NOTE: TO BE IMPLEMENTED >>>>>
    # def schedule_role_allocation_to_users(
    #     self, emails: List[str], scope: DatabaseAccessScope, execute_at: datetime
    # ):
    #     """
    #     Schedule the role allocation to the users.
    #     """
    #     task_manager = TaskManager()
    #     for email in emails:
    #         # TODO: ROUGH IMPLEMENTATION
    #         task_manager.schedule_task(
    #             email=email,
    #             scope=scope,
    #             execute_at=execute_at,
    #             task_type="ALLOCATE_ROLE",
    #         )

    # <<<<< NOTE: TO BE IMPLEMENTED >>>>>
    # def schedule_role_withdrawal_from_users(
    #     self, emails: List[str], scope: DatabaseAccessScope, execute_at: datetime
    # ):
    #     """
    #     Schedule the role withdrawal from the users.
    #     """
    #     task_manager = TaskManager()
    #     for email in emails:
    #         task_manager.schedule_task(
    #             email=email,
    #             scope=scope,
    #             execute_at=execute_at,
    #             task_type="WITHDRAW_ROLE",
    #         )


class PostgresqlUserRoleManager(DatabaseUserRoleManager):
    """
    PostgreSQL User Role Manager.
    """

    DEFAULT_ROLES_TO_ASSIGN = ["safe_data_reader"]

    def _create_db_user(self, db_user: str, db_password: str) -> None:
        """
        Create a database user.

        Args:
            db_user: The database username.
            db_password: The database password.

        """
        if not db_user or not db_password:
            logger.error("Invalid database user or password.")
            return

        try:
            with connections["admin"].cursor() as cursor:
                logger.info(
                    "POSTGRES USER UTILISED IN CONNECTION: %s",
                    connections["admin"].settings_dict["USER"],
                )
                query = sql.SQL("CREATE USER {db_user} WITH PASSWORD %s").format(
                    db_user=sql.Identifier(db_user)
                )
                logger.info(
                    "Executing query: %s",
                    cursor.mogrify(query, ["password_hidden"]).decode("utf-8"),
                )
                cursor.execute(query, [db_password])

                for role in self.DEFAULT_ROLES_TO_ASSIGN:
                    query = sql.SQL("GRANT {role} TO {db_user}").format(
                        role=sql.Identifier(role),
                        db_user=sql.Identifier(db_user),
                    )
                    logger.info(
                        "Executing query %s - Assigning default role %s to user: %s",
                        cursor.mogrify(query).decode("utf-8"),
                        role,
                        db_user,
                    )
                    cursor.execute(query)

            logger.info("Created postgresql user: %s", db_user)
        except Exception as exc:
            logger.error("Failed to create postgresql user %s -- %s", db_user, str(exc))
            raise

    def _get_db_user(self, email: str) -> Tuple[str, str | None]:
        """
        Retrieve or create a database user.

        Args:
            email: Everstage staff member email.

        Returns:
            Tuple of database username and password. Password is None if the user already exists.
        """
        db_user = self._construct_db_user_name(email)
        db_password = None
        if not self._does_db_user_exist(db_user):
            db_password = self._generate_db_user_password()
            self._create_db_user(db_user, db_password)
        else:
            logger.info("Database user already exists: %s", db_user)
        # TODO : Remove this once we have a way to ensure default roles are assigned to already created users
        for role in self.DEFAULT_ROLES_TO_ASSIGN:
            self.map_db_role_to_db_users([db_user], role)

        return db_user, db_password

    def get_db_username_alone(self, email: str) -> str:
        return self._get_db_user(email)[0]

    def _does_db_user_exist(self, db_user: str) -> bool:
        """
        Check if the database user exists.

        Args:
            db_user: The database username to check.

        Returns:
            True if the user exists, False otherwise.
        """
        try:
            with connections["admin"].cursor() as cursor:
                logger.info(
                    "POSTGRES USER UTILISED IN CONNECTION: %s",
                    connections["admin"].settings_dict["USER"],
                )
                query = "SELECT COUNT(*) FROM pg_roles WHERE rolname = %s"
                logger.info("Executing query: %s", cursor.mogrify(query, [db_user]))
                cursor.execute(query, [db_user])
                result = cursor.fetchone()
                return result[0] > 0 if result else False
        except Exception as exc:
            logger.exception(
                "Failed to check existence of PostgreSQL user '%s'", db_user
            )
            raise exc

    def _generate_db_user_password(self, length: int = 16) -> str:
        """
        Generate a secure password with a mix of uppercase, lowercase, numbers, and special characters.

        Args:
            length (int): The length of the password. Default is 16.

        Returns:
            str: A secure password.
        """
        if length < 16:
            raise ValueError(
                "Password length must be at least 16 characters to include all character types."
            )

        # Define the character sets
        uppercase_letters = string.ascii_uppercase
        lowercase_letters = string.ascii_lowercase
        digits = string.digits
        special_characters = string.punctuation

        # Ensure the password contains at least one character from each set
        password = [
            secrets.choice(uppercase_letters),
            secrets.choice(lowercase_letters),
            secrets.choice(digits),
            secrets.choice(special_characters),
        ]

        # Fill the rest of the password length with random choices from all sets
        all_characters = (
            uppercase_letters + lowercase_letters + digits + special_characters
        )
        password += [secrets.choice(all_characters) for _ in range(length - 4)]

        # Shuffle the password list to ensure randomness
        secrets.SystemRandom().shuffle(password)

        return "".join(password)

    def _update_db_user_password(self, db_user: str) -> str:
        """
        Update the password of a database user.

        Args:
            db_user: The database username.

        Returns:
            The updated password.
        """
        new_password = self._generate_db_user_password()
        try:
            with connections["admin"].cursor() as cursor:
                logger.info(
                    "POSTGRES USER UTILISED IN CONNECTION: %s",
                    connections["admin"].settings_dict["USER"],
                )
                query = sql.SQL("ALTER USER {db_user} WITH PASSWORD %s").format(
                    db_user=sql.Identifier(db_user)
                )
                logger.info(
                    "Executing query: %s", cursor.mogrify(query, ["password_hidden"])
                )
                cursor.execute(query, [new_password])
            logger.info("Password updated for PostgreSQL user: %s", db_user)
            return new_password
        except Exception as exc:
            logger.exception("Failed to update password for user '%s'", db_user)
            raise exc

    def map_db_role_to_db_users(self, db_users: List[str], db_role: str) -> None:
        """
        Assign a PostgreSQL database role to users.

        Args:
            db_users: List of database usernames.
            db_role: The role to assign.
        """
        if not db_users:
            logger.error("No users provided for role mapping.")
            return

        # TODO: Use Enum or a constant
        if db_role == "bypass_rls":
            self.grant_bypass_rls_to_users(db_users)
            return

        try:
            with connections["admin"].cursor() as cursor:
                logger.info(
                    "POSTGRES USER UTILISED IN CONNECTION: %s",
                    connections["admin"].settings_dict["USER"],
                )
                query = sql.SQL("GRANT {db_role} TO {users_list}").format(
                    db_role=sql.Identifier(db_role),
                    users_list=sql.SQL(", ").join(
                        sql.Identifier(user) for user in db_users
                    ),
                )
                logger.info(
                    "Executing query: %s", cursor.mogrify(query).decode("utf-8")
                )
                cursor.execute(query)
        except Exception as exc:
            logger.error(
                "Failed to map role %s to users %s. Error: %s",
                db_role,
                db_users,
                str(exc),
            )
            raise

    def unmap_db_role_from_db_users(self, db_users: List[str], db_role: str) -> None:
        """
        Remove a database role from users.

        Args:
            db_users: List of database usernames.
            db_role: The role to remove.
        """
        logger.info(
            "Inside unmap_db_role_from_db_users, db_users: %s, db_role: %s",
            db_users,
            db_role,
        )

        if not db_users:
            logger.error("No users provided for role unmapping.")
            return

        # TODO: Use Enum or a constant
        if db_role == "bypass_rls":
            self.revoke_bypass_rls_from_users(db_users)
            return

        try:
            with connections["admin"].cursor() as cursor:
                logger.info(
                    "POSTGRES USER UTILISED IN CONNECTION: %s",
                    connections["admin"].settings_dict["USER"],
                )
                query = sql.SQL("REVOKE {db_role} FROM {users_list}").format(
                    db_role=sql.Identifier(db_role),
                    users_list=sql.SQL(", ").join(
                        sql.Identifier(user) for user in db_users
                    ),
                )
                logger.info(
                    "Executing query: %s", cursor.mogrify(query).decode("utf-8")
                )
                cursor.execute(query)
        except Exception as exc:
            logger.error(
                "Failed to unmap role %s from users %s. Error: %s",
                db_role,
                db_users,
                str(exc),
            )
            raise

    def get_db_role(
        self, scope: PostgresqlAccessScope, client_id: Optional[int]
    ) -> Tuple[str]:
        """
        Construct and return the database role names based on the given scope and client ID.

        Args:
            scope: The scope; i.e. `PostgresqlAccessScope`.
            client_id: The client ID, required for client-specific scopes.

        Returns:
            A tuple of constructed role names.
        """
        roles: List[str] = []

        # TODO: Consider using Enum of PostgresqlAccessScope.(item).value
        client_specific_roles = {
            # PostgresqlAccessScope.CLIENT_SPECIFIC_SAFE_READER: "safe_data_reader",
            PostgresqlAccessScope.CLIENT_SPECIFIC_SENSITIVE_READER: "sensitive_data_reader",
            PostgresqlAccessScope.CLIENT_SPECIFIC_WRITER: "writer",
        }

        global_roles = {
            # PostgresqlAccessScope.GLOBAL_SAFE_READER: "safe_data_reader",
            PostgresqlAccessScope.GLOBAL_SENSITIVE_READER: "sensitive_data_reader",
            PostgresqlAccessScope.GLOBAL_WRITER: "writer",
        }

        # Handle client-specific roles
        if scope in client_specific_roles:
            if client_id is None:
                raise ValueError("Client ID is required for client-specific scopes.")
            roles.append(f"rls_client_{client_id}")
            roles.append(client_specific_roles[scope])

        elif scope in global_roles:
            roles.append("bypass_rls")
            roles.append(global_roles[scope])

        # Check and create roles if they do not exist
        for role in roles:
            # NOTE: We'll create only client-specific roles through TSAR, the generic roles like safe_data_reader, sensitive_data_reader, writer will be created by the script and these are at a DB level.
            if role.startswith("rls_client_"):
                self._check_if_db_role_exists_and_create_if_not(role)

        return tuple(roles)

    def _check_if_db_role_exists_and_create_if_not(self, db_role: str) -> None:
        """
        Args:
            db_role: The database role to check and create.
        Responsibilities:
            1. Check if the role exists.
            2. If not, create the role.
        """
        with connections["admin"].cursor() as cursor:
            logger.info(
                "POSTGRES USER UTILISED IN CONNECTION: %s",
                connections["admin"].settings_dict["USER"],
            )
            # Check if role exists
            check_query = "SELECT 1 FROM pg_roles WHERE rolname = %s"
            cursor.execute(check_query, [db_role])
            role_exists = cursor.fetchone() is not None

            if not role_exists:
                # Create role
                create_query = sql.SQL("CREATE ROLE {db_role}").format(
                    db_role=sql.Identifier(db_role)
                )
                logger.info(
                    "Executing query: %s", cursor.mogrify(create_query).decode("utf-8")
                )
                cursor.execute(create_query)

    def grant_bypass_rls_to_users(self, db_users: List[str]):
        """
        Args:
            db_users: The database users to grant the bypass rls to.

        Responsibilities:
            1. Grant the bypass rls to the users.
        """
        with connections["admin"].cursor() as cursor:
            logger.info(
                "POSTGRES USER UTILISED IN CONNECTION: %s",
                connections["admin"].settings_dict["USER"],
            )
            for user in db_users:
                # TODO: Use parameterisation
                bypass_rls_query = f'ALTER USER "{user}" BYPASSRLS'
                logger.info("Granting bypass rls to user %s", user)
                cursor.execute(bypass_rls_query)

    def revoke_bypass_rls_from_users(self, db_users: List[str]):
        """
        Args:
            db_users: The database users to revoke the bypass rls from.

        Responsibilities:
            1. Revoke the bypass rls from the users.
        """
        with connections["admin"].cursor() as cursor:
            logger.info(
                "POSTGRES USER UTILISED IN CONNECTION: %s",
                connections["admin"].settings_dict["USER"],
            )
            for user in db_users:
                # TODO: Use parameterisation
                revoke_bypass_rls_query = f'ALTER USER "{user}" NOBYPASSRLS'
                logger.info("Revoking bypass rls from user %s", user)
                cursor.execute(revoke_bypass_rls_query)

    def _get_secrets_manager_client(self, region: str):
        """Initialize and return the boto3 client for AWS Secrets Manager."""
        return boto3.client(
            "secretsmanager",
            region_name=region,
        )

    def _get_missing_users_in_aws_secrets_manager(
        self,
        emails: List[str],
        application: Application = Application.SPM,
        server: Server = Server.SPM_RDS,
    ) -> List[str]:
        """
        Check if any of the given database users exist in AWS Secrets Manager.

        Args:
            emails: The Everstage staff member emails.
            application: The application type (default: Application.SPM).
            server: The server type (default: Server.SPM_RDS).

        Returns:
            list: A list of emails that are not present in the Secrets Manager.
        """

        if os.getenv("TSAR_ENV") is None:
            raise ValueError("Environment variable TSAR_ENV is not set.")
        else:
            tsar_env = os.getenv("TSAR_ENV", "LOCALDEV").lower()

        if (
            tsar_env not in ENVIRONMENT_REGION
            or tsar_env not in SECRET_ENVIRONMENT_PREFIX
        ):
            raise ValueError(f"Unsupported environment: {tsar_env}")

        aws_region = ENVIRONMENT_REGION[tsar_env]
        secret_env_prefix = SECRET_ENVIRONMENT_PREFIX[tsar_env]

        client = self._get_secrets_manager_client(aws_region)
        missing_emails = []

        try:
            response = client.list_secrets(
                Filters=[
                    {
                        "Key": "name",
                        "Values": [
                            f"{secret_env_prefix}/{email}/{application}/{server}"
                            for email in emails
                        ],
                    }
                ]
            )

            found_secrets = {
                secret.get("Name", "") for secret in response.get("SecretList", [])
            }

            missing_emails = [
                email
                for email in emails
                if f"{secret_env_prefix}/{email}/{application}/{server}"
                not in found_secrets
            ]

            return missing_emails
        except ClientError as exc:
            logger.error(
                "Failed to list secrets in AWS Secrets Manager. Error: %s", str(exc)
            )
            raise

    def _insert_db_user_in_aws_secrets_manager(
        self,
        email: str,
        db_user: str,
        db_password: str,
        application: Application = Application.SPM,
        server: Server = Server.SPM_RDS,
    ) -> None:
        """
        Insert the database user in AWS Secrets Manager with applicable password rotation policies.
        This secret will be of type 'Credentials for Amazon RDS database' and include auto/manual rotation.

        Args:
            email: The Everstage staff member email.
            db_user: The database username.
            db_password: The database password.
            application: The application type (default: Application.SPM).
            server: The server type (default: Server.SPM_RDS).

        Returns: None
        """

        if os.getenv("TSAR_ENV") is None:
            raise ValueError("Environment variable TSAR_ENV is not set.")
        else:
            tsar_env = os.getenv("TSAR_ENV", "LOCALDEV").lower()

        if (
            tsar_env not in ENVIRONMENT_REGION
            or tsar_env not in SECRET_ENVIRONMENT_PREFIX
        ):
            raise ValueError(f"Unsupported environment: {tsar_env}")

        aws_region = ENVIRONMENT_REGION[tsar_env]
        secret_env_prefix = SECRET_ENVIRONMENT_PREFIX[tsar_env]
        secret_name = f"{secret_env_prefix}/{email}/{application}/{server}"

        rotation_lambda_arn = os.getenv("POSTGRES_SECRET_ROTATION_LAMBDA_ARN")
        if not rotation_lambda_arn:
            raise ValueError("Rotation Lambda ARN not set in environment variables.")

        server_details = SERVER_CONNECTION_DETAILS.get(server)
        if not server_details:
            raise ValueError(f"Server configuration not found for {server}")

        user_id = AwsIdentityCenterManager().get_user_id(email)
        if not user_id:
            raise LookupError(f"User ID not found for {email}.")

        client = self._get_secrets_manager_client(aws_region)

        try:
            client.create_secret(
                Name=secret_name,
                SecretString=json.dumps(
                    {
                        "username": db_user,
                        "password": db_password,
                        "host": server_details["db_host"],
                        "read_only_host": server_details["read_only"],
                        "port": server_details["db_port"],
                        "dbname": server_details["db_name"],
                        "engine": RDS_ENGINE,
                    }
                ),
                Description=f"Database credentials for {email} to access {tsar_env.upper()} environment {application.value.upper()} application {server.value.upper()} server.",
                Tags=[
                    {"Key": SECRET_TAG_KEYS["Owner"], "Value": email},
                    {"Key": SECRET_TAG_KEYS["Environment"], "Value": tsar_env},
                    {"Key": SECRET_TAG_KEYS["Application"], "Value": application},
                    {"Key": SECRET_TAG_KEYS["UserID"], "Value": user_id},
                ],
            )

            # Enable automatic rotation
            client.rotate_secret(
                SecretId=secret_name,
                RotationLambdaARN=rotation_lambda_arn,
                RotationRules={
                    "AutomaticallyAfterDays": POSTGRES_SECRET_ROTATION_FREQUENCY_DAYS
                },
            )

        except ClientError as exc:
            if exc.response["Error"]["Code"] == "ResourceExistsException":
                logger.error(f"Secret {secret_name} already exists.")
            else:
                raise exc

    def facilitate_db_users(self, emails: List[str]) -> List[str]:
        """
        Facilitate the users.

        Args:
            emails: The emails.

        Responsibilities:
            1. Check missing users in AWS Secrets Manager.
            2. Create missing users in DB and insert in AWS Secrets Manager.

        Returns: The database user names of all users.
        """
        db_users: List[str] = []

        if is_local_env():
            missing_emails = emails
            # Create the missing users in DB
            for email in missing_emails:
                db_user, db_password = self._get_db_user(email)
                db_password = db_password or self._update_db_user_password(db_user)
                db_users.append(db_user)
        else:
            missing_emails: List[str] = self._get_missing_users_in_aws_secrets_manager(
                emails
            )
            # Create the missing users in DB and insert in AWS Secrets Manager
            for email in missing_emails:
                db_user, db_password = self._get_db_user(email)
                if not db_password:
                    db_password = self._update_db_user_password(db_user)
                self._insert_db_user_in_aws_secrets_manager(email, db_user, db_password)
                db_users.append(db_user)

        # Get the existing users
        existing_emails: Set = set(emails) - set(missing_emails)
        for email in existing_emails:
            db_users.append(self.get_db_username_alone(email))

        return db_users

    def list_users_with_their_roles(self) -> List[Dict]:
        """
        List all database users with their assigned roles.
        If the user isn't assigned any role, it still has to be returned with an empty list in roles.

        Returns:
            List of dictionaries containing user-role mappings.

        Format:
        [
            {
                "user": "user1",
                "roles": ["role1", "role2"]
            },
            {
                "user": "user2",
                "roles": []
            }
        ]
        """
        query = """
        SELECT usename AS user, COALESCE(ARRAY_AGG(rolname), ARRAY[]::text[]) AS roles,
        usebypassrls::text AS bypassrls
        FROM pg_user
        LEFT JOIN pg_auth_members ON pg_user.usesysid = pg_auth_members.member
        LEFT JOIN pg_roles ON pg_auth_members.roleid = pg_roles.oid
        GROUP BY usename, bypassrls;
        """

        try:
            with connections["admin"].cursor() as cursor:
                logger.info(
                    "POSTGRES USER UTILISED IN CONNECTION: %s",
                    connections["admin"].settings_dict["USER"],
                )
                logger.info("Executing query: %s", query)
                cursor.execute(query)
                results = cursor.fetchall()

                # If roles is None, replace it with an empty list
                results = [
                    {
                        "user": row[0],
                        "roles": (["BYPASSRLS"] if row[2] == "true" else [])
                        + (row[1] if row[1] and row[1][0] is not None else []),
                    }
                    for row in results
                ]
                logger.info("Count of postgresql users: %d", len(results))
                return results
        except Exception as exc:
            logger.error(
                "Failed to list postgresql users with roles. Query: %s. Error: %s",
                query,
                str(exc),
            )
            raise


class SnowflakeUserRoleManager(DatabaseUserRoleManager):
    """
    Snowflake User Role Manager.
    """

    def _get_db_user(self, email: str) -> str:
        """
        Args:
            email: Everstage staff member email.

        Responsibilities:
            NOTE: Go through Snowflake API documentation for more details.
            1. Get the database user.
            2. If the user does not exist, create a new user.

        Returns: The database user name.
        """

        # Query to find the username associated with the email
        with snowflake.connector.connect(
            user=os.getenv("SNOWFLAKE_USER"),
            password=os.getenv("SNOWFLAKE_PASSWORD"),
            account=os.getenv("SNOWFLAKE_ACCOUNT"),
            warehouse=os.getenv("SNOWFLAKE_WAREHOUSE"),
            role=os.getenv("SNOWFLAKE_ROLE"),
        ) as conn:
            with conn.cursor() as cursor:
                try:
                    # if not found, create a new user with email as login name
                    username = email.lower()
                    create_user_query = f"""
                    CREATE USER IF NOT EXISTS "{username}"
                    EMAIL = "{username}"
                    LOGIN_NAME = "{username}"
                    DISPLAY_NAME = "{username}"
                    """
                    cursor.execute(create_user_query)

                    return username
                except Exception as exc:
                    logger.error("Failed to get or create Snowflake user: %s", str(exc))
                    raise

    def get_db_username_alone(self, email: str) -> str:
        """
        Args:
            email: The email.

        Returns: The database username.
        """
        return self._get_db_user(email)

    def get_db_role(self, scope: SnowflakeAccessScope, client_id: Optional[int]) -> str:
        """
        Args:
            scope: The scope; i.e. `SnowflakeAccessScope`
            client_id: The client id.

        Responsibilities:
            1. Construct the role name out of the client_id and scope.
            2. Check if the constructed role exists.

        Returns: The database constructed role name.

        [TODO]
        1. Create 2 roles - "GLOBAL_READER" and "GLOBAL_WRITER" for global access in current snowflake db.
        2. Modify this service to return the relevant role name based on the scope.
        """
        role_name = ""
        if os.getenv("TSAR_ENV") is None:
            raise ValueError("Environment variable TSAR_ENV is not set.")
        else:
            tsar_env = os.getenv("TSAR_ENV", "LOCALDEV").lower()

        if tsar_env not in SNOWFLAKE_ROLE_ENVIRONMENT_PREFIX:
            raise ValueError(f"Unsupported environment: {tsar_env}")

        prefix = SNOWFLAKE_ROLE_ENVIRONMENT_PREFIX[tsar_env].upper()

        if scope == SnowflakeAccessScope.CLIENT_SPECIFIC_READER:
            role_name = f"{prefix}_CLIENT_{client_id}_READER"
        elif scope == SnowflakeAccessScope.CLIENT_SPECIFIC_WRITER:
            role_name = f"{prefix}_CLIENT_{client_id}_WRITER"
        elif scope == SnowflakeAccessScope.GLOBAL_READER:
            role_name = f"{prefix}_GLOBAL_READER"
        elif scope == SnowflakeAccessScope.GLOBAL_WRITER:
            role_name = f"{prefix}_GLOBAL_WRITER"

        with snowflake.connector.connect(
            user=os.getenv("SNOWFLAKE_USER"),
            password=os.getenv("SNOWFLAKE_PASSWORD"),
            account=os.getenv("SNOWFLAKE_ACCOUNT"),
            warehouse=os.getenv("SNOWFLAKE_WAREHOUSE"),
            role=os.getenv("SNOWFLAKE_ROLE"),
        ) as conn:
            with conn.cursor() as cursor:
                # Execute the SHOW ROLES command
                cursor.execute("SHOW ROLES")
                # Fetch all roles
                roles = cursor.fetchall()

        # Check if the role exists
        role_exists = any(role[1] == role_name for role in roles)
        if not role_exists:
            raise LookupError(f"Role {role_name} does not exist.")
        return role_name

    def map_db_role_to_db_users(self, db_users: List[str], db_role: str) -> None:
        """
        Args:
            db_users: The list of database users.
            db_role: The database role.

        Responsibilities:
            1. Assign the role to each user in the list.

        Raises:
            Exception: If role assignment fails.
        """
        with snowflake.connector.connect(
            user=os.getenv("SNOWFLAKE_USER"),
            password=os.getenv("SNOWFLAKE_PASSWORD"),
            account=os.getenv("SNOWFLAKE_ACCOUNT"),
            warehouse=os.getenv("SNOWFLAKE_WAREHOUSE"),
            role=os.getenv("SNOWFLAKE_ROLE"),
        ) as conn:
            with conn.cursor() as cursor:
                for user in db_users:
                    try:
                        query = f'GRANT ROLE {db_role} TO USER "{user}"'
                        cursor.execute(query)
                        logger.info(
                            f"Role {db_role} successfully granted to user {user}."
                        )
                    except Exception as user_exc:
                        logger.error(
                            f"Failed to grant role {db_role} to user {user}: {user_exc}"
                        )
                        raise

    def unmap_db_role_from_db_users(self, db_users: List[str], db_role: str) -> None:
        """
        Args:
            db_users: The list of database users.
            db_role: The database role.

        Responsibilities:
            1. Remove the role from each user in the list.

        Raises:
            Exception: If role revocation fails.
        """
        with snowflake.connector.connect(
            user=os.getenv("SNOWFLAKE_USER"),
            password=os.getenv("SNOWFLAKE_PASSWORD"),
            account=os.getenv("SNOWFLAKE_ACCOUNT"),
            warehouse=os.getenv("SNOWFLAKE_WAREHOUSE"),
            role=os.getenv("SNOWFLAKE_ROLE"),
        ) as conn:
            with conn.cursor() as cursor:
                for user in db_users:
                    try:
                        query = f'REVOKE ROLE {db_role} FROM USER "{user}"'
                        cursor.execute(query)
                        logger.info(
                            f"Role {db_role} successfully unmapped from user {user}."
                        )
                    except Exception as user_exc:
                        logger.error(
                            f"Failed to unmap role {db_role} from user {user}: {user_exc}"
                        )
                        raise

    def facilitate_db_users(self, emails: List[str]) -> List[str]:
        """
        Facilitate the users.

        Args:
            emails: The emails.

        Responsibilities:
            1. Check missing users in AWS Identity Centre.
            2. Create missing users in Snowflake and insert in AWS Identity Centre.

        Returns: The database user names of all users.
        """
        db_users = []

        if is_local_env():
            missing_emails = emails
            for email in missing_emails:
                db_users.append(self._get_db_user(email))
        else:
            snowflake_application_arn = os.getenv("AWS_SNOWFLAKE_APPLICATION_ARN")
            if not snowflake_application_arn:
                logger.error("AWS_SNOWFLAKE_APPLICATION_ARN is not set")
                raise ValueError("AWS_SNOWFLAKE_APPLICATION_ARN is not set")

            missing_emails = (
                AwsIdentityCenterManager().get_unassigned_users_in_aws_sso_application(
                    emails, snowflake_application_arn
                )
            )

            for email in missing_emails:
                # Create the missing users in Snowflake and insert in AWS Identity Centre
                AwsIdentityCenterManager().assign_user_to_aws_sso_application(
                    email, snowflake_application_arn
                )
                db_users.append(self._get_db_user(email))

        # Get the existing users
        existing_emails = set(emails) - set(missing_emails)
        for email in existing_emails:
            db_users.append(self._get_db_user(email))

        return db_users

    def list_users_with_their_roles(self) -> List[Dict]:
        """
        List the users with their roles.

        Return format:
        [
            {
                "user": "user1",
                "roles": ["role1", "role2"]
            },
            {
                "user": "user2",
                "roles": ["role1", "role3"]
            }
        ]

        """
        with snowflake.connector.connect(
            user=os.getenv("SNOWFLAKE_USER"),
            password=os.getenv("SNOWFLAKE_PASSWORD"),
            account=os.getenv("SNOWFLAKE_ACCOUNT"),
            warehouse=os.getenv("SNOWFLAKE_WAREHOUSE"),
            role=os.getenv("SNOWFLAKE_ROLE"),
        ) as conn:
            with conn.cursor() as cursor:
                try:
                    # Execute the SHOW ROLES command
                    users = cursor.execute("SHOW USERS").fetchall()
                    roles = cursor.execute(
                        """
                    SELECT grantee_name, array_agg(role) FROM SNOWFLAKE.ACCOUNT_USAGE.GRANTS_TO_USERS
                    where deleted_on is null
                    group by grantee_name"""
                    ).fetchall()
                    role_dict = {row[0]: json.loads(row[1]) for row in roles}
                    users_with_roles = [
                        {
                            "user": user[0],
                            "login_name": user[2],
                            "email": user[6],
                            "roles": role_dict.get(user[0], []),
                        }
                        for user in users
                    ]
                    return users_with_roles
                except Exception as exc:
                    logger.error("Failed to list users with their roles: %s", str(exc))
                    raise exc


class TsarMembershipServices:
    application: TsarApplication = TsarApplication.DESKTOP_WEB
    flexible_duration_perm: RbacPermissions = (
        RbacPermissions.TSAR_DESKTOPWEB_ADDON_ADMINUI_FLEXIBLE_DURATION
    )
    honour_tsar_flag: bool = True
    is_membership_client_specific: bool = True
    is_membership_role_specific: bool = False
    is_scheduling_enabled: bool = True

    def __init__(
        self,
        now: datetime,
        login_user: str,
        permissions: List | Tuple | Set,
        audit: Dict,
    ):
        self.now: datetime = now
        self.login_user: str = login_user
        self.is_duration_flexible: bool = (
            self.flexible_duration_perm.value in permissions
        )
        self.audit: Dict = audit or {}
        self.notification_service: TsarNotificationService = TsarNotificationService(
            application=self.application
        )
        self.tsar_membership_accessor: TsarMembershipAccessor = TsarMembershipAccessor(
            self.application
        )

    def _resolve_duration(
        self,
        relative_duration: str,
        input_starts_at: Optional[str],
        input_ends_at: Optional[str],
    ) -> Tuple:
        """
        Resolve duration based on the input.
        - If relative_duration is provided, set starts_at and ends_at based on the relative_duration.
        - If starts_at is not provided, set starts_at to the current time.
        - Else, do nothing.
        """
        _, _, relative_durations = durations_config(self.is_duration_flexible)

        starts_at = input_starts_at
        ends_at = input_ends_at

        starts_now = self.now.replace(second=0, microsecond=0)
        if relative_duration in relative_durations:
            starts_at = starts_now
            ends_at = starts_now + relative_durations[relative_duration]
        elif starts_at in NULL_VALUES:
            starts_at = starts_now

        if not (starts_at and ends_at):
            raise ValidationError(
                "Invalid duration provided. Please provide valid input."
            )

        return starts_at, ends_at

    def check_time_window_availability(
        self,
        client_id: int | str | None,
        roles: List[str],
        members: List[str],
        relative_duration: str,
        starts_at: Optional[str],
        ends_at: Optional[str],
    ) -> bool:
        """
        Check if the time window is available.

        Args:
            data: The data.
                - client_id: The client ID.
                - roles: The list of roles.
                - members: The list of members.
                - starts_at: The start time.
                - ends_at: The end time.

        Returns:
            bool: True if the time window is available, False otherwise.
        """
        starts_at, ends_at = self._resolve_duration(
            relative_duration, starts_at, ends_at
        )

        if client_id in NULL_VALUES:
            client_id = None

        data = {
            "application": self.application,
            "client_id": client_id,
            "roles": roles,
            "members": members,
            "starts_at": starts_at,
            "ends_at": ends_at,
            "is_duration_flexible": self.is_duration_flexible,
            "honour_tsar_flag": self.honour_tsar_flag,
            "is_client_specific": self.is_membership_client_specific,
            "is_role_specific": self.is_membership_role_specific,
            "is_scheduling_enabled": self.is_scheduling_enabled,
        }

        # validate
        ser = MembershipNewTimeWindowAvailabilitySerializer(data=data)
        try:
            ser.is_valid(raise_exception=True)
            return True
        except ValidationError as exc:
            logger.error("Error in validating time window availability: %s", str(exc))
            return False

    def check_updated_time_window_availability(
        self,
        membership_id: str | UUID,
        new_starts_at: str,
        new_ends_at: str,
        members: List[str],
    ) -> bool:
        """
        Check if the updated time window is available.

        Args:
            membership_id: The membership ID.
            new_starts_at: The new start time.
            new_ends_at: The new end time.
            members: The updated list of members.
        """
        data = {
            "membership_id": membership_id,
            "new_starts_at": new_starts_at,
            "new_ends_at": new_ends_at,
            "members": members,
            "is_duration_flexible": self.is_duration_flexible,
            "honour_tsar_flag": self.honour_tsar_flag,
            "is_client_specific": self.is_membership_client_specific,
            "is_role_specific": self.is_membership_role_specific,
            "is_scheduling_enabled": self.is_scheduling_enabled,
        }

        # validate
        ser = MembershipUpdatedTimeWindowAvailabilitySerializer(data=data)
        try:
            ser.is_valid(raise_exception=True)
            return True
        except ValidationError as exc:
            logger.error(
                "Error in validating updated time window availability: %s", str(exc)
            )
            return False

    def validate_and_create_membership_record(
        self,
        client_id: int | str | None,
        roles: List[str],
        members: List[str],
        relative_duration: str,
        starts_at: Optional[str],
        ends_at: Optional[str],
        description: str,
        jira_url: str,
        comment: str,
    ) -> Tuple[TsarMembership, Dict]:
        """
        Create a membership record in the support_memberships table.

        Args:
            now: The current datetime.
            login_user: The login user.
            application: The application.
            client_id: The client ID.
            members: The list of members.
            relative_duration: The relative duration. (30m, 1h, 1d, 1w, 30d, Custom)
            starts_at: The start time.
            ends_at: The end time.
            description: The description.
            jira_url: The Jira URL.
            is_duration_flexible: True if the duration is flexible, False otherwise.
            audit: The audit.
            comment: The comment.

        Returns:
            Dict: The membership record.

        """
        starts_at, ends_at = self._resolve_duration(
            relative_duration, starts_at, ends_at
        )

        mem = {
            "knowledge_begin_date": self.now,
            "updated_by": self.login_user,
            "membership_id": uuid4(),
            "client": client_id,
            "application": self.application,
            "members": members,
            "roles": roles,
            "starts_at": starts_at,
            "ends_at": ends_at,
            "approver": self.login_user,
            "status": MembershipStatus.APPROVED,
            "created_at": self.now,
            "created_by": self.login_user,
            "approved_at": self.now,
            "description": description,
            "jira_url": jira_url,
            "comment": comment,
            "additional_details": self.audit,
            "is_duration_flexible": self.is_duration_flexible,
            "honour_tsar_flag": self.honour_tsar_flag,
            "is_client_specific": self.is_membership_client_specific,
            "is_role_specific": self.is_membership_role_specific,
            "is_scheduling_enabled": self.is_scheduling_enabled,
        }

        ser = TsarMembershipCreationSerializer(data=mem)
        ser.is_valid(raise_exception=True)
        instance = ser.save()
        return instance, ser.validated_data  # type: ignore

    def get_memberships(self, data: Dict) -> Dict:
        """
        Get memberships.

        Args:
            data
                page: The page.
                page_size: The page size.
                sort_by: The sort by field.
                sort_order: The sort order.
                quick_filter: The quick filter.
                search: The search text.

        Returns:
            Dict: The memberships.
                - rows: The tuple of memberships.
                - columns: The tuple of columns.
        """

        fields: Tuple = (
            "membership_id",
            "client_id",
            "created_at",
            "updated_at",  # calculated field
            "starts_at",
            "ends_at",
            "approver",
            "description",
            "jira_url",
            "calculated_status",  # calculated field
            "members",
            "roles",
            "application",
        )

        memberships: Tuple = self.tsar_membership_accessor.get_memberships(
            page=data["page"],
            page_size=data["page_size"],
            sort_by=data["sort_by"],
            sort_order=data["sort_order"],
            quick_filter=data["quick_filter"],
            search=data["search"],
            fields=fields,
        )

        return {
            "columns": tuple(camel_case(col) for col in fields),
            "rows": memberships,
            "context": {},  # add extra data here in overridden method
        }

    def _get_revokable_memberships_and_data(
        self, data: Dict
    ) -> QuerySet[TsarMembership]:
        """
        Get the revokable memberships and data.

        Args:
            data: The data.
                - memberships: List of membership ids to be revoked.
                - revoke_all: True if all memberships are to be revoked, False otherwise.
                - revoke_for_clients: List of client ids for which memberships are to be revoked.
                - comment: The comment.

        Returns: The revokable memberships with their data.
        """
        # invalidate active memberships current records
        sma = self.tsar_membership_accessor
        memberships_qs: QuerySet = sma.empty_queryset()
        if data["revoke_all"]:
            memberships_qs = sma.active_memberships_aware()
        elif data["revoke_for_clients"]:
            memberships_qs = sma.get_active_memberships_by_clients(
                data["revoke_for_clients"]
            )
        elif data["revoke_the_expired"]:
            if self.application == TsarApplication.DESKTOP_WEB:
                raise ValidationError("Desktop web cannot revoke expired memberships")
            memberships_qs = sma.get_expired_non_revoked_memberships()
        elif data["memberships"]:
            memberships_qs = sma.get_active_memberships_by_ids(data["memberships"])
        return memberships_qs

    def revoke_memberships(
        self,
        request_data: Dict,
        memberships_qs: Optional[QuerySet[TsarMembership]] = None,
    ) -> List[Dict]:
        """
        Revoke the memberships.

        Args:
            request_data
                - memberships: List of membership ids to be revoked.
                - revoke_all: True if all memberships are to be revoked, False otherwise.
                - revoke_for_clients: List of client ids for which memberships are to be revoked.
                - comment: The comment.
            memberships_data: The memberships data. (Optional)
            memberships_qs: The memberships queryset. (Optional)

        Flow:
            1. Ignore any obsolete memberships (already revoked/ relinquished/ cancelled/ rejected/ expired).
            2. Update existing record's knowledge_end_date (support_memberships table)
            3. Create new records with comment and relevant status ("ACCESS_REVOKED")

        Returns: List of revoked memberships.
        """
        memberships_qs = memberships_qs or self._get_revokable_memberships_and_data(
            request_data
        )
        memberships_data: List[Dict] = list(memberships_qs.values())

        for mem in memberships_data:
            mem["knowledge_begin_date"] = self.now
            mem["knowledge_end_date"] = None
            mem["updated_by"] = self.login_user
            mem["status"] = MembershipStatus.ACCESS_REVOKED
            mem["comment"] = request_data.get(
                "comment",
                COMMENT_PLACEHOLDER_FOR_REVOKE_MEMBERSHIP,
            )
            mem["additional_details"] = self.audit

            # rename fields; serializer expects these fields; while queryset.values() returns FK fields by appending _id
            mem["client"] = mem["client_id"]

            # remove unnecessary fields
            mem.pop("temporal_id")
            mem.pop("client_id")

        memberships_ser = TsarMembershipSerializer(data=memberships_data, many=True)
        memberships_ser.is_valid(raise_exception=True)

        # invalidate current records
        self.tsar_membership_accessor.invalidate_records(memberships_qs, self.now)

        # create new records
        memberships_ser.save()
        return memberships_ser.validated_data  # type: ignore

    def update_membership(self, update: Dict) -> Tuple[Dict, Dict]:
        """
        <<<< NOTE: Used only for Desktop Web currently. >>>>

        Update memberships by invalidating active memberships and creating new records with updated details.

        Args:
            now: The current datetime.
            login_user: The login user.
            update: The update data.
                - membership_id: The membership ID.
                - starts_at: The updated start time.
                - ends_at: The updated end time.
                - members: The list of members.
                - description: The description.
                - jira_url: The Jira URL.
                - comment: The comment.
            request_audit: The request audit.

        Returns:
            Tuple: The updated membership record and the old membership record.
        """
        qs: QuerySet = self.tsar_membership_accessor.get_active_memberships_by_ids(
            [update["membership_id"]]
        )
        mem: Optional[Dict] = qs.values().first()

        # create new records with updated details
        if mem is None:
            raise ValidationError("Membership is obsolete now, can't be updated.")

        # deep copy mem
        old_mem: Dict = copy.deepcopy(mem)

        # update fields
        mem["knowledge_begin_date"] = self.now
        mem["knowledge_end_date"] = None
        mem["updated_by"] = self.login_user
        mem["description"] = update.get("description", mem["description"])
        mem["jira_url"] = update.get("jira_url", mem["jira_url"])
        mem["comment"] = update.get("comment", "")
        mem["additional_details"] = self.audit

        # preserve the existing starts_at, ends_at and add new_starts_at, new_ends_at for validations in serializer
        mem["new_starts_at"] = update.get("starts_at", mem["starts_at"])
        mem["new_ends_at"] = update.get("ends_at", mem["ends_at"])
        mem["members"] = update.get("members", mem["members"])

        # rename fields; serializer expects these fields; while queryset.values() returns FK fields by appending _id
        mem["client"] = mem["client_id"]

        # remove unnecessary fields
        mem.pop("temporal_id")
        mem.pop("client_id")

        # adding additional logic fields
        mem["honour_tsar_flag"] = self.honour_tsar_flag
        mem["is_duration_flexible"] = self.is_duration_flexible
        mem["is_client_specific"] = self.is_membership_client_specific
        mem["is_role_specific"] = self.is_membership_role_specific
        mem["is_scheduling_enabled"] = self.is_scheduling_enabled

        memberships_ser = TsarMembershipUpdationSerializer(data=mem)
        memberships_ser.is_valid(raise_exception=True)

        # invalidate current records
        self.tsar_membership_accessor.invalidate_records(qs, self.now)
        # create new records
        memberships_ser.save()

        return memberships_ser.validated_data, old_mem  # type: ignore


class TsarDesktopWebService(TsarMembershipServices):
    """
    Service class for TSAR Desktop Web.
    """

    application: TsarApplication = TsarApplication.DESKTOP_WEB
    flexible_duration_perm: RbacPermissions = (
        RbacPermissions.TSAR_DESKTOPWEB_ADDON_ADMINUI_FLEXIBLE_DURATION
    )
    honour_tsar_flag: bool = True
    is_membership_client_specific: bool = True
    is_membership_role_specific: bool = False

    def _get_available_email_id_for_support_user(self, client_id: int) -> str:
        client: Client = get_client(client_id, fields=["domain"])
        domain_name = extract_domain_name(root_domain_name=client.domain.lower())
        taken_emails: Set = EmployeeReadAccessor(client_id).get_taken_emails()

        employee_email_id = f"everstage.support@{domain_name}.admin.com"
        suffix = 1
        while (
            employee_email_id in taken_emails
            or EmployeeReadAccessor.get_employees_with_same_email(  # to avoid multi-client same email issue
                employee_email_id
            ).exists()
        ):
            employee_email_id = f"everstage.support.{suffix}@{domain_name}.admin.com"
            suffix += 1
        return employee_email_id

    def _create_support_user_if_not_exists(self, client_id: int, role: UUID) -> None:
        """Creating everstage power admin for new clients"""
        if RolePermissionsAccessor(client_id).is_role_power_admin(role):
            # NOTE: This is to gracefully handle the case of tsar user login as a power admin; without requiring that power-admin user to be marked as `is_internal_support_user` = True.
            # TODO: This should be removed with future releases.
            support_users: List[str] = EmployeeReadAccessor(
                client_id
            ).get_active_employee_email_ids_by_roles([role])
        else:
            support_users: List[str] = EmployeeReadAccessor(
                client_id
            ).get_active_support_user_email_ids_by_roles([role])

        if len(support_users) > 0:
            return

        # means no support user exists; create one
        # create user in auth0
        employee_email_id = self._get_available_email_id_for_support_user(client_id)
        first_name = "everstage"
        last_name = "admin"
        create_oauth_user(
            {
                "employee_email_id": employee_email_id,
                "first_name": first_name,
                "last_name": last_name,
                "email_verified": True,
            }
        )

        # Create user in employee table
        emp_obj = Employee(
            client_id=client_id,
            knowledge_begin_date=self.now,
            employee_email_id=employee_email_id,
            user_role=[str(role)],
            first_name=first_name,
            last_name=last_name,
            created_date=self.now,
            status="Active",
            is_internal_support_user=True,
        )
        EmployeeWriteAccessor(client_id).persist_employee(emp_obj)

    def create(self, data: Dict) -> None:
        """
        `data` contains:
            - "client_id": int | str,
            - "role": <uuid>,
            - "members": [<>, <>],
            - "relative_duration": <>,
            - "starts_at": "2022-01-01T00:00:00+00:00",
            - "ends_at": "2022-01-02T00:00:00+00:00",
            - "description": "Test membership",
            - "jira_url": "https://interstage.atlassian.net/wiki/spaces/TECH/pages/edit-v2/757858305",
            - "comment": "Test membership",
        """
        # role and internal support user management
        client_id: int = data["client_id"]
        role: Optional[str] = data.get("role")
        if role is None:
            # [NOTE] default role is PowerAdmin; for now
            # [TODO] need to remove this possibility of `role` being None
            roles: List[UUID] = RolePermissionsAccessor(
                client_id
            ).get_all_power_admins_role_id()
            if not roles:
                raise LookupError("PowerAdmin role not found")
            role = str(roles[0])

        # create membership record
        membership_instance, membership_data = (
            self.validate_and_create_membership_record(
                client_id=client_id,
                roles=[role],
                members=data["members"],
                relative_duration=data["relative_duration"],
                starts_at=data.get("starts_at"),
                ends_at=data.get("ends_at"),
                description=data["description"],
                jira_url=data.get("jira_url", ""),
                comment=COMMENT_PLACEHOLDER_FOR_NEW_MEMBERSHIP,
            )
        )
        self.notification_service.notify_membership_creation(
            self.login_user, membership_data
        )

        # check if support user exists with this role; if not, create it
        self._create_support_user_if_not_exists(client_id, membership_instance.roles[0])

    def get(self, data: Dict) -> Dict:
        """
        Args: data
            - quick_filter: The quick filter.
            - search: The search string.
            - fields: The fields to return.
        """
        data: Dict = self.get_memberships(data)

        # fetch roles present in the memberships
        role_ids: Set = set()
        index: int = data["columns"].index("roles")
        for membership in data["rows"]:
            role_ids.update(membership[index])

        # get roles information
        roles: List[Dict] = get_role_by_role_permission_ids_client_agnostic(role_ids)
        data["context"]["roles_map"] = {
            str(role["role_permission_id"]): role["display_name"] for role in roles
        }

        return data

    def revoke(self, data: Dict) -> List[Dict]:
        """
        Args: data
            - memberships: List of membership ids to be revoked.
            - revoke_all: True if all memberships are to be revoked, False otherwise.
            - revoke_for_clients: List of client ids for which memberships are to be revoked.
            - comment: The comment.
        """
        revoked_memberships: List[Dict] = self.revoke_memberships(data)
        self.notification_service.notify_membership_revocation(
            self.login_user, revoked_memberships
        )
        return revoked_memberships

    def update(self, data: Dict) -> None:
        """
        Args: data
            - membership_id: The membership ID.
            - starts_at: The updated start time.
            - ends_at: The updated end time.
            - members: The list of members.
            - description: The description.
            - jira_url: The Jira URL.
            - comment: The comment.
        """
        updated_membership, old_membership = self.update_membership(data)
        self.notification_service.notify_membership_update(
            self.login_user, updated_membership, old_membership
        )


class TsarDatabaseBaseService(TsarMembershipServices, DatabaseUserRoleManager, ABC):
    """
    Base Service class for TSAR Database.
    """

    db_access_scope_cls = DatabaseAccessScope

    def create(self, data: Dict) -> None:
        """
        [TODO] Add docstring. Add what's in `data`.
        """
        # create membership record
        membership, membership_data = self.validate_and_create_membership_record(
            client_id=data.get("client_id"),
            roles=[data["role"]],
            members=data["members"],
            relative_duration=data["relative_duration"],
            starts_at=data.get("starts_at"),
            ends_at=data.get("ends_at"),
            description=data["description"],
            jira_url=data.get("jira_url", ""),
            comment=COMMENT_PLACEHOLDER_FOR_NEW_MEMBERSHIP,
        )

        # TODO: Test whether this flow is executing only after membership creation or not.
        is_membership_live = membership.starts_at <= self.now <= membership.ends_at

        # roles and cron management
        client_id: Optional[int] = (
            membership.client.client_id if membership.client else None
        )
        try:
            if is_membership_live:
                self.execute_role_allocation_to_users(
                    membership.members,
                    self.db_access_scope_cls(membership.roles[0]),
                    client_id,
                )
            else:
                # self.schedule_role_allocation_to_users(
                #     emails=membership.members,
                #     scope=self.db_access_scope_cls(membership.roles[0]),
                #     execute_at=membership.starts_at,
                # )
                pass
            # self.schedule_role_withdrawal_from_users(
            #     emails=membership.members,
            #     scope=self.db_access_scope_cls(membership.roles[0]),
            #     execute_at=membership.ends_at,
            # )
        except Exception as creation_exc:
            membership_rollback_error: str = "no-error-in-rollback"
            try:
                self._withdraw_roles_from_users_for_given_memberships(
                    memberships_ids=[membership.membership_id]
                )
                # TaskManager().cancel_all_tasks_for_membership(membership.membership_id)
            except Exception as rollback_exc:
                membership_rollback_error = str(rollback_exc)
            finally:
                # invalidate membership in case of any error
                error: str = (
                    f"""

                    ---------------------------------------
                    <<< System Generated Content: BEGIN >>>

                    Membership creation error: {str(creation_exc)}
                    
                    Membership rollback error: {membership_rollback_error}

                    <<< System Generated Content: END >>>
                    ---------------------------------------
                    """
                )
                membership.status = MembershipStatus.INTERNAL_SERVER_ERROR
                membership.description = membership.description + error
                membership.comment = error
                membership.save()

            # TODO: failure email notification
            raise creation_exc

        # success email notification
        self.notification_service.notify_membership_creation(
            self.login_user, membership_data
        )
        ###### END ######

    def revoke(self, data: Dict) -> List[Dict]:
        """
        Args: data
            - memberships: List of membership ids to be revoked.
            - revoke_all: True if all memberships are to be revoked, False otherwise.
            - comment: The comment.

        Flow:
            1. Ignore any obsolete memberships (already revoked/ relinquished/ cancelled/ rejected/ expired).
            2. Roles & Cron Management
                a. Delete the scheduled cron jobs for role withdrawal for all the revoking memberships.
                b. For live memberships, withdraw the roles. For scheduled memberships, delete the scheduled cron jobs for role allocation.
            3. Update existing record's knowledge_end_date (support_memberships table)
            4. Create new records with comment and relevant status ("ACCESS_REVOKED")
            5. Trigger email notifications to actor (who trigerred action), approver and members.

        Returns: List of revoked memberships.
        """
        memberships_qs: QuerySet[TsarMembership] = (
            self._get_revokable_memberships_and_data(data)
        )
        memberships_ids = memberships_qs.values_list("membership_id", flat=True)

        # roles and cron management
        try:
            self._withdraw_roles_from_users_for_given_memberships(memberships_ids)
            # TaskManager().cancel_all_tasks_for_memberships(memberships_qs.values_list('membership_id', flat=True))
            revoked_memberships: List[Dict] = self.revoke_memberships(
                data, memberships_qs
            )

            # TODO: email notification for successful and unsuccessful revocations separately
            self.notification_service.notify_membership_revocation(
                self.login_user, revoked_memberships
            )
            return revoked_memberships

        except Exception as exc:
            logger.error(
                "Error revoking memberships!\nAttempted to withdraw roles from users for given memberships - %s\nPlease manually ensure all the revokation and the roles withdrawal for all these. Exception: %s.\n",
                memberships_ids,
                str(exc),
            )
            raise exc

    def _withdraw_roles_from_users_for_given_memberships(
        self, memberships_ids: List[UUID]
    ):
        """
        Withdraw roles from users for the given memberships.

        Flow:
        1. Fetch all the given memberships at once from the db.
        2. Iterate over each membership and make a dict of user -> roles. (dict x)
        3. Find all the currently active memberships for each user.
        4. Iterate over each live membership and make another dict of user -> live roles. (dict y)
        5. Iterate over dict x for each user and withdraw (roles - live roles) from user.
        """
        # Step 1: Fetch only required fields
        memberships: QuerySet[TsarMembership] = (
            self.tsar_membership_accessor.get_memberships_by_ids(memberships_ids).only(
                "membership_id", "members", "roles", "client_id"
            )
        )

        # Step 2: Create a dict of db_user -> roles
        db_user_roles: Dict[str, Set[str]] = {}
        members: List[str] = []
        memberships_being_revoked: List[UUID] = []
        for membership in memberships:
            memberships_being_revoked.append(membership.membership_id)
            for member_email in membership.members:
                members.append(member_email)
                db_user: str = self.get_db_username_alone(member_email)
                db_roles: List[str] = []
                for scope in membership.roles:
                    _db_roles: Tuple[str] | str = self.get_db_role(
                        self.db_access_scope_cls(scope), membership.client_id
                    )
                    if isinstance(_db_roles, str):
                        db_roles.append(_db_roles)
                    else:
                        db_roles.extend(_db_roles)
                if db_user not in db_user_roles:
                    db_user_roles[db_user] = set()
                db_user_roles[db_user].update(db_roles)

        # Step 3: Find all live memberships for each user
        live_memberships = (
            self.tsar_membership_accessor.get_live_memberships_for_users(
                members, exclude_memberships=memberships_being_revoked
            )
        ).only("members", "roles", "client_id")

        # Step 4: Create a dict of user -> live roles for other memberships
        live_db_user_roles: Dict[str, Set[str]] = {}
        for membership in live_memberships:
            for member_email in membership.members:
                db_user = self.get_db_username_alone(member_email)
                db_roles: List[str] = []
                for scope in membership.roles:
                    _db_roles: Tuple[str] | str = self.get_db_role(
                        self.db_access_scope_cls(scope), membership.client_id
                    )
                    if isinstance(_db_roles, str):
                        db_roles.append(_db_roles)
                    else:
                        db_roles.extend(_db_roles)
                if db_user not in live_db_user_roles:
                    live_db_user_roles[db_user] = set()
                live_db_user_roles[db_user].update(db_roles)

        # Step 5: Identify roles to withdraw
        db_user_roles_to_withdraw: Dict[str, Set[str]] = {}
        for db_user, roles in db_user_roles.items():
            live_roles = live_db_user_roles.get(db_user, set())
            roles_to_withdraw = roles - live_roles
            if roles_to_withdraw:
                db_user_roles_to_withdraw[db_user] = roles_to_withdraw

        # Step 6: Pivot from user -> roles to role -> users to reduce the db calls.
        db_roles_users_to_withdraw: Dict[str, Set[str]] = {}
        for db_user, roles in db_user_roles_to_withdraw.items():
            for role in roles:
                if role not in db_roles_users_to_withdraw:
                    db_roles_users_to_withdraw[role] = set()
                db_roles_users_to_withdraw[role].add(db_user)

        # Step 7: Withdraw roles from users
        for role, db_users in db_roles_users_to_withdraw.items():
            self.unmap_db_role_from_db_users(list(db_users), role)

        return True


class TsarPostgresqlService(TsarDatabaseBaseService, PostgresqlUserRoleManager):
    """
    Service class for TSAR PostgreSQL.
    """

    application: TsarApplication = TsarApplication.POSTGRESQL
    flexible_duration_perm: RbacPermissions = (
        RbacPermissions.TSAR_POSTGRESQL_ADDON_ADMINUI_FLEXIBLE_DURATION
    )
    honour_tsar_flag: bool = False
    is_membership_client_specific: bool = True
    is_membership_role_specific: bool = True
    db_access_scope_cls = PostgresqlAccessScope
    is_scheduling_enabled: bool = False

    def check_memberships_for_data_access_mismatch(
        self, user: str, scope: PostgresqlAccessScope
    ) -> bool:
        """
        Check if the user has any overlapping memberships for the given scope.

        Explanation:

        If the user has a sensitive_data_reader access scope, then they cannot have a writer access scope.
        """
        undesired_roles: Set[str] = (
            POSTGRESQL_READER_ROLES
            if scope in POSTGRESQL_WRITER_ROLES
            else POSTGRESQL_WRITER_ROLES
        )
        return (
            self.tsar_membership_accessor.get_live_memberships_for_users([user])
            .filter(roles__has_any_keys=undesired_roles)
            .exists()
        )


class TsarSnowflakeService(TsarDatabaseBaseService, SnowflakeUserRoleManager):
    """
    Service class for TSAR Snowflake.
    """

    application: TsarApplication = TsarApplication.SNOWFLAKE
    flexible_duration_perm: RbacPermissions = (
        RbacPermissions.TSAR_SNOWFLAKE_ADDON_ADMINUI_FLEXIBLE_DURATION
    )
    honour_tsar_flag: bool = False
    is_membership_client_specific: bool = True
    is_membership_role_specific: bool = True
    db_access_scope_cls = SnowflakeAccessScope
    is_scheduling_enabled: bool = False
