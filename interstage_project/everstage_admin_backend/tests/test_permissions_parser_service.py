from unittest.mock import patch

import pytest

from commission_engine.utils.general_data import RbacPermissions
from everstage_admin_backend.services.permissions_parser_service import (
    AdminUIPermissionsParser,
)


class TestAdminUIPermissionsParser:
    """Test class for AdminUIPermissionsParser"""

    @pytest.mark.parametrize(
        "permissions,expected_set",
        [
            ([], set()),
            (
                [RbacPermissions.MANAGE_ADMINUI.value],
                {RbacPermissions.MANAGE_ADMINUI.value},
            ),
            (
                [
                    RbacPermissions.MANAGE_ADMINUI.value,
                    RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
                ],
                {
                    RbacPermissions.MANAGE_ADMINUI.value,
                    RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
                },
            ),
        ],
    )
    def test_init(self, permissions, expected_set):
        """Test initialization with various permission sets"""
        parser = AdminUIPermissionsParser(permissions)
        assert parser.raw_permissions_set == expected_set
        assert len(parser._permission_checks) == 5  # 5 registered permission checks

    def test_register_permission_check(self):
        """Test registering and overwriting permission checks"""
        parser = AdminUIPermissionsParser([])
        initial_count = len(parser._permission_checks)

        # Test registering new check
        def dummy_check():
            return True

        parser.register_permission_check("test_permission", dummy_check)
        assert len(parser._permission_checks) == initial_count + 1
        assert "test_permission" in parser._permission_checks
        assert parser._permission_checks["test_permission"] == dummy_check

        # Test overwriting existing check
        def new_check():
            return "custom_result"

        parser.register_permission_check("test_permission", new_check)
        assert len(parser._permission_checks) == initial_count + 1
        assert parser._permission_checks["test_permission"] == new_check

        # Verify new function is called
        result = parser.get_all_permissions()
        assert result["test_permission"] == "custom_result"

    @pytest.mark.parametrize(
        "permissions,expected_results",
        [
            # Test with individual permissions
            (
                [RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value],
                {
                    "is_auth0_users_manageable": True,
                    "is_etl_status_manageable": False,
                    "is_integrations_manageable": False,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": False,
                },
            ),
            (
                [RbacPermissions.MANAGE_ETLSTATUS.value],
                {
                    "is_auth0_users_manageable": False,
                    "is_etl_status_manageable": True,
                    "is_integrations_manageable": False,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": False,
                },
            ),
            (
                [RbacPermissions.MANAGE_INTEGRATIONS.value],
                {
                    "is_auth0_users_manageable": False,
                    "is_etl_status_manageable": False,
                    "is_integrations_manageable": True,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": False,
                },
            ),
            (
                [RbacPermissions.MANAGE_ADMINUI.value],
                {
                    "is_auth0_users_manageable": True,
                    "is_etl_status_manageable": True,
                    "is_integrations_manageable": True,
                    "is_admin_ui_manageable": True,
                    "is_customers_manageable": True,
                },
            ),
            (
                [RbacPermissions.MANAGE_CUSTOMERS.value],
                {
                    "is_auth0_users_manageable": False,
                    "is_etl_status_manageable": False,
                    "is_integrations_manageable": False,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": True,
                },
            ),
            # Test with combinations
            (
                [
                    RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
                    RbacPermissions.MANAGE_ETLSTATUS.value,
                ],
                {
                    "is_auth0_users_manageable": True,
                    "is_etl_status_manageable": True,
                    "is_integrations_manageable": False,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": False,
                },
            ),
            # Test with empty permissions
            (
                [],
                {
                    "is_auth0_users_manageable": False,
                    "is_etl_status_manageable": False,
                    "is_integrations_manageable": False,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": False,
                },
            ),
            # Test with irrelevant permissions
            (
                ["some:other:permission"],
                {
                    "is_auth0_users_manageable": False,
                    "is_etl_status_manageable": False,
                    "is_integrations_manageable": False,
                    "is_admin_ui_manageable": False,
                    "is_customers_manageable": False,
                },
            ),
        ],
    )
    def test_permission_checks(self, permissions, expected_results):
        """Test all permission checks with various permission combinations"""
        parser = AdminUIPermissionsParser(permissions)
        results = parser.get_all_permissions()
        assert results == expected_results

    @pytest.mark.parametrize(
        "permissions,expected_set",
        [
            (
                [
                    RbacPermissions.MANAGE_ADMINUI.value,
                    RbacPermissions.MANAGE_ADMINUI.value,  # Duplicate
                    RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
                ],
                {
                    RbacPermissions.MANAGE_ADMINUI.value,
                    RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
                },
            ),
            (
                ["", "   ", RbacPermissions.MANAGE_ADMINUI.value],
                {"", "   ", RbacPermissions.MANAGE_ADMINUI.value},
            ),
        ],
    )
    def test_permission_set_handling(self, permissions, expected_set):
        """Test handling of duplicate and empty string permissions"""
        parser = AdminUIPermissionsParser(permissions)
        assert parser.raw_permissions_set == expected_set

    def test_raw_permissions_list_immutability(self):
        """Test that modifying the original permissions list doesn't affect the parser"""
        permissions = [RbacPermissions.MANAGE_ADMINUI.value]
        parser = AdminUIPermissionsParser(permissions)

        # Verify initial state
        assert parser._check_manage_admin_ui() is True

        # Modify original list
        permissions.clear()
        permissions.append(RbacPermissions.MANAGE_CUSTOMERS.value)

        # Parser should be unaffected
        assert parser._check_manage_admin_ui() is True
        assert parser._check_manage_customers() is True
