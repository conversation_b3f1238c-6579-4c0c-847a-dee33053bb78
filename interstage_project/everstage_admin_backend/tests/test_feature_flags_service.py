from unittest.mock import MagicMock, patch

import pytest

from everstage_admin_backend.services.feature_flags_service import (
    get_all_feature_flags_across_clients,
)


class MockClient:
    def __init__(self, client_id: str, name: str):
        self.client_id = client_id
        self.name = name


@pytest.fixture
def mock_clients():
    return [
        MockClient("client1", "Test Client 1"),
        MockClient("client2", "Test Client 2"),
        MockClient("client3", "Another Client"),
        MockClient("client4", "Demo Client"),
    ]


@pytest.fixture
def mock_client_features():
    return {
        "feature1": True,
        "feature2": False,
    }


def test_get_all_feature_flags_basic_pagination(mock_clients, mock_client_features):
    """Test basic pagination functionality with default parameters"""
    with patch(
        "everstage_admin_backend.services.feature_flags_service.get_all_clients",
        return_value=mock_clients,
    ), patch(
        "everstage_admin_backend.services.feature_flags_service.get_client_features",
        return_value=mock_client_features,
    ):

        result = get_all_feature_flags_across_clients(page=1, page_size=2)

        assert result["page"] == 1
        assert result["page_size"] == 2
        assert result["total_clients"] == 4
        assert result["total_pages"] == 2
        assert len(result["results"]) == 2
        assert "Test Client 1 (client1)" in result["results"]
        assert "Test Client 2 (client2)" in result["results"]


def test_get_all_feature_flags_search_filtering(mock_clients, mock_client_features):
    """Test search term filtering functionality"""
    with patch(
        "everstage_admin_backend.services.feature_flags_service.get_all_clients",
        return_value=mock_clients,
    ), patch(
        "everstage_admin_backend.services.feature_flags_service.get_client_features",
        return_value=mock_client_features,
    ):

        result = get_all_feature_flags_across_clients(
            page=1, page_size=10, search_term="Test"
        )

        assert result["total_clients"] == 2
        assert "Test Client 1 (client1)" in result["results"]
        assert "Test Client 2 (client2)" in result["results"]
        assert "Another Client (client3)" not in result["results"]


def test_get_all_feature_flags_empty_results():
    """Test behavior when no clients are found"""
    with patch(
        "everstage_admin_backend.services.feature_flags_service.get_all_clients",
        return_value=[],
    ), patch(
        "everstage_admin_backend.services.feature_flags_service.get_client_features",
        return_value={},
    ):

        result = get_all_feature_flags_across_clients()

        assert result["page"] == 1
        assert result["page_size"] == 10
        assert result["total_clients"] == 0
        assert result["total_pages"] == 0
        assert len(result["results"]) == 0


def test_get_all_feature_flags_error_handling(mock_clients):
    """Test error handling when get_client_features raises an exception"""

    def mock_get_client_features(client_id):
        raise Exception("Test error")

    with patch(
        "everstage_admin_backend.services.feature_flags_service.get_all_clients",
        return_value=mock_clients,
    ), patch(
        "everstage_admin_backend.services.feature_flags_service.get_client_features",
        side_effect=mock_get_client_features,
    ):

        result = get_all_feature_flags_across_clients()

        assert result["page"] == 1
        assert result["page_size"] == 10
        assert result["total_clients"] == 0
        assert result["total_pages"] == 0
        assert len(result["results"]) == 0


def test_get_all_feature_flags_edge_cases(mock_clients, mock_client_features):
    """Test edge cases with page numbers and page sizes"""
    with patch(
        "everstage_admin_backend.services.feature_flags_service.get_all_clients",
        return_value=mock_clients,
    ), patch(
        "everstage_admin_backend.services.feature_flags_service.get_client_features",
        return_value=mock_client_features,
    ):

        # Test page number beyond total pages
        result = get_all_feature_flags_across_clients(page=3, page_size=2)
        assert result["page"] == 3
        assert len(result["results"]) == 0

        # Test with large page size
        result = get_all_feature_flags_across_clients(page=1, page_size=100)
        assert result["page"] == 1
        assert result["total_pages"] == 1
        assert len(result["results"]) == 4
