import logging
from collections import OrderedDict
from typing import Dict, List

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_api_exception, handle_ever_exception

from .serializers import (
    Auth0UserPasswordResetLinkDispatcherInputSerializer,
    GetHighProfileUsersInputSerializer,
    UserPasswordResetAuditViewInputSerializer,
    is_secure_admin_ui_auth0_user_mgmt_enabled_for_client,
)
from .services import (
    Auth0UserPasswordResetService,
    get_all_high_profile_users,
    get_user_password_reset_audit_details,
)

logger = logging.getLogger(__name__)


class Auth0UserManagementVersion(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    @handle_api_exception
    def get(self, request):
        """
        Get the Auth0 User Management version.
        Flag Display Name At Admin UI: "Secure AdminUI Auth0 User Management"
        Flag System Name: "is_secure_admin_ui_auth0_user_mgmt"

        Input:
        - client_id (int): The client ID.

        Returns:
        - is_secure_auth0_user_mgmt_enabled (bool): True if the Secure AdminUI Auth0 User Management is enabled; False otherwise.
        """
        client_id: int = int(request.query_params["client_id"])
        logger.info(
            "Getting the Auth0 User Management version for client: %s", client_id
        )
        is_secure_auth0_user_mgmt_enabled: bool = (
            is_secure_admin_ui_auth0_user_mgmt_enabled_for_client(client_id)
        )
        return Response(
            {
                "message": None,
                "is_secure_auth0_user_mgmt_enabled": is_secure_auth0_user_mgmt_enabled,
            },
            status=status.HTTP_200_OK,
        )


class GetHighProfileUsers(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    @handle_api_exception
    def get(self, request):
        """
        Get all the high profile users.
        'High Profile Users' are the users who have the permissions to 'Manage Roles and Permissions'.
        This will NOT include the PowerAdmin users.

        Input:
        - client_id (int): The client ID.

        Returns:
        - A list of high profile users

        Sample Response:
        {
            "message": null,
            "users": [
                {
                    "employeeEmailId": "<EMAIL>",
                    "firstName": "User",
                    "lastName": "1"
                },
                {
                    "employeeEmailId": "<EMAIL>",
                    "firstName": "Ankur",
                    "lastName": "Gupta"
                },
                {
                    "employeeEmailId": "<EMAIL>",
                    "firstName": "super",
                    "lastName": "admin"
                }
            ]
        }
        """
        ser = GetHighProfileUsersInputSerializer(data=request.query_params)
        ser.is_valid(raise_exception=True)
        data: OrderedDict = ser.validated_data  # type: ignore
        client_id: int = data["client_id"]
        logger.info("Getting all high profile users for client: %s", client_id)
        users: List[Dict] = get_all_high_profile_users(client_id)
        return Response(
            {
                "message": None,
                "users": users,
            },
            status=status.HTTP_200_OK,
        )


class Auth0UserPasswordResetLinkDispatcher(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    @handle_api_exception
    def post(self, request):
        """
        Actions:
        - Create the subject users in Auth0; if not already present.
        - Generate a password reset link and send it to the high profile user (`reset_link_recipient`).
        - Store the action details in the database.
        - Return a success message.

        Input:
        - client_id (int): The client ID.
        - subject_users (str): The email of the user; for whom the password reset link is to be sent.
        - reset_link_recipient (str): The email of the high profile user; to whom the password reset link is to be sent.

        Validations:
        - Check if the client has Secure AdminUI Auth0 User Management enabled.
        - Check if the subject user and the high profile user (i.e. reset_link_recipient) belong to the client.
        - Check if the high profile user (reset_link_recipient) has the required permissions; i.e. 'Manage Roles and Permissions'.

        Input:
        - client_id (int): The client ID.
        - subject_users (List[str]): The email of the target user.
        - reset_link_recipient (str): The email of the high profile user.

        Returns:
        - A success message.


        """
        login_email: str = request.user.username
        ser = Auth0UserPasswordResetLinkDispatcherInputSerializer(data=request.data)
        ser.is_valid(raise_exception=True)
        data: Dict = ser.validated_data  # type: ignore

        client_id: int = data["client_id"]
        subject_users: List[str] = data["subject_users"]
        reset_link_recipient: str = data["reset_link_recipient"]

        Auth0UserPasswordResetService(
            client_id=client_id,
            subject_users=subject_users,
            reset_link_recipient=reset_link_recipient,
            login_email=login_email,
        ).dispatch_user_password_reset_link()

        return Response(
            {"message": "Password reset link sent successfully."},
            status=status.HTTP_200_OK,
        )


class UserPasswordResetAuditView(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    @handle_api_exception
    def get(self, request):
        """
        Get the user password reset audit details.

        Input:
        - client_id (int): The client ID.

        Returns:
        - A list of user password reset audit details.

        Sample Response:
        {
            "message": null,
            "audit": {
                "columns": [
                    "id",
                    "resetAt",
                    "subjectUsers",
                    "resetLinkRecipient",
                    "resetBy"
                ],
                "rows": [
                    [
                        8,
                        "2024-12-08T07:13:15.500292Z",
                        [
                            "<EMAIL>"
                        ],
                        "<EMAIL>",
                        "<EMAIL>"
                    ],
                    [
                        7,
                        "2024-12-08T06:30:21.708489Z",
                        [
                            "<EMAIL>"
                        ],
                        "<EMAIL>",
                        "<EMAIL>"
                    ],
                ],
            }
        }
        """
        ser = UserPasswordResetAuditViewInputSerializer(data=request.query_params)
        ser.is_valid(raise_exception=True)
        client_id: int = ser.validated_data["client_id"]  # type: ignore

        audit_details = get_user_password_reset_audit_details(client_id)
        return Response(
            {
                "message": None,
                "audit": audit_details,
            },
            status=status.HTTP_200_OK,
        )
