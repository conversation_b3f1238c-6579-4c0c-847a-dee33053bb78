from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from everstage_etl.accessors.etl_config_accessor import ETLConfigAccessor


class ETLConfigView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get ETL configuration by type
        Query params:
            config_type: Type of the ETL configuration to retrieve
        """
        config_type = request.query_params.get("config_type")
        if not config_type:
            return Response(
                {"error": "config_type parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            etl_config = ETLConfigAccessor().get(config_type)
            return Response(
                {
                    "id": etl_config.id,
                    "config_type": etl_config.config_type,
                    "config": etl_config.config,
                    "description": etl_config.description,
                }
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        """
        Upsert (create or update) ETL configuration of any type
        Request body:
            config_type: Type of the ETL configuration (required)
            config_data: Dictionary containing the configuration data (required)
            description: Description of the configuration (optional)
        """
        config_type = request.data.get("config_type")
        config_data = request.data.get("config_data")
        description = request.data.get("description")  # Optional field

        if not config_type:
            return Response(
                {"error": "config_type is required in request body"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not config_data:
            return Response(
                {"error": "config_data is required in request body"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            etl_config = ETLConfigAccessor().upsert_config(
                config_type=config_type,
                config_data=config_data,
                description=description,
            )
            return Response(
                {
                    "id": etl_config.id,
                    "config_type": etl_config.config_type,
                    "config": etl_config.config,
                    "description": etl_config.description,
                }
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
