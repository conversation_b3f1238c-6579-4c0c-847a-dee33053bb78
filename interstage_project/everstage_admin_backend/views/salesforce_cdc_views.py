import logging
import uuid
from typing import Any, Dict

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.salesforce_cdc import SalesforceCDCCommandService
from interstage_project.auth_utils import requires_scope

logger = logging.getLogger(__name__)


class InstructionShutdownWorkerView(APIView):
    """
    API endpoint to shutdown a Salesforce CDC event listener worker.

    This view handles POST requests to trigger the shutdown of a background worker
    responsible for listening to Salesforce Change Data Capture (CDC) events for a
    specific Salesforce connection. The shutdown is performed by invoking the
    shutdown_event_listener method of the SalesforceCDCCommandService.

    Permissions:
        - Requires the MANAGE_ADMINUI RBAC permission.

    Request Body:
        - client_id (int): The client identifier.
        - salesforce_connection_id (int): The Salesforce connection identifier (required).

    Responses:
        - 200 OK: Shutdown was successful.
        - 400 Bad Request: Missing required fields or an error occurred during shutdown.
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ADMINUI.value), name="dispatch"
    )
    def post(self, request: Request):
        request_data: Dict[str, Any] = request.data or {}  # type: ignore
        client_id = request_data.get("client_id")
        salesforce_connection_id = request_data.get("salesforce_connection_id")

        if salesforce_connection_id is None:
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"data": {"error": "salesforce_connection_id is required"}},
            )
        try:
            SalesforceCDCCommandService(
                client_id, int(salesforce_connection_id)
            ).shutdown_event_listener()
            return Response(
                status=status.HTTP_200_OK,
            )
        except Exception:
            logger.exception(
                f"Error shutting down worker for connection: {salesforce_connection_id}"
            )
            return Response(status=status.HTTP_400_BAD_REQUEST)


class ObjectSubscriptionView(APIView):
    """
    API endpoint to enable or disable CDC event subscription for a Salesforce object.

    This view handles POST requests to manage (enable/disable) Change Data Capture (CDC)
    event subscriptions for a specific Salesforce object, using the
    manage_object_event_subscription method of the SalesforceCDCCommandService.

    Permissions:
        - Requires the MANAGE_ADMINUI RBAC permission.

    Request Body:
        - client_id (int): The client identifier.
        - salesforce_connection_id (int): The Salesforce connection identifier (required).
        - object_integration_id (str or UUID): The identifier of the Salesforce object (required).
        - is_enable_subscription (bool): Whether to enable or disable the subscription.

    Responses:
        - 200 OK: Subscription management was successful.
        - 400 Bad Request: Missing required fields or an error occurred during the operation.
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ADMINUI.value), name="dispatch"
    )
    def post(self, request: Request):
        request_data: Dict[str, Any] = request.data or {}  # type: ignore
        client_id = request_data.get("client_id")
        salesforce_connection_id = request_data.get("salesforce_connection_id")
        object_integration_id = request_data.get("object_integration_id")
        is_enable_subscription = request_data.get("is_enable_subscription")

        if salesforce_connection_id is None:
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"data": {"error": "salesforce_connection_id is required"}},
            )
        if object_integration_id is None:
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"data": {"error": "object_integration_id is required"}},
            )
        try:
            if isinstance(object_integration_id, str):
                object_integration_id = uuid.UUID(object_integration_id)
            SalesforceCDCCommandService(
                client_id=int(client_id),  # type: ignore
                sf_connection_id=int(salesforce_connection_id),
            ).manage_object_event_subscription(
                object_integration_id=object_integration_id,
                cdc_enabled=is_enable_subscription,  # type: ignore
                request_audit={
                    "action": "cdc_enable_disable",
                    "updated_by": str(request.user),
                },
            )
            return Response(
                status=status.HTTP_200_OK,
            )
        except Exception:
            logger.exception(
                f"Error enabling/disabling cdc for object: {object_integration_id}"
            )
            return Response(status=status.HTTP_400_BAD_REQUEST)
