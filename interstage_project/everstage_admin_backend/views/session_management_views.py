import traceback

from django.db import transaction
from django.utils import timezone
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from everstage_admin_backend.services.permissions_parser_service import (
    AdminUIPermissionsParser,
)
from everstage_admin_backend.services.session_management_services import (
    session_management_for_login,
    session_management_for_logout,
)
from everstage_admin_backend.tsar.services import is_tsar_accessible_at_frontend
from interstage_project.utils import log_me


class LoginSessionManagement(APIView):
    @transaction.atomic
    def post(self, request: Request) -> Response:
        """
        Handles the login session management.

        1. Invalidate & Blacklist all the tokens for the current user's other sessions
        2. Save the new access token
        3. Return TSAR accessibility and user permissions

        Args:
            request (Request): The HTTP request object.

        Returns:
            Response: The HTTP response object containing TSAR accessibility and permissions.

        Raises:
            ValidationError: If there is a validation error.
            Exception: If there is an unexpected error.
        """
        try:
            timestamp = timezone.now()
            logger = request.logger
            logger.info("BEGIN: Login Session Management")

            session_management_for_login(
                access_token=request.auth,
                id_token=request.data["id_token"],
                meta=request.META,
                audit=request.audit,
                timestamp=timestamp,
                logger=logger,
            )
            logger.info("END: Login Session Management")

            # Parse permissions
            permissions_parser = AdminUIPermissionsParser(
                request.decoded_token["permissions"]
            )
            permissions = permissions_parser.get_all_permissions()

            return Response(
                {
                    "is_tsar_accessible": is_tsar_accessible_at_frontend(
                        request.decoded_token["permissions"]
                    ),
                    "permissions": permissions,
                },
                status=status.HTTP_200_OK,
            )
        except ValidationError as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN SESSION MANAGEMENT VALIDATION: {}".format(exc))
            return Response(exc.detail, status=status.HTTP_400_BAD_REQUEST)
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN SESSION MANAGEMENT: {}".format(exc))
            return Response(str(exc), status=status.HTTP_400_BAD_REQUEST)


class LogoutSessionManagement(APIView):
    @transaction.atomic
    def post(self, request):
        """
        Handles the logout session management.

        Args:
            request (Request): The HTTP request object.

        Returns:
            Response: The HTTP response object.

        Raises:
            ValidationError: If there is a validation error.
            Exception: If there is an unexpected error.
        """
        try:
            timestamp = timezone.now()
            logger = request.logger
            logger.info("BEGIN: Logout Session Management")

            session_management_for_logout(
                access_token=request.auth,
                id_token=request.data["id_token"],
                meta=request.META,
                audit=request.audit,
                timestamp=timestamp,
                logger=logger,
            )
            logger.info("END: Logout Session Management")

            return Response("SUCCESS", status=status.HTTP_200_OK)
        except ValidationError as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN LOGOUT MANAGEMENT VALIDATION: {}".format(exc))
            return Response(exc.detail, status=status.HTTP_400_BAD_REQUEST)
        except Exception as exc:
            log_me(traceback.print_exc())
            log_me("EXCEPTION IN LOGOUT MANAGEMENT: {}".format(exc))
            return Response(str(exc), status=status.HTTP_400_BAD_REQUEST)


class TokenHealthCheck(APIView):
    def post(self, request):
        """
        Performs a health check on the token.

        * Almost a dummy API but with great purpose
        * Used to check if the token is valid or not
        * If the token is invalid (or blacklisted), then this method would never be called; and an invalid token exception would have been raised already
        * If reaching this method, means the token is valid and active

        Args:
            request (Request): The HTTP request object.

        Returns:
            Response: The HTTP response object.
        """
        logger = request.logger
        logger.info("Token Health Check Successful")
        return Response("ACTIVE", status=status.HTTP_200_OK)
