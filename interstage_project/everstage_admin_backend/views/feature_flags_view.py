import logging

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_admin_backend.services.feature_flags_service import (
    get_all_feature_flags_across_clients,
)
from interstage_project.auth_utils import requires_scope

logger = logging.getLogger(__name__)


class FeatureFlagView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_ADMINUI.value]),
        name="dispatch",
    )
    def get(self, request):
        logger.info("Getting all feature flags across clients")

        try:
            # Parse query params
            try:
                page = int(request.query_params.get("page", 1))
                page_size = int(request.query_params.get("page_size", 10))
                search_term = request.query_params.get("search_term", "")
            except ValueError:
                return Response(
                    {"error": "Invalid pagination parameters"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Fetch paginated feature flags
            feature_flags = get_all_feature_flags_across_clients(
                page=page, page_size=page_size, search_term=search_term
            )

            return Response(feature_flags, status=status.HTTP_200_OK)

        except Exception:
            logger.exception("Error getting all feature flags across clients")
            return Response(
                {"error": "Error getting all feature flags across clients"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
