from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

import everstage_admin_backend.services.add_user_to_oauth_service as auo
from commission_engine.accessors.client_accessor import (
    is_secure_admin_ui_auth0_user_mgmt_enabled_for_client,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope


class CreateUserInOauth(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        params = request.data
        client_id = params["client_id"]
        if is_secure_admin_ui_auth0_user_mgmt_enabled_for_client(client_id):
            return Response(
                {
                    "message": f"Secure AdminUI Auth0 User Management is enabled for client. | Client ID: {client_id}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        res = auo.add_user_to_oauth(params, request)
        if res["errors"]:
            return Response(res, status=status.HTTP_409_CONFLICT)
        return Response(res, status=status.HTTP_201_CREATED)
