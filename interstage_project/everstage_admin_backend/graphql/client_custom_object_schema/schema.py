# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test

import graphene

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectVariableAccessor,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required
from spm.graphql.custom_object_schema.schema import CustomObjectVariableType


class ClientCustomObjectQuery(object):
    client_variables_for_custom_object = graphene.List(
        CustomObjectVariableType,
        client_id=graphene.Int(required=True),
        custom_object_id=graphene.Int(required=True),
    )

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
            RbacPermissions.MANAGE_INTEGRATIONS.value,
        ]
    )
    def resolve_client_variables_for_custom_object(
        self, _, client_id: int, custom_object_id: int
    ):
        return list(
            CustomObjectVariableAccessor(client_id=client_id).all_variables_in_object(
                object_ids=[custom_object_id]
            )
        )
