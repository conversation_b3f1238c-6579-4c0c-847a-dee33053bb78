# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test, unused-import

import graphene
from django.db.models.functions import Lower
from graphene_django.types import DjangoObjectType

from commission_engine.models.client_models import Client
from commission_engine.utils.general_data import R<PERSON>c<PERSON>ermissions
from interstage_project.auth_utils import permission_required
from spm.graphql.client_schema.schema import ClientAdminType, ClientType


class ClientQuery(object):
    all_clients = graphene.List(ClientType, sort=graphene.String())
    all_client_admin_console = graphene.List(ClientAdminType)

    def resolve_all_clients(self, info, **kwargs):
        # Add sorting as conditional param
        sort = kwargs.get("sort", "asc")
        if sort == "asc":
            res = Client.objects.all().order_by(Lower("name"))
        else:
            res = Client.objects.all()
        return res

    @permission_required(
        [
            RbacPermissions.MANAGE_ADMINUI.value,
            RbacPermissions.MANAGE_AGENT_STUDIO.value,
            RbacPermissions.MANAGE_CUSTOMERS.value,
        ]
    )
    def resolve_all_client_admin_console(self, info, **kwargs):
        return Client.objects.all()
