import logging
from math import ceil

from commission_engine.accessors.client_accessor import (
    get_all_clients,
    get_client_features,
)

logger = logging.getLogger(__name__)


def get_all_feature_flags_across_clients(
    page: int = 1, page_size: int = 10, search_term: str = ""
):
    """
    Get all feature flags across all clients with pagination.
    Args:
        page (int): The page number (1-indexed).
        page_size (int): Number of clients per page.
    Returns:
        dict: {
            "page": current page,
            "page_size": page size,
            "total_clients": total client count,
            "total_pages": total pages,
            "results": {
                "<client_name> (<client_id>)": feature_flags,
                ...
            }
        }
    """
    result = {
        "page": page,
        "page_size": page_size,
        "total_clients": 0,
        "total_pages": 0,
        "results": {},
    }

    try:
        logger.info("Getting all clients")
        all_clients = get_all_clients()

        # Fetch the clients that match the search term
        clients = [
            client
            for client in all_clients
            if search_term.lower() in client.name.lower()
        ]

        # Paginate the clients (after filtering)
        start = (page - 1) * page_size
        end = start + page_size
        filtered_paginated_clients = clients[start:end]

        # Get the feature flags for each client
        client_ids_and_names = [
            (client.client_id, client.name) for client in filtered_paginated_clients
        ]

        logger.info(f"Returning page {page} with {len(client_ids_and_names)} clients")

        for client_id, client_name in client_ids_and_names:
            client_key = f"{client_name} ({client_id})"
            feature_flags = get_client_features(client_id)
            result["results"][client_key] = feature_flags

        result["total_clients"] = len(clients)
        result["total_pages"] = ceil(result["total_clients"] / page_size)

    except Exception:
        logger.exception("Error getting paginated feature flags across clients")

    return result
