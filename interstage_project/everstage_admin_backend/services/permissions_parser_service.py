import logging
from typing import Dict, List

from commission_engine.utils.general_data import RbacPermissions

logger = logging.getLogger(__name__)


class AdminUIPermissionsParser:
    """Parser for admin UI permissions"""

    def __init__(self, raw_permissions: List[str]):
        self.raw_permissions_set = set(raw_permissions)
        self._permission_checks = {}
        self._register_permission_checks()

    def _register_permission_checks(self):
        """Register all permission check methods"""
        self.register_permission_check(
            "is_auth0_users_manageable", self._check_manage_auth0_users
        )
        self.register_permission_check(
            "is_etl_status_manageable", self._check_manage_etl_status
        )
        self.register_permission_check(
            "is_integrations_manageable", self._check_manage_integrations
        )
        self.register_permission_check(
            "is_admin_ui_manageable", self._check_manage_admin_ui
        )
        self.register_permission_check(
            "is_customers_manageable", self._check_manage_customers
        )
        # Add more permission checks as needed

    def register_permission_check(self, name: str, check_func):
        """Register a permission check function"""
        self._permission_checks[name] = check_func

    def get_all_permissions(self) -> Dict[str, bool]:
        """Run all registered permission checks and return results"""
        results = {}
        failed_checks = []

        for name, check_func in self._permission_checks.items():
            try:
                results[name] = check_func()
            except Exception as e:
                failed_checks.append((name, str(e)))
                results[name] = False

        if failed_checks:
            logger.error(
                "Permission check failures: %s",
                ", ".join(f"{name}: {error}" for name, error in failed_checks),
            )

        return results

    def _check_manage_auth0_users(self) -> bool:
        """Check if user has permission to manage Auth0 users"""
        return bool(
            self.raw_permissions_set.intersection(
                {
                    RbacPermissions.MANAGE_ADMINUI_AUTH0_USERS.value,
                    RbacPermissions.MANAGE_ADMINUI.value,
                }
            )
        )

    def _check_manage_etl_status(self) -> bool:
        """Check if user has permission to manage ETL status"""
        return bool(
            self.raw_permissions_set.intersection(
                {
                    RbacPermissions.MANAGE_ETLSTATUS.value,
                    RbacPermissions.MANAGE_ADMINUI.value,
                }
            )
        )

    def _check_manage_integrations(self) -> bool:
        """Check if user has permission to manage integrations"""
        return bool(
            self.raw_permissions_set.intersection(
                {
                    RbacPermissions.MANAGE_INTEGRATIONS.value,
                    RbacPermissions.MANAGE_ADMINUI.value,
                }
            )
        )

    def _check_manage_admin_ui(self) -> bool:
        """Check if user has permission to manage admin UI"""
        return bool(
            self.raw_permissions_set.intersection(
                {RbacPermissions.MANAGE_ADMINUI.value}
            )
        )

    def _check_manage_customers(self) -> bool:
        """Check if user has permission to view customers"""
        return bool(
            self.raw_permissions_set.intersection(
                {
                    RbacPermissions.MANAGE_CUSTOMERS.value,
                    RbacPermissions.MANAGE_ADMINUI.value,
                }
            )
        )
