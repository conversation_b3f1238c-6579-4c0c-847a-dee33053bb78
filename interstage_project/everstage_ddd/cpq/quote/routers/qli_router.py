from django.db import transaction
from django.http import HttpResponse
from ninja import Router
from ninja.decorators import decorate_view

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.cpq.forms.everstage_pdf_adapter import transform_quote_data_for_pdf
from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)
from everstage_ddd.cpq.quote.schema import PhaseDetail
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector
from everstage_ddd.cpq.quote.service.quote_pdf_download_service import (
    download_quote_as_pdf,
    get_pdf_template_context,
)
from everstage_ddd.cpq.quote.utils import get_duration_label
from interstage_project.auth_utils import requires_scope
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor

from ..schema import (  # CloneProductsFromPhaseToPhasesPayload,; CloneProductsFromPhaseToPhasesResponse,
    ConfigurePricePayload,
    ConfigurePricesResponse,
    DownloadQuoteAsPdfPayload,
    EditPhasePayload,
    EditPhaseResponse,
    EditQuoteLineItemPayload,
    EditQuoteLineItemResponse,
    GetPdfTemplatePayload,
    GetPdfTemplateResponse,
    GetQuoteLineItemForPdfPayload,
    GetQuoteLineItemForPdfResponse,
    PricebookRecordPayload,
    PricebookRecordResponse,
    ShowQuoteLineItemPayload,
    ShowQuoteLineItemResponse,
    SubscriptionDetailsPayload,
    SubscriptionDetailsResponse,
)
from ..service.qli_phase_service import edit_phases, manage_phases
from ..service.quote_line_item_service import (  # build_basic_quote_line_item,; edit_phases,; manage_phases,
    create_quote_line_items,
    edit_quote_line_items,
    get_quote_summary,
    show_quote_line_items,
)
from ..service.quote_service import (
    configure_pricing,
    get_catalog_records,
    get_filtered_products_for_quote,
    update_subscription_details,
)

qli_router = Router(tags=["quote_line_items"])


@qli_router.post("get_pricebooks_records", response=PricebookRecordResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def get_catalog_records_for_pb(
    request,
    payload: PricebookRecordPayload,
) -> PricebookRecordResponse:
    client_id = request.client_id
    price_book_id = payload.pricebook_id
    filters = payload.filters
    context = payload.context
    query_params = {
        "offset": (payload.page_number - 1) * payload.page_size,
        "limit": payload.page_size,
        "search_term": payload.search_term,
        "added_skus": payload.added_skus,
    }
    response = get_filtered_products_for_quote(
        client_id, price_book_id, filters, query_params, context
    )
    return PricebookRecordResponse(**response)


@qli_router.post("subscription_details", response=SubscriptionDetailsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def modify_subscription_details(
    request, payload: SubscriptionDetailsPayload
) -> SubscriptionDetailsResponse:
    subscription_details_res, error = update_subscription_details(
        request.client_id,
        payload.quote_id,
        details={
            "start_date": payload.start_date,
            "end_date": payload.end_date,
            "duration_value": payload.duration_value,
            "duration_type": payload.duration_type,
        },
    )
    existing_phases = (
        [phase.model_dump() for phase in payload.phases] if payload.phases else []
    )
    phase_info = {"existing_phases": existing_phases}
    manage_phases_result = {}
    if subscription_details_res["change_type"]:
        phase_info["change_type"] = subscription_details_res["change_type"]
        phase_info["duration_value"] = payload.duration_value
        phase_info["duration_type"] = payload.duration_type
        phase_info["start_date"] = payload.start_date
        phase_info["end_date"] = payload.end_date
        manage_phases_result = manage_phases(
            request.client_id, payload.quote_id, phase_info
        )
        phase_info.update(manage_phases_result)
    if manage_phases_result and manage_phases_result.get("should_add_automatically"):
        phase_info["existing_phases"] = manage_phases_result["existing_phases"]
    if manage_phases_result and not manage_phases_result.get(
        "should_add_automatically"
    ):
        # if should_add_automatically is False, no need to add or remove phases
        phase_info["change_type"] = ""

    result = edit_phases(request.client_id, payload.quote_id, phase_info)
    summary, _ = get_quote_summary(request.client_id, payload.quote_id, use_kd=False)
    phases = show_quote_line_items(request.client_id, payload.quote_id, use_kd=False)[
        "phases"
    ]
    return SubscriptionDetailsResponse(
        status=result["status"], error=result["error"], summary=summary, phases=phases
    )


@qli_router.post("add_product", response=ConfigurePricesResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def add_product(request, payload: ConfigurePricePayload) -> ConfigurePricesResponse:
    client_id = request.client_id
    phase_id = payload.phase_id
    phase_name = payload.phase_name
    start_date = payload.start_date
    end_date = payload.end_date
    duration_type = payload.duration_type
    duration_value = payload.duration_value
    pricebook_id = payload.pricebook_id
    skus = payload.skus
    new_skus = payload.skus
    known_price_factors = payload.known_price_factors
    quote_id = payload.quote_id
    copy_flag = payload.copy_flag
    (sku_pricepoint_dict, sku_catalog_dict) = configure_pricing(
        client_id, quote_id, pricebook_id, skus, known_price_factors
    )
    phase_details = {
        "phase_id": phase_id,
        "start_date": start_date,
        "end_date": end_date,
        "phase_name": phase_name,
        "duration_type": duration_type,
        "duration_value": duration_value,
    }
    qli_accessor = QuoteLineItemAccessor(client_id)
    if not qli_accessor.does_quote_have_line_items(quote_id):
        update_subscription_details(client_id, quote_id, phase_details)
    line_item_dict, errors = create_quote_line_items(
        client_id,
        quote_id,
        new_skus,
        phase_details,
        sku_pricepoint_dict=sku_pricepoint_dict,
        sku_catalog_dict=sku_catalog_dict,
        copy_flag=copy_flag,
    )
    changed_phase_ids = list(line_item_dict.keys())
    summary, phase_totals = get_quote_summary(client_id, quote_id, use_kd=False)
    changed_phase_totals = {}
    for changed_id in changed_phase_ids:
        changed_phase_totals[changed_id] = phase_totals[changed_id]
    data = {
        "sku_pricepoint": sku_pricepoint_dict,
        "line_item": line_item_dict,
        "sub_total": changed_phase_totals,
        "summary": summary,
    }
    return ConfigurePricesResponse(data=data, header={}, errors=errors)


# @transaction.atomic
# def clone_products_from_phase_to_phases(
#     request, payload: CloneProductsFromPhaseToPhasesPayload
# ) -> CloneProductsFromPhaseToPhasesResponse:
#     from_phase_id = payload.from_phase_id
#     to_phase_ids = payload.to_phase_ids
#     product_skus = payload.product_skus
#     quote_id = payload.quote_id
#     quote_line_item_acc = QuoteLineItemAccessor(request.client_id)
#     quote_line_items = quote_line_item_acc.get_line_items_for_phase(
#         quote_id, from_phase_id
#     )
#     quote_line_items_to_insert = []
#     for quote_line_item in quote_line_items:
#         if quote_line_item.sku in product_skus:
#             for to_phase_id in to_phase_ids:
#                 quote_line_item.phase_id = to_phase_id
#                 result = build_basic_quote_line_item(quote_line_item)
#                 quote_line_items_to_insert.append(result)
#     quote_line_item_acc.insert_object(quote_line_items_to_insert)
#     summary, _ = get_quote_summary(request.client_id, quote_id, use_kd=False)
#     return CloneProductsFromPhaseToPhasesResponse(summary=summary)


@qli_router.post("edit", response=EditQuoteLineItemResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def edit_quote_lineitems(
    request, payload: EditQuoteLineItemPayload
) -> EditQuoteLineItemResponse:
    client_id = request.client_id
    quote_id = payload.quote_id
    change_type = payload.type
    phase_detail = payload.phase_detail
    changed_attribute = payload.changed_attribute
    copy_flag = payload.copy_flag
    is_bulk = payload.is_bulk

    (data, errors) = edit_quote_line_items(
        client_id,
        quote_id,
        changed_attribute,
        change_type=change_type,
        phase_detail=phase_detail.model_dump(),
        copy_flag=copy_flag,
        is_bulk=is_bulk,
    )
    changed_phase_ids = data.get("changed_phase_ids", [])
    (modified_phase_detail, summary, _) = get_quote_summary(
        client_id, quote_id, use_kd=False, phase_ids=changed_phase_ids
    )
    status = "success" if not errors else "error"
    modified_phase_details = [
        PhaseDetail(**modified_phase) for modified_phase in modified_phase_detail
    ]
    return EditQuoteLineItemResponse(
        status=status,
        phase_detail=modified_phase_details,
        summary=summary,
        errors=errors,
    )


@qli_router.post("get_pdf_template", response=GetPdfTemplateResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def get_pdf_template(request, payload: GetPdfTemplatePayload) -> GetPdfTemplateResponse:
    client_id = request.client_id
    quote_id = payload.quote_id
    template = get_pdf_template_context(client_id, quote_id)
    res = {"template": template}
    return GetPdfTemplateResponse(**res)


@qli_router.post("get_quote_lineitems_for_pdf")
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def get_quote_lineitems_for_pdf(
    request, payload: GetQuoteLineItemForPdfPayload
) -> GetQuoteLineItemForPdfResponse:
    client_id = request.client_id
    quote_id = payload.quote_id
    is_edit_mode = payload.is_edit
    date_format = "%b %d, %Y"
    currency_code_symbol_map = {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }

    quote = QuoteSelector(client_id).get_quote_by_id(quote_id)
    quote_currency = quote.quote_currency
    currency_symbol = currency_code_symbol_map.get(quote_currency, "$")
    res = show_quote_line_items(client_id, quote_id, use_kd=(not is_edit_mode))
    catalog_records = get_catalog_records(client_id)
    catalog_dict = {record["sku"]: record for record in catalog_records}
    quote_summary, phase_details = get_quote_summary(
        client_id, quote_id, use_kd=(not is_edit_mode)
    )
    res["start_date"] = (
        res["start_date"].strftime(date_format) if res["start_date"] else ""
    )
    res["end_date"] = res["end_date"].strftime(date_format) if res["end_date"] else ""

    pdf_data = transform_quote_data_for_pdf(
        res, phase_details, catalog_dict, currency_symbol
    )
    res["table_data"] = pdf_data
    res["duration_type"] = get_duration_label(
        res["duration_type"], res["duration_value"]
    )

    return GetQuoteLineItemForPdfResponse(**res)


@qli_router.post("download_quote_pdf", response=bytes)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def download_quote_pdf(request, payload: DownloadQuoteAsPdfPayload) -> bytes:
    client_id = request.client_id
    pdf_data = payload.pdf_data
    pdf_bytes = download_quote_as_pdf(client_id, pdf_data)
    response = HttpResponse(content_type="application/pdf")
    response["Content-Disposition"] = "attachment;"
    response.write(pdf_bytes)
    return response


@qli_router.post("show", response=ShowQuoteLineItemResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def show_quote_lineitems(
    request, payload: ShowQuoteLineItemPayload
) -> ShowQuoteLineItemResponse:
    client_id = request.client_id
    quote_id = payload.quote_id
    pricebook_id = payload.pricebook_id
    known_price_factors = payload.known_price_factors
    res = show_quote_line_items(client_id, quote_id)
    skus = set()
    for phase in res["phases"]:
        if "row_data" in phase:
            for li in phase["row_data"]:
                skus.add(li["sku"])
    (sku_pricepoint_dict, sku_catalog_dict) = configure_pricing(
        client_id, quote_id, pricebook_id, list(skus), known_price_factors
    )
    res["sku_pricepoint_dict"] = sku_pricepoint_dict
    res["summary"], _ = get_quote_summary(client_id, quote_id)
    return ShowQuoteLineItemResponse(**res)


@qli_router.post("update_phases", response=EditPhaseResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
@transaction.atomic
def edit_subscription_phase(request, payload: EditPhasePayload) -> EditPhaseResponse:
    client_id = request.client_id
    quote_id = payload.quote_id
    phase_info = {}
    existing_phases = []
    removed_phase_ids = payload.removed_phase_id
    new_phase = None
    if payload.existing_phases:
        existing_phases = [phase.model_dump() for phase in payload.existing_phases]
    if payload.new_phase:
        new_phase = payload.new_phase.model_dump()
    if removed_phase_ids and isinstance(removed_phase_ids, str):
        removed_phase_ids = [removed_phase_ids]
    phase_info["change_type"] = payload.type
    phase_info["existing_phases"] = existing_phases
    phase_info["removed_phase_id"] = removed_phase_ids
    phase_info["new_phase"] = [new_phase]
    res = edit_phases(client_id, quote_id, phase_info)
    summary, _ = get_quote_summary(client_id, quote_id, use_kd=False)
    phases = show_quote_line_items(client_id, quote_id, use_kd=False)["phases"]
    return EditPhaseResponse(
        status=res["status"], error=res["error"], phases=phases, summary=summary
    )
