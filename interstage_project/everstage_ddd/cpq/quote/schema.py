from datetime import datetime
from typing import Any, Dict, List, Literal, Optional
from uuid import UUID

from ninja import Schema
from pydantic import Field

from .types import QuoteDetailsItem, QuoteLineItemModel, QuoteListItem


class QuoteCreateOrClonePayload(Schema):
    request_type: Literal["new", "clone"]
    form_builder_id: UUID | None = None
    quote_id: UUID | None = None
    additional_details: dict | None = None


class QuoteSummary(Schema):
    list_quote_total: float
    net_quote_total: float
    recurring_total: float
    discount_amount: float


class PendingQuoteCountResponse(Schema):
    count: int


class QuoteListResponse(Schema):
    quotes: list[QuoteListItem]


class QuoteDeleteResponse(Schema):
    quote_id: UUID
    quote_status: UUID | None = None


class QuoteFilterPayload(Schema):
    status: Optional[str] = None
    created_by: Optional[str] = None
    opportunity_id: Optional[str] = None


class QuoteFilterResponse(Schema):
    quotes: list[QuoteListItem]


class QuoteSearchResponse(Schema):
    quotes: list[QuoteListItem]


class QuoteSearchPayload(Schema):
    search_term: str
    search_value: str


class QuoteDeletePayload(Schema):
    quote_id: UUID


class QuoteMarkPrimaryPayload(Schema):
    quote_id: UUID


class QuoteMarkPrimaryResponse(Schema):
    quote_id: UUID
    is_primary: bool


class ChangeQuoteStatusPayload(Schema):
    quote_id: UUID
    status: str


class ChangeQuoteStatusResponse(Schema):
    status: str


class PhaseDetail(Schema):
    phase_id: str
    start_date: datetime | str
    end_date: datetime | str | None
    phase_name: str
    row_data: List[QuoteLineItemModel]
    sub_total: Optional[float | None] = None


class PricebookRecordPayload(Schema):
    pricebook_id: str
    filters: Optional[Dict[str, Any]] = {}
    page_size: int = Field(default=20, gt=0)
    page_number: int = Field(default=0, ge=0)
    search_term: str = ""
    added_skus: Optional[List[str]] = None
    context: Optional[str] = None


class PricebookRecordResponse(Schema):
    data: List[Dict[str, Any]]


class SubscriptionDetailsPayload(Schema):
    quote_id: str
    start_date: datetime | str
    end_date: datetime | str | None
    duration_value: int | None
    duration_type: str
    phases: Optional[List[PhaseDetail]] = None


class SubscriptionDetailsResponse(Schema):
    status: str
    error: Optional[str] = None
    summary: Optional[QuoteSummary] = None
    phases: Optional[List[PhaseDetail]] = None


class ConfigurePricePayload(Schema):
    phase_id: str
    phase_name: str
    start_date: str | datetime
    duration_type: str
    duration_value: int | None | str
    end_date: str | None | datetime
    pricebook_id: str
    quote_id: str
    skus: List[str]
    known_price_factors: Optional[Dict[str, Any]] = None
    copy_flag: Optional[bool] = None


class ConfigurePricesResponse(Schema):
    data: Dict[str, Any]
    header: Dict[str, str]


class CloneProductsFromPhaseToPhasesPayload(Schema):
    from_phase_id: str
    to_phase_ids: List[str]
    product_skus: List[str]
    quote_id: str


class CloneProductsFromPhaseToPhasesResponse(Schema):
    summary: QuoteSummary


class CreateQuoteLineItemResponse(Schema):
    start_date: str
    end_date: str
    sku: str
    quantity: int
    list_unit_price: float
    net_unit_price: float
    discount: Optional[float] = None
    data: Optional[dict] = None
    pricebook_data: Optional[dict] = None


class CreateQuoteLineItemPayload(Schema):
    quote_id: str
    phase_id: str
    start_date: str
    end_date: str
    sku_pricepoint: Dict[str, Any]


class EditQuoteLineItemPayload(Schema):
    quote_id: str
    type: str
    phase_detail: PhaseDetail
    changed_attribute: Optional[str | None] = None
    copy_flag: Optional[bool] = None
    is_bulk: Optional[bool] = None


class EditQuoteLineItemResponse(Schema):
    status: str
    summary: QuoteSummary
    errors: Optional[dict] = None
    phase_detail: Optional[list[PhaseDetail]] = None


class GetQuoteResponse(Schema):
    form_spec: Dict[str, Any]


class GetQuoteDetailsResponse(Schema):
    quote_name: str
    quote_status: str
    quote_owner: str
    quote_owner_id: str
    quote_total: float
    form_id: str
    form_builder_id: str
    form_spec: Dict[str, Any]
    source_rule_fields: List[str]
    dependent_fields: Dict[str, List[str]]
    table_spec: Dict[int, Any]
    last_updated_at: datetime


class PublishQuotePayload(Schema):
    quote_id: str
    form_data: Dict[str, Any]
    form_spec: Dict[str, Any]
    comments: Optional[str]
    pdf_data: Dict[str, Any] = {}


class PublishQuoteResponse(Schema):
    status: str
    last_updated_time: datetime
    errors: Optional[dict] = None


class GetPdfTemplateResponse(Schema):
    template: Dict[str, Any]


class GetPdfTemplatePayload(Schema):
    quote_id: str


class GetQuoteLineItemForPdfResponse(Schema):
    start_date: str | None | datetime
    end_date: str | None | datetime
    duration_value: int | None
    duration_type: str
    is_arr_greater_than_threshold: bool | None = None
    phases: List[PhaseDetail]
    table_data: Dict[str, Any]


class GetQuoteLineItemForPdfPayload(Schema):
    quote_id: str
    is_edit: Optional[bool] = False


class DownloadQuoteAsPdfPayload(Schema):
    pdf_data: Dict[str, Any]


class ShowQuoteLineItemResponse(Schema):
    start_date: str | None | datetime
    end_date: str | None | datetime
    duration_value: int | None
    duration_type: str | None
    phases: List[PhaseDetail]
    sku_pricepoint_dict: Dict[str, Any]
    summary: QuoteSummary


class ShowQuoteLineItemPayload(Schema):
    quote_id: str
    pricebook_id: str
    known_price_factors: Optional[Dict[str, Any]] = None


class EditPhaseResponse(Schema):
    status: str
    error: Optional[str] = None
    phases: List[PhaseDetail]
    summary: Optional[QuoteSummary] = None


class EditPhasePayload(Schema):
    quote_id: str
    type: str
    existing_phases: Optional[List[PhaseDetail]] = None
    removed_phase_id: Optional[str] = None
    new_phase: Optional[PhaseDetail] = None


class ValidateAndPublishQuotePayload(Schema):
    quote_id: str
    form_builder_id: str
    form_data: Dict[str, Any]
    form_spec: Dict[str, Any]
    pdf_data: Dict[str, Any] = {}


class ValidateAndPublishQuoteResponse(Schema):
    status: str
    approval_cycles: Optional[List[Dict[str, Any]]] = None
    last_updated_time: Optional[datetime] = None
    errors: Optional[dict] = None


class ExitQuoteResponse(Schema):
    status: str


class ExitQuotePayload(Schema):
    quote_id: str
    form_id: str
    form_builder_id: str
    form_data: Dict[str, Any]


class QuoteListFilterRequest(Schema):
    limit_value: int
    offset_value: int
    search_term: Optional[str] = None
    status: Optional[list[str]] = None
    account_id: Optional[list[str]] = None
    owner_id: Optional[list[str]] = None
    approvals_waiting_on_you: bool = False


class AccountDetailsResponse(Schema):
    quote_status: list[dict]
    employee_details: dict
    account_details: list[dict]


class QuoteDetailsResponse(Schema):
    quotes: list[QuoteDetailsItem]
