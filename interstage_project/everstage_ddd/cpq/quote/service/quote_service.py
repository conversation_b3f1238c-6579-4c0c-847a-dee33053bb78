from logging import getLogger

import pandas as pd
from django.db import transaction
from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_settings
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    fetch_json_filtered_datasheet_data,
)
from commission_engine.utils.date_utils import (
    convert_str_to_date,
    end_of_day,
    make_aware_wrapper,
    start_of_day,
)
from everstage_ddd.cpq.approvals import (
    get_all_fields_for_approvals,
    get_approval_cycle_for_quote,
)
from everstage_ddd.cpq.enums import Status
from everstage_ddd.cpq.forms import (
    AutoSaveFormAccessor,
    EverstageFormAccessor,
    FormSpecSelector,
)
from everstage_ddd.cpq.quote.models.quote_models import Quote
from everstage_ddd.stormbreaker.utils import convert_to_final_data
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    ENTITY_KEY_DELIMETER,
)
from spm.services.approval_workflow_services.approval_instance_service_utils import (
    get_approval_wf_instances,
)

from ...price_book import (
    PriceBookDatasheetTableParams,
    PriceBookFactory,
    PriceBookStrategy,
)
from ...product_catalog import (
    ProductCatalogDatasheetTable,
    ProductCatalogDatasheetTableParams,
)
from ...product_catalog.accessors.pricepoint_accessor import PricePointAccessor
from ...product_catalog.constants import PRICE_BOOK_ID
from ...product_catalog.product_catalog_tables import (
    PricePointTable,
    PricePointTableParams,
    ProductCatalogTable,
    ProductCatalogTableParams,
)
from ...product_catalog.utils import get_status
from ..quote_status import QuoteStatusEnum
from ..selectors.quote_selector import QuoteSelector
from ..utils import get_table_variables

logger = getLogger(__name__)


def get_variables(client_id, datasheet_id):
    ds_vars = DatasheetVariableAccessor(client_id).get_objects_by_datasheet_id(
        datasheet_id=datasheet_id,
        projection=[
            "system_name",
            "display_name",
            "data_type_id",
            "tags",
            "field_order",
        ],
    )
    return ds_vars


def get_variable_tag_dict(variables):
    variable_tag_dict = {}
    for var in variables:
        if var["tags"]:
            tag = var["tags"].get("semantic")
            if tag:
                variable_tag_dict[tag] = var.get("system_name")
    return variable_tag_dict


def pb_variable_tag(client_id, pricebook_id=None):
    if not pricebook_id:
        client_settings = get_client_settings(client_id)
        pricebook_id = client_settings.get("cpq_settings", {}).get("pricebook_id")
    ds_vars = get_variables(client_id, pricebook_id)
    variable_tag_dict = get_variable_tag_dict(ds_vars)
    return variable_tag_dict


def cat_variable_tag(client_id, catalog_id=None):
    if not catalog_id:
        client_settings = get_client_settings(client_id)
        catalog_id = client_settings.get("cpq_settings", {}).get("catalog_id")
    ds_vars = get_variables(client_id, catalog_id)
    variable_tag_dict = get_variable_tag_dict(ds_vars)
    return variable_tag_dict


def get_pricebook_table_name(client_id, pricebook_id, type="datasheet"):
    if type == "datasheet":
        table = f"datasheet_data_{client_id}_{pricebook_id}"
    return table


def get_catalog_table_name(client_id, catalog_id, type="datasheet"):
    if type == "datasheet":
        table = f"datasheet_data_{client_id}_{catalog_id}"
    return table


def get_all_price_factors(client_id, pricebook_id):
    price_factors = []
    ds_vars = get_variables(client_id, pricebook_id)
    sku_var = None
    for var in ds_vars:
        tag = var.get("tags", {})
        marker_tag = tag.get("marker", [])
        semantic_tag = tag.get("semantic")
        if marker_tag and "price_factor" in tag:
            price_factors.append(var["system_name"])
        if semantic_tag == "sku":
            sku_var = var["system_name"]

    if sku_var:
        price_factors.append(sku_var)

    return price_factors


def get_pricebook_evertable_object(client_id, pricebook_ds_id, pricebook_db_id):
    pricebook_variables = get_table_variables(client_id, pricebook_ds_id)
    pricebook_ibis_table = PriceBookFactory.get_pricebook_instance(
        pricebook_type=PriceBookStrategy.DATASHEET,
        params=PriceBookDatasheetTableParams(
            client_id=client_id,
            table_columns=pricebook_variables,
            primary_key_column="row_key",
            unique_table_id=pricebook_ds_id,
            unique_schema_id=pricebook_db_id,
        ),
    )
    return pricebook_ibis_table


def get_pricebook_evertable_object_postgres(client_id):
    pricebook_ibis_table = PricePointTable(
        params=PricePointTableParams(
            client_id=client_id,
            primary_key_column="row_key",
        ),
    )
    return pricebook_ibis_table


def get_product_catalog_evertable_object(
    client_id, product_catalog_ds_id, product_catalog_db_id
):
    product_catalog_variables = get_table_variables(client_id, product_catalog_ds_id)
    product_catalog_ibis_table = ProductCatalogDatasheetTable(
        params=ProductCatalogDatasheetTableParams(
            client_id=client_id,
            table_columns=product_catalog_variables,
            primary_key_column="row_key",
            unique_table_id=product_catalog_ds_id,
            unique_schema_id=product_catalog_db_id,
        )
    )
    return product_catalog_ibis_table


def get_product_catalog_evertable_object_postgres(client_id):
    product_catalog_ibis_table = ProductCatalogTable(
        params=ProductCatalogTableParams(
            client_id=client_id,
            primary_key_column="row_key",
        ),
    )
    return product_catalog_ibis_table


def get_filtered_pricebook_product_catalog_records(
    client_id, params, query_params
) -> tuple[pd.DataFrame, int]:
    filters = params.get("filters")

    offset = query_params.get("offset")
    limit = query_params.get("limit")
    search_term = query_params.get("search_term")
    added_skus = query_params.get("added_skus", [])

    pricebook = get_pricebook_evertable_object_postgres(client_id)
    product_catalog = get_product_catalog_evertable_object_postgres(client_id)

    # fetching data from parquet files and processing using pandas API

    product_catalog_obj = product_catalog.get_operational_records_df()
    product_catalog_obj = product_catalog_obj.order_by("temporal_id")
    if offset and limit:
        product_catalog_obj = product_catalog_obj.limit(limit, offset=offset)
    product_catalog_df = product_catalog_obj.execute().reset_index(drop=True)

    pricebook_obj = pricebook.get_operational_records_df()
    pricebook_obj = pricebook_obj.filter(
        pricebook_obj.sku.isin(
            [
                str(product_id)
                for product_id in product_catalog_df["product_id"].tolist()
            ]
        )
    )
    pricebook_df = pricebook_obj.execute().reset_index(drop=True)

    # group pricebook by sku
    grouped_df = (
        pricebook_df.groupby("sku")
        .agg(price_count=("sku", "size"), min_price=("list_price", "min"))
        .reset_index()
    )

    # join grouped pricebook and product catalog on sku
    pc_pb_joined_df = pd.merge(product_catalog_df, grouped_df, on="sku", how="left")

    # removing the product that don't have any price points
    pc_pb_joined_df = pc_pb_joined_df[pc_pb_joined_df["price_count"].notnull()]

    # apply price factors filters
    if filters:
        for tag, value in filters.items():
            pc_pb_joined_df = pc_pb_joined_df[pc_pb_joined_df[tag] == value]

    # apply search filter on product name if search_term is provided
    if search_term:
        pc_pb_joined_df = pc_pb_joined_df[
            pc_pb_joined_df["name"].str.contains(search_term, case=False, na=False)
        ]

    # filter out records that are in the added_skus list
    if added_skus:
        pc_pb_joined_df = pc_pb_joined_df[~pc_pb_joined_df["sku"].isin(added_skus)]

    # get total count
    total_count = len(pc_pb_joined_df)

    return pc_pb_joined_df, total_count


def get_filtered_products_for_quote(
    client_id, price_book_id, filters, query_params, context
):
    client_settings = get_client_settings(client_id)
    pricebook_ds_id = client_settings.get("cpq_settings", {}).get("pricebook_id")
    price_book_db_id = client_settings.get("cpq_settings", {}).get("pricebook_db_id")
    product_catalog_ds_id = client_settings.get("cpq_settings", {}).get("catalog_id")
    product_catalog_db_id = client_settings.get("cpq_settings", {}).get("catalog_db_id")
    params = {}
    params["pricebook_ds_id"] = pricebook_ds_id
    params["pricebook_db_id"] = price_book_db_id
    params["product_catalog_ds_id"] = product_catalog_ds_id
    params["product_catalog_db_id"] = product_catalog_db_id
    params["filters"] = filters

    pricebook_product_catalog_df, _ = get_filtered_pricebook_product_catalog_records(
        client_id, params, query_params
    )
    result = convert_to_final_data(
        pricebook_product_catalog_df, as_dataframe=False, fetch_null_as_null=True
    )
    if context == "form_rules":
        pricepoints = PricePointAccessor(
            client_id, PRICE_BOOK_ID
        ).get_pricepoints_by_product_ids()
        product_price_point_map = {}
        for pp in pricepoints:
            if str(pp.product_id) not in product_price_point_map:
                product_price_point_map[str(pp.product_id)] = []
            product_price_point_map[str(pp.product_id)].append(
                {"pricepoint_id": str(pp.pricepoint_id), "name": pp.name}
            )
        for _data in result:
            _data["pricepoints"] = product_price_point_map.get(_data["sku"], [])

    response = {"data": result}
    return response


def group_tier_and_volume_data(
    pb_variable_tag_dict, price_factors_list, price_book_records
):
    pb_model_name = pb_variable_tag_dict["pricing_method"]
    data = {}
    tier_data_dict = {}
    volume_data_dict = {}
    for rec in price_book_records:
        key = "#_#".join([str(rec[pf]) for pf in price_factors_list])
        if rec[pb_model_name] in ("Tier", "Volume"):
            tier_data = {}
            tier_data["lower_bound"] = rec.get(pb_variable_tag_dict["lower_bound"])
            tier_data["upper_bound"] = rec.get(pb_variable_tag_dict["upper_bound"])
            tier_data["list_price"] = rec.get(pb_variable_tag_dict["list_price"])
            tier_data["unit_discount"] = rec.get(pb_variable_tag_dict["discount"])
            tier_data["flat_price"] = rec.get(pb_variable_tag_dict["flat_price"])
            tier_data["tier_name"] = "name"
            if rec[pb_model_name] == "Tier":
                if key not in tier_data_dict:
                    rec["tier_data"] = [tier_data]
                    tier_data_dict[key] = rec
                else:
                    rec = tier_data_dict[key]  # noqa: PLW2901
                    rec["tier_data"].append(tier_data)
            elif rec[pb_model_name] == "Volume":
                if key not in volume_data_dict:
                    rec["tier_data"] = [tier_data]
                    volume_data_dict[key] = rec
                else:
                    rec = volume_data_dict[key]  # noqa: PLW2901
                    rec["tier_data"].append(tier_data)
        data[key] = rec

    for rec in data.values():
        if "tier_data" in rec:
            rec["tier_data"].sort(key=lambda x: int(x["lower_bound"]))
    return list(data.values())


def get_price_book_records(
    client_id, pricebook_id, skus=None, price_factors=None, added_skus=None
):
    client_settings = get_client_settings(client_id)
    pricebook_ds_id = client_settings.get("cpq_settings", {}).get("pricebook_id")
    pricebook_db_id = client_settings.get("cpq_settings", {}).get("pricebook_db_id")
    pb_variable_tag_dict = pb_variable_tag(client_id, pricebook_id)
    price_factors_list = get_all_price_factors(client_id, pricebook_id)
    params_dict = {
        "client_id": client_id,
        "databook_id": pricebook_db_id,
        "datasheet_id": pricebook_ds_id,
        "logged_in_user_email": None,
        "page_size": 100,
    }
    filters = []

    # Filter for SKUs
    sku_filter = {
        "col_name": pb_variable_tag_dict["sku"],
        "operator": "IN",
        "value": added_skus if added_skus else skus,
    }
    if sku_filter["value"] and len(sku_filter["value"]) == 1:
        sku_filter["operator"] = "=="
        sku_filter["value"] = sku_filter["value"][0]
    filters.append(sku_filter)

    # Add price factor filters
    if price_factors:
        for col, value in price_factors.items():
            filters.append({"col_name": col, "operator": "==", "value": value})

    params_dict["filters"] = filters
    price_book_records = fetch_json_filtered_datasheet_data(
        params_dict, apply_datasheet_permissions=False
    )
    data = group_tier_and_volume_data(
        pb_variable_tag_dict, price_factors_list, price_book_records["data"]
    )
    return data


def get_catalog_records(client_id, skus=None):
    # client_settings = get_client_settings(client_id)
    # catalog_ds_id = client_settings.get("cpq_settings", {}).get("catalog_id")
    # catalog_db_id = client_settings.get("cpq_settings", {}).get("catalog_db_id")
    catalog = get_product_catalog_evertable_object_postgres(client_id)
    catalog_df = catalog.get_operational_records_df()
    if skus:
        catalog_df = catalog_df.filter(catalog_df.sku.isin(skus))
    catalog_pd_df = catalog_df.execute()
    catalog_records = convert_to_final_data(
        catalog_pd_df, as_dataframe=False, fetch_null_as_null=True
    )
    return catalog_records


def configure_pricing(
    client_id,
    quote_id,
    pricebook_id: str,
    skus: list,
    known_price_factors: dict | None = None,
):
    # client_settings = get_client_settings(client_id)
    # pricebook_ds_id = client_settings.get("cpq_settings", {}).get("pricebook_id")
    # pricebook_db_id = client_settings.get("cpq_settings", {}).get("pricebook_db_id")
    # product_catalog_ds_id = client_settings.get("cpq_settings", {}).get("catalog_id")
    # product_catalog_db_id = client_settings.get("cpq_settings", {}).get("catalog_db_id")

    pricebook = get_pricebook_evertable_object_postgres(client_id)
    product_catalog = get_product_catalog_evertable_object_postgres(client_id)

    pricebook_df = pricebook.get_operational_records_df()
    product_catalog_df = product_catalog.get_operational_records_df()

    product_catalog_df = product_catalog_df.filter(product_catalog_df.sku.isin(skus))
    pricebook_df = pricebook_df.filter(pricebook_df.sku.isin(skus))

    # apply price factors filters
    if known_price_factors:
        for column, value in known_price_factors.items():
            pricebook_df = pricebook_df.filter(pricebook_df[column] == value)

    pricebook_pd_df = pricebook_df.execute()
    pricebook_records = convert_to_final_data(
        pricebook_pd_df, as_dataframe=False, fetch_null_as_null=True
    )

    product_catalog_pd_df = product_catalog_df.execute()
    product_catalog_records = convert_to_final_data(
        product_catalog_pd_df, as_dataframe=False, fetch_null_as_null=True
    )

    # all_price_factors = pricebook.get_all_price_factors()

    sku_pricepoint_dict = {}
    for record in pricebook_records:
        key = record["sku"]
        if key not in sku_pricepoint_dict:
            sku_pricepoint_dict[key] = []
        # record["label"] = " - ".join([str(record.get(pf)) for pf in all_price_factors])
        record["label"] = record["name"]
        record["billing_frequency"] = record.get("billing_frequency", "").lower()
        record["original_list_price"] = record.get("list_price", None)
        record["original_flat_price"] = record.get("flat_price", None)
        if "tier_data" in record and record["tier_data"]:
            # add additional columns
            tier_list = []
            for tier in record["tier_data"]:
                tier_dict = {}
                tier_dict["list_flat_price"] = tier.get("flat_price") or 0
                tier_dict["original_list_flat_price"] = tier.get("flat_price") or 0
                tier_dict["net_flat_price"] = tier.get("flat_price") or 0
                tier_dict["flat_price_discount"] = 0
                tier_dict["list_unit_price"] = tier.get("list_price") or 0
                tier_dict["original_list_unit_price"] = tier.get("list_price") or 0
                tier_dict["net_unit_price"] = tier.get("list_price") or 0
                tier_dict["unit_price_discount"] = 0
                tier_dict["lower_bound"] = tier.get("lower_bound", None)
                tier_dict["upper_bound"] = tier.get("upper_bound", None)
                tier_list.append(tier_dict)
            record["tier_data"] = tier_list
            record["tier_data"].sort(key=lambda x: int(x["lower_bound"]))

        if (
            get_status(record["effective_start_date"], record["effective_end_date"])
            == "active"
        ):
            sku_pricepoint_dict[key].append(record)

    sku_catalog_dict = {}
    for record in product_catalog_records:
        record["billing_type"] = record.get("billing_type", "").lower()
        sku_catalog_dict[record["sku"]] = record

    return (sku_pricepoint_dict, sku_catalog_dict)


def resolve_quote_status(client_id, approval_wf_instance_ids):
    from ..quote_status import QuoteStatusContext

    filters = {
        "entity_type": APPROVAL_ENTITY_TYPES.QUOTE.value,
        "approval_wf_instance_id__in": approval_wf_instance_ids,
    }
    _final_stage_instances = get_approval_wf_instances(
        client_id,
        filters=filters,
        projection=["approval_wf_instance_id", "entity_key"],
    )
    if _final_stage_instances:
        for instance in _final_stage_instances:
            quote_id = instance["entity_key"].split(ENTITY_KEY_DELIMETER)[1]
            quote_status_context = QuoteStatusContext(client_id, quote_id)
            quote_status_context.process()


def compare_quote_subscription_details(existing_details, details):
    result = {}
    existing_value = 1
    current_value = details.get("duration_value")
    existing_duration_type = ""
    if existing_details:
        existing_value = existing_details.get("duration_value")
        existing_duration_type = existing_details.get("duration_type", "")
    is_same_type = existing_duration_type == details.get("duration_type")
    change_type = ""
    existing_value_in_months = existing_value
    current_value_in_months = current_value
    if existing_duration_type and existing_duration_type.lower() == "years":
        existing_value_in_months = existing_value * 12
    if details.get("duration_type").lower() == "years":
        current_value_in_months = current_value * 12

    if existing_value_in_months and current_value_in_months:
        if existing_value_in_months < current_value_in_months:
            change_type = "add"
        elif existing_value_in_months > current_value_in_months:
            change_type = "remove"

    result["is_same_type"] = is_same_type
    result["change_type"] = change_type
    return result


def update_subscription_details(client_id, quote_id, details):
    existing_quote = QuoteSelector(client_id).get_quote_by_id(quote_id)
    existing_details = {}
    if existing_quote:
        existing_details = {
            "duration_value": existing_quote.duration_value,
            "duration_type": existing_quote.duration_type,
        }
    result = compare_quote_subscription_details(existing_details, details)

    start_date = details.get("start_date")
    end_date = details.get("end_date")
    if start_date:
        start_date = convert_str_to_date(start_date)
        start_date = make_aware_wrapper(start_of_day(start_date))
    if end_date:
        end_date = convert_str_to_date(end_date)
        end_date = make_aware_wrapper(end_of_day(end_date))
    QuoteSelector(client_id).update_quote(
        quote_id,
        {
            "start_date": start_date,
            "end_date": end_date,
            "duration_value": details.get("duration_value"),
            "duration_type": details.get("duration_type"),
        },
    )
    # ////// check if 2 quotes valid quote exists
    valid_quotes = QuoteSelector(client_id).get_quote_for_quote_id_order_by_kd(quote_id)
    temp_id_to_delete = []

    if len(valid_quotes) > 1:
        temp_id_to_delete = [quote.temporal_id for quote in valid_quotes[:-1]]
        QuoteSelector(client_id).delete_quote_by_id_temporal_id(
            quote_id, temp_id_to_delete
        )
    return result, None


def get_quote_details(client_id, quote_id):
    from everstage_ddd.cpq.approvals import get_approvals_source_rule_fields
    from everstage_ddd.cpq.forms import (
        get_form_rules,
        get_rules_dependent_and_source_fields,
        prepare_form_spec,
    )

    from ..quote_status import QuoteStatusSelector

    quote_selector = QuoteSelector(client_id=client_id)
    quote = quote_selector.get_quote_by_id(quote_id=quote_id)
    form_id = quote.form_id
    form_object = EverstageFormAccessor(client_id).get_object(form_id=form_id)
    form_builder_id = form_object.form_builder_id

    response = {}
    everstage_rules = get_form_rules(
        client_id, form_builder_id, status=Status.ACTIVE.value
    )

    source_rule_fields, dependent_fields = get_rules_dependent_and_source_fields(
        everstage_rules
    )

    quote = QuoteSelector(client_id).get_quote_by_id(
        quote_id, projection=["quote_name", "form_id", "owner_id", "net_quote_total"]
    )
    form_id = quote["form_id"]
    quote_name = quote["quote_name"]

    quote_owner_id = quote["owner_id"]
    quote_total = quote["net_quote_total"]
    employee_accessor = EmployeeAccessor(client_id=client_id)
    quote_owner = employee_accessor.get_employee_by_email_id(quote["owner_id"])

    quote_status = QuoteStatusSelector(client_id).get_quote_status(
        quote_id, projection=["status"]
    )

    form_object = EverstageFormAccessor(client_id).get_object(form_id=form_id)
    form_builder_id = form_object.form_builder_id

    approval_source_fields = get_approvals_source_rule_fields(
        client_id, form_builder_id=form_builder_id
    )

    last_updated_at = None

    final_form_spec = {}
    if quote_status and quote_status["status"] == QuoteStatusEnum.DRAFT.value:
        auto_save_form = AutoSaveFormAccessor(client_id).get_last_saved_form(
            form_id, projection=["form_data", "knowledge_begin_date"]
        )
        auto_save_form_data = auto_save_form["form_data"]
        form_spec_change = FormSpecSelector(client_id).get_form_spec_change_by_id(
            form_id, projection=["form_spec"]
        )
        form_spec = form_spec_change["form_spec"]
        form_spec = prepare_form_spec(auto_save_form_data, form_spec)
        final_form_spec = form_spec
        last_updated_at = auto_save_form["knowledge_begin_date"]
    else:
        form_object = EverstageFormAccessor(client_id).get_object(form_id=form_id)
        final_form_spec = form_object.form_spec  # send form_id, form_builder_id
        last_updated_at = form_object.knowledge_begin_date

    response = {
        "form_id": str(form_id),
        "quote_name": quote_name,
        "quote_status": quote_status["status"] if quote_status else None,
        "quote_owner": (f"{quote_owner[0].first_name}" if quote_owner else ""),
        "quote_owner_id": quote_owner_id,
        "quote_total": float(quote_total) if quote_total is not None else 0.0,
        "form_builder_id": str(form_builder_id),
        "form_spec": final_form_spec,
        "source_rule_fields": list(set(source_rule_fields + approval_source_fields)),
        "dependent_fields": dependent_fields,
        "table_spec": {},
        "last_updated_at": last_updated_at,
    }

    return response


@transaction.atomic
def publish_quote(client_id, quote_id, form_data, form_spec, **kwargs):
    from commission_engine.accessors.client_accessor import is_notification_v2
    from everstage_ddd.cpq.forms import prepare_form_spec
    from everstage_ddd.cpq.forms.service.form_service import validate_form_data
    from everstage_ddd.cpq.notifications.notification_infra.cpq_quote_notifications.utils import (
        notify_quote_updated,
    )

    from ..accessors.quote_line_item_accessor import QuoteLineItemAccessor
    from ..quote_status import QuoteStatusContext
    from .quote_form_service import mapping_quote_form_to_quote

    logger.info(f"BEGIN: Publishing quote {quote_id}")

    logged_in_user = kwargs.get("logged_in_user")
    additional_details = kwargs.get("additional_details")
    comments = kwargs.get("comments")
    current_time = timezone.now()

    quote: Quote = QuoteSelector(client_id).get_quote_by_id(quote_id)

    form_id = quote.form_id
    autosave_update_data = {
        "form_data": form_data,
        "additional_details": additional_details,
    }

    updated_form_spec = prepare_form_spec(form_data, form_spec)

    errors = validate_form_data(client_id, form_data, quote_id)

    qli_accessor = QuoteLineItemAccessor(str(client_id))
    if not qli_accessor.does_quote_have_line_items(quote_id):
        # need to change the static field name to a dynamic one
        errors["field38"] = "Add at least one product to the quote before publishing"
    if qli_accessor.check_qli_without_pricepoints(quote_id):
        # need to change the static field name to a dynamic one
        errors["field38"] = (
            "All products need to have their price set before publishing"
        )

    if errors:
        return current_time, errors

    AutoSaveFormAccessor(client_id).update_auto_saved_form(
        form_id,
        autosave_update_data,
        knowledge_date=current_time,
    )
    form_update_data = {
        "form_spec": updated_form_spec,
        "form_data": form_data,
        "additional_details": additional_details,
    }
    updated_form = EverstageFormAccessor(client_id).update_form(
        form_id, form_update_data, knowledge_date=current_time
    )
    form_builder_id = updated_form.form_builder_id
    mapping_quote_form_to_quote(
        quote_id,
        updated_form,
        additional_details,
        pdf_data=kwargs.get("pdf_data"),
        knowledge_date=current_time,
    )

    # TODO: add some validation before moving to next state.
    approval_params = {
        "form_id": updated_form.form_id,
        "quote_id": quote_id,
        "form_data": updated_form.form_data,
        "logged_in_user": logged_in_user,
        "form_builder_id": form_builder_id,
        "comments": comments,
    }
    quote_status_context = QuoteStatusContext(
        client_id, quote_id, knowledge_date=current_time
    )
    quote_status_context.process(approval_params=approval_params)

    # sending notification if quote was edited by someone other than the owner
    if quote.owner_id != logged_in_user and is_notification_v2(client_id):
        notify_quote_updated(client_id, quote_id=quote_id, updated_by=logged_in_user)

    logger.info("END: Publishing quote.")
    return current_time, None


def validate_quote_for_approval_instance(client_id, logged_in_user, **kwargs):
    logger.info(f"BEGIN: Validating quote for approval {kwargs['quote_id']}")
    quote_id = kwargs["quote_id"]
    form_builder_id = kwargs["form_builder_id"]
    form_data = kwargs["form_data"]

    fields_df = get_all_fields_for_approvals(
        client_id,
        logged_in_user,
        None,
        quote_id=quote_id,
        form_data=form_data,
    )

    response = get_approval_cycle_for_quote(
        client_id=client_id,
        logged_in_user=logged_in_user,
        form_builder_id=form_builder_id,
        fields_df=fields_df,
        quote_id=quote_id,
    )

    logger.info(f"END: Validating quote for approval {kwargs['quote_id']}")
    return response
