test_visible_section_input = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section1"],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Address",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1", "field8"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field8": {
                    "id": "field8",
                    "label": "Billing address same as shipping address",
                    "label_placement": "right",
                    "help_text": "",
                    "field_type": "checkbox",
                    "data_type": "boolean",
                    "value": True,
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
            "sub_section_order": ["sub_section1", "sub_section2"],
            "sub_sections": {
                "sub_section1": {
                    "id": "sub_section1",
                    "label": "Shipping Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field2",
                    ],
                    "fields": {
                        "field2": {
                            "id": "field2",
                            "label": "Flat",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_line",
                            "data_type": "string",
                            "value": "",
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
                "sub_section2": {
                    "id": "sub_section2",
                    "label": "Billing Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field9",
                    ],
                    "fields": {
                        "field9": {
                            "id": "field9",
                            "label": "Flat",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_line",
                            "data_type": "string",
                            "value": "",
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
            },
        },
    },
}

test_visible_section_result = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section1"],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Address",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1", "field8"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field8": {
                    "id": "field8",
                    "label": "Billing address same as shipping address",
                    "label_placement": "right",
                    "help_text": "",
                    "field_type": "checkbox",
                    "data_type": "boolean",
                    "value": True,
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
            "sub_section_order": ["sub_section1", "sub_section2"],
            "sub_sections": {
                "sub_section1": {
                    "id": "sub_section1",
                    "label": "Shipping Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field2",
                    ],
                    "fields": {
                        "field2": {
                            "id": "field2",
                            "label": "Flat",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_line",
                            "data_type": "string",
                            "value": "",
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
                "sub_section2": {
                    "id": "sub_section2",
                    "label": "Billing Address",
                    "help_text": "",
                    "is_hidden": True,
                    "field_order": [
                        "field9",
                    ],
                    "fields": {
                        "field9": {
                            "id": "field9",
                            "label": "Flat",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_line",
                            "data_type": "string",
                            "value": "",
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
            },
        },
    },
}

test_options_ups_dhl_input = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section1", "section2", "section3"],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Address",
            "help_text": "",
            "is_hidden": False,
            "field_order": [],
            "fields": {},
            "sub_section_order": ["sub_section1"],
            "sub_sections": {
                "sub_section1": {
                    "id": "sub_section1",
                    "label": "Shipping Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field6",
                    ],
                    "fields": {
                        "field6": {
                            "id": "field6",
                            "label": "Country",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_select",
                            "data_type": "string",
                            "value": "INDIA",
                            "options": [
                                {"label": "India", "value": "INDIA"},
                                {"label": "USA", "value": "US"},
                                {"label": "UK", "value": "UK"},
                            ],
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
            },
        },
        "section2": {
            "id": "section2",
            "label": "Parcel Details",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1", "field16", "field25"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field16": {
                    "id": "field16",
                    "label": "Service Provider",
                    "label_placement": "top",
                    "help_text": "Choose any one",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "options": [
                        {"label": "FedEx", "value": "fedex"},
                        {"label": "UPS", "value": "ups"},
                        {"label": "DHL", "value": "dhl"},
                    ],
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
            "sub_section_order": [],
            "sub_sections": {},
        },
    },
}

test_options_ups_dhl_result = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section1", "section2", "section3"],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Address",
            "help_text": "",
            "is_hidden": False,
            "field_order": [],
            "fields": {},
            "sub_section_order": ["sub_section1"],
            "sub_sections": {
                "sub_section1": {
                    "id": "sub_section1",
                    "label": "Shipping Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field6",
                    ],
                    "fields": {
                        "field6": {
                            "id": "field6",
                            "label": "Country",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_select",
                            "data_type": "string",
                            "value": "INDIA",
                            "options": [
                                {"label": "India", "value": "INDIA"},
                                {"label": "USA", "value": "US"},
                                {"label": "UK", "value": "UK"},
                            ],
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
            },
        },
        "section2": {
            "id": "section2",
            "label": "Parcel Details",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1", "field16", "field25"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field16": {
                    "id": "field16",
                    "label": "Service Provider",
                    "label_placement": "top",
                    "help_text": "Choose any one",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "options": [
                        {"label": "UPS", "value": "ups"},
                        {"label": "DHL", "value": "dhl"},
                    ],
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
            "sub_section_order": [],
            "sub_sections": {},
        },
    },
}

test_options_fedx_input = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section1", "section2", "section3"],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Address",
            "help_text": "",
            "is_hidden": False,
            "field_order": [],
            "fields": {},
            "sub_section_order": ["sub_section1"],
            "sub_sections": {
                "sub_section1": {
                    "id": "sub_section1",
                    "label": "Shipping Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field6",
                    ],
                    "fields": {
                        "field6": {
                            "id": "field6",
                            "label": "Country",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_select",
                            "data_type": "string",
                            "value": "US",
                            "options": [
                                {"label": "India", "value": "INDIA"},
                                {"label": "USA", "value": "US"},
                                {"label": "UK", "value": "UK"},
                            ],
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
            },
        },
        "section2": {
            "id": "section2",
            "label": "Parcel Details",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1", "field16", "field25"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field16": {
                    "id": "field16",
                    "label": "Service Provider",
                    "label_placement": "top",
                    "help_text": "Choose any one",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "options": [
                        {"label": "FedEx", "value": "fedex"},
                        {"label": "UPS", "value": "ups"},
                        {"label": "DHL", "value": "dhl"},
                    ],
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
            "sub_section_order": [],
            "sub_sections": {},
        },
    },
}

test_options_fedx_result = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section1", "section2", "section3"],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Address",
            "help_text": "",
            "is_hidden": False,
            "field_order": [],
            "fields": {},
            "sub_section_order": ["sub_section1"],
            "sub_sections": {
                "sub_section1": {
                    "id": "sub_section1",
                    "label": "Shipping Address",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field6",
                    ],
                    "fields": {
                        "field6": {
                            "id": "field6",
                            "label": "Country",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "single_select",
                            "data_type": "string",
                            "value": "US",
                            "options": [
                                {"label": "India", "value": "INDIA"},
                                {"label": "USA", "value": "US"},
                                {"label": "UK", "value": "UK"},
                            ],
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                },
            },
        },
        "section2": {
            "id": "section2",
            "label": "Parcel Details",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1", "field16", "field25"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field16": {
                    "id": "field16",
                    "label": "Service Provider",
                    "label_placement": "top",
                    "help_text": "Choose any one",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "options": [
                        {"label": "FedEx", "value": "fedex"},
                    ],
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
            "sub_section_order": [],
            "sub_sections": {},
        },
    },
}

total_price_formula = [
    {
        "token_type": "FORM_VARIABLES",
        "token": {
            "system_name": "field17",
            "key": "field17",
            "name": "field17",
            "data_type": "Integer",
        },
    },
    {"token_type": "OPERATORS", "token": {"name": "*", "key": "*"}},
    {
        "token_type": "FORM_VARIABLES",
        "token": {
            "system_name": "field18",
            "key": "field18",
            "name": "field18",
            "data_type": "Integer",
        },
    },
]

test_total_price_formula_input = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section3"],
    "sections": {
        "section3": {
            "id": "section3",
            "label": "Additional Details",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
            },
            "sub_section_order": ["sub_section3"],
            "sub_sections": {
                "sub_section3": {
                    "id": "sub_section3",
                    "label": "Order Details",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field17",
                        "field18",
                        "field24",
                    ],
                    "fields": {
                        "field17": {
                            "id": "field17",
                            "label": "Price",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "currency",
                            "data_type": "number",
                            "value": 5,
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                        "field18": {
                            "id": "field18",
                            "label": "Quantity",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "number",
                            "data_type": "number",
                            "value": 4,
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                        "field24": {
                            "id": "field24",
                            "label": "Total Price",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "currency",
                            "data_type": "number",
                            "formula": total_price_formula,
                            "value": "",
                            "properties": {
                                "is_mandatory": False,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                }
            },
        },
    },
    "page_author": "<EMAIL>",
    "other_page_properties": {},
}

test_total_price_formula_result = {
    "label": "Address Form (Sample 1)",
    "section_order": ["section3"],
    "sections": {
        "section3": {
            "id": "section3",
            "label": "Additional Details",
            "help_text": "",
            "is_hidden": False,
            "field_order": ["field1"],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
            },
            "sub_section_order": ["sub_section3"],
            "sub_sections": {
                "sub_section3": {
                    "id": "sub_section3",
                    "label": "Order Details",
                    "help_text": "",
                    "is_hidden": False,
                    "field_order": [
                        "field17",
                        "field18",
                        "field24",
                    ],
                    "fields": {
                        "field17": {
                            "id": "field17",
                            "label": "Price",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "currency",
                            "data_type": "number",
                            "value": 5,
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                        "field18": {
                            "id": "field18",
                            "label": "Quantity",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "number",
                            "data_type": "number",
                            "value": 4,
                            "properties": {
                                "is_mandatory": True,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                        "field24": {
                            "id": "field24",
                            "label": "Total Price",
                            "label_placement": "top",
                            "help_text": "",
                            "field_type": "currency",
                            "data_type": "number",
                            "formula": total_price_formula,
                            "value": 20,
                            "properties": {
                                "is_mandatory": False,
                                "is_hidden": False,
                                "is_read_only": False,
                            },
                        },
                    },
                }
            },
        },
    },
    "page_author": "<EMAIL>",
    "other_page_properties": {},
}
