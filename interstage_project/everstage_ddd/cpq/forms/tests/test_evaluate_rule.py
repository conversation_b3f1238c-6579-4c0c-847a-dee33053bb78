import pytest

from everstage_ddd.cpq.forms import evaluate_form_spec
from everstage_ddd.cpq.forms.condition import rule_spec
from everstage_ddd.cpq.forms.tests.rule_data_fixture import (
    test_options_fedx_input,
    test_options_fedx_result,
    test_options_ups_dhl_input,
    test_options_ups_dhl_result,
    test_total_price_formula_input,
    test_total_price_formula_result,
    test_visible_section_input,
    test_visible_section_result,
)

CLIENT_ID = 9007
form_builder_id = "f7c3c817-2a5c-4a88-94f1-fae970bd7395"


@pytest.mark.django_db
@pytest.mark.parametrize(
    "curr_form, expected_form",
    [
        (test_visible_section_input, test_visible_section_result),
    ],
)
def test_evaluate_visible_rule(curr_form, expected_form):
    visible_rule = [rule_spec[0]]
    new_form_spec = evaluate_form_spec(
        CLIENT_ID, curr_form, visible_rule, form_builder_id
    )
    assert new_form_spec == expected_form


@pytest.mark.django_db
@pytest.mark.parametrize(
    "curr_form, expected_form",
    [
        (test_options_ups_dhl_input, test_options_ups_dhl_result),
        (test_options_fedx_input, test_options_fedx_result),
    ],
)
def test_evaluate_options_rule(curr_form, expected_form):
    visible_rule = [rule_spec[1]]
    new_form_spec = evaluate_form_spec(
        CLIENT_ID, curr_form, visible_rule, form_builder_id
    )
    assert new_form_spec == expected_form


@pytest.mark.django_db
@pytest.mark.parametrize(
    "curr_form, expected_form",
    [
        (test_total_price_formula_input, test_total_price_formula_result),
    ],
)
def test_evaluate_value_and_formula_rule(curr_form, expected_form):
    visible_rule = [rule_spec[2]]
    new_form_spec = evaluate_form_spec(
        CLIENT_ID, curr_form, visible_rule, form_builder_id
    )
    assert new_form_spec == expected_form
