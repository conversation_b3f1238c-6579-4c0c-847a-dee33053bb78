from collections import defaultdict
from copy import deepcopy

freq_map = {
    "one-time": "",
    "daily": "d",
    "monthly": "mo",
    "quarterly": "qtr",
    "half-yearly": "hy",
    "annual": "yr",
}

DARK_BLUE = "#002855"
HEADER_STYLE = {
    "backgroundColor": DARK_BLUE,
    "color": "white",
    "fontWeight": "bold",
    "textAlign": "left",
}

COL_STYLE = {
    "services": {
        "maxWidth": "140px",
        "wordWrap": "break-word",
    },
    "type": {"maxWidth": "90px", "wordWrap": "break-word"},
    "qty": {"maxWidth": "70px", "textAlign": "right"},
    "tier": {"maxWidth": "82px"},
    "from": {"maxWidth": "70px", "textAlign": "right"},
    "to": {"maxWidth": "70px", "textAlign": "right"},
    "price": {"maxWidth": "95px", "textAlign": "right"},
    "approved_price": {
        "maxWidth": "95px",
        "textAlign": "right",
    },
    "total": {
        "maxWidth": "95px",
        "textAlign": "right",
    },
}

header_part1 = [
    {
        "content": "Services",
        "styles": {**HEADER_STYLE, **COL_STYLE["services"]},
        "colspan": 1,
    },
    {"content": "Type", "styles": {**HEADER_STYLE, **COL_STYLE["type"]}, "colspan": 1},
    {"content": "Qty", "styles": {**HEADER_STYLE, **COL_STYLE["qty"]}, "colspan": 1},
]

tier_part = [
    {"content": "Tier", "styles": {**HEADER_STYLE, **COL_STYLE["tier"]}, "colspan": 1},
    # {"content": "From", "styles": {**HEADER_STYLE, **COL_STYLE["from"]}, "colspan": 1},
    # {"content": "To", "styles": {**HEADER_STYLE, **COL_STYLE["to"]}, "colspan": 1},
]

header_part2 = [
    {
        "content": "Price",
        "styles": {**HEADER_STYLE, **COL_STYLE["price"]},
        "colspan": 1,
    },
    {
        "content": "Approved Price",
        "styles": {**HEADER_STYLE, **COL_STYLE["approved_price"]},
        "colspan": 1,
    },
    {
        "content": "Total",
        "styles": {**HEADER_STYLE, **COL_STYLE["total"]},
        "colspan": 1,
    },
]

pricing_header = [
    {
        "content": "Pricing Type",
        "styles": {**HEADER_STYLE, **COL_STYLE["services"]},
        "colspan": 1,
    },
]


def format_currency(value, currency_symbol):
    if value != 0 and not value:
        value = "-"
    if value == "-":
        return value
    try:
        value = round(float(value), 2)
        if value.is_integer():
            return f"{currency_symbol}{value:,.0f}"
        return f"{currency_symbol}{value:,.2f}"
    except ValueError:
        return value


def format_per_item(
    unit_price, flat_price, currency_symbol, billing_frequency_formatted
):
    if unit_price and unit_price != "-":
        if flat_price and flat_price != "-":
            formatted_list_price = (
                str(format_currency(unit_price, currency_symbol))
                + billing_frequency_formatted
                + "\n + "
                + str(format_currency(flat_price, currency_symbol))
                + billing_frequency_formatted
                + " flat"
            )
        else:
            formatted_list_price = (
                str(format_currency(unit_price, currency_symbol))
                + billing_frequency_formatted
            )
    elif flat_price and flat_price != "-":
        formatted_list_price = (
            str(format_currency(flat_price, currency_symbol))
            + billing_frequency_formatted
            + " flat"
        )
    else:
        formatted_list_price = str(format_currency(unit_price, currency_symbol))

    return formatted_list_price


def convert_to_table_format(  # noqa: PLR0912, PLR0915
    tables, phase_details, catalog_dict, currency_symbol
):
    table_data = {}
    pricing_data = defaultdict(lambda: defaultdict(float))

    for table in tables:
        phase = table.get("phase_name", "Phase")
        if len(tables) == 1:
            phase = ""

        has_tier = any(
            row.get("custom_data", {}).get("tier_allocations")
            for row in table.get("row_data", {})
            if row.get("custom_data", {})
        )
        if has_tier:
            table_data[phase] = [header_part1 + tier_part + header_part2]
        else:
            table_data[phase] = [header_part1 + header_part2]

        for row in table.get("row_data", []):
            sub_rows = []
            tier_details = {}
            sku = row.get("sku", "")
            catalog_record = catalog_dict.get(sku, {})
            uom = catalog_record.get("uom", "")
            custom_data = row.get("custom_data", {})
            pricepoint_data = row.get("pricepoint_data", {})
            billing_frequency = freq_map.get(
                pricepoint_data.get("billing_frequency", ""), ""
            )
            billing_frequency_formatted = (
                " /" + billing_frequency if billing_frequency else ""
            )
            # pricing_method = pricepoint_data.get("pricing_method", "")
            tier_details = (
                custom_data.get("tier_allocations", {}) if custom_data else {}
            )
            billing_type = catalog_record.get("billing_type", "-").lower()

            if pricepoint_data:
                tier_meta_data = pricepoint_data.get("tier_data", {})
                show_future_tiers = pricepoint_data.get("show_future_tiers", False)
                tier_details_dict = {
                    tier_details[tier].get("id", ""): tier_details[tier]
                    for tier in tier_details
                }
                if show_future_tiers:
                    for tier in tier_meta_data:
                        tier_id = tier.get("id", "")
                        if tier_id not in tier_details_dict:
                            tier["name"] = tier.get("tier_name", "")
                            tier_details[tier_id] = tier

            product_name = row.get("product_name", "-")
            qty = str(row.get("quantity", "-"))
            list_price = row.get("list_unit_price", "-")
            flat_list_price = row.get("list_flat_price", "-")
            approved_price = row.get("net_unit_price", "-")
            flat_approved_price = row.get("net_flat_price", "-")
            net_price = row.get("prorated_net_total", "-")
            pricing_data[phase][billing_type] += float(net_price) if net_price else 0

            row_part_1 = [
                {
                    "content": product_name,
                    "styles": COL_STYLE["services"],
                    "colspan": 1,
                },
                {
                    "content": uom,
                    "styles": COL_STYLE["type"],
                    "colspan": 1,
                },
                {
                    "content": qty,
                    "styles": COL_STYLE["qty"],
                    "colspan": 1,
                },
            ]

            row_part_2 = [
                {
                    "content": format_per_item(
                        list_price,
                        flat_list_price,
                        currency_symbol,
                        billing_frequency_formatted,
                    ),
                    "styles": COL_STYLE["price"],
                    "colspan": 1,
                },
                {
                    "content": format_per_item(
                        approved_price,
                        flat_approved_price,
                        currency_symbol,
                        billing_frequency_formatted,
                    ),
                    "styles": COL_STYLE["approved_price"],
                    "colspan": 1,
                },
                {
                    "content": str(format_currency(net_price, currency_symbol)),
                    "styles": COL_STYLE["total"],
                    "colspan": 1,
                },
            ]

            if has_tier:
                tier_data = [
                    {"content": "-", "styles": COL_STYLE["tier"], "colspan": 1},
                    # {
                    #     "content": "0" if tier_details else "-",
                    #     "styles": COL_STYLE["from"],
                    #     "colspan": 1,
                    # },
                    # {
                    #     "content": "0" if tier_details else "-",
                    #     "styles": COL_STYLE["to"],
                    #     "colspan": 1,
                    # },
                ]
                for tier in tier_details.values():
                    sub_row_part_1 = deepcopy(row_part_1)
                    sub_row_part_2 = deepcopy(row_part_2)
                    sub_tier_data = deepcopy(tier_data)

                    sub_qty = str(tier.get("quantity", "-"))
                    sub_list_price = tier.get("list_unit_price", "-")
                    sub_flat_list_price = tier.get("list_flat_price", "-")
                    sub_net_price = tier.get("net_unit_price", "-")
                    sub_flat_net_price = tier.get("net_flat_price", "-")
                    sub_total = tier.get("prorated_net_total", "0")

                    sub_row_part_1[2]["content"] = sub_qty
                    sub_row_part_2[0]["content"] = format_per_item(
                        sub_list_price,
                        sub_flat_list_price,
                        currency_symbol,
                        billing_frequency_formatted,
                    )

                    sub_row_part_2[1]["content"] = format_per_item(
                        sub_net_price,
                        sub_flat_net_price,
                        currency_symbol,
                        billing_frequency_formatted,
                    )

                    sub_row_part_2[2]["content"] = str(
                        format_currency(sub_total, currency_symbol)
                    )

                    # tier_name = tier.get("name", "-")
                    upper_bound = tier.get("upper_bound", "-")
                    lower_bound = str(tier.get("lower_bound", "-"))
                    if (
                        not upper_bound
                        or upper_bound == "-"
                        or (
                            isinstance(upper_bound, int)
                            and upper_bound > 9999  # noqa: PLR2004
                        )
                    ):
                        upper_bound = ""

                    sub_tier_data[0]["content"] = lower_bound + (
                        " - " + str(upper_bound) if upper_bound else "+"
                    )

                    # sub_tier_data[1]["content"] = lower_bound
                    # sub_tier_data[2]["content"] = str(upper_bound)
                    sub_rows.append(sub_row_part_1 + sub_tier_data + sub_row_part_2)
                if not sub_rows:
                    sub_rows.append(row_part_1 + tier_data + row_part_2)
            else:
                sub_rows.append(row_part_1 + row_part_2)

            table_data[phase] += sub_rows

        total_row = [
            {
                "content": "Total",
                "styles": {
                    "fontWeight": "bold",
                    "backgroundColor": "#E5E7EB",
                },
                "colspan": 6 if has_tier else 5,
            },
            {
                "content": format_currency(
                    phase_details.get(table.get("phase_id", "-"), "-"), currency_symbol
                ),
                "styles": {
                    "fontWeight": "bold",
                    "backgroundColor": "#E5E7EB",
                    **COL_STYLE["total"],
                },
                "colspan": 1,
            },
        ]

        table_data[phase].append(total_row)

    pricing_table = [
        deepcopy(pricing_header),
        [{"content": "Recurring Fee"}],
        [{"content": "One-Time Fee"}],
    ]
    for phase in pricing_data:
        pricing_table[0].append(
            {
                "content": phase if phase else "Total",
                "styles": {**HEADER_STYLE, **COL_STYLE["approved_price"]},
                "colspan": 1,
            }
        )
        recurring_price = pricing_data[phase].get("recurring", 0)
        one_time_price = pricing_data[phase].get("one_time", 0)
        pricing_table[2].append(
            {
                "content": format_currency(one_time_price, currency_symbol),
                "styles": COL_STYLE["approved_price"],
                "colspan": 1,
            }
        )
        pricing_table[1].append(
            {
                "content": format_currency(recurring_price, currency_symbol),
                "styles": COL_STYLE["approved_price"],
                "colspan": 1,
            }
        )

    table_data["Recurring/One-Time Price Breakdown"] = pricing_table

    return table_data


def transform_quote_data_for_pdf(
    quote_data, phase_details, catalog_dict, currency_symbol
):
    phases = quote_data.get("phases", [])
    table_data = convert_to_table_format(
        phases, phase_details, catalog_dict, currency_symbol
    )
    return table_data
