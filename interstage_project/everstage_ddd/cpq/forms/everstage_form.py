from .everstage_conditions import connectors_wording_formula, full_address_formula

everstage_table = [
    {
        "form_builder_id": 123,
        "table_id": 1,
        "column_id": "column1",
        "column_name": "Product",
        "data_type": "string",
        "field_type": "single_Select",
        "tag": "sku",
        "properties": {
            "is_mandatory": True,
            "is_hidden": False,
            "is_read_only": False,
        },
    },
    {
        "form_builder_id": 123,
        "table_id": 1,
        "column_id": "column2",
        "column_name": "ListPrice",
        "data_type": "number",
        "field_type": "currency",
        "tag": "list_price",
        "properties": {
            "is_mandatory": True,
            "is_hidden": False,
            "is_read_only": False,
        },
    },
    {
        "form_builder_id": 123,
        "table_id": 1,
        "column_id": "column3",
        "column_name": "Quantity",
        "data_type": "number",
        "field_type": "number",
        "tag": "quantity",
        "properties": {
            "is_mandatory": True,
            "is_hidden": False,
            "is_read_only": False,
        },
    },
    {
        "form_builder_id": 123,
        "table_id": 1,
        "column_id": "column4",
        "column_name": "NetPrice",
        "data_type": "number",
        "field_type": "currency",
        "tag": "prorated_net_total",
        "properties": {
            "is_mandatory": False,
            "is_hidden": False,
            "is_read_only": True,
        },
    },
    {
        "form_builder_id": 123,
        "table_id": 1,
        "column_id": "column5",
        "column_name": "Discount",
        "data_type": "number",
        "field_type": "number",
        "tag": "discount",
        "properties": {
            "is_mandatory": True,
            "is_hidden": False,
            "is_read_only": False,
        },
    },
    {
        "form_builder_id": 123,
        "table_id": 1,
        "column_id": "column6",
        "column_name": "Total",
        "data_type": "number",
        "field_type": "currency",
        "tag": "total",
        "properties": {
            "is_mandatory": False,
            "is_hidden": False,
            "is_read_only": True,
        },
    },
]

everstage_form = {
    "label": "Deal Hub Form",
    "section_order": [
        "section1",
        "section2",
        "section3",
        "section4",
        "section5",
        "section6",
        "section7",
        "section8",
    ],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Opportunity Details",
            "help_text": "",
            "description": "Basic information about the Quote like Opportunity, Account , Contact , etc. ",
            "is_hidden": False,
            "field_order": [
                "field1",
                "field2",
                "field3",
                "field4",
                "field5",
                "field6",
                "field7",
                "field52",
                "field8",
                "field9",
                "field10",
                "field11",
                "field12",
                "field13",
            ],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field2": {
                    "id": "field2",
                    "label": "Quote Name",
                    "label_placement": "top",
                    "description": "Quote Name",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
                "field3": {
                    "id": "field3",
                    "label": "Opportunity",
                    "label_placement": "top",
                    "description": "Opportunity Name",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25",
                        "column": "co_5_deal_name",
                        "filters": [],
                        "source": "datasheet_data",
                    },
                },
                "field4": {
                    "id": "field4",
                    "label": "Opportunity Id",
                    "label_placement": "top",
                    "description": "Opportunity Id, lookup from Opportunity Name",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25",
                        "column": "co_5_record_id",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_5_deal_name",
                                "col_display_name": "Opportunity Name",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field3",
                                "value": "field3",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field5": {
                    "id": "field5",
                    "label": "Account",
                    "label_placement": "top",
                    "description": "Account Name",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_companyname",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Record Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field6": {
                    "id": "field6",
                    "label": "Account Id",
                    "label_placement": "top",
                    "description": "Account Id",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25",
                        "column": "co_5_company_id",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_5_deal_name",
                                "col_display_name": "Opportunity Name",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field3",
                                "value": "field3",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field7": {
                    "id": "field7",
                    "label": "Contact",
                    "label_placement": "top",
                    "description": "Contact Name",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27",
                        "column": "cf_name",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_4_record_id",
                                "col_display_name": "Record Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field8",
                                "value": "field8",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field52": {
                    "id": "field52",
                    "label": "Contact Email",
                    "label_placement": "top",
                    "description": "Contact Email",
                    "help_text": "",
                    "field_type": "email",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "cf6007bc-9cf1-4cc8-b413-35c5eaee1e27",
                        "column": "co_4_email",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_4_record_id",
                                "col_display_name": "Record Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field8",
                                "value": "field8",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field8": {
                    "id": "field8",
                    "label": "Contact Id",
                    "label_placement": "top",
                    "description": "Contact Id",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b62c1287-190c-4329-8e91-4bb07ffccd25",
                        "column": "co_5_contact_id",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_5_deal_name",
                                "col_display_name": "Opportunity Name",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field3",
                                "value": "field3",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field9": {
                    "id": "field9",
                    "label": "Currency",
                    "label_placement": "top",
                    "description": "Currency",
                    "tag": "currency",
                    "tag_type": "pricebook",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "USD",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field10": {
                    "id": "field10",
                    "label": "Price Book",
                    "label_placement": "top",
                    "description": "Price Book",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                },
                "field11": {
                    "id": "field11",
                    "label": "Valid Till",
                    "label_placement": "top",
                    "description": "Valid Till",
                    "help_text": "",
                    "field_type": "date",
                    "data_type": "date",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
                "field12": {
                    "id": "field12",
                    "label": "Deal Type",
                    "label_placement": "top",
                    "description": "Type of Deal",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "New Business", "value": "New Business"},
                        {"label": "Change Order", "value": "Change Order"},
                    ],
                },
                "field13": {
                    "id": "field13",
                    "label": "Data Center Location",
                    "label_placement": "top",
                    "description": "US / EU data center",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "US Data Center", "value": "US Data Center"},
                        {"label": "EU Data Center", "value": "EU Data Center"},
                    ],
                },
            },
        },
        "section2": {
            "id": "section2",
            "label": "Contact Details",
            "help_text": "",
            "is_hidden": False,
            "description": "General Contact Details ",
            "field_order": [
                "field14",
                "field15",
                "field16",
                "field17",
                "field18",
                "field19",
                "field20",
                "field21",
                "field22",
            ],
            "fields": {
                "field14": {
                    "id": "field14",
                    "label": "Country",
                    "label_placement": "top",
                    "description": "Country",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_country_region__1",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Company Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field15": {
                    "id": "field15",
                    "label": "State",
                    "label_placement": "top",
                    "description": "State",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_state_region",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Company Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field16": {
                    "id": "field16",
                    "label": "City",
                    "label_placement": "top",
                    "description": "City",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_city",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Company Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field17": {
                    "id": "field17",
                    "label": "Street Address Line 1",
                    "label_placement": "top",
                    "description": "Street Address Line 1",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_street_address",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Company Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field18": {
                    "id": "field18",
                    "label": "Street Address Line 2",
                    "label_placement": "top",
                    "description": "Street Address Line 2",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_street_address_2",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Company Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field19": {
                    "id": "field19",
                    "label": "Postal Code",
                    "label_placement": "top",
                    "description": "Postal Code",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options_lookup_spec": {
                        "databook_id": "132c4b7f-9bf4-4a50-b812-c9f186a94287",
                        "datasheet_id": "b3d0e741-ba81-436d-848b-4b7a6c90a82c",
                        "column": "co_2_postal_code",
                        "filters": [
                            {
                                "type": "FILTER",
                                "is_valid": True,
                                "col_name": "co_2_record_id",
                                "col_display_name": "Company Id",
                                "data_type": "String",
                                "operator": "==",
                                "needs_operand": True,
                                "multi_valued": False,
                                "source_field": "field6",
                                "value": "field6",
                            }
                        ],
                        "source": "datasheet_data",
                    },
                },
                "field20": {
                    "id": "field20",
                    "label": "Full Address from Hubspot",
                    "label_placement": "top",
                    "description": "Full Address from Hubspot",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "formula": full_address_formula,
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
                "field21": {
                    "id": "field21",
                    "label": "Change Address?",
                    "label_placement": "right",
                    "description": "Change Address?",
                    "help_text": "",
                    "field_type": "checkbox",
                    "data_type": "boolean",
                    "value": False,
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
                "field22": {
                    "id": "field22",
                    "label": "Manual Address",
                    "label_placement": "top",
                    "description": "Manual Address",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                },
            },
        },
        "section3": {
            "id": "section3",
            "label": "Support & Connectors",
            "help_text": "",
            "is_hidden": False,
            "description": "Details about the support limitations, connectors, and related charges.",
            "field_order": ["field23", "field24", "field25", "field26", "field27"],
            "fields": {
                "field23": {
                    "id": "field23",
                    "label": "Include limitation of 100 hours/year White Glove support",
                    "label_placement": "top",
                    "description": "Include limitation of 100 hours/year White Glove support",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "Include (Limit)", "value": "Include (Limit)"},
                        {"label": "Exclude (No limit)", "value": "Exclude (No limit)"},
                    ],
                },
                "field24": {
                    "id": "field24",
                    "label": "Waive of support",
                    "label_placement": "top",
                    "description": "Waive of support",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "Yes", "value": "Yes"},
                        {"label": "No", "value": "No"},
                    ],
                },
                "field25": {
                    "id": "field25",
                    "label": "Number of Connectors",
                    "label_placement": "top",
                    "description": "Number of Connectors",
                    "help_text": "",
                    "field_type": "number",
                    "data_type": "number",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                        "min": 0,
                        "max": 15,
                    },
                },
                "field26": {
                    "id": "field26",
                    "label": "Connector names",
                    "label_placement": "top",
                    "description": "Connector names",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                },
                "field27": {
                    "id": "field27",
                    "label": "Waive of connector charges",
                    "label_placement": "top",
                    "description": "Waive of connector charges",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "Yes", "value": "Yes"},
                        {"label": "No", "value": "No"},
                    ],
                },
            },
        },
        "section4": {
            "id": "section4",
            "help_text": "",
            "is_hidden": True,
            "label": "Implementation Fee & Modules",
            "description": "Details about the implementation package and modules.",
            "field_order": ["field28", "field29"],
            "fields": {
                "field28": {
                    "id": "field28",
                    "label": "Include implementation package?",
                    "label_placement": "top",
                    "description": "Include implementation package?",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "Yes", "value": "Yes"},
                        {"label": "No", "value": "No"},
                    ],
                },
                "field29": {
                    "id": "field29",
                    "label": "Include ASC606 module?",
                    "label_placement": "top",
                    "description": "Include ASC606 module?",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "Yes", "value": "Yes"},
                        {"label": "No", "value": "No"},
                    ],
                },
            },
        },
        "section5": {
            "id": "section5",
            "label": "Payment Terms",
            "help_text": "",
            "is_hidden": False,
            "description": "Payment term details, including method and credit terms.",
            "field_order": ["field30", "field31", "field32"],
            "fields": {
                "field30": {
                    "id": "field30",
                    "label": "Payment Term",
                    "label_placement": "top",
                    "description": "Select the payment term.",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "Annual Upfront", "value": "Annual Upfront"},
                        {"label": "Semi Annual", "value": "Semi Annual"},
                    ],
                },
                "field31": {
                    "id": "field31",
                    "label": "Payment Method",
                    "label_placement": "top",
                    "description": "Select the payment method.",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {
                            "label": "Wire Transfer to Everstage",
                            "value": "Wire Transfer to Everstage",
                        },
                        {"label": "ACH", "value": "ACH"},
                    ],
                },
                "field32": {
                    "id": "field32",
                    "label": "Credit Terms",
                    "label_placement": "top",
                    "description": "Select the credit terms in days.",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {"label": "30", "value": "30"},
                        {"label": "45", "value": "45"},
                        {"label": "60", "value": "60"},
                        {"label": "75", "value": "75"},
                        {"label": "90", "value": "90"},
                    ],
                },
            },
        },
        "section6": {
            "id": "section6",
            "label": "Output Document",
            "help_text": "",
            "is_hidden": False,
            "description": "Details related to the output document and terms.",
            "field_order": [
                "field33",
                "field34",
                "field51",
                "field35",
                "field36",
                "field37",
            ],
            "fields": {
                "field33": {
                    "id": "field33",
                    "label": "Terms of Use",
                    "label_placement": "top",
                    "description": "Select the Terms of Use.",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {
                            "label": "Online Terms (Website)",
                            "value": "Online Terms (Website)",
                        },
                        {"label": "MSA", "value": "MSA"},
                    ],
                },
                "field34": {
                    "id": "field34",
                    "label": "DPA Signed?",
                    "label_placement": "top",
                    "description": "Has the DPA been signed?",
                    "help_text": "",
                    "field_type": "single_select",
                    "data_type": "string",
                    "value": "No",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "options": [
                        {
                            "label": "Yes",
                            "value": "Yes",
                        },
                        {"label": "No", "value": "No"},
                    ],
                },
                "field51": {
                    "id": "field51",
                    "label": "DPA Wording",
                    "label_placement": "top",
                    "description": "DPA Wording",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                },
                "field35": {
                    "id": "field35",
                    "label": "Connectors Wording",
                    "label_placement": "top",
                    "description": "The order form includes charges for the following connectors:",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "formula": connectors_wording_formula,
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field36": {
                    "id": "field36",
                    "label": "Change Terms",
                    "label_placement": "right",
                    "description": "Option to change terms.",
                    "help_text": "",
                    "field_type": "checkbox",
                    "data_type": "boolean",
                    "value": False,
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
                "field37": {
                    "id": "field37",
                    "label": "Auto Renewal",
                    "label_placement": "right",
                    "description": "Is auto-renewal enabled?",
                    "help_text": "",
                    "field_type": "checkbox",
                    "data_type": "boolean",
                    "value": False,
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                },
            },
        },
        "section7": {
            "id": "section7",
            "label": "Select Product",
            "help_text": "",
            "is_hidden": False,
            "description": "Select Product Step",
            "field_order": ["field38"],
            "tag": "select_product",
            "fields": {
                "field38": {
                    "id": "field38",
                    "label": "Select Product",
                    "label_placement": "top",
                    "help_text": "",
                    "tag": "select_product",
                    "field_type": "table",
                    "description": "Table containing order details",
                    "table_id": 1,
                    "data_type": "array",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                }
            },
        },
        "section8": {
            "id": "section8",
            "label": "Approval Matrix",
            "help_text": "",
            "is_hidden": True,
            "description": "Approval Matrix hidden fields",
            "field_order": [
                "field45",
                "field46",
                "field47",
                "field48",
                "field49",
                "field50",
            ],
            "tag": "approval_matrix",
            "fields": {
                "field45": {
                    "id": "field45",
                    "label": "EP - Mike Approval",
                    "label_placement": "top",
                    "description": "EP Mike Approval",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field46": {
                    "id": "field46",
                    "label": "EP - Siva Approval",
                    "label_placement": "top",
                    "description": "EP Siva Approval",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field47": {
                    "id": "field47",
                    "label": "Implementation - Mike Approval",
                    "label_placement": "top",
                    "description": "Implementation Mike Approval",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field48": {
                    "id": "field48",
                    "label": "Implementation - Siva Approval",
                    "label_placement": "top",
                    "description": "Implementation Siva Approval",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field49": {
                    "id": "field49",
                    "label": "Support - Mike Approval",
                    "label_placement": "top",
                    "description": "Support Mike Approval",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field50": {
                    "id": "field50",
                    "label": "Support - Siva Approval",
                    "label_placement": "top",
                    "description": "Support Siva Approval",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
            },
        },
    },
}


default_form = {
    "label": "Untitled Form 1",
    "section_order": [
        "section1",
        "section2",
        "section3",
    ],
    "sections": {
        "section1": {
            "id": "section1",
            "label": "Customer Details",
            "help_text": "",
            "description": "",
            "is_hidden": False,
            "field_order": [
                "field1",
                "field2",
                "field3",
                "field4",
                "field5",
                "field6",
                "field7",
                "field8",
                "field9",
                "field10",
                "field11",
            ],
            "fields": {
                "field1": {
                    "id": "field1",
                    "label": "Quote ID",
                    "label_placement": "top",
                    "description": "Quote ID",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                    "tag": "quote_id",
                },
                "field2": {
                    "id": "field2",
                    "label": "Quote Name",
                    "label_placement": "top",
                    "description": "Quote Name",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "quote_name",
                },
                "field3": {
                    "id": "field3",
                    "label": "Opportunity",
                    "label_placement": "top",
                    "description": "Opportunity Name",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "opportunity_name",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field4": {
                    "id": "field4",
                    "label": "Opportunity Id",
                    "label_placement": "top",
                    "description": "Opportunity Id, lookup from Opportunity Name",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "tag": "opportunity_id",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field5": {
                    "id": "field5",
                    "label": "Account",
                    "label_placement": "top",
                    "description": "Account Name",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "account_name",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field6": {
                    "id": "field6",
                    "label": "Account Id",
                    "label_placement": "top",
                    "description": "Account Id",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "tag": "account_id",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field7": {
                    "id": "field7",
                    "label": "Contact Name",
                    "label_placement": "top",
                    "description": "Contact Name",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "contact_name",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field8": {
                    "id": "field8",
                    "label": "Contact Id",
                    "label_placement": "top",
                    "description": "Contact Id",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": False,
                    },
                    "tag": "contact_id",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field9": {
                    "id": "field9",
                    "label": "Currency",
                    "label_placement": "top",
                    "description": "Currency",
                    "tag": "currency",
                    "tag_type": "pricebook",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "value": "USD",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": True,
                        "is_read_only": True,
                    },
                },
                "field10": {
                    "id": "field10",
                    "label": "Contact Email",
                    "label_placement": "top",
                    "description": "Contact Email",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "contact_email",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field11": {
                    "id": "field11",
                    "label": "Valid Till",
                    "label_placement": "top",
                    "description": "Valid Till",
                    "help_text": "",
                    "field_type": "date",
                    "data_type": "date",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "valid_till",
                },
            },
        },
        "section2": {
            "id": "section2",
            "label": "Billing Address",
            "help_text": "",
            "description": "",
            "is_hidden": False,
            "field_order": [
                "field17",
                "field16",
                "field15",
                "field19",
                "field14",
                "field20",
            ],
            "fields": {
                "field17": {
                    "id": "field17",
                    "label": "Street Address",
                    "label_placement": "top",
                    "description": "Street Address Line 1",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "street_address",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field16": {
                    "id": "field16",
                    "label": "City",
                    "label_placement": "top",
                    "description": "City",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "city",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field15": {
                    "id": "field15",
                    "label": "State",
                    "label_placement": "top",
                    "description": "State",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "state",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field19": {
                    "id": "field19",
                    "label": "Postal Code",
                    "label_placement": "top",
                    "description": "Postal Code",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "postal_code",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field14": {
                    "id": "field14",
                    "label": "Country",
                    "label_placement": "top",
                    "description": "Country",
                    "help_text": "",
                    "field_type": "lookup",
                    "data_type": "string",
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "country",
                    "options_lookup_spec": {
                        "databook_id": "",
                        "datasheet_id": "",
                        "column": "",
                        "filters": [],
                        "source": "",
                    },
                },
                "field20": {
                    "id": "field20",
                    "label": "Full Address",
                    "label_placement": "top",
                    "description": "Full Address from Hubspot",
                    "help_text": "",
                    "field_type": "single_line",
                    "data_type": "string",
                    "formula": None,
                    "value": "",
                    "properties": {
                        "is_mandatory": False,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                    "tag": "full_address",
                },
            },
        },
        "section3": {
            "id": "section3",
            "label": "Select Product",
            "help_text": "",
            "is_hidden": False,
            "description": "Select Product Step",
            "field_order": ["field38"],
            "tag": "select_product",
            "fields": {
                "field38": {
                    "id": "field38",
                    "label": "Select Product",
                    "label_placement": "top",
                    "help_text": "",
                    "tag": "select_product",
                    "field_type": "table",
                    "description": "Table containing order details",
                    "table_id": 1,
                    "data_type": "array",
                    "value": "",
                    "properties": {
                        "is_mandatory": True,
                        "is_hidden": False,
                        "is_read_only": False,
                    },
                }
            },
        },
    },
}
