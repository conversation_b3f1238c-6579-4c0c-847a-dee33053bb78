import uuid

from django.utils import timezone

from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.cpq.forms.models.form_models import (
    AutoSaveForm,
    EverstageForm,
    FormBuilder,
    FormRules,
)


class AutoSaveFormAccessor(BiTemporalSelector):
    def __init__(self, client_id):
        self.model = AutoSaveForm
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def insert_object(self, form_data, form_id, form_builder_id):
        st = timezone.now()
        existing_obj = self.client_kd_deleted_aware().filter(
            form_id=form_id, form_builder_id=form_builder_id
        )
        if existing_obj.exists():
            existing_obj.update(knowledge_end_date=st)
        obj = AutoSaveForm.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=st,
            form_builder_id=form_builder_id,
            form_id=form_id,
            form_data=form_data,
        )
        return obj

    def get_all_values_of_field(self, field_id):
        qs = self.client_kd_deleted_aware().values_list(
            f"form_data__{field_id}", flat=True
        )
        return list(qs)

    def create_object(self, form_id, form_builder_id, kd=None, form_data=None):
        if form_data is None:
            form_data = {}
        kd = kd if kd else timezone.now()
        obj = AutoSaveForm.objects.create(
            form_builder_id=form_builder_id,
            form_id=form_id,
            form_data=form_data,
            client_id=self.client_id,
            knowledge_begin_date=kd,
        )
        return obj

    def update_auto_saved_form(self, form_id, data, knowledge_date=None):
        """
        Update a auto saved form.

        Args:
            form_id (int): The ID of the auto saved form to update.
            data (dict): The data to update the auto saved form with.

        Returns:
            AutoSaveForm: The updated auto saved form object.
        """
        record = self.get_last_saved_form(form_id=form_id)
        return self.bitemporal_update(
            record_identifier=record.pk, data=data, invalidation_date=knowledge_date
        )

    def get_last_saved_form(
        self,
        form_id,
        projection=None,
    ):
        query = self.client_kd_deleted_aware().filter(form_id=form_id)
        if projection:
            return query.values(*projection).first()
        auto_save_form = query.first()
        return auto_save_form

    def get_objects_by_form_ids(self, form_ids):
        query = self.client_kd_deleted_aware().filter(form_id__in=form_ids)
        return list(query)

    def delete_object(self, form_id, knowledge_end_date=None):
        knowledge_end_date = knowledge_end_date or timezone.now()
        return (
            self.client_kd_aware()
            .filter(form_id=form_id)
            .update(
                knowledge_end_date=knowledge_end_date,
                is_deleted=True,
            )
        )


class EverstageFormAccessor(BiTemporalSelector):
    def __init__(self, client_id):
        self.model = EverstageForm
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def get_objects(self, form_builder_id, form_id):
        return list(
            self.client_kd_deleted_aware().filter(
                form_builder_id=form_builder_id, form_id=form_id
            )
        )

    def get_all_first_sections(self):
        return list(
            self.client_kd_deleted_aware()
            .filter(section_id="section1")
            .order_by("form_builder_id", "form_id")
        )

    def create_object(self, form_builder_id, user, form_spec, form_data):
        st = timezone.now()
        obj = EverstageForm(
            client_id=self.client_id,
            form_builder_id=form_builder_id,
            knowledge_begin_date=st,
            form_spec=form_spec,
            form_data=form_data,
            created_by=user,
            updated_by=user,
            created_at=st,
        )
        obj.save()
        return obj

    def get_object(self, form_id):
        return self.client_kd_deleted_aware().get(form_id=form_id)

    def insert_object(self, knowledge_date, obj):
        form_builder_id = obj["form_builder_id"]
        form_id = obj["form_id"]
        self.client_kd_deleted_aware().filter(
            form_builder_id=form_builder_id, form_id=form_id
        ).update(knowledge_end_date=knowledge_date)
        obj = EverstageForm(**obj)
        obj.save()
        return obj

    def create_and_clone_form(self, form, updated_form_spec, updated_form_data, audit):
        cloned_form = EverstageForm.objects.create(
            client_id=self.client_id,
            form_builder_id=form.form_builder_id,
            form_id=uuid.uuid4(),
            form_data=updated_form_data,
            form_spec=updated_form_spec,
            knowledge_begin_date=audit.get("knowledge_begin_date"),
            created_by=audit.get("created_by"),
            updated_by=audit.get("updated_by"),
        )
        return cloned_form

    def delete_object(self, form_id, knowledge_end_date=None):
        knowledge_end_date = knowledge_end_date or timezone.now()
        return (
            self.client_kd_aware()
            .filter(form_id=form_id)
            .update(
                knowledge_end_date=knowledge_end_date,
                is_deleted=True,
            )
        )

    def update_form(self, form_id, data, knowledge_date=None):
        record = self.get_object(form_id=form_id)
        return self.bitemporal_update(
            record_identifier=record.pk, data=data, invalidation_date=knowledge_date
        )

    def get_objects_by_form_ids(self, form_ids):
        return self.client_kd_deleted_aware().filter(form_id__in=form_ids)

    def get_auto_save_form(self, form_id):
        auto_save_form_acc = AutoSaveFormAccessor(self.client_id)
        return auto_save_form_acc.get_last_saved_form(form_id=form_id)

    def get_form_data(self, form_ids: list[uuid.UUID]) -> list[dict]:
        return list(
            self.client_kd_aware()
            .filter(form_id__in=form_ids)
            .values_list("form_data", flat=True)
        )


class FormBuilderAccessor(BiTemporalSelector):
    def __init__(self, client_id):
        self.model = FormBuilder
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def get_all_form_builders(
        self, search_term=None, limit=None, offset=None, status=None
    ):
        query = self.client_kd_deleted_aware().order_by("-created_at")
        if search_term:
            query = query.filter(form_builder_name__icontains=search_term)
        if status:
            query = query.filter(status=status)
        if limit is not None and offset is not None:
            query = query[offset : offset + limit].values(
                "form_builder_id",
                "form_builder_name",
                "form_builder_description",
                "status",
            )
        else:
            query = query.values(
                "form_builder_id",
                "form_builder_name",
                "form_builder_description",
                "status",
            )
        return list(query)

    def get_form_builder(self, form_builder_id):
        return (
            self.client_kd_deleted_aware()
            .filter(form_builder_id=form_builder_id)
            .first()
        )

    def delete_object(self, form_builder_id, knowledge_end_date=None):
        knowledge_end_date = knowledge_end_date or timezone.now()
        return (
            self.client_kd_aware()
            .filter(form_builder_id=form_builder_id)
            .update(
                knowledge_end_date=knowledge_end_date,
                is_deleted=True,
            )
        )

    def invalidate_object(self, form_builder_id, knowledge_end_date=None):
        knowledge_end_date = knowledge_end_date or timezone.now()
        return (
            self.client_kd_aware()
            .filter(form_builder_id=form_builder_id)
            .update(
                knowledge_end_date=knowledge_end_date,
            )
        )

    def insert_object(self, form_builder):
        obj = FormBuilder.objects.create(
            **form_builder,
            client_id=self.client_id,
        )
        obj.save()
        return obj

    def get_form_builders(self, form_builder_ids, projection=None):
        query = self.client_kd_deleted_aware().filter(
            form_builder_id__in=form_builder_ids
        )
        if projection:
            return query.values(*projection)
        return list(query)


class FormRuleAccessor(BiTemporalSelector):
    def __init__(self, client_id):
        self.model = FormRules
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def create_object(self, form_rule):
        obj = FormRules.objects.create(
            client_id=self.client_id,
            **form_rule,
        )
        obj.save()
        return obj

    def bulk_insert_objects(self, form_rules):
        obj = FormRules.objects.bulk_create(
            [FormRules(**form_rule) for form_rule in form_rules]
        )
        return obj

    def invalidate_object(
        self, form_rule_id, knowledge_end_date=None, is_automated_rule=False
    ):
        knowledge_end_date = knowledge_end_date or timezone.now()
        qs = self.client_kd_aware().filter(form_rule_id=form_rule_id)
        if is_automated_rule:
            qs = qs.filter(is_automated_rule=is_automated_rule)
        qs.update(
            knowledge_end_date=knowledge_end_date,
        )
        return qs

    def invalidate_object_by_form_builder(
        self, form_builder_id, knowledge_end_date=None, is_automated_rule=False
    ):
        knowledge_end_date = knowledge_end_date or timezone.now()
        qs = self.client_kd_aware().filter(form_builder_id=form_builder_id)
        if is_automated_rule:
            qs = qs.filter(is_automated_rule=is_automated_rule)
        qs.update(
            knowledge_end_date=knowledge_end_date,
        )
        return qs

    def get_form_rule(self, form_rule_id):
        """Get a form rule by ID"""
        return self.client_kd_aware().filter(form_rule_id=form_rule_id).first()

    def get_non_automated_form_rule(self, form_rule_id):
        """Get a form rule by ID"""
        return (
            self.client_kd_deleted_aware()
            .filter(form_rule_id=form_rule_id, is_automated_rule=False)
            .first()
        )

    def delete_object(self, form_rule_id, knowledge_end_date=None):
        """Delete a form rule by ID"""
        knowledge_end_date = knowledge_end_date or timezone.now()
        return (
            self.client_kd_aware()
            .filter(form_rule_id=form_rule_id)
            .update(
                knowledge_end_date=knowledge_end_date,
                is_deleted=True,
            )
        )

    def insert_object(self, form_rule_data):
        """Insert a new form rule"""
        obj = FormRules.objects.create(
            **form_rule_data,
            client_id=self.client_id,
        )
        obj.save()
        return obj

    def get_all_form_rules(
        self, search_term=None, limit=None, offset=None, is_automated_rule=None
    ):
        """Get all form rules"""
        query = self.client_kd_deleted_aware().order_by("-created_at")
        if search_term:
            query = query.filter(form_rule_name__icontains=search_term)
        if is_automated_rule is not None:
            query = query.filter(is_automated_rule=is_automated_rule)
        if limit is not None and offset is not None:
            query = query[offset : offset + limit].values()
        else:
            query = query.values()
        return list(query)

    def get_form_rules_by_form_builder_id(self, form_builder_id, status=None):
        query = self.client_kd_aware().filter(form_builder_id=form_builder_id)
        if status:
            query = query.filter(status=status)
        return list(query.order_by("-created_at").values())

    def form_rule_exists_by_name(self, form_rule_name, rule_id=None):
        qs = self.client_kd_aware().filter(form_rule_name=form_rule_name)
        if rule_id:
            qs = qs.exclude(form_rule_id=rule_id)
        return qs.exists()
