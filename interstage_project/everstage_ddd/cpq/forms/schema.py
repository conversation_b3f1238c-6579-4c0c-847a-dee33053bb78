from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from ninja import Schema


class FormSpec(Schema):
    label: str
    section_order: List[str]
    sections: Dict[str, Any]
    page_author: Optional[str] = None
    other_page_properties: Optional[Dict[str, Any]] = None
    approval_cycles: Optional[List[Dict[str, Any]]] = None


class QueryParams(Schema):
    limit_value: int
    page_num: int
    search_term: Optional[str] = None


class LookupOption(Schema):
    label: str
    value: str
    key: str


class EvaluateRulePayload(Schema):
    form_id: str
    form_builder_id: str
    changed_field: str
    form_spec: FormSpec
    form_data: Dict[str, Any]


class GetLookupOptionsPayload(Schema):
    form_data: Dict[str, Any]
    form_builder_id: str
    look_up_field: str
    query_params: QueryParams
    quote_id: str
    form_builder_id: str


class SaveLookupValuePayload(Schema):
    form_builder_id: str
    look_up_field: str
    selected_value: str
    quote_id: str


class GetFormSpecResponse(Schema):
    form_spec: FormSpec
    source_rule_fields: List[str]
    dependent_fields: Dict[str, List[str]]
    table_spec: Dict[int, Any]


class EvaluateRuleResponse(FormSpec):
    pass


class GetLookupOptionsResponse(Schema):
    options: List[LookupOption]


class SaveLookupValueResponse(FormSpec):
    pass


class AutoSaveFormPayload(Schema):
    form_id: str
    form_builder_id: str
    form_data: Dict[str, Any]


class AutoSaveFormResponse(Schema):
    status: str


class SubmitFormResponse(Schema):
    status: str


class SubmitFormPayload(Schema):
    form_id: str
    form_builder_id: str
    form_spec: FormSpec
    form_data: Dict[str, Any]


class FormsResponse(Schema):
    form_id: str
    form_builder_id: str
    status: str
    created_at: str
    updated_at: str
    updated_by: str
    created_by: str
    form_builder_name: str
    form_name: str


class GetAllFormsResponse(Schema):
    forms: List[FormsResponse]


class GetAllFormsPayload(Schema):
    pass


class RefreshObjectsPayload(Schema):
    form_id: UUID


class RefreshObjectsResponse(Schema):
    message: str
    e2e_sync_run_id: UUID | None


class FormBuilders(Schema):
    form_builder_id: UUID
    form_builder_name: str
    form_builder_description: str | None
    status: str


class GetAllFormBuildersPayload(Schema):
    search_term: Optional[str] = None
    limit_value: int
    page_number: int
    status: Optional[str] = None


class GetAllFormBuildersResponse(Schema):
    form_builders: List[FormBuilders]


class FormBuilderPayload(Schema):
    form_builder_id: Optional[str] = None
    form_builder_name: Optional[str] = None
    form_builder_description: Optional[str] = None


class FormBuilderResponse(Schema):
    form_builder_id: UUID
    form_builder_name: str
    form_builder_description: str | None
    form_spec: FormSpec
    status: str
    created_by: str
    created_at: datetime
    updated_by: str | None
    source_fields: List[str]
    default_fields: List[str]


class DeleteFormBuilderResponse(Schema):
    status: str
    message: str


class GetFormBuilderResponse(Schema):
    status: str
    message: str
    form_builder: Optional[FormBuilderResponse] = None


class GetFormBuilderPayload(Schema):
    form_builder_id: str


class UpdateFormBuilderResponse(Schema):
    status: str
    message: str


class FormBuilderFields(Schema):
    form_builder_name: Optional[str] = None
    form_builder_description: Optional[str] = None
    status: Optional[str] = None
    form_spec: Optional[FormSpec] = None


class UpdateFormBuilderPayload(Schema):
    form_builder_id: str
    updated_fields: FormBuilderFields


class CreateFormRulePayload(Schema):
    form_rule_id: Optional[str] = None
    form_rule_name: Optional[str] = None
    form_rule_description: Optional[str] = None
    form_builder_id: Optional[str] = None


class CreateFormRuleResponse(Schema):
    message: str
    status: str
    form_rule_id: Optional[UUID] = None


class FormRuleFields(Schema):
    form_rule_name: Optional[str] = None
    form_rule_description: Optional[str] = None
    is_automated_rule: Optional[bool] = None
    source_fields: Optional[List[str]] = None
    destination_fields: Optional[List[str]] = None
    trigger: Optional[str] = None
    actions: Optional[List] = None
    is_lookup: Optional[bool] = None
    is_formula: Optional[bool] = None
    status: Optional[str] = None


class UpdateFormRulePayload(Schema):
    form_rule_id: str
    updated_fields: FormRuleFields


class UpdateFormRuleResponse(Schema):
    status: str
    message: str


class DeleteFormRuleResponse(Schema):
    status: str
    message: str


class DeleteFormRulePayload(Schema):
    form_rule_id: str


class FormRuleResponse(Schema):
    """Response schema for getting a form rule by ID"""

    form_rule_id: UUID
    form_builder_id: UUID
    form_rule_name: str
    form_rule_description: str | None
    is_automated_rule: bool
    source_fields: List[str]
    destination_fields: List[str]
    is_lookup: bool
    is_formula: bool
    trigger: str | None
    actions: List[Dict[str, Any]] | None
    created_by: str | None
    updated_by: str | None
    created_at: datetime | None
    updated_at: datetime | None


class GetFormRuleResponse(Schema):
    form_rule: Optional[FormRuleResponse] = None
    status: Optional[str] = None
    message: Optional[str] = None


class FormRules(Schema):
    form_rule_id: str | UUID
    form_rule_name: str | None
    form_rule_description: str | None
    status: str
    form_builder_id: str | UUID
    form_builder_name: str


class GetAllFormRulesPayload(Schema):
    search_term: Optional[str] = None
    limit_value: int
    page_number: int


class GetAllFormRulesResponse(Schema):
    form_rules: List[FormRules]


class AutoCompleteContextPayload(Schema):
    variables: List[str]
    form_builder_id: str
    datasheet_id: Optional[str] = None
    databook_id: Optional[str] = None


class AutoCompleteContextData(Schema):
    group: str
    label: str
    value: str
    meta: dict


class AutoCompleteContextResponse(Schema):
    data: List[AutoCompleteContextData]


class DeleteFieldPayload(Schema):
    form_builder_id: str
    field_id: str


class DeleteFieldResponse(Schema):
    status: str
    message: str


class ExpressionValidatePayload(Schema):
    expression: list[dict[str, Any]]


class ExpressionValidateResponse(Schema):
    data_type: str
    data_type_id: int
    status: str
    msg: str


class GetFieldsAndSectionsResponse(Schema):
    fields: Dict[str, Any]
    sections: Dict[str, Any]


class GetProductsPayload(Schema):
    pricebook_id: str
    price_factor: str


class GetReturnColumnsResponse(Schema):
    columns: List[Dict[str, Any]]
