import pydash

from .form_rules_helper import get_source_fields_used
from .form_types import FormModel


class FormSpecNavigator:
    def __init__(self, form_spec):
        """
        Takes a form spec and creates a logical structure to easily navigate thru the form spec
        """
        self.form_spec = form_spec
        self.field_section_map, self.sub_sec_section_map = self.get_logical_structure()

    def get_logical_structure(self):
        """
        creates 2 dicts for easy navigation.
        field_section_map - dict with field_id as key and section_id or (section_id, sub_section_id) as value
        sub_sec_section_map - dict with sub_section_id as key and section_id as value
        """
        form_model = FormModel(**self.form_spec)  # type: ignore
        section_ids = form_model.sections.keys()
        field_section_map = {}
        sub_sec_section_map = {}
        for sec_id in section_ids:
            section_model = form_model.sections[sec_id]
            fields = section_model.fields
            sub_sections = section_model.sub_sections
            if fields:
                for field_id in list(fields.keys()):
                    field_section_map[field_id] = sec_id
            if sub_sections:
                for sub_sec_id, seb_sec_model in sub_sections.items():
                    fields = seb_sec_model.fields
                    sub_sec_section_map[sub_sec_id] = sec_id
                    for field_id in list(fields.keys()):
                        field_section_map[field_id] = (sec_id, sub_sec_id)
        return field_section_map, sub_sec_section_map

    def get_section(self, section_id):
        """
        Returns the section with the given section_id
        """
        return self.form_spec["sections"][section_id]

    def get_sub_section(self, sub_section_id):
        """
        Returns the sub section with the given sub_section
        """
        section_id = self.sub_sec_section_map[sub_section_id]
        return self.form_spec["sections"][section_id]["sub_sections"][sub_section_id]

    def get_field(self, field_id):
        """
        Returns the field with the given field_id
        """
        if isinstance(self.field_section_map[field_id], tuple):
            section_id, sub_section_id = self.field_section_map[field_id]
            return self.form_spec["sections"][section_id]["sub_sections"][
                sub_section_id
            ]["fields"][field_id]
        else:
            section_id = self.field_section_map[field_id]
            return self.form_spec["sections"][section_id]["fields"][field_id]

    def get_section_key(self):
        """
        Returns the section key
        """
        return pydash.get(self.form_spec, "sections")

    def get_section_order_key(self):
        """
        Returns the section order key
        """
        return pydash.get(self.form_spec, "section_order")

    def get_form_data_from_spec(self):
        """
        Returns the form data from the form spec
        """
        form_data = {}
        field_ids = list(self.field_section_map.keys())
        for field_id in field_ids:
            field = self.get_field(field_id)
            form_data[field_id] = field["value"]
        return form_data

    def get_mandatory_fields(self):
        """
        Returns the mandatory fields
        """
        return [
            field["id"]
            for section in self.form_spec["sections"].values()
            for field in (
                list(section.get("fields", {}).values())
                + [
                    f
                    for sub in section.get("sub_sections", {}).values()
                    for f in sub["fields"].values()
                ]
            )
            if field.get("properties", {}).get("is_mandatory")
        ]

    def get_all_fields(self):
        """
        Returns all fields(keys) in the form spec
        """
        all_fields = []

        # Iterate through all sections
        for section in self.form_spec.get("sections", {}).values():
            # Add fields from main section
            if "fields" in section:
                all_fields.extend(section["fields"].keys())

            # Add fields from sub-sections
            if "sub_sections" in section:
                for sub_section in section["sub_sections"].values():
                    if "fields" in sub_section:
                        all_fields.extend(sub_section["fields"].keys())

        return all_fields

    def convert_lookup_filters_to_camel_case(self):
        """
        Converts the lookup filters to camel case
        """
        from commission_engine.services.commission_calculation_service.commission_simulation.utils import (
            change_keys,
        )

        for section in self.form_spec["sections"].values():
            for field in section["fields"].values():
                if (
                    "options_lookup_spec" in field
                    and "filters" in field["options_lookup_spec"]
                ):
                    field["options_lookup_spec"]["filters"] = change_keys(
                        field["options_lookup_spec"]["filters"], pydash.snake_case
                    )
        return self.form_spec

    def get_lookup_fields_with_filters(self):
        """
        Returns a dictionary mapping field IDs to their source fields from options_lookup_spec filters

        Returns:
            dict: {field_id: [source_field1, source_field2, ...]}
        """
        lookup_fields = {}
        for section in self.form_spec["sections"].values():
            # Process fields in main section
            for field_id, field in section.get("fields", {}).items():
                filters = field.get("options_lookup_spec", {}).get("filters", [])
                if filters:
                    source_fields = get_source_fields_used(filters)
                    lookup_fields[field_id] = source_fields
            # Process fields in sub-sections
            for sub in section.get("sub_sections", {}).values():
                for field_id, field in sub.get("fields", {}).items():
                    filters = field.get("options_lookup_spec", {}).get("filters", [])
                    if filters:
                        source_fields = get_source_fields_used(filters)
                        lookup_fields[field_id] = source_fields

        return lookup_fields

    def get_formula_fields(self) -> list[dict]:
        """
        Returns all fields that contain a formula in their specification, along with their source fields.

        Returns:
            list[dict]: List of dictionaries containing:
                - field_id: ID of the field containing the formula
                - source_fields: List of field IDs used in the formula
        """
        formula_fields = []
        sections = self.form_spec.get("sections", {})

        # Process fields in main sections
        for section in sections.values():
            for field_id, field in section.get("fields", {}).items():
                if field.get("formula") is not None:
                    source_fields = get_source_fields_used(field["formula"])
                    for obj in field["formula"]:
                        if (
                            obj.get("token_type") == "FUNCTIONS"
                            and obj.get("token", {}).get("function_name")
                            == "GetLineItemValue"
                        ):
                            source_fields.append("quote_line_item")

                    formula_fields.append(
                        {
                            "field_id": field_id,
                            "source_fields": source_fields,
                        }
                    )

            # Process fields in sub-sections
            for sub in section.get("sub_sections", {}).values():
                for field_id, field in sub.get("fields", {}).items():
                    if field.get("formula") is not None:
                        source_fields = get_source_fields_used(field["formula"])
                        for obj in field["formula"]:
                            if (
                                obj.get("token_type") == "FUNCTIONS"
                                and obj.get("token", {}).get("function_name")
                                == "GetLineItemValue"
                            ):
                                source_fields.append("quote_line_item")

                        formula_fields.append(
                            {
                                "field_id": field_id,
                                "source_fields": source_fields,
                            }
                        )

        return formula_fields

    def get_quote_line_item_fields(self):
        """
        Returns all fields that are quote line item fields
        """
        return [
            field_id
            for field_id in self.get_all_fields()
            if self.get_field(field_id).get("is_quote_line_item_field")
        ]

    def get_fields_by_data_type(self, data_type: str):
        """
        Returns all fields that have the given data type
        """
        return [
            field_id
            for field_id in self.get_all_fields()
            if self.get_field(field_id).get("data_type") == data_type
        ]

    # ruff: noqa: FBT001
    def get_all_lookup_fields_by_data_type(
        self, data_type: str, is_quote_line_item_field: bool
    ):
        """
        Returns all fields that are lookup fields
        """
        lookup_fields = []
        for field_id in self.get_all_fields():
            field = self.get_field(field_id)
            if (
                "options_lookup_spec" in field
                and field.get("data_type") == data_type
                and field.get("is_quote_line_item_field") == is_quote_line_item_field
            ):
                lookup_fields.append(field_id)
        return lookup_fields

    def get_fields_and_sections(self):
        """
        Returns a list of fields and sections
        """
        fields = {}
        sections = {}
        for section_id, section in self.form_spec["sections"].items():
            sections[section_id] = {
                "id": section_id,
                "label": section["label"],
                "help_text": section["help_text"],
                "description": section["description"],
                "is_hidden": section["is_hidden"],
            }
            for field_id, field in section["fields"].items():
                if field_id == "field1":
                    continue
                fields[field_id] = field
        return {"fields": fields, "sections": sections}
