import logging
import re
from enum import Enum
from typing import Dict, List

from everstage_ddd.cpq.forms.accessors.form_accessor import (
    FormBuilderAccessor,
    FormRuleAccessor,
)

logger = logging.getLogger(__name__)


class FormDataType(Enum):
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"


# Mapping of internal data types to formatted data types
DATA_TYPE_MAPPING: Dict[str, str] = {
    FormDataType.STRING.value: "String",
    FormDataType.NUMBER.value: "Integer",
    FormDataType.BOOLEAN.value: "Boolean",
}


def _get_all_form_rule_names(client_id: int) -> list[str]:
    """
    Get all form builder names for a given client.

    Args:
        client_id (int): The ID of the client.

    Returns:
        list[str]: A list of form builder names in lowercase.

    Example:
        _get_all_form_builder_names(1)
        >>> ["test form builder", "sample form", "quotation form"]
    """
    # Fetch all form builders for the client and extract names
    form_rules = FormRuleAccessor(client_id).get_all_form_rules(is_automated_rule=False)
    # Convert names to lowercase for case-insensitive comparison
    all_form_rule_names = [
        form_rule["form_rule_name"].lower() for form_rule in form_rules
    ]
    return all_form_rule_names


def get_new_form_rule_name(client_id: int, form_rule_name: str) -> str:
    """
    Generate a unique name for a form rule by appending 'Copy(n)' if name already exists.

    Args:
        client_id (int): The ID of the client.
        form_rule_name (str): The original form rule name.

    Returns:
        str: A unique form builder name.

    Example:
        get_new_form_rule_name(1, "Sample Form rule")
        >>> "Sample Form rule(1)"  # if "Sample Form rule" already exists
        >>> "Sample Form rule(2)"  # if "Sample Form rule(1)" also exists
    """
    logger.info("BEGIN: Get new form rule name for client_id: %s", client_id)
    all_form_rule_names = _get_all_form_rule_names(client_id)
    # Regex to match the pattern 'Copy(<number>)' at the end of the quote name
    copy_pattern = re.compile(r"(.*?)( Copy\((\d+)\))?$")

    # Match the quote name to see if it already contains a 'Copy <number>'
    match = copy_pattern.match(form_rule_name)
    copy_number = 0
    base_name = form_rule_name
    if match:
        base_name = match.group(1)  # Original quote name without 'Copy <number>'
        copy_number = (
            int(match.group(3)) if match.group(3) else 0
        )  # Existing copy number

    new_form_rule_name = form_rule_name
    counter = (
        copy_number + 1
    )  # Start incrementing from the next number if it already exists
    while new_form_rule_name.lower() in all_form_rule_names:
        new_form_rule_name = f"{base_name} Copy({counter})"
        counter += 1
    return new_form_rule_name


def source_to_dest_mapping(dest_to_source):
    source_to_dest = {}

    # Iterate through the original mapping
    for dest_field, source_fields in dest_to_source.items():
        # For each source field in the array
        for source_field in source_fields:
            # If source field is not yet a key in new map, initialize it with empty list
            if source_field not in source_to_dest:
                source_to_dest[source_field] = []
            # Add the destination field to the source field's array
            source_to_dest[source_field].append(dest_field)

    return source_to_dest


def get_source_fields_used(condition: list):
    """
    This function gets the form variables used in the condition
    """
    from commission_engine.services.expression_designer import TokenTypesV2

    variables = set()
    for token in condition:
        if isinstance(token, list):
            variables.update(get_source_fields_used(token))
        if not isinstance(token, dict):
            continue
        if token.get("token_type") == TokenTypesV2.FORM_VARIABLES.name:
            variables.add(token["token"]["system_name"])
        elif token.get("token_type") == TokenTypesV2.FUNCTIONS.name and isinstance(
            token["token"]["args"], list
        ):
            variables.update(get_source_fields_used(token["token"]["args"]))
    return list(variables)


def _get_formatted_data_type(field: dict) -> str:
    """
    Convert internal data type to formatted data type for form fields.

    Args:
        field (dict): Field specification containing data_type

    Returns:
        str: Formatted data type string. Defaults to "String" if type is unknown

    Example:
        >>> _get_formatted_data_type({"data_type": "number"})
        'Integer'
    """
    data_type = field.get("data_type", "")
    return DATA_TYPE_MAPPING.get(data_type, "String")


def get_not_null_condition(
    client_id: int, fields: list[str], form_builder_id: str
) -> list[dict]:
    """
    Generate a list of conditions checking if any of the given fields are not empty,
    connected by OR operators.

    Args:
        fields: List of field names (e.g., ['field14', 'field19'])

    Returns:
        List of dictionaries representing the condition structure
    """
    from everstage_ddd.cpq.forms.service.form_spec_navigator import FormSpecNavigator

    conditions = []
    form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)
    form_spec = form_builder.form_spec
    form_navigator = FormSpecNavigator(form_spec)
    all_fields = form_navigator.get_all_fields()
    for i, field_id in enumerate(fields):
        # Add OR operator between conditions (except for the first field)
        if i > 0:
            conditions.append(
                {"token_type": "OPERATORS", "token": {"name": "OR", "key": "OR"}}
            )
        if field_id not in all_fields:
            conditions.append(
                {
                    "token_type": "CONSTANT_VARIABLES",
                    "token": {
                        "args": ["Boolean", "True"],
                        "name": "True",
                        "key": "True",
                        "data_type": "Boolean",
                    },
                }
            )
            continue
        field = form_navigator.get_field(field_id)
        # Add IsNotEmpty condition for the field
        conditions.append(
            {
                "token_type": "FUNCTIONS",
                "token": {
                    "name": f"IsNotEmpty({field_id})",
                    "key": f"IsNotEmpty({field_id})",
                    "data_type": "Boolean",
                    "function_name": "IsNotEmpty",
                    "args": [
                        {
                            "token_type": "FORM_VARIABLES",
                            "token": {
                                "system_name": field_id,
                                "key": field_id,
                                "name": field_id,
                                "data_type": _get_formatted_data_type(field),
                            },
                        }
                    ],
                    "type": "VARIABLE",
                    "token_category": "DYNAMIC",
                },
            }
        )

    return conditions


def create_trigger_condition(source_fields: List[str]) -> str:
    """Creates a trigger condition string using HasChanged for each source field."""
    conditions = [f"HasChanged({field})" for field in source_fields]
    return " or ".join(conditions)
