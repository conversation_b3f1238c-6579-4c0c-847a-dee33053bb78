import uuid
from copy import deepcopy
from logging import getLogger
from typing import Any, Dict, List

from everstage_ddd.cpq.forms.accessors.form_accessor import FormBuilderAccessor
from everstage_ddd.cpq.forms.service.form_rules_helper import (
    create_trigger_condition,
    get_not_null_condition,
)
from everstage_ddd.cpq.forms.service.form_spec_navigator import FormSpecNavigator

logger = getLogger(__name__)


def get_default_actions_for_rule(client_id, actions, form_builder_id):
    default_actions = []
    for _action in actions:
        then_actions = _action.get("then", [])
        for then_action in then_actions:
            default_action = deepcopy(then_action)
            if default_action.get("function") in [
                "MakeVisible",
                "MakeMandatory",
                "MakeReadOnly",
            ]:
                default_action["value"] = not default_action["value"]
            elif default_action.get("function") == "SetValue":
                default_action["value"] = _get_default_value_for_set_value(
                    client_id, default_action.get("component_id"), form_builder_id
                )
            elif default_action.get("function") == "SetOptions":
                default_action["value"] = ""
            elif default_action.get("function") == "AddQuoteLineItem":
                default_action["function"] = "RemoveQuoteLineItem"
            elif default_action.get("function") == "RemoveQuoteLineItem":
                default_action["function"] = "AddQuoteLineItem"
            # elif default_action.get("function") == "UpdateQuoteLineItem":
            #     default_action["function"] = "RemoveQuoteLineItem"
            default_actions.append(default_action)

    return default_actions


def _get_default_value_for_set_value(client_id, field, form_builder_id):
    form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)
    form_spec = form_builder.form_spec
    form_navigator = FormSpecNavigator(form_spec)
    field = form_navigator.get_field(field)

    # move this to a const file
    default_values = {"string": "", "number": 0, "boolean": False}

    return default_values.get(field.get("data_type"), "")


def create_rules_for_lookup_fields(
    client_id, form_builder_id, source_to_dest, form_navigator
):
    logger.info(
        "BEGIN: create_rules_for_lookup_fields for client_id: %s - form_builder_id: %s",
        client_id,
        form_builder_id,
    )
    rules = []

    # Create rules from the mapping
    for source_field, dest_fields in source_to_dest.items():
        _source_field = [source_field, "quote_line_item"]
        rule = {
            "rule_id": uuid.uuid4(),
            "source_fields": _source_field,
            "destination_fields": dest_fields,
            "is_lookup": True,
            "trigger": f"HasChanged({source_field})",
            "actions": [
                {
                    "condition": get_not_null_condition(
                        client_id, [source_field], form_builder_id
                    ),
                    "then": [],
                },
                {
                    "default": [
                        {
                            "function": "SetValue",
                            "form_component": "field",
                            "component_id": dest_field,
                            "value": "",
                        }
                        for dest_field in dest_fields
                    ]
                },
            ],
        }
        rules.append(rule)
    logger.info(
        "END: create_rules_for_lookup_fields for client_id: %s - form_builder_id: %s",
        client_id,
        form_builder_id,
    )
    return rules


def create_rules_for_formula_fields(
    client_id: int, form_builder_id: str, formula_fields_map: list[dict]
) -> List[Dict[str, Any]]:
    """
    Constructs rules based on formula fields in the required structure with triggers and actions.

    Args:
        client_id: The client ID
        form_builder_id: The form builder ID

    Returns:
        List of rules with triggers and actions
    """
    logger.info(
        "BEGIN: create_rules_for_formula_fields for client_id: %s - form_builder_id: %s",
        client_id,
        form_builder_id,
    )
    rules = []

    for field in formula_fields_map:
        if not field.get("source_fields"):
            continue
        rule = {
            "rule_id": uuid.uuid4(),
            "source_fields": field.get("source_fields"),
            "destination_fields": [field["field_id"]],
            "is_formula": True,
            "trigger": create_trigger_condition(field.get("source_fields") or []),
            "actions": [
                {
                    "condition": get_not_null_condition(
                        client_id, field.get("source_fields") or [], form_builder_id
                    ),
                    "then": [
                        {
                            "function": "SetValue",
                            "form_component": "field",
                            "component_id": field["field_id"],
                            "value": "",  # The actual formula value will be computed runtime
                        }
                    ],
                },
                {
                    "default": [
                        {
                            "function": "SetValue",
                            "form_component": "field",
                            "component_id": field["field_id"],
                            "value": "",
                        }
                    ],
                },
            ],
        }

        rules.append(rule)
    logger.info(
        "END: create_rules_for_formula_fields for client_id: %s - form_builder_id: %s",
        client_id,
        form_builder_id,
    )

    return rules
