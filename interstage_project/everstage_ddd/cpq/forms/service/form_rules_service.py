from copy import deepcopy
from logging import getLogger

from django.db import transaction
from django.forms.models import model_to_dict
from django.utils import timezone

from everstage_ddd.cpq.approvals.service.approvals_helper_service import (
    convert_to_snake_case,
)
from everstage_ddd.cpq.exceptions import CPQCustomError
from everstage_ddd.cpq.forms import FormSpecNavigator
from everstage_ddd.cpq.forms.accessors.form_accessor import (
    FormBuilderAccessor,
    FormRuleAccessor,
)
from everstage_ddd.cpq.forms.service.form_rules_handler import (
    create_rules_for_formula_fields,
    create_rules_for_lookup_fields,
    get_default_actions_for_rule,
)
from everstage_ddd.cpq.forms.service.form_rules_helper import (
    get_new_form_rule_name,
    source_to_dest_mapping,
)

logger = getLogger(__name__)


@transaction.atomic
def create_default_rules(client_id, form_builder_id, logged_in_user):
    logger.info("BEGIN: create_default_rules for client_id: %s", client_id)
    form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)
    form_spec = form_builder.form_spec
    form_navigator = FormSpecNavigator(form_spec)
    lookup_fields_map = form_navigator.get_lookup_fields_with_filters()
    formula_fields_map = form_navigator.get_formula_fields()
    # Create a mapping of source fields to their dependent fields
    source_to_dest = source_to_dest_mapping(lookup_fields_map)
    lookup_rules = create_rules_for_lookup_fields(
        client_id, form_builder_id, source_to_dest, form_navigator
    )
    formula_rules = create_rules_for_formula_fields(
        client_id, form_builder_id, formula_fields_map
    )
    form_rule_accessor = FormRuleAccessor(client_id)
    default_rules = [
        {
            "form_builder_id": form_builder_id,
            "form_rule_id": rule["rule_id"],
            "source_fields": rule["source_fields"],
            "destination_fields": rule["destination_fields"],
            "is_lookup": rule.get("is_lookup", False),
            "is_formula": rule.get("is_formula", False),
            "trigger": rule["trigger"],
            "actions": rule["actions"],
            "is_automated_rule": True,
            "status": rule.get("status", "active"),
            "knowledge_begin_date": timezone.now(),
            "created_by": logged_in_user,
            "created_at": timezone.now(),
            "client_id": client_id,
        }
        for rule in lookup_rules + formula_rules
    ]
    form_rule_accessor.invalidate_object_by_form_builder(
        form_builder_id, is_automated_rule=True
    )
    form_rule_accessor.bulk_insert_objects(default_rules)
    logger.info("END: create_default_rules for client_id: %s", client_id)
    return default_rules


@transaction.atomic
def clone_form_rule_service(client_id, logged_in_user, form_rule_id):
    logger.info("BEGIN: Clone form rule for client_id: %s", client_id)
    form_rule_accessor = FormRuleAccessor(client_id)
    form_rule = form_rule_accessor.get_form_rule(form_rule_id)
    if not form_rule:
        logger.error(
            "Form rule not found for client_id: %s, form_rule_id: %s",
            client_id,
            form_rule_id,
        )
        return {
            "message": "Form rule not found",
            "status": "error",
        }
    cloned_form_rule = {
        "form_builder_id": form_rule.form_builder_id,
        "form_rule_name": get_new_form_rule_name(client_id, form_rule.form_rule_name),
        "form_rule_description": form_rule.form_rule_description,
        "is_automated_rule": form_rule.is_automated_rule,
        "actions": form_rule.actions,
        "source_fields": form_rule.source_fields,
        "destination_fields": form_rule.destination_fields,
        "is_lookup": form_rule.is_lookup,
        "is_formula": form_rule.is_formula,
        "trigger": form_rule.trigger,
        "status": "inactive",
        "created_by": logged_in_user,
        "created_at": timezone.now(),
        "knowledge_begin_date": timezone.now(),
    }
    cloned_form_rule = form_rule_accessor.create_object(cloned_form_rule)
    cloned_form_rule_dict = model_to_dict(cloned_form_rule)

    logger.info("END: Clone form rule for client_id: %s", client_id)
    return {
        "message": "Form rule cloned successfully",
        "status": "success",
        "form_rule_id": cloned_form_rule_dict["form_rule_id"],
    }


@transaction.atomic
def create_form_rule_service(
    client_id: int, logged_in_user: str, **kwargs: dict
) -> dict:
    """
    Create a new form rule.

    Args:
        client_id: The client ID
        logged_in_user: Username of the user creating the rule
        **kwargs: Form rule data including form_builder_id, rule name, description, etc.

    Returns:
        dict: Created form rule data
    """
    logger.info("BEGIN: Create form rule for client_id: %s", client_id)
    form_builder_id = kwargs["form_builder_id"]
    form_rule_name = kwargs["form_rule_name"]
    form_rule_description = kwargs.get("form_rule_description")

    form_rule_data = {
        "form_builder_id": form_builder_id,
        "form_rule_name": form_rule_name,
        "form_rule_description": form_rule_description,
        "source_fields": [],
        "destination_fields": [],
        "status": "inactive",
        "created_by": logged_in_user,
        "created_at": timezone.now(),
        "knowledge_begin_date": timezone.now(),
    }

    form_rule_name_exists = FormRuleAccessor(client_id).form_rule_exists_by_name(
        form_rule_name
    )
    if form_rule_name_exists:
        logger.error(
            "Form rule name already exists for client_id: %s, form_rule_name: %s",
            client_id,
            form_rule_name,
        )
        raise CPQCustomError(
            code="FORM_RULE_NAME_ALREADY_EXISTS",
            message="Quote rule name already exists",
        )

    form_rule = FormRuleAccessor(client_id).create_object(form_rule_data)
    form_rule_dict = model_to_dict(form_rule)

    logger.info("END: Create form rule for client_id: %s", client_id)
    return {
        "message": "Form rule created successfully",
        "status": "success",
        "form_rule_id": form_rule_dict["form_rule_id"],
    }


@transaction.atomic
def delete_form_rule_service(client_id: int, **kwargs: dict) -> dict:
    """
    Delete a form rule by ID.

    Args:
        client_id: The client ID
        **kwargs: Contains form_rule_id

    Returns:
        dict: Status message
    """
    logger.info("BEGIN: Delete form rule for client_id: %s", client_id)

    form_rule_id = kwargs["form_rule_id"]
    form_rule_accessor = FormRuleAccessor(client_id)

    # Check if form rule exists
    form_rule = form_rule_accessor.get_form_rule(form_rule_id)
    if not form_rule:
        logger.error(
            "Form rule not found for client_id: %s, form_rule_id: %s",
            client_id,
            form_rule_id,
        )
        return {"status": "error", "message": "Form rule not found"}

    form_rule_accessor.delete_object(form_rule_id)

    logger.info("END: Delete form rule for client_id: %s", client_id)
    return {"status": "success", "message": "Form rule deleted successfully"}


@transaction.atomic
def update_form_rule_service(
    client_id: int, logged_in_user: str, **kwargs: dict
) -> dict:
    """
    Update a form rule by ID.

    Args:
        client_id: The client ID
        logged_in_user: Username of the user updating the rule
        **kwargs: Contains form_rule_id and updated_fields

    Returns:
        dict: Status message
    """
    logger.info("BEGIN: Update form rule for client_id: %s", client_id)

    form_rule_id = kwargs["form_rule_id"]
    updated_fields = kwargs["updated_fields"]

    form_rule_accessor = FormRuleAccessor(client_id)
    form_rule = form_rule_accessor.get_non_automated_form_rule(form_rule_id)

    if not form_rule:
        logger.error(
            "Form rule not found for client_id: %s, form_rule_id: %s",
            client_id,
            form_rule_id,
        )
        return {"status": "error", "message": "Form rule not found"}

    # Create a deep copy to avoid modifying the original object
    updated_form_rule = deepcopy(form_rule)

    updated_form_rule.knowledge_begin_date = timezone.now()
    updated_form_rule.updated_by = logged_in_user
    updated_form_rule.updated_at = timezone.now()

    if "form_rule_name" in updated_fields and updated_fields["form_rule_name"]:
        # check if the form rule name already exists
        form_rule_name_exists = form_rule_accessor.form_rule_exists_by_name(
            updated_fields["form_rule_name"],
            form_rule_id,
        )
        if form_rule_name_exists:
            raise CPQCustomError(
                code="FORM_RULE_NAME_ALREADY_EXISTS",
                message="Form rule name already exists",
            )
    # Update fields with new values
    for key, value in updated_fields.items():
        if value is not None or key == "form_rule_description":
            setattr(updated_form_rule, key, value)

    formatted_actions = None
    if (
        "actions" in updated_fields
        and updated_fields["actions"]
        and len(updated_fields["actions"]) > 0
    ):
        formatted_actions = []
        condition = convert_to_snake_case(updated_fields["actions"][0]["condition"])
        then = convert_to_snake_case(updated_fields["actions"][0]["actionsToPerform"])
        formatted_actions.append({"condition": condition, "then": then})
        default_actions = get_default_actions_for_rule(
            client_id, formatted_actions, form_rule.form_builder_id
        )
        formatted_actions.append({"default": default_actions})
    # Invalidate the old version
    form_rule_accessor.invalidate_object(form_rule_id)
    updated_rule_dict = {
        "form_builder_id": updated_form_rule.form_builder_id,
        "form_rule_id": updated_form_rule.form_rule_id,
        "form_rule_name": updated_form_rule.form_rule_name,
        "form_rule_description": updated_form_rule.form_rule_description,
        "is_automated_rule": updated_form_rule.is_automated_rule,
        "status": updated_form_rule.status,
        "source_fields": updated_form_rule.source_fields,
        "destination_fields": updated_form_rule.destination_fields,
        "is_lookup": updated_form_rule.is_lookup,
        "is_formula": updated_form_rule.is_formula,
        "trigger": updated_form_rule.trigger,
        "actions": (
            formatted_actions if formatted_actions else updated_form_rule.actions
        ),
        "created_by": updated_form_rule.created_by,
        "updated_by": updated_form_rule.updated_by,
        "created_at": updated_form_rule.created_at,
        "updated_at": updated_form_rule.updated_at,
        "knowledge_begin_date": timezone.now(),
    }
    # Insert new version
    form_rule = form_rule_accessor.insert_object(updated_rule_dict)

    logger.info("END: Update form rule for client_id: %s", client_id)
    return {"status": "success", "message": "Form rule updated successfully"}


def get_form_rule_service(client_id: int, form_rule_id: str) -> dict:
    """
    Get a form rule by ID.

    Args:
        client_id: The client ID
        form_rule_id: The form rule ID

    Returns:
        dict: Form rule data if found, error message if not found
    """
    logger.info("BEGIN: Get form rule for client_id: %s", client_id)

    form_rule_accessor = FormRuleAccessor(client_id)
    form_rule = form_rule_accessor.get_form_rule(form_rule_id)

    if not form_rule:
        logger.error(
            "Form rule not found for client_id: %s, form_rule_id: %s",
            client_id,
            form_rule_id,
        )
        return {"status": "error", "message": "Form rule not found"}
    form_rule_dict = model_to_dict(form_rule)
    if "actions" in form_rule_dict and form_rule_dict["actions"]:
        form_rule_dict["actions"] = [
            action for action in form_rule_dict["actions"] if "default" not in action
        ]

    logger.info("END: Get form rule for client_id: %s", client_id)
    return {
        "form_rule": form_rule_dict,
        "status": "success",
    }


def get_all_form_rules_service(client_id: int, **kwargs: dict) -> list[dict]:
    """
    Get all form rules for a client with their associated form builder names.

    Args:
        client_id: The client ID

    Returns:
        list[dict]: List of form rules with form builder details
    """
    logger.info("BEGIN: Get all form rules for client_id: %s", client_id)

    search_term = kwargs.get("search_term")
    limit = kwargs.get("limit_value", 100)
    page_number = kwargs.get("page_number", 1)
    offset = (page_number - 1) * limit

    form_rule_accessor = FormRuleAccessor(client_id)
    form_builder_accessor = FormBuilderAccessor(client_id)

    # Get all form rules
    form_rules = form_rule_accessor.get_all_form_rules(
        search_term=search_term,
        limit=limit,
        offset=offset,
        is_automated_rule=False,
    )

    # Get all form builders to create a mapping of id to name
    form_builders = {
        str(fb["form_builder_id"]): fb["form_builder_name"]
        for fb in form_builder_accessor.get_all_form_builders()
    }

    # Combine form rules with form builder names
    result = []
    for rule in form_rules:
        # rule_dict = model_to_dict(rule)
        rule["form_builder_name"] = form_builders.get(
            str(rule["form_builder_id"]), "Unknown"
        )
        result.append(rule)

    logger.info("END: Get all form rules for client_id: %s", client_id)
    return result


def get_fields_and_sections_service(client_id: int, form_builder_id: str) -> list[dict]:
    """
    Get fields and sections for a form builder.

    Args:
        client_id: The client ID
        form_builder_id: The form builder ID

    Returns:
        list[dict]: List of fields and sections
    """
    logger.info("BEGIN: Get fields and sections for client_id: %s", client_id)

    form_builder_accessor = FormBuilderAccessor(client_id)
    form_builder = form_builder_accessor.get_form_builder(form_builder_id)
    form_spec = form_builder.form_spec
    form_navigator = FormSpecNavigator(form_spec)
    fields_and_sections = form_navigator.get_fields_and_sections()
    logger.info("END: Get fields and sections for client_id: %s", client_id)
    return fields_and_sections
