from ..form_types import FormModel
from .form_action import FormAction


class MakeMandatory(FormAction):
    def __init__(self, client_id, form_spec: FormModel) -> None:
        self.client_id = client_id
        self.form_spec = form_spec
        self.property = "is_mandatory"
        super().__init__(form_spec)

    def make_change(
        self, form_component: str, component_id: str, value: bool  # noqa: FBT001
    ):
        if form_component == "section":
            section = self.form_navigator.get_section(component_id)
            section[self.property] = value
        elif form_component == "sub_section":
            sub_section = self.form_navigator.get_sub_section(component_id)
            sub_section[self.property] = value
        elif form_component == "field":
            field = self.form_navigator.get_field(component_id)
            field["properties"][self.property] = value
        return self.form_spec
