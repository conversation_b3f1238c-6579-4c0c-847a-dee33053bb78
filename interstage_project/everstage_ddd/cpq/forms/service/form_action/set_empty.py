from typing import Any

from ..form_types import FormModel
from .form_action import FormAction


class SetEmpty(FormAction):
    def __init__(self, client_id: str, form_spec: FormModel) -> None:
        self.client_id = client_id
        self.form_spec = form_spec
        self.property = "value"
        super().__init__(form_spec)

    # ruff: noqa: ARG002
    def make_change(
        self, form_component: str, component_id: str, value: Any = None
    ) -> FormModel:
        if form_component == "field":
            field = self.form_navigator.get_field(component_id)
            if field.get("field_type") == "multi_select":
                field[self.property] = []
            elif field.get("field_type") == "checkbox":
                field[self.property] = False
            else:
                field[self.property] = None
        return self.form_spec
