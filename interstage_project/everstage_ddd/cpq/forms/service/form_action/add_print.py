import logging

from .form_action import FormAction

logger = logging.getLogger(__name__)


class AddPrint(FormAction):
    def __init__(self, client_id, form_spec) -> None:
        self.client_id = client_id
        self.form_spec = form_spec
        super().__init__(form_spec)

    def make_change(self, form_component, component_id, value):  # noqa: ARG002
        logger.info("IN ADD PRINT - %s", value)
        return self.form_spec
