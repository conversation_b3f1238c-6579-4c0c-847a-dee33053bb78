import logging

from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)
from everstage_ddd.cpq.quote.service.quote_line_item_service import (
    build_basic_quote_line_item,
    build_quote_line_item_rec,
)
from everstage_ddd.cpq.quote.service.quote_service import configure_pricing

from ....product_catalog.constants import PRICE_BOOK_ID
from ....product_catalog.utils import sanitize_for_json
from ..form_spec_navigator import FormSpecNavigator
from ..form_types import FormModel
from .form_action import FormAction

logger = logging.getLogger(__name__)


class UpdateQuoteLineItem(FormAction):
    def __init__(self, client_id, form_spec: FormModel) -> None:
        form_navigator = FormSpecNavigator(form_spec)
        quote_id_field = form_navigator.get_field("field1")
        self.form_spec = form_spec
        self.client_id = client_id
        self.quote_id = quote_id_field["value"]
        super().__init__(form_spec)

    # value = [
    #     {
    #         "sku": "a3b0dad0-89c1-4ba0-a156-e780a5cbd21c",
    #         "property":"discount_percent",
    #         "value":20
    #     },
    #     {
    #         "sku": "a3b0dad0-89c1-4ba0-a156-e780a5cbd21c",
    #         "property":"discount_percent",
    #         "value":20
    #     }
    # ]
    # ruff: noqa: ARG002 PLR0912
    def make_change(self, form_component: str, component_id: str, value: list):
        quote_line_item_acc = QuoteLineItemAccessor(self.client_id)
        existing_skus = []
        existing_line_items = []
        unique_skus = set([val.get("sku") for val in value])
        for sku in unique_skus:
            _existing_line_items = quote_line_item_acc.get_line_items_for_quote_by_sku(
                self.quote_id, sku, as_dicts=True
            )
            if not _existing_line_items:
                logger.info(f"Quote Line Item not found for product {sku}")
                continue
            existing_skus.append(sku)
            existing_line_items.extend(_existing_line_items)
        product_property_map = {}
        for obj in value:
            _sku = obj.get("sku")
            _property = obj.get("property")
            _value = obj.get("value")
            if _sku not in existing_skus:
                logger.info(f"Quote Line Item not found for product {_sku}")
                continue
            if _sku not in product_property_map:
                product_property_map[_sku] = {}
            product_property_map[_sku][_property] = _value

        quote_line_item_rec = []
        (sku_pricepoint_dict, sku_catalog_dict) = configure_pricing(
            self.client_id, self.quote_id, PRICE_BOOK_ID, existing_skus, {}
        )
        for existing_line_item in existing_line_items:
            price_point_updated = False
            price_point_id = None
            existing_line_item_sku = existing_line_item.get("sku")

            start_date = existing_line_item.get("start_date")
            end_date = existing_line_item.get("end_date")
            for property, new_value in product_property_map.get(
                existing_line_item_sku, {}
            ).items():
                if property == "pricepoints":
                    price_point_updated = True
                    price_point_id = new_value
                    continue
                existing_line_item[property] = new_value

            pb_rec = existing_line_item.get("pricepoint_data")
            if (start_date and end_date and pb_rec) or price_point_updated:
                if price_point_updated:
                    pricepoints = sku_pricepoint_dict.get(existing_line_item_sku)
                    if pricepoints:
                        for pricepoint in pricepoints:
                            if str(pricepoint.get("pricepoint_id")) == str(
                                price_point_id
                            ):
                                pb_rec = pricepoint
                                existing_line_item["list_unit_price"] = pb_rec[
                                    "list_price"
                                ]
                                existing_line_item["net_unit_price"] = pb_rec[
                                    "list_price"
                                ]
                                existing_line_item["discount_percent"] = 0
                                break
                catalog_record = sku_catalog_dict.get(existing_line_item_sku)
                rec = build_quote_line_item_rec(
                    self.client_id,
                    existing_line_item_sku,
                    pb_rec,
                    catalog_record,
                    existing_line_item,
                )
                quote_line_item_rec.append(sanitize_for_json(rec))
            else:  # call simple function
                quote_line_item_rec.append(
                    sanitize_for_json(
                        build_basic_quote_line_item(self.client_id, existing_line_item)
                    )
                )
        if quote_line_item_rec:
            quote_line_item_acc.update_quote_line_items(
                self.quote_id, quote_line_item_rec
            )
        return self.form_spec
