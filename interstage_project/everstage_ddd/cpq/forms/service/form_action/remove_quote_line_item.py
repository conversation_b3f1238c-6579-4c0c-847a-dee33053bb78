from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)

from ..form_spec_navigator import FormSpecNavigator
from ..form_types import FormModel
from .form_action import FormAction


class RemoveQuoteLineItem(FormAction):
    def __init__(self, client_id, form_spec: FormModel) -> None:
        form_navigator = FormSpecNavigator(form_spec)
        quote_id_field = form_navigator.get_field("field1")
        self.form_spec = form_spec
        self.client_id = client_id
        self.quote_id = quote_id_field["value"]
        super().__init__(form_spec)

    # ruff: noqa: ARG002
    def make_change(self, form_component: str, component_id: str, value: dict):
        quote_line_item_acc = QuoteLineItemAccessor(self.client_id)
        sku = value
        quote_line_item_acc.remove_sku_from_quote(self.quote_id, sku)
        return self.form_spec
