from dateutil.relativedelta import relativedelta
from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_settings
from everstage_ddd.cpq.quote.accessors.quote_line_item_accessor import (
    QuoteLineItemAccessor,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector
from everstage_ddd.cpq.quote.service.quote_line_item_service import (
    _check_duplicates,
    create_quote_line_items,
)
from everstage_ddd.cpq.quote.service.quote_service import configure_pricing

from ..form_spec_navigator import FormSpecNavigator
from ..form_types import FormModel
from .form_action import FormAction


class AddQuoteLineItem(FormAction):
    def __init__(self, client_id, form_spec: FormModel) -> None:
        form_navigator = FormSpecNavigator(form_spec)
        quote_id_field = form_navigator.get_field("field1")
        self.form_spec = form_spec
        self.client_id = client_id
        self.quote_id = quote_id_field["value"]
        super().__init__(form_spec)

    # copy_flag should be set to True , so that addition happends in all phases
    # ruff: noqa: ARG002
    def make_change(self, form_component: str, component_id: str, value: dict):
        quote_line_item_acc = QuoteLineItemAccessor(self.client_id)
        existing_line_items = quote_line_item_acc.get_line_items_for_quote(
            self.quote_id, as_dicts=True
        )
        sku = value

        client_settings = get_client_settings(self.client_id)
        pricebook_id = client_settings.get("cpq_settings", {}).get("pricebook_id")

        (sku_pricepoint_dict, sku_catalog_dict) = configure_pricing(
            self.client_id, self.quote_id, pricebook_id, sku, {}
        )
        quote_obj = QuoteSelector(self.client_id).get_quote_by_id(self.quote_id)
        duration_type = quote_obj.duration_type
        duration_value = quote_obj.duration_value
        phase_info = {}
        if not existing_line_items:
            # inserting first line item
            # create new phase with QUOTE'S DURATION TYPE and DURATION VALUE and start date as current date
            phase_info["duration_type"] = duration_type if duration_type else "years"
            phase_info["duration_value"] = duration_value if duration_value else 1
            start_date = quote_obj.start_date
            end_date = quote_obj.end_date
            if not start_date:
                start_date = timezone.now()
            if not end_date:
                end_date = start_date + relativedelta(years=1)
            phase_info["start_date"] = start_date
            phase_info["end_date"] = end_date
        else:
            existing_line_items = sorted(
                existing_line_items, key=lambda x: x["start_date"]
            )

            if duration_type:
                phase_info["duration_type"] = duration_type
                phase_info["duration_value"] = quote_obj.duration_value
                phase_info["start_date"] = existing_line_items[0]["start_date"]
                phase_info["end_date"] = existing_line_items[0]["end_date"]
            else:  # existing items also added using rules.. set default duration to 1 yr
                phase_info["duration_type"] = "years"
                phase_info["duration_value"] = 1
                phase_info["start_date"] = existing_line_items[0]["start_date"]
                phase_info["end_date"] = existing_line_items[0]["end_date"]

        (line_items_dicts, errors) = create_quote_line_items(
            self.client_id,
            self.quote_id,
            sku,
            phase_info,
            sku_pricepoint_dict=sku_pricepoint_dict,
            sku_catalog_dict=sku_catalog_dict,
            copy_flag=True,
        )
        quote_line_items = quote_line_item_acc.get_line_items_for_quote(
            self.quote_id, timezone.now(), as_dicts=False
        )
        _check_duplicates(self.client_id, self.quote_id, quote_line_items)

        return self.form_spec
