import logging

from django.core.cache import cache

from ..form_spec_navigator import FormSpecNavigator
from .look_up_data import LookUpData

logger = logging.getLogger(__name__)


class LookUpDatasheetData(LookUpData):
    """
    LookUpDatasheetData class is used to fetch data from datasheet
    """

    def __init__(self, client_id, look_up_spec, quote_id):
        self.look_up_spec = look_up_spec
        self.client_id = client_id
        self.databook_id = look_up_spec.get("databook_id")
        self.datasheet_id = look_up_spec.get("datasheet_id")
        self.filters = look_up_spec.get("filters")
        self.is_value_spec = look_up_spec.get("is_value_spec")
        self.column = look_up_spec.get("column")
        self.quote_id = quote_id

    def set_quote_field_values(self, token, form_navigator, df):
        """
        This is used to set the values of the quote fields in the dataframe
        """
        from commission_engine.services.expression_designer import TokenTypesV2

        token_type = token.get("token_type")
        if token_type == TokenTypesV2.FORM_VARIABLES.name:
            return self._handle_form_variables_token(token, form_navigator, df)
        if token_type == TokenTypesV2.FUNCTIONS.name:
            # Handle nested FORM_VARIABLES in function args
            function_token = token.get("token", {})
            args = function_token.get("args", [])
            updated_args = [
                self.set_quote_field_values(arg, form_navigator, df) for arg in args
            ]
            function_token["args"] = updated_args
            return token
        return token

    def _handle_form_variables_token(self, token, form_navigator, df):
        """
        Handles the conversion of FORM_VARIABLES token type to CONSTANT_VARIABLES
        """
        field_id = token.get("token").get("system_name")
        value = df[field_id].iloc[0]
        field = form_navigator.get_field(field_id)
        data_type = field.get("data_type", "String").capitalize()
        if data_type == "Number":
            data_type = "Integer"
        if value is None:
            value = 0 if data_type == "Integer" else ""

        return {
            "token_type": "CONSTANT_VARIABLES",
            "token": {
                "args": [data_type, value],
                "name": value,
                "key": value,
                "data_type": data_type,
            },
        }

    def process_filters(self, df):
        """
        The 'value' property in each filter of self.filters will be updated with form values dynamically
        df - pandas dataframe of form data
        """

        from ..form_builder_service import get_form_spec_by_quote_id

        form_spec = get_form_spec_by_quote_id(self.client_id, self.quote_id)

        form_navigator = FormSpecNavigator(form_spec)
        updated_filters = []
        for filter_item in self.filters:
            filter = self.set_quote_field_values(filter_item, form_navigator, df)
            updated_filters.append(filter)
        self.filters = updated_filters
        return self.filters

    def contains_filter(self, term):
        """
        This is used to filter out options as the user types in lookup field.
        """
        return {
            "token_type": "FUNCTIONS",
            "token": {
                "name": "Contains()",
                "key": "Contains()",
                "data_type": "Boolean",
                "function_name": "Contains",
                "args": [
                    {
                        "token_type": "DATASHEET_VARIABLES",
                        "token": {
                            "system_name": self.column,
                            "key": self.column,
                            "name": self.column,
                        },
                    },
                    {
                        "token_type": "CONSTANT_VARIABLES",
                        "token": {
                            "args": ["String", term],
                            "name": term,
                            "key": term,
                            "data_type": "String",
                        },
                    },
                ],
                "type": "VARIABLE",
                "token_category": "DYNAMIC",
            },
        }

    def get_data(self, df, params):
        from everstage_ddd.datasheet.data_models import DatasheetDataRequest
        from everstage_ddd.datasheet.services.datasheet_data import datasheet_data_fetch
        from spm.services.config_services.employee_services import (
            get_power_admin_users_for_client,
        )

        try:
            self.filters = self.process_filters(df)
            search_term = params.get("search_term")
            if search_term:
                search_filter = self.contains_filter(search_term)
                if len(self.filters) > 0:
                    self.filters.append(
                        {
                            "token_type": "OPERATORS",
                            "token": {"name": "AND", "key": "AND"},
                        }
                    )
                    self.filters.append(search_filter)
                else:
                    self.filters = [search_filter]
            power_admin = get_power_admin_users_for_client(self.client_id)[0]
            page_number = params.get("page_num", 0) + 1
            limit_value = params.get("limit_value", 100)

            cache_key = self.generate_cache_key(
                self.client_id, self.datasheet_id, self.filters, params
            )
            cached_data = cache.get(cache_key)

            if cached_data:
                data = cached_data
            else:
                request_model = DatasheetDataRequest(
                    client_id=self.client_id,
                    filters=self.filters,
                    page_number=page_number,
                    page_size=limit_value,
                    datasheet_id=self.datasheet_id,
                    databook_id=self.databook_id,
                    logged_in_user_email=power_admin,
                )
                result = datasheet_data_fetch(
                    request_model=request_model,
                )
                data = result.data
                cache.set(cache_key, data, timeout=3600)

            options_data = [
                {
                    "label": str(item[self.column]),
                    "value": str(item[self.column]),
                    "key": str(item["row_key"]),
                }
                for item in data
            ]

            return options_data
        except Exception as e:
            logger.info(f"Error in get_data: {e}")
            return []

    def generate_cache_key(self, client_id, datasheet_id, filters, params):
        filter_key = ""
        for filter_item in filters:
            token_type = filter_item.get("token_type")
            name = filter_item.get("token").get("name")
            filter_key += f"{token_type}:{name}::"
        search_term = params.get("search_term")
        page_number = params.get("page_num", 0)
        limit_value = params.get("limit_value", 100)
        return f"{client_id}_{datasheet_id}_{filter_key}_{search_term}_{page_number}_{limit_value}"
