from rest_framework.status import HTTP_400_BAD_REQUEST


class CPQError(Exception):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.code = "CPQ_ERROR"
        self.message = "CPQ Error"
        self.status = HTTP_400_BAD_REQUEST


class CPQCustomError(Exception):
    def __init__(self, code, message, status=HTTP_400_BAD_REQUEST) -> None:
        super().__init__()
        self.code = code
        self.message = message
        self.status = status
