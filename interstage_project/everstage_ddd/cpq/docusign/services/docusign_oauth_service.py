import datetime
import logging
import os

from django.core.cache import cache
from django.utils import timezone

from spm.serializers.docusign_serializers import DocusignSerializer

from ..accessors.docusign_accessor import DocusignCpqAccessor
from ..docusign_exception import DocusignCpqError
from .docusign_api_service import DocusignCpqApiService

DOCUSIGN_CLIENT_ID, DOCUSIGN_CLIENT_SECRET = (
    os.environ["DOCUSIGN_CLIENT_ID"],
    os.environ["DOCUSIGN_CLIENT_SECRET"],
)


def get_docusign_auth_details(client_id, email_id):
    oauth_data = DocusignCpqAccessor(client_id).get_oauth_details()
    if oauth_data is None:
        message = "Oauth data not present for the user"
        code = "DOCUSIGN_CPQ_OAUTH"
        raise DocusignCpqError(code, message)
    account_id = oauth_data.account_id
    docusign_service = DocusignCpqApiService(client_id, email_id, account_id)
    api_client = docusign_service.get_api_client()
    return account_id, api_client


def authenticate_docusign_user(client_id, api_client, code, login_email_id):
    time = timezone.now()
    oauth_data = {}
    response = api_client.generate_access_token(
        client_id=DOCUSIGN_CLIENT_ID,
        client_secret=DOCUSIGN_CLIENT_SECRET,
        code=code,
    )
    access_token = response.access_token
    refresh_token = response.refresh_token
    timeout = int(response.expires_in)
    refresh_token_expiry = time + datetime.timedelta(
        days=30
    )  # standard expiry is 30 days for refresh token
    # get user info
    user_info = api_client.get_user_info(access_token=access_token)
    base_uri_response = api_client.sanitize_for_serialization(user_info)
    logging.info("DOCUSIGN: get_user_info response %s", base_uri_response)

    # docusign data of the user
    oauth_data["client"] = client_id
    oauth_data["refresh_token"] = refresh_token
    oauth_data["refresh_token_expires_at"] = refresh_token_expiry
    oauth_data["knowledge_begin_date"] = timezone.now()
    oauth_data["email_id"] = base_uri_response.get("email")
    oauth_data["login_email_id"] = login_email_id
    oauth_data["additional_details"] = {"module": "CPQ"}
    accounts = base_uri_response.get("accounts", [])
    for account in accounts:
        is_default = account.get("is_default")
        if (
            str(is_default).lower() == "true"
        ):  # converting both bool and string value to string
            oauth_data["account_id"] = account.get("account_id")
            oauth_data["base_uri"] = account.get("base_uri")
            break
    cache_key = (
        f'docusign_token##{oauth_data.get("email_id")}##{oauth_data.get("account_id")}'
    )
    cache.set(cache_key, access_token, timeout)

    ser = DocusignSerializer(data=oauth_data)
    if ser.is_valid(raise_exception=True):
        DocusignCpqAccessor(client_id).invalidate(
            account_id=oauth_data["account_id"], knowledge_date=time
        )
        ser.save()


def disconnect_docusign_user(client_id, login_email_id, action_summary):
    time = timezone.now()
    logging.info({"email_id": login_email_id, "action": action_summary})

    prev_record = DocusignCpqAccessor(client_id).get_oauth_details()

    DocusignCpqAccessor(client_id).delete_client_account(
        prev_record=prev_record, knowledge_date=time
    )
    return {"status": "Success"}


def has_docusign_integration(client_id, login_email_id):
    oauth_data = DocusignCpqAccessor(client_id).get_oauth_details()
    token_threshold = timezone.now() + datetime.timedelta(days=1)
    if oauth_data and oauth_data.refresh_token_expires_at <= token_threshold:
        try:
            disconnect_docusign_user(
                client_id=client_id,
                login_email_id=login_email_id,
                action_summary="Disconnected Docusign (Token Expired)",
            )

            return {
                "has_integration": False,
                "email": None,
            }
        except Exception as e:
            logging.exception("An error occurred: %s", exc_info=e)
            return {"has_integration": False, "email": None}
    elif oauth_data:
        return {"has_integration": True, "email": oauth_data.email_id}
    else:
        return {"has_integration": False, "email": None}
