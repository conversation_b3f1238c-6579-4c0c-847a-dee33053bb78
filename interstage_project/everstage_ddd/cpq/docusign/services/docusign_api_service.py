import datetime
import os
from base64 import b64encode

import requests
from django.core.cache import cache
from django.utils import timezone
from docusign_esign.client.api_client import Api<PERSON>lient

from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.utils import LogWithContext

from ..accessors.docusign_accessor import DocusignCpqAccessor
from ..docusign_exception import DocusignApiError, DocusignNoDataFoundError

DOCUSIGN_CLIENT_ID, DOCUSIGN_CLIENT_SECRET = (
    os.environ["DOCUSIGN_CLIENT_ID"],
    os.environ["DOCUSIGN_CLIENT_SECRET"],
)


class DocusignCpqApiService:
    """
    Service to get the api_client for accessing the docusign API's
    """

    def __init__(self, client_id, login_email_id, account_id):
        self.client_id = client_id
        self.login_email_id = login_email_id
        self.account_id = account_id
        self.cache_key = f"docusign_token##{login_email_id}##{account_id}"
        self.api_client = None
        self.log = LogWithContext({"email_id": login_email_id, "client_id": client_id})

    def get_access_token_from_refresh_token(self, refresh_token):
        """
            Authorization: Basic BASE64_COMBINATION_OF_INTEGRATION_AND_SECRET_KEYS
            grant_type: refresh_token

            endpoint : https://account-d.docusign.com/oauth/token

        :return:
            access_token
        """
        kd = timezone.now()
        auth_string = "{0}:{1}".format(DOCUSIGN_CLIENT_ID, DOCUSIGN_CLIENT_SECRET)
        auth_encoded_hash = b64encode(auth_string.encode("utf-8"))
        auth_header = auth_encoded_hash.decode("utf-8")

        if is_prod_env():
            url = "https://account.docusign.com/oauth/token"
        else:
            url = "https://account-d.docusign.com/oauth/token"

        headers = {
            "Authorization": "Basic {0}".format(auth_header),
            "Content-Type": "application/x-www-form-urlencoded",
        }

        body = {"grant_type": "refresh_token", "refresh_token": refresh_token}
        temp_response = requests.post(url, data=body, headers=headers, timeout=20)
        response = temp_response.json()
        if response.get("error") == "invalid_grant":
            message = "Error in getting access token. Refresh token expired"
            self.log.info(message)
            raise DocusignApiError(message)

        docusign_acc = DocusignCpqAccessor(client_id=self.client_id)
        docusign_data = docusign_acc.get_docusign_data_by_account(
            account_id=self.account_id
        )
        if docusign_data:
            self.log.info("DocuSign: Saving new refresh token - ")
            docusign_acc.invalidate(
                account_id=self.account_id,
                knowledge_date=kd,
            )
            docusign_data.refresh_token = response.get("refresh_token")
            docusign_data.refresh_token_expires_at = kd + datetime.timedelta(days=30)
            docusign_data.knowledge_begin_date = kd
            docusign_acc.create(docusign_data)
        return response

    def get_api_client(self):
        if self.api_client is None or cache.get(self.cache_key) is None:
            self.api_client = self.create_api_client()
        return self.api_client

    def create_api_client(self):
        """
        creates an api_client using the access_token for the user
        """

        log = LogWithContext(
            {"client_id": self.client_id, "email_id": self.login_email_id}
        )
        log.info("Creating docusign api client...")

        oauth_data = DocusignCpqAccessor(self.client_id).get_oauth_details()
        if oauth_data:
            refresh_token = oauth_data.refresh_token
        else:
            message = "Oauth details not present for the user"
            code = "DOCUSIGN_API_OAUTH_NOT_FOUND"
            raise DocusignNoDataFoundError(code, message)

        if cache.get(self.cache_key) is None:
            log.info(f"Cache miss. cache key -  {self.cache_key}")
            response = self.get_access_token_from_refresh_token(refresh_token)
            access_token = response.get("access_token")
            timeout = response.get("expires_in", 3600)
            cache.set(self.cache_key, access_token, timeout - 120)
        else:
            log.info(f"Cache hit. cache key -  {self.cache_key}")
            access_token = cache.get(self.cache_key)

        api_client = ApiClient()
        api_client.host = oauth_data.base_uri + "/restapi/"
        api_client.set_default_header(
            header_name="Authorization", header_value=f"Bearer {access_token}"
        )

        return api_client
