from django.contrib.postgres.indexes import GinIndex
from django.db.models import Index, Q

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import EsCharField, EsJSONField


class QuoteTemplateDetails(MultiTenantTemporal):
    account_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    template_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    template_name = EsCharField(max_length=254, null=True, is_sensitive=False)
    quote_id = EsCharField(max_length=254, null=False, is_sensitive=False)
    sharing_type = EsCharField(max_length=254, null=False, is_sensitive=False)
    envelope_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    template_meta_data = EsJSONField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "quote_template_details"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client",
                    "is_deleted",
                    "knowledge_end_date",
                    "quote_id",
                    "sharing_type",
                ],
                name="quo_tem_ked_qi_shi_idx",
            ),
            Index(
                fields=[
                    "is_deleted",
                    "knowledge_end_date",
                    "sharing_type",
                    "account_id",
                    "envelope_id",
                ],
                name="quo_isd_ked_shi_ai_ei_idx",
            ),
        ]


class DocusignAccountSettings(MultiTenantTemporal):
    account_data = EsJSONField(null=True, is_sensitive=False)
    default_signer = EsJSONField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "docusign_account_settings"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client",
                    "is_deleted",
                    "knowledge_end_date",
                ],
                name="doc_acc_sett_ked_idx",
            ),
            Index(
                fields=["is_deleted", "knowledge_end_date"],
                name="doc_acc_sett_isdel_ked_idx",
            ),
            GinIndex(
                fields=["account_data"],
                name="acc_data_has_key_idx",
                condition=Q(is_deleted=False, knowledge_end_date__isnull=True),
            ),
        ]
