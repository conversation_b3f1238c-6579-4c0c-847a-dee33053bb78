from http import HTTPStatus

from everstage_ddd.common import BaseError


class DocusignCpqError(BaseError):
    def __init__(self, code, message) -> None:
        super().__init__(code=code, message=message, status=HTTPStatus.BAD_REQUEST)


class DocusignNoDataFoundError(BaseError):
    def __init__(self, code, message) -> None:
        super().__init__(
            code=code,
            message=message,
            status=HTTPStatus.NOT_FOUND,
        )


class DocusignApiError(BaseError):
    def __init__(self, message) -> None:
        super().__init__(
            code="DOCUSIGN_API",
            message=message,
            status=HTTPStatus.BAD_GATEWAY,
        )
