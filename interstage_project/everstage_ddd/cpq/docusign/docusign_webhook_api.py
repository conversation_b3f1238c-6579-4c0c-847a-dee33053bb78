import base64
import json
import logging
import os

from django.views.decorators.csrf import csrf_exempt
from ninja import Ninja<PERSON><PERSON>
from ninja.errors import HttpError
from rest_framework import status

from commission_engine.utils.cloudfront_utils import invalidate_cloudfront_cache
from commission_engine.utils.s3_utils import S3Uploader
from everstage_ddd.cpq.quote.quote_status.enums import QuoteStatusEnum
from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
    QuoteStatusSelector,
)
from everstage_ddd.cpq.quote.quote_status.quote_status_service import (
    change_quote_status,
)

from .accessors.docusign_accessor import DocusignQuoteTemplateDetailsAccessor
from .docusign_enums import DocusignWebhookEventEnum, SharingTypeEnum
from .docusign_exception import DocusignCpqError, DocusignNoDataFoundError
from .services.docusign_template_service import invalidate_quote_data

api = NinjaAPI(urls_namespace="docusign_webhook")
logger = logging.getLogger(__name__)


def update_template_recipient_list(
    quote_template_recipients, recipient_id, event, generated_date_time
):
    for recipient in quote_template_recipients:
        if recipient.get("id") == recipient_id:
            recipient["event"] = event
            recipient["eventTime"] = generated_date_time
            break


def get_envelope_documents_file_url(
    envelope_documents_list, client_id, quote_id, account_id, envelope_id
):
    cloudfront_cdn = os.environ.get("S3_AVATAR_CDN")
    bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    files_with_s3_urls = []
    if envelope_documents_list:
        for document in envelope_documents_list:
            if document.get("type") == "content":
                binary_file = base64.b64decode(document.get("PDFBytes"))
                logging.info("Document encoded and ready to upload to S3")
                document_name = document.get("name")
                document_id = document.get("documentId")
                s3_path = f"quote_template_docusign_documents/{client_id}/{quote_id}/{account_id}/{envelope_id}/{document_id}/{document_name}"
                S3Uploader(bucket_name).upload_file(binary_file, s3_path)
                logging.info("Document uploaded to S3")
                document_url = f"{cloudfront_cdn}/{s3_path}"
                files_with_s3_urls.append(
                    {
                        "documentName": document_name,
                        "documentUrl": document_url,
                    }
                )
                invalidate_cloudfront_cache(cloudfront_cdn, "/" + s3_path)
    return files_with_s3_urls


def process_docusign_webhook_request(parsed_data):
    """
    if envelope is voided or envelope is declined or recipient is declined to sign
        delete the quote data and update the quote status as Approved
    else
        update the recipient status when mail is sent to recipient or recipient is completed their work
        update the file s3 url with latest document
        update the status mark as Won when envelope is completed

    """
    generated_date_time = parsed_data.get("generatedDateTime")
    data = parsed_data.get("data", {})
    event = parsed_data.get("event")
    account_id = data.get("accountId")
    recipient_id = data.get("recipientId")
    envelope_id = data.get("envelopeId")
    envelope_summary = data.get("envelopeSummary", {})
    envelope_documents_list = envelope_summary.get("envelopeDocuments", [])
    if account_id and envelope_id:
        quote_template_data = DocusignQuoteTemplateDetailsAccessor().get_quote_template_by_account_and_envelope(
            account_id, envelope_id, SharingTypeEnum.DOCUSIGN.value
        )
        if quote_template_data:
            client_id = quote_template_data.client_id
            quote_id = quote_template_data.quote_id
            if event in (
                DocusignWebhookEventEnum.ENVELOPE_VOIDED.value,
                DocusignWebhookEventEnum.ENVELOPE_DECLINED.value,
                DocusignWebhookEventEnum.RECIPIENT_DECLINED.value,
            ):
                QuoteStatusSelector(client_id).update_quote_status(
                    quote_id, QuoteStatusEnum.APPROVED.value
                )
                invalidate_quote_data(
                    client_id, quote_id, SharingTypeEnum.DOCUSIGN.value
                )

            else:
                template_meta_data = (
                    quote_template_data.template_meta_data
                    if quote_template_data.template_meta_data
                    else {}
                )
                quote_template_recipients = (
                    template_meta_data["recipients"]
                    if template_meta_data.get("recipients")
                    else []
                )
                files_with_s3_urls = get_envelope_documents_file_url(
                    envelope_documents_list,
                    client_id,
                    quote_id,
                    account_id,
                    envelope_id,
                )
                if event == DocusignWebhookEventEnum.ENVELOPE_COMPLETED.value:
                    change_quote_status(
                        client_id,
                        quote_id,
                        QuoteStatusEnum.WON.value,
                    )
                if (
                    event == DocusignWebhookEventEnum.ENVELOPE_SENT.value
                    and template_meta_data.get("envelope_sent_date_time") is None
                ):
                    template_meta_data["envelope_sent_date_time"] = generated_date_time
                if files_with_s3_urls:
                    template_meta_data["files_with_s3_urls"] = files_with_s3_urls
                if (
                    event
                    in (
                        DocusignWebhookEventEnum.RECIPIENT_SENT.value,
                        DocusignWebhookEventEnum.RECIPIENT_COMPLETED.value,
                    )
                    and recipient_id is not None
                ):
                    update_template_recipient_list(
                        quote_template_recipients,
                        recipient_id,
                        event,
                        generated_date_time,
                    )
                    template_meta_data["recipients"] = quote_template_recipients
                DocusignQuoteTemplateDetailsAccessor(client_id).update_quote_template(
                    quote_id,
                    SharingTypeEnum.DOCUSIGN.value,
                    {"template_meta_data": template_meta_data},
                )
        else:
            message = f"Quote Template data not found for account_id: {account_id} and envelope_id: {envelope_id}"
            code = "DOCUSIGN_WEBHOOK_NO_DATA_FOUND"
            raise DocusignNoDataFoundError(code, message)
    else:
        message = "Missing accountId or envelopeId in DocuSign webhook payload"
        code = "DOCUSIGN_WEBHOOK"
        raise DocusignCpqError(code, message)


@api.post("docusign")
@csrf_exempt
def docusign_webhook(request) -> dict:
    logger.info("Docusign Webhook event received")
    try:
        parsed_data = json.loads(request.body)
        process_docusign_webhook_request(parsed_data)

    except DocusignCpqError as e:
        logger.exception("Docusign webhook bad request error", exc_info=e)
        raise HttpError(e.status.value, str(e)) from e

    except DocusignNoDataFoundError as e:
        logger.exception("Docusign webhook not data found error", exc_info=e)
        raise HttpError(e.status.value, str(e)) from e

    except Exception as e:
        logger.exception("Docusign webhook error", exc_info=e)
        raise HttpError(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e)) from e

    return {"status": "SUCCESS", "data": "Webhook received!"}
