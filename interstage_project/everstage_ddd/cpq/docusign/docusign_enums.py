from enum import Enum


class DocusignEnum(Enum):
    NEEDS_TO_SIGN = "Needs To Sign"
    RECEIVES_A_COPY = "Receives A Copy"
    NEEDS_TO_VIEW = "Needs To View"


class DocusignWebhookEventEnum(Enum):
    ENVELOPE_SENT = "envelope-sent"
    ENVELOPE_COMPLETED = "envelope-completed"
    ENVELOPE_DECLINED = "envelope-declined"
    RECIPIENT_SENT = "recipient-sent"
    RECIPIENT_COMPLETED = "recipient_completed"
    RECIPIENT_DECLINED = "recipient-declined"
    ENVELOPE_VOIDED = "envelope-voided"


class SharingTypeEnum(Enum):
    DOCUSIGN = "docusign"
    OFFLINE = "offline"
