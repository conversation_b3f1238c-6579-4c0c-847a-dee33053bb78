import uuid
from collections import defaultdict

from everstage_ddd.cpq.product_catalog.accessors.pricepoint_accessor import (
    PricePointAccessor,
)

from ..constants import PRICE_BOOK_ID
from ..data_models.pricepoint_data_models import PricePointListItem
from ..utils import convert_date_format, get_status


def create_pricepoint(client_id, pricepoint_data):
    pricepoint_id = pricepoint_data.get("pricepoint_id")
    pricepoint_data["effective_start_date"] = convert_date_format(
        pricepoint_data.get("effective_start_date")
    )
    update_status = get_status(
        pricepoint_data.get("effective_start_date"),
        pricepoint_data.get("effective_end_date"),
    )
    if pricepoint_data.get("list_price") is None:
        pricepoint_data["list_price"] = pricepoint_data.get("flat_price", 0)

    pricepoint_selector = PricePointAccessor(client_id, PRICE_BOOK_ID)
    if pricepoint_id:
        pricepoint_recs = pricepoint_selector.get_pricepoint(
            pricepoint_id, as_dict=True
        )

        latest_pricepoint = pricepoint_recs[0]
        effective_start_date = latest_pricepoint["effective_start_date"]
        effective_end_date = latest_pricepoint["effective_end_date"]
        latest_status = get_status(effective_start_date, effective_end_date)

        if len(pricepoint_recs) == 1:
            if latest_status == "active" and update_status == "inactive":
                pricepoint_selector.create_pricepoint(
                    {
                        **pricepoint_data,
                        "price_book_id": latest_pricepoint["price_book_id"],
                    }
                )
                pricepoint_selector.update_pricepoint(
                    latest_pricepoint["temporal_id"],
                    {
                        "effective_end_date": pricepoint_data["effective_start_date"],
                    },
                )
            else:
                pricepoint_selector.update_pricepoint(
                    latest_pricepoint["temporal_id"], pricepoint_data
                )
        elif len(pricepoint_recs) > 1:
            older_pricepoint = pricepoint_recs[1]
            older_status = get_status(
                older_pricepoint["effective_start_date"],
                older_pricepoint["effective_end_date"],
            )
            if latest_status == "active" and older_status == "inactive":
                pricepoint_selector.delete_pricepoint_by_temporal_id(
                    older_pricepoint["temporal_id"]
                )
                pricepoint_selector.create_pricepoint(
                    {
                        **pricepoint_data,
                        "price_book_id": latest_pricepoint["price_book_id"],
                    }
                )
                pricepoint_selector.update_pricepoint(
                    latest_pricepoint["temporal_id"],
                    {
                        "effective_end_date": pricepoint_data["effective_start_date"],
                    },
                )
            elif latest_status == "inactive" and older_status == "active":
                if update_status == "active":
                    pricepoint_selector.delete_pricepoint_by_temporal_id(
                        latest_pricepoint["temporal_id"]
                    )
                    pricepoint_selector.update_pricepoint(
                        older_pricepoint["temporal_id"],
                        {**pricepoint_data, "effective_end_date": None},
                    )
                else:
                    pricepoint_selector.update_pricepoint(
                        latest_pricepoint["temporal_id"],
                        {**pricepoint_data, "effective_end_date": None},
                    )
                    pricepoint_selector.update_pricepoint(
                        older_pricepoint["temporal_id"],
                        {
                            "effective_end_date": pricepoint_data[
                                "effective_start_date"
                            ],
                        },
                    )

    else:
        pricepoint_id = uuid.uuid4()
        pricepoint_data["pricepoint_id"] = pricepoint_id
        pricepoint_data["price_book_id"] = PRICE_BOOK_ID
        pricepoint_selector.create_pricepoint(pricepoint_data)

    return pricepoint_id


def delete_update_schedule(client_id, pricepoint_id):
    pricepoint_selector = PricePointAccessor(client_id, PRICE_BOOK_ID)
    pricepoint_recs = pricepoint_selector.get_pricepoint(pricepoint_id, as_dict=True)
    schedule_pricepoint = pricepoint_recs[0]
    original_pricepoint = pricepoint_recs[1]
    original_pricepoint["effective_end_date"] = None
    pricepoint_selector.update_pricepoint(
        original_pricepoint["temporal_id"], original_pricepoint
    )
    return pricepoint_selector.delete_pricepoint_by_temporal_id(
        schedule_pricepoint["temporal_id"]
    )


def activate_pricepoint(client_id, pricepoint_id, schedule_start_date, delete_schedule):
    if delete_schedule:
        schedule_start_date = None
    else:
        schedule_start_date = convert_date_format(schedule_start_date)

    return PricePointAccessor(client_id, PRICE_BOOK_ID).activate_pricepoint(
        pricepoint_id, schedule_start_date
    )


def deactivate_pricepoint(client_id, pricepoint_id, schedule_end_date, delete_schedule):
    if delete_schedule:
        schedule_end_date = None
    else:
        schedule_end_date = convert_date_format(schedule_end_date)

    return PricePointAccessor(client_id, PRICE_BOOK_ID).deactivate_pricepoint(
        pricepoint_id, schedule_end_date
    )


def get_pricepoint(client_id, pricepoint_id):
    pricepoint_records = PricePointAccessor(client_id, PRICE_BOOK_ID).get_pricepoint(
        pricepoint_id, as_dict=True
    )
    pricepoint = {}
    for pricepoint_record in pricepoint_records:
        status = get_status(
            pricepoint_record["effective_start_date"],
            pricepoint_record["effective_end_date"],
        )
        pricepoint[status] = pricepoint_record
        pricepoint[status]["schedule_start_date"] = pricepoint_record[
            "effective_start_date"
        ]
        pricepoint[status]["schedule_end_date"] = pricepoint_record[
            "effective_end_date"
        ]

    return pricepoint


def list_pricepoints(client_id, product_id):
    pricepoints = PricePointAccessor(
        client_id, PRICE_BOOK_ID
    ).get_pricepoints_by_product_id(product_id)

    pricepoint_dict = {}
    for pricepoint in pricepoints:
        status = get_status(
            pricepoint.effective_start_date, pricepoint.effective_end_date
        )

        if pricepoint.pricepoint_id in pricepoint_dict:
            if status == "active":
                has_update = (
                    pricepoint.effective_start_date
                    < pricepoint_dict[pricepoint.pricepoint_id][
                        "pricepoint"
                    ].effective_start_date
                )
                pricepoint_dict[pricepoint.pricepoint_id] = {
                    "pricepoint": pricepoint,
                    "status": status,
                    "has_scheduled_update": has_update,
                }
            else:
                has_update = (
                    pricepoint.effective_start_date
                    > pricepoint_dict[pricepoint.pricepoint_id][
                        "pricepoint"
                    ].effective_start_date
                )
                pricepoint_dict[pricepoint.pricepoint_id][
                    "has_scheduled_update"
                ] = has_update
        else:
            pricepoint_dict[pricepoint.pricepoint_id] = {
                "pricepoint": pricepoint,
                "status": status,
                "has_scheduled_update": False,
            }

    return [
        PricePointListItem(
            pricepoint_id=pricepoint_data["pricepoint"].pricepoint_id,
            name=pricepoint_data["pricepoint"].name,
            status=pricepoint_data["status"],
            price_model=pricepoint_data["pricepoint"].price_model,
            currency=pricepoint_data["pricepoint"].currency,
            billing_frequency=pricepoint_data["pricepoint"].billing_frequency,
            custom_factors=pricepoint_data["pricepoint"].custom_factors,
            list_price=pricepoint_data["pricepoint"].list_price,
            flat_price=pricepoint_data["pricepoint"].flat_price,
            max_discount=pricepoint_data["pricepoint"].max_discount,
            modification_treshold=pricepoint_data["pricepoint"].modification_treshold,
            flat_max_discount=pricepoint_data["pricepoint"].flat_max_discount,
            flat_modification_treshold=pricepoint_data[
                "pricepoint"
            ].flat_modification_treshold,
            tier_data=pricepoint_data["pricepoint"].tier_data,
            schedule_start_date=pricepoint_data["pricepoint"].effective_start_date,
            schedule_end_date=pricepoint_data["pricepoint"].effective_end_date,
            has_scheduled_update=pricepoint_data["has_scheduled_update"],
        )
        for pricepoint_data in pricepoint_dict.values()
    ]


def list_pricepoints_by_product_ids(client_id, product_ids):
    pricepoints = PricePointAccessor(
        client_id, PRICE_BOOK_ID
    ).get_pricepoints_by_product_ids(product_ids)

    product_pricepoint_map = defaultdict(list)
    pricepoint_dict = {}
    for pricepoint in pricepoints:
        status = get_status(
            pricepoint.effective_start_date, pricepoint.effective_end_date
        )

        if pricepoint.pricepoint_id in pricepoint_dict:
            if status == "active":
                has_update = (
                    pricepoint.effective_start_date
                    < pricepoint_dict[pricepoint.pricepoint_id][
                        "pricepoint"
                    ].effective_start_date
                )
                pricepoint_dict[pricepoint.pricepoint_id] = {
                    "pricepoint": pricepoint,
                    "status": status,
                    "has_scheduled_update": has_update,
                }
            else:
                has_update = (
                    pricepoint.effective_start_date
                    > pricepoint_dict[pricepoint.pricepoint_id][
                        "pricepoint"
                    ].effective_start_date
                )
                pricepoint_dict[pricepoint.pricepoint_id][
                    "has_scheduled_update"
                ] = has_update
        else:
            pricepoint_dict[pricepoint.pricepoint_id] = {
                "pricepoint": pricepoint,
                "status": status,
                "has_scheduled_update": False,
            }

    for pricepoint_data in pricepoint_dict.values():
        if pricepoint_data["status"] == "active":
            product_id = pricepoint_data["pricepoint"].product_id
            product_pricepoint_map[product_id].append(pricepoint_data["pricepoint"])

    product_pricepoint_records = {
        product_id: [
            {
                "pricepoint_id": pricepoint.pricepoint_id,
                "name": pricepoint.name,
            }
            for pricepoint in pricepoints
        ]
        for product_id, pricepoints in product_pricepoint_map.items()
    }

    return product_pricepoint_records
