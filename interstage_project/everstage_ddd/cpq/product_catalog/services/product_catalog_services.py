import re
import uuid
from datetime import datetime, timezone

from ...quote.accessors.quote_line_item_accessor import QuoteLineItemAccessor
from ..accessors.pricepoint_accessor import PricePointAccessor
from ..accessors.product_catalog_accessor import ProductCatalogAccessor
from ..constants import PRICE_BOOK_ID
from ..data_models.product_catalog_data_models import (
    ProductBasicDetails,
    ProductDetails,
    ProductListItem,
)
from ..services.pricepoint_services import list_pricepoints_by_product_ids
from ..utils import convert_date_format, get_status


class ProductInUseError(Exception):
    def __init__(self):
        super().__init__("Product is being used in quotes")


def get_copy_name(name, name_list):
    # Regex to match the pattern 'Copy(<number>)' at the end of the quote name
    copy_pattern = re.compile(r"(.*?)( Copy\((\d+)\))?$")

    # Match the quote name to see if it already contains a 'Copy <number>'
    match = copy_pattern.match(name)
    if match:
        base_name = match.group(1)  # Original quote name without 'Copy <number>'
        copy_number = (
            int(match.group(3)) if match.group(3) else 0
        )  # Existing copy number

    new_name = name
    counter = (
        copy_number + 1
    )  # Start incrementing from the next number if it already exists

    while new_name.lower() in name_list:
        new_name = f"{base_name} Copy({counter})"
        counter += 1
    return new_name


def get_new_product_name_sku(
    client_id: int, product_name: str, product_sku: str
) -> tuple[str, str]:
    """
    Get the new product name for a given client. If the product is already a copy,
    increment the number after 'Copy' instead of adding another 'Copy'.
    """
    product_selector = ProductCatalogAccessor(client_id=client_id)
    product_records = product_selector.get_products(columns=["name", "sku"])
    all_product_names = []
    all_product_skus = []
    for record in product_records:
        all_product_names.append(record["name"].lower())
        all_product_skus.append(record["sku"].lower())

    return get_copy_name(product_name, all_product_names), get_copy_name(
        product_sku, all_product_skus
    )


def create_product(client_id, product_data):
    if product_data.get("product_id"):
        ProductCatalogAccessor(client_id).update_product(
            product_data.get("product_id"), product_data
        )
        return product_data.get("product_id")
    else:
        product_id = uuid.uuid4()
        created_at = datetime.now(tz=timezone.utc)
        product_data["product_id"] = product_id
        product_data["created_at"] = created_at
        product_data["sku"] = product_data["sku"].upper()
        ProductCatalogAccessor(client_id).create_product(product_data)
        return product_id


def clone_product(client_id, product_id, clone_pricepoints=False):
    product_data = ProductCatalogAccessor(client_id).get_product(
        product_id, as_dict=True
    )
    new_product_id = uuid.uuid4()
    product_data.pop("temporal_id")
    product_data["name"], product_data["sku"] = get_new_product_name_sku(
        client_id, product_data["name"], product_data["sku"]
    )
    product_data["product_id"] = new_product_id
    product_data["created_at"] = datetime.now(tz=timezone.utc)
    product_data["effective_start_date"] = None
    product_data["effective_end_date"] = None

    ProductCatalogAccessor(client_id).create_product(product_data)
    if clone_pricepoints:
        pricepoint_accessor = PricePointAccessor(client_id, PRICE_BOOK_ID)
        pricepoints = pricepoint_accessor.get_pricepoints_by_product_id(
            product_id, as_dict=True
        )

        pricepoint_map = {}
        for pricepoint in pricepoints:
            if pricepoint["pricepoint_id"] in pricepoint_map:
                cloned_pricepoint_id = pricepoint_map[pricepoint["pricepoint_id"]]
            else:
                cloned_pricepoint_id = uuid.uuid4()
                pricepoint_map[pricepoint["pricepoint_id"]] = cloned_pricepoint_id
            pricepoint.pop("temporal_id")
            pricepoint_accessor.create_pricepoint(
                {
                    **pricepoint,
                    "product_id": new_product_id,
                    "pricepoint_id": cloned_pricepoint_id,
                }
            )

    return new_product_id


def delete_product(client_id, product_id):
    quote_line_item_selector = QuoteLineItemAccessor(client_id)
    if quote_line_item_selector.check_if_product_exists(product_id):
        raise ProductInUseError()
    pricepoint_selector = PricePointAccessor(client_id, PRICE_BOOK_ID)
    pricepoint_selector.delete_pricepoints_by_product_id(product_id)
    return ProductCatalogAccessor(client_id).delete_product(product_id)


def activate_product(client_id, product_id, schedule_start_date, delete_schedule=False):
    if delete_schedule:
        schedule_start_date = None
    else:
        schedule_start_date = convert_date_format(schedule_start_date)

    return ProductCatalogAccessor(client_id).activate_product(
        product_id, schedule_start_date
    )


def deactivate_product(client_id, product_id, schedule_end_date, delete_schedule=False):
    if delete_schedule:
        schedule_end_date = None
    else:
        schedule_end_date = convert_date_format(schedule_end_date)

    return ProductCatalogAccessor(client_id).deactivate_product(
        product_id, schedule_end_date
    )


def get_product(client_id, product_id):
    product = ProductCatalogAccessor(client_id).get_product(product_id)
    status = "inactive"
    has_pricepoints = (
        PricePointAccessor(client_id, PRICE_BOOK_ID)
        .check_pricepoint_exists([product_id])
        .get(product_id)
    )
    if has_pricepoints:
        status = get_status(product.effective_start_date, product.effective_end_date)

    schedule_start_date = product.effective_start_date
    created_on = product.created_at
    last_modified = product.knowledge_begin_date
    active_till = product.effective_end_date

    return ProductDetails(
        product_id=product.product_id,
        sku=product.sku,
        name=product.name,
        billing_type=product.billing_type,
        category=product.category,
        description=product.description,
        status=status,
        schedule_start_date=schedule_start_date,
        created_on=created_on,
        last_modified=last_modified,
        active_till=active_till,
        charge_unit=product.charge_unit,
    )


def list_products(client_id):
    product_records = ProductCatalogAccessor(client_id).get_products()
    products = []
    has_pricepoints = PricePointAccessor(
        client_id, PRICE_BOOK_ID
    ).check_pricepoint_exists([record.product_id for record in product_records])
    for record in product_records:
        status = "inactive"
        if has_pricepoints[record.product_id]:
            status = get_status(record.effective_start_date, record.effective_end_date)

        product = ProductListItem(
            id=record.product_id,
            name=record.name,
            sku=record.sku,
            status=status,
            billing_type=record.billing_type,
            category=record.category,
            description=record.description,
            managed_by=record.custom_factors.get("manager_id"),
            charge_unit=record.charge_unit,
        )
        products.append(product)
    return products


def get_products_by_skus(client_id, product_ids):
    """
    Get active products by their Product IDs with active pricepoints.

    Args:
        client_id: The client ID
        product_ids: List of Product IDs to fetch

    Returns:
        List[ProductBasicDetails]: List of basic product details with price points for active products with active pricepoints
    """

    products = []
    product_records = ProductCatalogAccessor(client_id).get_products_by_ids(product_ids)
    product_pricepoint_map = list_pricepoints_by_product_ids(client_id, product_ids)
    has_pricepoints = PricePointAccessor(
        client_id, PRICE_BOOK_ID
    ).check_pricepoint_exists([uuid.UUID(product_id) for product_id in product_ids])

    for record in product_records:
        status = "inactive"
        if has_pricepoints[record["product_id"]]:
            status = get_status(
                record["effective_start_date"], record["effective_end_date"]
            )

        product = ProductBasicDetails(
            product_id=record["product_id"],
            name=record["name"],
            sku=record["sku"],
            pricepoints=product_pricepoint_map[record["product_id"]],
        )
        if status == "active":
            products.append(product)

    return products
