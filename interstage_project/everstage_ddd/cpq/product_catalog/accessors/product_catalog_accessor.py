from django.utils import timezone

from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.cpq.product_catalog.models import ProductCatalog


class ProductCatalogAccessor(BiTemporalSelector):
    def __init__(self, client_id):
        self.model = ProductCatalog
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    # WRITE OPERATIONS

    def create_product(self, product_data):
        return self.bitemporal_create(data=product_data)

    def update_product(self, product_id, product_data):
        product = self.get_product(product_id)
        identifier = product.temporal_id if product else product_id
        return self.bitemporal_update(record_identifier=identifier, data=product_data)

    def delete_product(self, product_id):
        return (
            self.client_kd_aware()
            .filter(product_id=product_id)
            .update(
                knowledge_end_date=timezone.now(),
                is_deleted=True,
            )
        )

    def activate_product(self, product_id, schedule_start_date):
        product = self.get_product(product_id)
        identifier = product.temporal_id if product else product_id
        return self.bitemporal_update(
            record_identifier=identifier,
            data={
                "effective_start_date": schedule_start_date,
                "effective_end_date": None,
            },
        )

    def deactivate_product(self, product_id, schedule_end_date):
        product = self.get_product(product_id)
        identifier = product.temporal_id if product else product_id
        return self.bitemporal_update(
            record_identifier=identifier,
            data={"effective_end_date": schedule_end_date},
        )

    # READ OPERATIONS

    def get_product(self, product_id, as_dict=False):
        query = self.client_kd_deleted_aware().filter(product_id=product_id)
        return query.values().first() if as_dict else query.first()

    def get_products(self, columns: list[str] | None = None):
        if columns:
            return list(self.client_kd_deleted_aware().values(*columns))
        return list(self.client_kd_deleted_aware().all())

    def get_products_by_ids(self, product_ids: list[str]):
        return list(
            self.client_kd_deleted_aware()
            .filter(product_id__in=product_ids)
            .values(
                "product_id",
                "name",
                "sku",
                "effective_start_date",
                "effective_end_date",
            )
        )
