from django.utils import timezone

from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.cpq.product_catalog.models import PricePoint
from everstage_ddd.cpq.product_catalog.utils import get_status


class PricePointAccessor(BiTemporalSelector):
    def __init__(self, client_id, pricebook_id):
        self.model = PricePoint
        self.pricebook_id = pricebook_id
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    # WRITE OPERATIONS

    def create_pricepoint(self, pricepoint_data):
        return self.bitemporal_create(data=pricepoint_data)

    def update_pricepoint(self, identifier, pricepoint_data):
        return self.bitemporal_update(
            record_identifier=identifier, data={**pricepoint_data}
        )

    def activate_pricepoint(self, pricepoint_id, schedule_start_date):
        pricepoint_recs = self.get_pricepoint(pricepoint_id)
        pricepoint = pricepoint_recs[0]
        identifier = pricepoint.temporal_id
        return self.bitemporal_update(
            record_identifier=identifier,
            data={
                "effective_start_date": schedule_start_date,
                "effective_end_date": None,
            },
        )

    def deactivate_pricepoint(self, pricepoint_id, schedule_end_date):
        pricepoint_recs = self.get_pricepoint(pricepoint_id)
        pricepoint = pricepoint_recs[0]

        if len(pricepoint_recs) > 1:
            stale_pricepoint = pricepoint_recs[1]
            status = get_status(
                stale_pricepoint.effective_start_date,
                stale_pricepoint.effective_end_date,
            )
            if status != "active":
                self.delete_pricepoint_by_temporal_id(stale_pricepoint.temporal_id)

        identifier = pricepoint.temporal_id
        return self.bitemporal_update(
            record_identifier=identifier,
            data={
                "effective_end_date": schedule_end_date,
            },
        )

    def delete_pricepoint_by_temporal_id(self, temporal_id):
        return (
            self.client_kd_aware()
            .filter(temporal_id=temporal_id)
            .update(
                knowledge_end_date=timezone.now(),
                is_deleted=True,
            )
        )

    def delete_pricepoints_by_product_id(self, product_id):
        return (
            self.client_kd_aware()
            .filter(product_id=product_id)
            .update(
                knowledge_end_date=timezone.now(),
                is_deleted=True,
            )
        )

    # READ OPERATIONS

    def get_pricepoint(self, pricepoint_id, as_dict=False):
        query = (
            self.client_kd_deleted_aware()
            .filter(pricepoint_id=pricepoint_id, price_book_id=self.pricebook_id)
            .order_by("-effective_start_date", "-effective_end_date")
        )
        return query.values() if as_dict else query

    def get_pricepoints_by_product_id(self, product_id, as_dict=False):
        query = (
            self.client_kd_deleted_aware()
            .filter(product_id=product_id, price_book_id=self.pricebook_id)
            .order_by("-effective_start_date", "-effective_end_date")
        )
        return query.values() if as_dict else list(query.all())

    def get_pricepoints_by_product_ids(self, product_ids=None, as_dict=False):
        query = self.client_kd_deleted_aware().filter(price_book_id=self.pricebook_id)
        if product_ids:
            query = query.filter(product_id__in=product_ids)
        query = query.order_by("-effective_start_date", "-effective_end_date")

        return query.values() if as_dict else list(query.all())

    def check_pricepoint_exists(self, product_ids):
        existing_product_ids = set(
            self.client_kd_deleted_aware()
            .filter(product_id__in=product_ids, price_book_id=self.pricebook_id)
            .values_list("product_id", flat=True)
            .distinct()
        )

        return {
            product_id: product_id in existing_product_ids for product_id in product_ids
        }

    def get_all_pricepoints(self):
        return (
            self.client_kd_deleted_aware().filter(price_book_id=self.pricebook_id).all()
        )
