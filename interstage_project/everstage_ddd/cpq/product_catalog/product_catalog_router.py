"""
This module contains the router and API endpoints for managing products,
price factors, and price book products.
"""

from uuid import UUID

from ninja import Router
from ninja.decorators import decorate_view

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.cpq.product_catalog.data_models.product_catalog_data_models import (
    ActivateProductRequest,
    ActivateProductResponse,
    CloneProductRequest,
    CloneProductResponse,
    CreateProductPayload,
    CreateProductResponse,
    DeactivateProductRequest,
    DeactivateProductResponse,
    GetProductsBySKUsRequest,
    GetProductsBySKUsResponse,
    ListProductsResponse,
    ProductDetails,
)
from interstage_project.auth_utils import requires_scope

from .services.product_catalog_services import (
    activate_product,
    clone_product,
    create_product,
    deactivate_product,
    delete_product,
    get_product,
    get_products_by_skus,
    list_products,
)

product_catalog_router = Router(tags=["product_catalog"])


@product_catalog_router.post("/create", response=CreateProductResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def create_product_api(request, payload: CreateProductPayload) -> CreateProductResponse:
    """
    Create a new product.

    Args:
        request: The HTTP request object.
        payload (CreateProductRequest): The request payload containing product details.

    Returns:
        CreateProductResponse: The response containing the created product details.
    """

    product_data = {
        "product_id": payload.product_id,
        "name": payload.name,
        "sku": payload.sku,
        "billing_type": payload.billing_type,
        "category": payload.category,
        "description": payload.description,
        "charge_unit": payload.charge_unit,
    }
    product_id = create_product(client_id=request.client_id, product_data=product_data)
    return CreateProductResponse(
        success=True, message="Product created successfully", product_id=product_id
    )


@product_catalog_router.post("/clone/{product_id}", response=CloneProductResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def clone_product_api(
    request, product_id: UUID, payload: CloneProductRequest
) -> CloneProductResponse:
    """
    Clone a product.
    """
    clone_product(
        client_id=request.client_id,
        product_id=product_id,
        clone_pricepoints=payload.clone_pricepoints,
    )
    return CloneProductResponse(success=True, message="Product cloned successfully")


@product_catalog_router.delete("/delete/{product_id}")
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def delete_product_api(request, product_id: UUID) -> CloneProductResponse:
    """
    Delete a product.
    """
    return delete_product(client_id=request.client_id, product_id=product_id)


@product_catalog_router.post("/activate")
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def activate_product_api(request, payload: ActivateProductRequest):
    """
    Activate a product.
    """
    activate_product(
        client_id=request.client_id,
        product_id=payload.product_id,
        schedule_start_date=payload.schedule_start_date,
        delete_schedule=payload.delete_schedule,
    )
    message = (
        "Schedule deleted successfully"
        if payload.delete_schedule
        else "Product activated successfully"
    )
    return ActivateProductResponse(success=True, message=message)


@product_catalog_router.post("/deactivate")
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def deactivate_product_api(request, payload: DeactivateProductRequest):
    """
    Deactivate a product.
    """
    deactivate_product(
        client_id=request.client_id,
        product_id=payload.product_id,
        schedule_end_date=payload.schedule_end_date,
        delete_schedule=payload.delete_schedule,
    )
    message = (
        "Schedule deleted successfully"
        if payload.delete_schedule
        else "Product deactivated successfully"
    )
    return DeactivateProductResponse(success=True, message=message)


@product_catalog_router.get("/products", response=ListProductsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def list_products_api(request) -> ListProductsResponse:
    """
    List all products for a client.

    Args:
        request: The HTTP request object.

    Returns:
        ListProductsResponse: The response containing a list of products.
    """
    client_id = request.client_id
    products = list_products(client_id=client_id)
    return ListProductsResponse(products=products)


@product_catalog_router.get("/product/{product_id}", response=ProductDetails)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def get_product_api(request, product_id: UUID) -> ProductDetails:
    """
    Get a product by its ID.
    """
    return get_product(client_id=request.client_id, product_id=product_id)


@product_catalog_router.post("/products/by-skus", response=GetProductsBySKUsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_CONFIG.value))
def get_products_by_skus_api(
    request, payload: GetProductsBySKUsRequest
) -> GetProductsBySKUsResponse:
    """
    Get products by their Product IDs.

    Args:
        request: The HTTP request object
        payload: The request payload containing list of Product IDs

    Returns:
        GetProductsBySKUsResponse: The response containing list of product basic details
    """
    products = get_products_by_skus(
        client_id=request.client_id, product_ids=payload.skus
    )
    return GetProductsBySKUsResponse(products=products)
