from typing import List

from ninja import Schema
from pydantic import Field
from typing_extensions import NotRequired, TypedDict


class NotificationStatus_(TypedDict):  # noqa: N801
    email: bool
    slack: bool
    msTeams: bool


NotificationStatus = NotificationStatus_ | None


class NotificationTaskResp(TypedDict):
    name: str
    label: str
    isAdminNotification: bool
    isOptedOut: bool
    status: NotificationStatus
    category: str
    categoryLocalized: str
    helpText: str | None
    allowedFrequencies: List[str] | None
    frequency: str | None
    channelConfig: NotRequired[dict | None]


class AllNotificationsResponse(Schema):
    data: list[NotificationTaskResp]


class LegacyStatus(TypedDict):
    enabled: bool


class NotificationTaskReq(TypedDict):
    name: str
    status: NotificationStatus
    frequency: str | None
    isOptedOut: bool
    channelConfig: NotRequired[dict | None]


class IntegrationConfig(TypedDict):
    connected: bool
    connectEndpoint: str | None
    disconnectEndpoint: str | None


class IntegrationConfigResponse(TypedDict):
    emailId: str
    slack: IntegrationConfig
    email: IntegrationConfig
    msTeams: IntegrationConfig


class NotificationIntegrationsResponse(Schema):
    integrations: IntegrationConfigResponse


class SlackChannel(TypedDict):
    id: str
    name: str


class SlackChannelsResponse(Schema):
    channels: list[SlackChannel]


class CpqClientNotification(Schema):
    cpq_client_notification: bool


class UsersNotificationFilters(Schema):
    col_name: str = Field(alias="colName")
    value: list[str] | list[bool]


class UsersNotificationReq(Schema):
    start_row: int = Field(alias="startRow")
    end_row: int = Field(alias="endRow")
    search_term: str = Field(alias="searchTerm")
    filters: list[UsersNotificationFilters]


class BulkSelections(Schema):
    toggle_rows: List[str] = Field(alias="toggleRows")
    selected_all: bool = Field(alias="selectedAll")


class UpdateUsersNotificationReq(Schema):
    search_term: str | None = Field(alias="searchTerm")
    filters: list[UsersNotificationFilters] | None = Field(alias="filters")
    to_enable: bool | None = Field(alias="toEnable")
    selections: list[str] | None = Field(alias="selections")
    bulk_selections: BulkSelections | None = Field(alias="bulkSelections")


class SlackChannelsReq(Schema):
    search_term: str | None = Field(alias="searchTerm")
    offset: int = Field(alias="offsetValue")
    limit: int = Field(alias="limitValue")
    channel_ids: list[str] | None = Field(alias="channelIds")
    notification_name: str | None = Field(alias="notificationName")
