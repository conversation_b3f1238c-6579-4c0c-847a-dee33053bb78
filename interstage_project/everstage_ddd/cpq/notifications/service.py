import ast
import copy
import datetime
import logging

import pandas as pd
import pydash
from django.core.cache import cache
from django.db import connection, transaction
from django.utils import timezone
from requests import request
from slack_sdk import WebClient  # type: ignore

from commission_engine.accessors.client_accessor import (
    get_client,
    get_cpq_client_notification,
    is_cpq_slack_connected,
    is_msteams_connected,
)
from commission_engine.utils.general_data import (
    APPROVALS_PENDING_REQUESTS_NOTIFY_TIME,
    CpqNotification,
    ModuleType,
    NotificationMode,
    SegmentEvents,
    SegmentProperties,
    Task,
)
from everstage_infra.aws_infra.ecs import is_prod_env, is_staging_env
from slack_everstage.services.slack_app_service import get_valid_token
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.accessors.employee_accessor_v2 import (
    Employ<PERSON><PERSON><PERSON><PERSON>ccessor,
    Employee<PERSON>riteAccessor,
)
from spm.accessors.notification_accessors import ClientNotificationAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.constants.approval_workflow_constants import CPQ_APPROVAL_NOTIFICATION_TYPES
from spm.models.notification_models import ClientNotification
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.approval_workflow_services.approval_cron_services import (
    create_approval_task_entry_for_client,
    remove_approval_task_entry_for_client,
)
from spm.services.localization_services import get_localized_message_service
from spm.services.query_builder.base import BaseQueryBuilder
from spm.services.query_builder.employees import EmployeeQueryBuilder

from .types import NotificationTaskReq, NotificationTaskResp, UsersNotificationFilters

logger = logging.getLogger(__name__)


def get_all_cpq_notification_tasks(client_id: int) -> list[NotificationTaskResp]:
    """Get all notification tasks to be visible in cpq settings -> notifications page."""

    notif_map = ClientNotificationAccessor(client_id).get_notifications_map()

    all_notifications = []

    for notification in CpqNotification:
        notif_value = copy.deepcopy(notification.value)
        category_localized = (
            notif_value.get("category_localized")
            if notif_value.get("category_localized")
            else ""
        )
        notif_value["category_localized"] = get_localized_message_service(
            category_localized, client_id
        )
        notif_value["is_admin_notification"] = not notif_value.get("show_to_user")
        notif_value["frequency"] = None

        # Notifications that are currently visible under profile -> notifications
        if notif_value.get("show_to_user"):
            notif_value["is_opted_out"] = True
            notif_value["status"] = None
        # Notifications that are currently visible under settings -> notifications
        else:
            notif_value["is_opted_out"] = False
            notif_value["status"] = {"email": False, "slack": False, "ms_teams": False}
            notif_value["channel_config"] = {"slack": {"channelIds": []}}
        # Check if the values are overwritten in client_notification table
        if notif_map.get(notification.name):
            notif_value["is_opted_out"] = notif_map[notification.name].can_payee_opt_out
            notif_value["status"] = notif_map[notification.name].status
            notif_value["frequency"] = notif_map[notification.name].frequency
            if notif_map[notification.name].channel_config:
                notif_value["channel_config"] = notif_map[
                    notification.name
                ].channel_config

        notif_value["help_text"] = None
        # Some notifications has different labels when showing as admin notification
        notif_label = notif_value.get("admin_label") or notif_value["label"]
        # Localize the terms present in the notification label
        notif_value["label"] = get_localized_message_service(notif_label, client_id)

        all_notifications.append(notif_value)
    # parsed_notif = notif_task_resp_adapter.validate_python(all_notifications)

    return all_notifications


@transaction.atomic
def add_cpq_client_notifications(
    client_id: int,
    notif_tasks: list[NotificationTaskReq],
    knowledge_date: datetime.datetime,
    audit: dict | None = None,
):
    """
    Invalidate and updates the client notification tasks. The entries in this table are
    replcements from the interstage clients table. The table is also made temporal to
    keep track of changes.
    """
    notif_names = [task["name"] for task in notif_tasks]

    # Invalidating existing client notification tasks
    ClientNotificationAccessor(client_id).invalidate_notifications(
        notif_names, knowledge_date
    )

    # Creating new client notification tasks
    new_notifs = []
    for notif in notif_tasks:
        notification = CpqNotification[notif["name"]].value
        is_opted_out = notif["is_opted_out"]
        channel_config = notif["channel_config"]["slack"]["channel_ids"]
        new_notifs.append(
            ClientNotification(
                client_id=client_id,
                knowledge_begin_date=knowledge_date,
                additional_details=audit,
                notification_name=notif["name"],
                frequency=None if is_opted_out else notif["frequency"],
                can_payee_opt_out=is_opted_out,
                is_admin_notification=not notification["show_to_user"],
                # Make status to None when the notification status is handed over to payees
                status=None if is_opted_out else notif["status"],
                channel_config=notif["channel_config"] if channel_config else None,
            )
        )

    # Saving new client notification tasks
    ClientNotificationAccessor.persist_notifications(new_notifs)


def send_analytics_data_for_approval_notif(
    notif_tasks: list[NotificationTaskReq], login_user
):
    """Record analytics for creation of new approval tasks"""
    new_approvals = []
    everyday_approval = []

    for task in notif_tasks:
        if task["name"] == "CPQ_NEW_QUOTE_APPROVALS_NOTIFICATION":
            if task["status"]:
                for ch, enabled in task["status"].items():
                    if enabled:
                        new_approvals.append(ch)
        elif (
            task["name"] == "CPQ_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION"
            and task["status"]
        ):
            for ch, enabled in task["status"].items():
                if enabled:
                    everyday_approval.append(ch)

    analytics_data = {
        "user_id": login_user,
        "event_name": SegmentEvents.APPROVALS.value,
        "event_properties": {
            SegmentProperties.NEW_APPROVAL_REQUEST_NOTIFICATION.value: new_approvals,
            SegmentProperties.EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value: everyday_approval,
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)


def set_cpq_approvals_notifications(
    client_id: int,
    approval_tasks,
    client_timezone: str | None,
    login_user: str | None = None,
):
    """
    Create approval notifications cron jobs and send analytics for the same.
    """

    for task in approval_tasks:
        if (
            task["name"]
            == CPQ_APPROVAL_NOTIFICATION_TYPES.CPQ_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.value
        ):
            pending_requests_notify_task_name = (
                Task.CPQ_APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK.value["name"]
            )
            pending_requests_notify_function = (
                Task.CPQ_APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK.value["function"]
            )
            if (
                task["status"]
                and any(task["status"].values())
                # This is to remove the cron jobs scheduled for the notification
                # when making the notification as opted out.
                and not task["is_opted_out"]
            ):
                create_approval_task_entry_for_client(
                    client_id,
                    client_timezone,
                    pending_requests_notify_task_name,
                    pending_requests_notify_function,
                    APPROVALS_PENDING_REQUESTS_NOTIFY_TIME,
                )
            else:
                remove_approval_task_entry_for_client(
                    client_id,
                    client_timezone,
                    pending_requests_notify_task_name,
                    APPROVALS_PENDING_REQUESTS_NOTIFY_TIME,
                )

    send_analytics_data_for_approval_notif(approval_tasks, login_user)


@transaction.atomic
def add_tasks_for_admin(
    client_id: int,
    admin_tasks: list[NotificationTaskReq],
    login_user: str | None = None,
):
    """
    Add cron jobs for non-event based admin notifications. Approval notifications will
    be handled separately.
    """
    approval_notification_tasks = [
        CpqNotification.CPQ_NEW_QUOTE_APPROVALS_NOTIFICATION.name,
        CpqNotification.CPQ_EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION.name,
    ]

    client = get_client(client_id)
    # notification_modes = [mode.value["name"] for mode in NotificationMode]

    admin_approval_tasks = []
    client_timezone = client.time_zone
    for task in admin_tasks:
        notification_name = task["name"]
        # Skipping approval tasks as they'll be handled separately
        if notification_name in approval_notification_tasks:
            admin_approval_tasks.append(task)

    if admin_approval_tasks:
        set_cpq_approvals_notifications(
            client_id, admin_approval_tasks, client_timezone, login_user=login_user
        )


def save_all_cpq_notification_tasks(
    client_id: int,
    notif_tasks: list[NotificationTaskReq],
    login_user: str | None = None,
    audit: dict | None = None,
) -> list[NotificationTaskResp]:
    """
    Save all the given notification tasks that are triggered from settings -> notifications.
    It separates the tasks into admin and payee tasks and then creates the tasks accordingly.
    All the tasks will be persisted in client notification table also.
    Finally, it returns the updated notification tasks to display in the frontend.
    """
    if not get_cpq_client_notification(client_id):
        raise ValueError("Client notification not enabled")  # noqa: TRY003

    curr_time = timezone.now()
    """
    Handling admin notification tasks
    not needed as the only cron job is approval which is handled separately
    in future if we add more cron jobs, we can add them here
    """

    admin_tasks = [
        notif.name for notif in CpqNotification if not notif.value["show_to_user"]
    ]
    notif_admin_tasks = [task for task in notif_tasks if task["name"] in admin_tasks]
    if notif_admin_tasks:
        add_tasks_for_admin(client_id, notif_admin_tasks, login_user=login_user)

    """
    Handling payee notification tasks
    We dont have any payee notification tasks for cpq currently
    """
    # payee_tasks = [notif.name for notif in Notification if notif.value["show_to_user"]]
    # notif_payee_tasks = [
    #     task
    #     for task in notif_tasks
    #     # Create notification task entries only when the notification is not opted out
    #     if task["name"] in payee_tasks and task["is_opted_out"] is False
    # ]
    # if notif_payee_tasks:
    #     logger.info("Adding payee tasks", extra={"tasks": notif_payee_tasks})
    #     add_notification_tasks_for_payees(
    #         client_id, notif_payee_tasks, curr_time, audit=audit
    #     )

    # Saving notifications in client notifications table
    add_cpq_client_notifications(client_id, notif_tasks, curr_time, audit=audit)
    # Return updated notifications to display in the frontend
    return get_all_cpq_notification_tasks(client_id)


def get_notification_integrations(client_id, email_id):
    employee = EmployeeAccessor(client_id).get_employee(email_id)
    integration_configs = {}

    response = {"email_id": employee.employee_email_id}
    for mode in NotificationMode:
        if mode == NotificationMode.MS_TEAMS:
            connected = is_msteams_connected(client_id)
            config_record = (
                IntegrationConfigAccessor().get_msteams_config_record_by_mail_id(
                    client_id=client_id, mail_id=email_id
                )
            )
            if config_record:
                for key, val in config_record.config.items():
                    integration_configs[key] = val

        elif mode == NotificationMode.SLACK:
            connected = is_cpq_slack_connected(client_id)
            config_record = (
                IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
                    client_id=client_id, mail_id=email_id, module=ModuleType.CPQ.value
                )
            )
            if config_record:
                for key, val in config_record.config.items():
                    integration_configs[key] = val

        elif mode == NotificationMode.EMAIL:
            connected = get_cpq_client_notification(client_id)

        if mode.value["employee_config_key"]:
            for config_value in mode.value["employee_config_key"]:
                connected = connected and (
                    integration_configs.get(config_value, None) is not None
                )
        elif mode == NotificationMode.EMAIL:
            connected = get_cpq_client_notification(client_id)
        else:
            connected = False

        response_data = {
            "connected": connected,
            "connect_endpoint": mode.value["cpq_connect_url"],
            "disconnect_endpoint": mode.value["cpq_disconnect_url"],
        }

        response[mode.value["name"]] = response_data

    return response


def get_slack_channels(
    client_id: int,
    email_id: str,
    **kwargs,
):
    """
    Get slack channels for a given client and email id with pagination support.
    Handles pagination to fetch all channels and caches them, then returns paginated results.
    """
    offset = kwargs.get("offset", 0)
    limit = kwargs.get("limit", 50)
    search_term = kwargs.get("search_term", "")
    channel_ids = kwargs.get("channel_ids", [])
    notification_name = kwargs.get("notification_name", "")

    configs = list(
        IntegrationConfigAccessor().get_slack_config_record_for_mail_ids(
            client_id, [email_id], module=ModuleType.CPQ.value
        )
    )
    slack_config = {
        config.employee_email_id: (config.config or {}) for config in configs
    }

    user_id = slack_config[email_id]["slack_user_id"]
    team_id = slack_config[email_id]["slack_team_id"]

    tokens = get_valid_token(user_id, team_id, module=ModuleType.CPQ.value)
    client = WebClient(token=tokens.get("bot_token"))

    cache_key = get_slack_channels_cache_key(client_id, notification_name)
    cached_channels = cache.get(cache_key)

    # Fetch and cache all channels if not cached
    if cached_channels is None:
        all_channels = []
        cursor = None

        while True:
            response = client.conversations_list(
                types="public_channel,private_channel",
                exclude_archived=False,
                cursor=cursor,
                limit=1000,  # Maximum allowed by Slack API
            )

            all_channels.extend(response["channels"])

            # Check if there are more pages
            cursor = response.get("response_metadata", {}).get("next_cursor")
            if not cursor:
                break

        cached_channels = [
            {"id": channel["id"], "name": channel["name"]} for channel in all_channels
        ]

        # Sort channels alphabetically by name
        cached_channels.sort(key=lambda x: x["name"].lower())

    # Sort channels to prioritize those in channel_ids
    if channel_ids and offset == 0:
        # Create a set for faster lookup
        channel_ids_set = set(channel_ids)

        # Separate channels into priority and regular lists
        selected_channels = []
        not_selected_channels = []

        for channel in cached_channels:
            if channel["id"] in channel_ids_set:
                selected_channels.append(channel)
            else:
                not_selected_channels.append(channel)

        # Combine with selected channels first
        cached_channels = selected_channels + not_selected_channels
    elif not channel_ids:
        cached_channels.sort(key=lambda x: x["name"].lower())

    cache.set(cache_key, cached_channels, timeout=60 * 5)

    # Apply search filter if provided
    if search_term:
        cached_channels = [
            channel
            for channel in cached_channels
            if search_term.lower() in channel["name"].lower()
        ]

    # Apply pagination to filtered results
    paginated_channels = cached_channels[offset : offset + limit]

    return paginated_channels


def toggle_cpq_client_notification(client_id: int, cpq_client_notification):
    """Enable/disable cpq client notification for a given client."""
    client = get_client(client_id)
    client.cpq_client_notification = cpq_client_notification
    client.save()


def apply_search_and_filters(
    search_term: str | None,
    filters: list[UsersNotificationFilters],
    base_query: BaseQueryBuilder,
) -> BaseQueryBuilder:
    """
    Apply search and filters on the base query and return the query object.
    The following fields are valid in the filters:
    - reporting_manager_email_id
    - employment_country
    - user_group_id
    - cpq_user_notification
    - user_role
    """
    if search_term:
        base_query = base_query.search_employee_full_name(search_term)

    # The user_role is not present in this map as it should be handled separately
    filters_map = {
        # filter_key: ( table_name, data_type, operator )
        "reporting_manager_email_id": ("hierarchy", "String", "IN"),
        "employment_country": ("employee_payroll_details", "String", "IN"),
        "user_group_id": ("user_group_members", "String", "IN"),
        "cpq_user_notification": ("employee", "Boolean", "=="),
    }

    queries_map = {
        "employee": base_query.empty_condition(),
        "employee_payroll_details": base_query.empty_condition(),
        "hierarchy": base_query.empty_condition(),
        "user_group_members": base_query.empty_condition(),
    }

    for each_filter in filters:
        col_name = each_filter.col_name
        value = each_filter.value
        if not value:
            continue

        # Since value type of send_notification is boolean and frontend sends it as a list
        # we need to convert it to a boolean by extracting the first element.
        value = value[0] if col_name == "cpq_user_notification" else value

        # The user_role column name will not be present in the filters map
        table_name, data_type, operator = filters_map.get(col_name, (None, None, None))
        if table_name is None:
            continue

        field = base_query.get_field(col_name, table_name=table_name)
        queries_map[table_name] = base_query.and_aggregation(
            [
                queries_map[table_name],
                base_query.get_condition(
                    field=field,
                    query_type=operator,
                    value=value,
                    field_data_type=data_type,
                ),
            ]
        )

    # Joining the base table with user_group_members if filter by user group
    if not base_query.is_condition_empty(queries_map["user_group_members"]):
        base_query = base_query.join_user_group_members(
            join_type="LEFT",
        ).where_conditions(queries_map["user_group_members"])

    # Filter by employment country
    if not base_query.is_condition_empty(queries_map["employee_payroll_details"]):
        base_query = base_query.where_conditions(
            queries_map["employee_payroll_details"]
        )

    # Filter by manger email
    if not base_query.is_condition_empty(queries_map["hierarchy"]):
        base_query = base_query.where_conditions(queries_map["hierarchy"])

    # Filter by send_notification
    if not base_query.is_condition_empty(queries_map["employee"]):
        base_query = base_query.where_conditions(queries_map["employee"])

    # Filter by roles. The value for user_role must be a list of role ids
    user_role_filter = pydash.find(filters, lambda x: x.col_name == "user_role")
    if user_role_filter and user_role_filter.value:
        base_query = base_query.where_employee_user_role_in_array(
            user_role_filter.value
        )

    return base_query


def get_users_notification(
    client_id: int,
    search_term: str | None = None,
    start_row: int | None = None,
    end_row: int | None = None,
    filters: list[UsersNotificationFilters] | None = None,
):
    """
    Get users to be displayed in settings -> notifications page to enable notifications
    for them. The users are filtered based on the search term and filters provided.
    """
    effective_date = timezone.now()

    base_query = (
        EmployeeQueryBuilder(client_id)
        .kd_aware()
        .exit_date_aware()
        .left_join_employee_payroll_details(effective_date=effective_date)
        .join_hierarchy(effective_date=effective_date)
    )

    filters = filters or []
    base_query = apply_search_and_filters(search_term, filters, base_query)

    count_sql = base_query.clone_deep().count_distinct_employee_email_id().get_sql()

    users_query = (
        base_query.join_reporting_manager_details(use_alias=True)
        .select_employee_email_id()
        .select_employee_full_name()
        .select_employee_profile_picture()
        .select_table_field("user_role", table_name="employee")
        .select_field("employment_country")
        .select_table_field("cpq_user_notification", table_name="employee")
        .order_by_employee_full_name()
        .order_by_employee_email_id()
    )
    if start_row is not None and end_row is not None:
        users_query.limit_and_offset(limit=end_row - start_row, offset=start_row)

    users_sql = users_query.get_sql()

    with connection.cursor() as cursor:
        # Getting users count
        cursor.execute(count_sql)
        count_res = cursor.fetchone()
        users_count = count_res[0] if count_res else 0

        # Getting users
        cursor.execute(users_sql)
        users_res = cursor.fetchall()
        columns = [col[0] for col in cursor.description]  # type: ignore

    df = pd.DataFrame(users_res, columns=columns)
    if df.empty:
        return {"users": [], "users_count": users_count}

    role_permissions = RolePermissionsAccessor(client_id).get_all_roles()
    role_permissions_map = {
        str(rp["role_permission_id"]): rp["display_name"] for rp in role_permissions
    }

    def get_role_names(role_ids):
        return ", ".join(
            [
                role_permissions_map.get(role_id, "-")
                for role_id in ast.literal_eval(role_ids) or []
            ]
        )

    df["user_role_name"] = df["user_role"].map(get_role_names)

    # When reporting manager is not present, the SQL qurey returns a space character.
    # Replace it with None.
    df["reporting_manager_full_name"] = (
        df["reporting_manager_full_name"].str.strip().replace("", None)
    )

    df.drop(
        columns=[
            "reporting_manager_email_id",
            "reporting_manager_first_name",
            "reporting_manager_last_name",
            "user_role",
        ],
        inplace=True,
    )

    user_records = df.to_dict(orient="records")

    return {"users": user_records, "users_count": users_count}


@transaction.atomic
def save_users_notification(
    client_id: int,
    **kwargs,
):
    """
    Save notification status for the given users. Note that the given search term and
    filters will be applied and then the given selections or bulk selections will be
    considered. If `selections` and `bulk_selections` are provided, selections will only
    be considered.
    """
    if not get_cpq_client_notification(client_id):
        return {
            "status": "FAILED",
            "error": "Client notification not enabled",
        }

    to_enable = kwargs.get("to_enable", False)
    search_term = kwargs.get("search_term", None)
    filters = kwargs.get("filters", None)
    selections = kwargs.get("selections", None)
    bulk_selections = kwargs.get("bulk_selections", None)

    users = get_users_notification(client_id, search_term, filters=filters)["users"]
    email_ids = pydash.map_(users, "employee_email_id")
    knowledge_date = timezone.now()
    updated_count = 0

    # selections is the list of email ids selected in UI
    if selections:
        email_ids = pydash.intersection(email_ids, selections)
        if not email_ids:
            return {"status": "FAILED", "message": "No users selected"}

        emp_records = EmployeeReadAccessor(client_id).get_employees(
            employee_email_ids=email_ids, as_dicts=False
        )
        for emp in emp_records:
            emp.knowledge_begin_date = knowledge_date  # type: ignore
            # Toggle the send_notification status for selected users
            emp.cpq_user_notification = not emp.cpq_user_notification  # type: ignore

    elif bulk_selections:
        toggle_rows = bulk_selections.toggle_rows
        # If selected all is true, filter the users present in the toggle rows
        if bulk_selections.selected_all:
            email_ids = pydash.difference(email_ids, toggle_rows)
        else:
            email_ids = pydash.intersection(email_ids, toggle_rows)

        if not email_ids:
            return {"status": "FAILED", "message": "No users selected"}

        emp_records = EmployeeReadAccessor(client_id).get_employees(
            employee_email_ids=email_ids, as_dicts=False
        )
        for emp in emp_records:
            emp.knowledge_begin_date = knowledge_date  # type: ignore
            emp.cpq_user_notification = to_enable  # type: ignore
    else:
        return {
            "status": "FAILED",
            "message": "Selections and bulk selections should not be absent together",
        }

    EmployeeWriteAccessor(client_id).invalidate_and_create_objects(
        new_records=emp_records,
        invalidate_employee_email=email_ids,
        knowledge_date=knowledge_date,
    )
    updated_count = len(emp_records)

    # Adding non-opted out / compulsory notifications for the payees
    # notif_enabled_emails = [
    #     emp.employee_email_id for emp in emp_records if emp.send_notification  # type: ignore
    # ]
    # add_compulsory_notifications_for_payees(
    #     client_id, notif_enabled_emails, knowledge_date=knowledge_date, audit=audit
    # )

    return {"status": "SUCCESS", "updated_count": updated_count}


def send_slack_message_to_channel(client_id, **kwargs):

    logger.info("START: Send slack message to channel")

    notif_task = kwargs.get("notif_task")
    module = kwargs.get("module", "ICM")
    components = kwargs.get("components")
    text = components[0]
    blocks = components[1]

    notification = ClientNotificationAccessor(client_id).get_notification(notif_task)
    if not notification.channel_config:
        logger.info("No notification channel found for the event %s", notif_task)
        return

    channels = notification.channel_config.get("slack", {}).get("channel_ids", [])
    # if no channels are configured for the notification event then skip the slack notification
    if not channels:
        logger.info("No channels configured for the notification event")
        return

    configs = list(
        IntegrationConfigAccessor().get_all_slack_configs_for_client_id(
            client_id, module
        )
    )
    all_users = [config.employee_email_id for config in configs]
    # The config map will be used while sending notification
    email_config_map = {
        config.employee_email_id: (config.config or {}) for config in configs
    }

    email_connected_map = {}
    for email_id, config in email_config_map.items():
        config_keys = NotificationMode.SLACK.value["employee_config_key"]
        connected = all(config.get(key) is not None for key in config_keys)
        email_connected_map[email_id] = connected

    # Get the list of connected users - who has the required config for slack or ms teams
    connected_users = list(filter(email_connected_map.get, all_users))
    if not connected_users:
        logger.info(
            "No connected users for the client %s and module %s", client_id, module
        )
        return

    for email_id in connected_users:
        user_id = email_config_map[email_id]["slack_user_id"]
        team_id = email_config_map[email_id]["slack_team_id"]
        break

    if is_prod_env() or is_staging_env():
        tokens = get_valid_token(user_id, team_id, module)
        slack_client = WebClient(token=tokens.get("bot_token"))

        for channel in channels:
            # Check if the bot is already a member of the channel
            members_res = slack_client.conversations_members(channel=channel)
            if not members_res.get("ok"):
                logger.error(
                    f"Failed to fetch members for channel {channel}: {members_res.get('error')}"
                )
                continue

            bot_user_id = slack_client.auth_test().get("user_id")
            if bot_user_id in members_res.get("members", []):
                logger.info(f"Bot is already a member of the channel {channel}")
            else:
                logger.info(
                    f"Bot is not a member of the channel {channel}. Attempting to join."
                )
                res = request(
                    method="POST",
                    url="https://slack.com/api/conversations.join",
                    headers={
                        "Authorization": f"Bearer {tokens.get('bot_token')}",
                        "Content-Type": "application/json; charset=utf-8",
                    },
                    json={"channel": channel},
                )
                if not res.json().get("ok"):
                    logger.error(
                        f"Failed to join channel {channel}: {res.json().get('error')}"
                    )
                    continue

            # Send the message to the channel
            slack_client.chat_postMessage(
                text=text,
                channel=channel,
                as_user=True,
                blocks=blocks,
            )
    else:
        logger.info("Cannot send notification in non-prod environment")

    logger.info("END: Send slack message to channel")


def get_slack_channels_cache_key(client_id, notification_name=""):
    return f"{client_id}-SLACK-CHANNELS-{notification_name}"
