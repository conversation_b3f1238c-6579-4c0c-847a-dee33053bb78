"""
This module contains the router and API endpoints for managing notifications.
"""

from django.db import transaction
from ninja import Router
from ninja.decorators import decorate_view
from pydash import camel_case, snake_case

from commission_engine.accessors.client_accessor import get_cpq_client_notification
from commission_engine.services.commission_calculation_service.commission_simulation.utils import (
    change_keys,
)
from commission_engine.services.payee_notification_service import disconnect_slack
from commission_engine.utils.general_data import ModuleType, RbacPermissions
from everstage_ddd.cpq.notifications.service import (
    get_all_cpq_notification_tasks,
    get_notification_integrations,
    get_slack_channels,
    get_users_notification,
    save_all_cpq_notification_tasks,
    save_users_notification,
    toggle_cpq_client_notification,
)
from everstage_ddd.cpq.notifications.types import (
    AllNotificationsResponse,
    CpqClientNotification,
    NotificationIntegrationsResponse,
    NotificationTaskReq,
    SlackChannelsReq,
    SlackChannelsResponse,
    UpdateUsersNotificationReq,
    UsersNotificationReq,
)
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import LogWithContext

notification_router = Router(tags=["notifications"])


@notification_router.get("/list", response=AllNotificationsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def get_notification_tasks(request) -> AllNotificationsResponse:
    """
    Get all the notification tasks for the client.
    """
    client_id = request.client_id
    notif_tasks = get_all_cpq_notification_tasks(client_id)
    return AllNotificationsResponse(
        data=change_keys(notif_tasks, camel_case),
    )


@notification_router.post("/save", response=AllNotificationsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def save_notification_tasks(
    request, payload: list[NotificationTaskReq]
) -> AllNotificationsResponse:
    """
    Save the notification tasks for the client.
    """
    client_id = request.client_id
    updated_notif_tasks = save_all_cpq_notification_tasks(
        client_id,
        change_keys(payload, snake_case),
        login_user=request.user.username,
        audit=request.audit,
    )

    return AllNotificationsResponse(
        data=change_keys(updated_notif_tasks, camel_case),
    )


@notification_router.get(
    "/integrations-config", response=NotificationIntegrationsResponse
)
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def get_integrations_config(request) -> NotificationIntegrationsResponse:
    """
    Get the integrations config for the client.

    Response: contains config details for slack, ms teams and email
    example:
        "slack": {
                "connected": true,
                "connectEndpoint": "http://localhost:3000/slack/install",
                "disconnectEndpoint": "/spm/payee_notification/slack/disconnect",
                "connectedBy": "sachin agrawal",
                "connectedAt": "2025-03-28T05:05:59.107145+00:00",
                "disconnectedBy": null,
                "disconnectedAt": null,
                "isUserDeleted": false
            },
    """
    client_id = request.client_id
    email_id = request.user.username
    notif_integrations = get_notification_integrations(client_id, email_id)
    return NotificationIntegrationsResponse(
        integrations=change_keys(notif_integrations, camel_case),
    )


@notification_router.post("/slack-channels", response=SlackChannelsResponse)
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def slack_channels(request, payload: SlackChannelsReq) -> SlackChannelsResponse:
    """
    Get the slack channels for the client with pagination support.
    """
    client_id = request.client_id
    email_id = request.user.username
    limit = payload.limit
    offset = payload.offset
    search_term = payload.search_term
    channel_ids = payload.channel_ids
    notification_name = payload.notification_name
    slack_channels_data = get_slack_channels(
        client_id,
        email_id,
        offset=int(offset),
        limit=int(limit),
        search_term=search_term,
        channel_ids=channel_ids,
        notification_name=notification_name,
    )
    return SlackChannelsResponse(channels=change_keys(slack_channels_data, camel_case))


@notification_router.post("/slack/disconnect")
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def slack_disconnect(request):
    """
    Disconnect the slack integration for the client.
    """
    client_id = request.client_id
    email_id = request.user.username
    logger = LogWithContext({"client_id": client_id})
    logger.update_context({"email_id": email_id})
    disconnect_slack(client_id, email_id, logger, module=ModuleType.CPQ.value)
    return {"status": "success"}


@notification_router.get("/cpq-client-notification")
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def get_client_notification(request) -> CpqClientNotification:
    """
    Get the cpq client notification for the client.
    """
    client_id = request.client_id
    cpq_client_notification = get_cpq_client_notification(client_id)
    return CpqClientNotification(cpq_client_notification=cpq_client_notification)


@notification_router.post("/toggle-cpq-client-notification")
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def toggle_client_notification(request, payload: CpqClientNotification):
    """
    Toggle the cpq client notification for the client.
    """
    client_id = request.client_id
    cpq_client_notification = payload.cpq_client_notification
    toggle_cpq_client_notification(
        client_id, cpq_client_notification=cpq_client_notification
    )
    return {
        "message": f"Notifications {'enabled' if cpq_client_notification else 'disabled'} successfully"
    }


@notification_router.post("/get-users-notification")
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def get_cpq_users_notification(request, payload: UsersNotificationReq):
    """
    Get the users notification for the client.
    """
    client_id = request.client_id
    start_row = payload.start_row
    end_row = payload.end_row
    search_term = payload.search_term
    filters = payload.filters
    data = get_users_notification(
        client_id,
        start_row=start_row,
        end_row=end_row,
        search_term=search_term,
        filters=filters,
    )
    return change_keys(data, camel_case)


@notification_router.post("/update-users-notification")
@decorate_view(requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value))
@transaction.atomic
def update_cpq_users_notification(request, payload: UpdateUsersNotificationReq):
    """
    Update the users notification for the client.
    """
    client_id = request.client_id
    search_term = payload.search_term
    filters = payload.filters
    to_enable = payload.to_enable
    selections = payload.selections
    bulk_selections = payload.bulk_selections
    res = save_users_notification(
        client_id,
        to_enable=to_enable,
        search_term=search_term,
        filters=filters,
        selections=selections,
        bulk_selections=bulk_selections,
    )
    return res
