from ninja import Router

from .approvals.router import approvals_router
from .basic_settings.router import basic_settings_router
from .docusign.router import docusign_router
from .forms.router import forms_router
from .notifications.router import notification_router
from .price_book.price_book_router import price_book_router
from .product_catalog.pricepoint_router import pricepoint_router
from .product_catalog.product_catalog_router import product_catalog_router
from .quote.routers.qli_router import qli_router
from .quote.routers.quote_router import quote_router

cpq_router = Router(tags=["cpq"])

cpq_router.add_router("forms/", forms_router)
cpq_router.add_router("approvals/", approvals_router)
cpq_router.add_router("price_book/", price_book_router)
cpq_router.add_router("quotes/", quote_router)
cpq_router.add_router("quote_line_items/", qli_router)
cpq_router.add_router("notifications/", notification_router)
cpq_router.add_router("product_catalog/", product_catalog_router)
cpq_router.add_router("pricepoint/", pricepoint_router)
cpq_router.add_router("docusign/", docusign_router)
cpq_router.add_router("basic_settings/", basic_settings_router)
