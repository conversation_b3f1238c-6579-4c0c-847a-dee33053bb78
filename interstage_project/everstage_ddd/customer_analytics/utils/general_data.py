from enum import Enum


class Task(Enum):
    ANALYTICS_ACTIVE_USERS = {
        "name": "ANALYTICS_ACTIVE_USERS",
        "function": "everstage_ddd.customer_analytics.tasks.active_users_data.push_active_users_data_to_snowflake",
    }
    ANALYTICS_USER_MODULE_ACCESS = {
        "name": "ANALYTICS_USER_MODULE_ACCESS",
        "function": "everstage_ddd.customer_analytics.tasks.modules_usage_data.push_modules_usage_data_to_snowflake",
    }
    ANALYTICS_AGGREGATED_DATA = {
        "name": "ANALYTICS_AGGREGATED_DATA",
        "function": "everstage_ddd.customer_analytics.tasks.aggregated_data.push_aggregated_data_to_snowflake",
    }
    ANALYTICS_COMMISSION_SUMMARY_REPORT = {
        "name": "ANALYTICS_COMMISSION_SUMMARY_REPORT",
        "function": "everstage_ddd.customer_analytics.tasks.analytics_commission_summary_report.push_commission_summary_report_to_snowflake",
    }
    ANALYTICS_AGGREGATED_COMMISSION_SUMMARY_REPORT = {
        "name": "ANALYTICS_AGGREGATED_COMMISSION_SUMMARY_REPORT",
        "function": "everstage_ddd.customer_analytics.tasks.aggregated_commission_summary_report.push_aggregated_commission_summary_data_to_snowflake",
    }
    ANALYTICS_CLIENT_FEATURES = {
        "name": "ANALYTICS_CLIENT_FEATURES",
        "function": "everstage_ddd.customer_analytics.tasks.client_features_report.push_client_features_data",
    }
    ANALYTICS_AGGREGATED_CLIENT_FEATURES_DATA = {
        "name": "ANALYTICS_AGGREGATED_CLIENT_FEATURES_DATA",
        "function": "everstage_ddd.customer_analytics.tasks.aggregated_client_features_report.push_aggregated_client_features_data",
    }
    ANALYTICS_BILLING_INFORMATION_SYNC = {
        "name": "ANALYTICS_BILLING_INFORMATION_SYNC",
        "function": "everstage_ddd.customer_analytics.tasks.billing_information_sync.push_billing_information_to_snowflake",
    }
    ANALYTICS_ACTIVE_USERS_CUSTOM_OBJECT_SYNC = {
        "name": "ANALYTICS_ACTIVE_USERS_CUSTOM_OBJECT_SYNC",
        "function": "everstage_ddd.customer_analytics.tasks.sync_active_users_to_custom_object.sync_active_users_to_custom_object",
    }
