import logging
import os
from datetime import datetime, timedelta

import pytz
from celery import shared_task
from django.db import connection
from snowflake.connector.errors import DatabaseError
from snowflake.snowpark.functions import col, hash

from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.customer_analytics.snowflake_database.snowflake_connection import (
    create_snowpark_session,
)
from everstage_ddd.customer_analytics.snowflake_database.snowflake_query_utils import (
    create_snowpark_dataframe,
    snowflake_delete_records,
    snowflake_insert_records,
)
from everstage_ddd.customer_analytics.snowflake_database.snowflake_schemas import (
    ACTIVE_USERS_TABLE_NAME,
)

logger = logging.getLogger(__name__)

BASE_QUERY = """
SELECT
    client."name" AS "client_name",
    client."client_id" as "client_id",
    "employee"."employee_email_id" AS "email",
    "employee"."first_name" AS "first_name",
    "employee"."last_name" AS "last_name",
    "employee_payroll_details"."designation" AS "designation",
    "employee_payroll_details"."employment_country" AS "employment_country",
    "employee_payroll_details"."pay_currency" AS "payout_currency",
    "employee"."created_date" AS "added_date",
    CASE
        WHEN "employee"."exit_date" IS NOT NULL AND "employee"."exit_date" < NOW() THEN 'Inactive'
        ELSE "employee"."status"
    END AS "status",
    CASE 
        WHEN "employee_payroll_details"."payout_frequency" NOT IN ('Monthly', 'Annual','Quarterly','Halfyearly')
        THEN (select name from "custom_calendar" cc where cc."client_id" = "employee"."client_id" and cc."is_deleted"= false and
        cc."knowledge_end_date" isnull and cc."calendar_id" = CAST("employee_payroll_details"."payout_frequency" AS UUID))
    ELSE "employee_payroll_details"."payout_frequency"
    END AS "payout_frequency",
    CASE
        WHEN "employee"."exit_date" IS NOT NULL AND "employee"."exit_date" < NOW() THEN Null
        WHEN (
            SELECT COUNT(*)
            FROM "commission_plan" cp
            INNER JOIN "plan_details" pd ON cp."plan_id" = pd."plan_id"
            WHERE cp."client_id" = "employee"."client_id"
                AND NOT cp."is_deleted"
                AND cp."knowledge_end_date" IS NULL
                AND NOT cp."is_draft"
                AND pd."employee_email_id" = "employee"."employee_email_id"
                AND pd."effective_start_date" <= NOW()
                AND (pd."effective_end_date" IS NULL OR pd."effective_end_date" >= NOW())
                AND "hierarchy"."employee_email_id" IS NOT NULL
        ) > 0 THEN 'Mapped'
        ELSE 'Unmapped'
    END AS "mapped_or_unmapped",
    rp."display_name" AS "user_role",
    "employee"."exit_date" AS "exit_date",
    "employee"."last_commission_date" AS "last_commission_date",
    string_agg(DISTINCT CASE WHEN "commission_plan"."plan_type" = 'SPIFF' THEN "commission_plan"."plan_name" ELSE NULL END, ', ') AS "spiff",
    string_agg(DISTINCT CASE WHEN "commission_plan"."plan_type" = 'MAIN' THEN "commission_plan"."plan_name" ELSE NULL END, ', ') AS "main"
FROM "employee"
LEFT JOIN "employee_payroll_details"
    ON "employee"."client_id" = "employee_payroll_details"."client_id"
        AND NOT "employee"."is_deleted"
        AND NOT "employee_payroll_details"."is_deleted"
        AND "employee"."knowledge_end_date" IS NULL
        AND "employee_payroll_details"."knowledge_end_date" IS NULL
        AND "employee"."employee_email_id" = "employee_payroll_details"."employee_email_id"
        AND "employee_payroll_details"."effective_start_date" <= NOW()
        AND ("employee_payroll_details"."effective_end_date" IS NULL OR "employee_payroll_details"."effective_end_date" >= NOW())
LEFT JOIN "hierarchy"
    ON "employee"."client_id" = "hierarchy"."client_id"
        AND NOT "employee"."is_deleted"
        AND NOT "hierarchy"."is_deleted"
        AND "employee"."knowledge_end_date" IS NULL
        AND "hierarchy"."knowledge_end_date" IS NULL
        AND "employee"."employee_email_id" = "hierarchy"."employee_email_id"
        AND "hierarchy"."effective_start_date" <= NOW()
        AND ("hierarchy"."effective_end_date" IS NULL OR "hierarchy"."effective_end_date" >= NOW())
LEFT JOIN "interstage_clients" AS client
    ON "employee"."client_id" = client."client_id"
LEFT JOIN "role_permissions" AS rp
    ON "employee"."client_id" = rp."client_id"
        AND NOT rp."is_deleted"
        AND rp."knowledge_end_date" IS NULL
        AND replace(("employee"."user_role" -> 0)::text, '"','') = rp."role_permission_id"::text
LEFT JOIN "plan_details"
    ON "employee"."client_id" = "plan_details"."client_id"
        AND NOT "plan_details"."is_deleted"
        AND "plan_details"."knowledge_end_date" IS NULL
        AND "plan_details"."employee_email_id" = "employee"."employee_email_id"
        AND "plan_details"."effective_start_date" <= NOW()
        AND ("plan_details"."effective_end_date" IS NULL OR "plan_details"."effective_end_date" >= NOW())
LEFT JOIN "commission_plan"
    ON "employee"."client_id" = "commission_plan"."client_id"
        AND NOT "commission_plan"."is_deleted"
        AND "commission_plan"."knowledge_end_date" IS NULL
        AND NOT "commission_plan"."is_draft"
        AND "commission_plan"."plan_id" = "plan_details"."plan_id"
WHERE "employee"."knowledge_end_date" IS NULL
    AND NOT "employee"."is_deleted"
    {created_date_filter}
    {client_id_filter}
GROUP BY 
    client."name",
    client."client_id",
    "employee"."employee_email_id",
    "employee"."first_name",
    "employee"."last_name",
    "employee"."status",
    "employee"."created_date",
    "employee"."exit_date",
    "employee"."last_commission_date",
    rp."display_name",
    "employee"."client_id",
    "employee_payroll_details"."designation",
    "employee_payroll_details"."employment_country",
    "employee_payroll_details"."pay_currency",
    "employee_payroll_details"."payout_frequency",
    "hierarchy"."employee_email_id"  -- Include this column in GROUP BY
ORDER BY client."name" ;
"""


def get_active_user_records(
    client_id=None, snapshot_date=None, live_data=False
) -> tuple[list[dict], str]:
    if snapshot_date is None:
        snapshot_date = datetime.now(tz=pytz.UTC)

    first_day_of_month = snapshot_date.replace(
        day=1, hour=0, minute=0, second=0, microsecond=0
    )
    created_date_filter = f"""AND "employee"."created_date" < '{first_day_of_month}'"""

    if live_data:
        query = BASE_QUERY.format(
            created_date_filter="",
            client_id_filter=(
                f"""AND client."client_id" = {client_id}""" if client_id else ""
            ),
        )
    else:
        query = BASE_QUERY.format(
            created_date_filter=created_date_filter,
            client_id_filter=(
                f"""AND client."client_id" = {client_id}""" if client_id else ""
            ),
        )

    logger.info(f"Executing query to fetch all active users data...{query}")
    try:
        with connection.cursor() as cursor:
            cursor.execute(query)
            users_records: list = cursor.fetchall()
    except Exception:
        logger.exception("Error executing query to fetch active users data")
        raise

    if cursor.description and len(users_records) > 0:
        cols = [desc[0] for desc in cursor.description]
        users = [dict(zip(cols, record)) for record in users_records]
    else:
        users = []

    last_day_of_prev_month = snapshot_date.replace(
        day=1, hour=23, minute=59, second=59
    ) - timedelta(days=1)
    first_day_of_prev_month = last_day_of_prev_month.replace(
        day=1, hour=0, minute=0, second=0, microsecond=0
    )
    formatted_snapshot_value = last_day_of_prev_month.strftime("%B %Y")

    for user in users:
        user["snapshot_date"] = snapshot_date.replace(microsecond=0)
        user["snapshot_month"] = formatted_snapshot_value
        user["added_date"] = user["added_date"].replace(microsecond=0)
        if user["exit_date"] is not None:
            user["exit_date"] = user["exit_date"].replace(microsecond=0)
        if user["last_commission_date"] is not None:
            user["last_commission_date"] = user["last_commission_date"].replace(
                microsecond=0
            )
        if (
            user["exit_date"] is None
            or user["exit_date"] >= first_day_of_prev_month
            or (
                user["last_commission_date"] is not None
                and user["last_commission_date"] >= first_day_of_prev_month
            )
        ):
            user["is_active"] = True
        else:
            user["is_active"] = False
        user["region"] = os.getenv("ENV")
    logger.info(
        "Fetched all active users data as on {0} {1}".format(
            snapshot_date.strftime("%d-%m-%YT%H:%M:%S"), snapshot_date.tzname()
        )
    )

    return users, formatted_snapshot_value


@shared_task(base=EverCeleryBaseTask)
def push_active_users_data_to_snowflake():
    users, snapshot_month = get_active_user_records()
    # The get_active_user_records() function returns records in this format:
    # ruff: noqa: ERA001
    # [
    #     {
    #         "client_name": "usertest",
    #         "client_id": 2006,
    #         "email": "<EMAIL>",
    #         "first_name": "Abe",
    #         "last_name": "McDougle",
    #         "designation": None,
    #         "employment_country": None,
    #         "payout_currency": None,
    #         "status": "Added",
    #         "payout_frequency": None,
    #         "mapped_or_unmapped": "Unmapped",
    #         "user_role": "Payee",
    #         "exit_date": None,
    #         "spiff": None,
    #         "main": None,
    #         "snapshot_date": datetime.datetime(
    #             2024, 11, 13, 15, 28, 48, 774431, tzinfo=<UTC>
    #         ),
    #     },
    #     {
    #         "client_name": "usertest",
    #         "client_id": 2006,
    #         "email": "<EMAIL>",
    #         "first_name": "Adel",
    #         "last_name": "Birts",
    #         "designation": "VP Sales 2",
    #         "employment_country": "IND",
    #         "payout_currency": "CAD",
    #         "status": "Active",
    #         "payout_frequency": "Monthly",
    #         "mapped_or_unmapped": "Unmapped",
    #         "user_role": "Payee",
    #         "exit_date": None,
    #         "spiff": None,
    #         "main": None,
    #         "snapshot_date": datetime.datetime(
    #             2024, 11, 13, 15, 28, 48, 774431, tzinfo=<UTC>
    #         ),
    #     },
    # ]

    try:
        logger.info("Pushing the data to Snowflake Analytics DB...")
        with create_snowpark_session() as session:
            logger.info(
                f"Deleting existing records for the month {snapshot_month} if any"
            )
            delete_result = snowflake_delete_records(
                snowpark_session=session,
                table_name=ACTIVE_USERS_TABLE_NAME,
                column_name="snapshot_month",
                value=snapshot_month,
            )
            logger.info(
                f"{delete_result.rows_deleted} existing records found and deleted"
            )
            df = create_snowpark_dataframe(
                snowpark_session=session,
                records=users,
                object_type=ACTIVE_USERS_TABLE_NAME,
            )
            df = df.with_column("analytics_user_id", hash(col("email")))
            snowflake_insert_records(table_name=ACTIVE_USERS_TABLE_NAME, records=df)
        logger.info("Pushed Successfully!")

        logger.info("Pushing to Snowflake Analytics Internal DB...")
        with create_snowpark_session(internal=True) as session:
            logger.info(
                f"Deleting existing records for the month {snapshot_month} if any"
            )
            delete_result = snowflake_delete_records(
                snowpark_session=session,
                table_name=ACTIVE_USERS_TABLE_NAME,
                column_name="snapshot_month",
                value=snapshot_month,
            )
            logger.info(
                f"{delete_result.rows_deleted} existing records found and deleted"
            )
            df = create_snowpark_dataframe(
                snowpark_session=session,
                records=users,
                object_type=ACTIVE_USERS_TABLE_NAME,
            )
            df = df.with_column("analytics_user_id", hash(col("email")))
            df = df.drop("email", "first_name", "last_name")
            snowflake_insert_records(table_name=ACTIVE_USERS_TABLE_NAME, records=df)
        logger.info("Pushed Successfully!")

    except DatabaseError as e:
        logger.exception(f"Couldn't connect to Snowflake: {e.msg}")
