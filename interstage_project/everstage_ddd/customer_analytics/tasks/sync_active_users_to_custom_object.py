import logging
import os

import pandas as pd
from celery import shared_task

from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.customer_analytics.tasks.active_users_data import (
    get_active_user_records,
)

logger = logging.getLogger(__name__)

CLIENT_ID = int(os.getenv("ANALYTICS_ACTIVE_USERS_CLIENT_ID", "0"))
CUSTOM_OBJECT_NAME = os.getenv("ANALYTICS_ACTIVE_USERS_CUSTOM_OBJECT_NAME", "")


def get_records_to_delete(source_records: list[dict], custom_object_id: int) -> list:
    """
    Get records to delete from the source records.
    """
    from commission_engine.snowflake_accessors.custom_object_data_accessor import (
        get_custom_object_data,
        get_row_keys,
    )

    # 1. Get all current records from the custom object
    existing_row_keys = get_row_keys(
        client_id=CLIENT_ID, custom_object_id=custom_object_id
    )

    # 2. Create a set of unique identifiers from the source data
    source_keys = {
        f"{record['client_id']}#:::#{record['email']}" for record in source_records
    }

    # 3. Find records that exist in the custom object but not in the source data
    row_keys_to_delete = [
        row_key for row_key in existing_row_keys if row_key not in source_keys
    ]

    records_to_delete = get_custom_object_data(
        client_id=CLIENT_ID,
        custom_object_id=custom_object_id,
        options={"row_key": row_keys_to_delete},
    )
    return [record["data"] for record in records_to_delete]


@shared_task(base=EverCeleryBaseTask)
def sync_active_users_to_custom_object():
    """
    Sync active users data to a custom object, updating only changed records and adding new users.
    Also handles deletion of records that no longer exist in the source data.
    """

    from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
    from spm.constants.data_import_constants import TASKS
    from spm.tasks import upload_data_to_custom_object

    custom_object_id = CustomObjectAccessor(
        client_id=CLIENT_ID
    ).get_custom_object_id_by_name(custom_object_name=CUSTOM_OBJECT_NAME)
    if not custom_object_id:
        logger.error(
            f"Custom object {CUSTOM_OBJECT_NAME} not found in client {CLIENT_ID}"
        )
        return

    logger.info(
        f"Starting sync of active users data to custom object {custom_object_id} in client {CLIENT_ID}"
    )

    # Get active users data
    try:
        users, _ = get_active_user_records(live_data=True)
    except Exception:
        logger.exception("Error while fetching active user records")
        return

    # Remove snapshot_month field as it's not needed and format dates
    for user in users:
        if "snapshot_month" in user:
            del user["snapshot_month"]
        user["added_date"] = user["added_date"].strftime("%d-%b-%Y")
        user["exit_date"] = (
            user["exit_date"].strftime("%d-%b-%Y") if user["exit_date"] else ""
        )
        user["last_commission_date"] = (
            user["last_commission_date"].strftime("%d-%b-%Y")
            if user["last_commission_date"]
            else ""
        )
        user["snapshot_date"] = user["snapshot_date"].strftime("%d-%b-%Y")

    # Convert users list to DataFrame
    df = pd.DataFrame(users)

    # Perform the upsert operation
    # This will:
    # 1. Insert new records that don't exist
    # 2. Update existing records that have changed
    # 3. Leave unchanged records as they are
    try:
        upload_data_to_custom_object(
            client_id=CLIENT_ID,
            report_df=df,
            custom_object_id=custom_object_id,
            upload_type=TASKS.UPSERT.value,
        )
    except Exception:
        logger.exception("Error while uploading data to custom object")
        return
    logger.info(
        f"Inserted and updated records in custom object {custom_object_id} in client {CLIENT_ID}"
    )

    # Handle deleted records
    try:
        records_to_delete = get_records_to_delete(users, custom_object_id)
    except Exception:
        logger.exception("Error while fetching records to delete")
        return

    # Delete these records if any exist
    if records_to_delete:
        # Create a DataFrame with the records to delete
        delete_df = pd.DataFrame(records_to_delete)
        delete_df.rename(
            columns={
                f"co_{custom_object_id}_main": "main",
                f"co_{custom_object_id}_email": "email",
                f"co_{custom_object_id}_spiff": "spiff",
                f"co_{custom_object_id}_region": "region",
                f"co_{custom_object_id}_status": "status",
                f"co_{custom_object_id}_client_id": "client_id",
                f"co_{custom_object_id}_exit_date": "exit_date",
                f"co_{custom_object_id}_is_active": "is_active",
                f"co_{custom_object_id}_last_name": "last_name",
                f"co_{custom_object_id}_user_role": "user_role",
                f"co_{custom_object_id}_added_date": "added_date",
                f"co_{custom_object_id}_first_name": "first_name",
                f"co_{custom_object_id}_client_name": "client_name",
                f"co_{custom_object_id}_designation": "designation",
                f"co_{custom_object_id}_snapshot_date": "snapshot_date",
                f"co_{custom_object_id}_payout_currency": "payout_currency",
                f"co_{custom_object_id}_payout_frequency": "payout_frequency",
                f"co_{custom_object_id}_employment_country": "employment_country",
                f"co_{custom_object_id}_mapped_or_unmapped": "mapped_or_unmapped",
                f"co_{custom_object_id}_last_commission_date": "last_commission_date",
            },
            inplace=True,
        )
        try:
            upload_data_to_custom_object(
                client_id=CLIENT_ID,
                report_df=delete_df,
                custom_object_id=custom_object_id,
                upload_type=TASKS.DELETE.value,
            )
        except Exception:
            logger.exception("Error while deleting records from custom object")
            return
        logger.info(
            f"Deleted {len(records_to_delete)} records that no longer exist in source data"
        )

    logger.info(
        f"Completed sync of active users data to custom object {custom_object_id} in client {CLIENT_ID}"
    )
