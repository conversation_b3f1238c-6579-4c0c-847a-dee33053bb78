import sys
from unittest.mock import MagicMock

import pytest

from everstage_ddd.customer_analytics.tasks.sync_active_users_to_custom_object import (
    CLIENT_ID,
    get_records_to_delete,
)

CUSTOM_OBJECT_ID = 2


@pytest.fixture(autouse=True)
def mock_modules():
    """Fixture to mock modules and their imports. Automatically used by all tests."""
    # Create mock modules
    mock_commission_engine = MagicMock()
    mock_commission_engine.snowflake_accessors = MagicMock()
    mock_commission_engine.snowflake_accessors.custom_object_data_accessor = MagicMock()
    mock_get_custom_object_data = MagicMock()
    mock_get_row_keys = MagicMock()
    mock_commission_engine.snowflake_accessors.custom_object_data_accessor.get_custom_object_data = (
        mock_get_custom_object_data
    )
    mock_commission_engine.snowflake_accessors.custom_object_data_accessor.get_row_keys = (
        mock_get_row_keys
    )

    # Store original modules if they exist
    original_modules = {}
    for module_name in [
        "commission_engine",
        "commission_engine.snowflake_accessors",
        "commission_engine.snowflake_accessors.custom_object_data_accessor",
    ]:
        if module_name in sys.modules:
            original_modules[module_name] = sys.modules[module_name]

    # Add mocks to sys.modules
    sys.modules["commission_engine"] = mock_commission_engine
    sys.modules["commission_engine.snowflake_accessors"] = (
        mock_commission_engine.snowflake_accessors
    )
    sys.modules["commission_engine.snowflake_accessors.custom_object_data_accessor"] = (
        mock_commission_engine.snowflake_accessors.custom_object_data_accessor
    )

    yield {
        "get_custom_object_data": mock_get_custom_object_data,
        "get_row_keys": mock_get_row_keys,
    }

    # Restore original modules
    for module_name, module in original_modules.items():
        sys.modules[module_name] = module


class TestGetRecordsToDelete:
    """Test cases for get_records_to_delete function"""

    def test_no_records_to_delete(self, mock_modules):
        """Test when all source records exist in the custom object"""
        # Setup source records
        source_records = [
            {"client_id": 1001, "email": "<EMAIL>"},
            {"client_id": 1001, "email": "<EMAIL>"},
        ]

        # Setup existing row keys that match source records
        existing_row_keys = [
            "1001#:::#<EMAIL>",
            "1001#:::#<EMAIL>",
        ]
        mock_modules["get_row_keys"].return_value = existing_row_keys

        # No records should be returned for deletion
        mock_modules["get_custom_object_data"].return_value = []

        # Call the function
        result = get_records_to_delete(source_records, CUSTOM_OBJECT_ID)

        # Verify function calls
        mock_modules["get_row_keys"].assert_called_once_with(
            client_id=CLIENT_ID, custom_object_id=CUSTOM_OBJECT_ID
        )
        mock_modules["get_custom_object_data"].assert_called_once_with(
            client_id=CLIENT_ID,
            custom_object_id=CUSTOM_OBJECT_ID,
            options={"row_key": []},
        )

        # Verify result
        assert result == []

    def test_some_records_to_delete(self, mock_modules):
        """Test when some records need to be deleted"""
        # Setup source records
        source_records = [
            {"client_id": 1001, "email": "<EMAIL>"},
            {"client_id": 1001, "email": "<EMAIL>"},
        ]

        # Setup existing row keys with an extra record
        existing_row_keys = [
            "1001#:::#<EMAIL>",
            "1001#:::#<EMAIL>",
            "1001#:::#<EMAIL>",  # This one should be deleted
        ]
        mock_modules["get_row_keys"].return_value = existing_row_keys

        # Setup records to be returned for deletion
        records_to_delete = [
            {
                "data": {
                    f"co_{CUSTOM_OBJECT_ID}_client_id": 1001,
                    f"co_{CUSTOM_OBJECT_ID}_email": "<EMAIL>",
                }
            }
        ]
        mock_modules["get_custom_object_data"].return_value = records_to_delete

        # Call the function
        result = get_records_to_delete(source_records, CUSTOM_OBJECT_ID)

        # Verify function calls
        mock_modules["get_row_keys"].assert_called_once_with(
            client_id=CLIENT_ID, custom_object_id=CUSTOM_OBJECT_ID
        )
        mock_modules["get_custom_object_data"].assert_called_once_with(
            client_id=CLIENT_ID,
            custom_object_id=CUSTOM_OBJECT_ID,
            options={"row_key": ["1001#:::#<EMAIL>"]},
        )

        # Verify result
        assert result == [records_to_delete[0]["data"]]

    def test_all_records_to_delete(self, mock_modules):
        """Test when all existing records need to be deleted"""
        # Setup empty source records
        source_records = []

        # Setup existing row keys
        existing_row_keys = [
            "1001#:::#<EMAIL>",
            "1001#:::#<EMAIL>",
        ]
        mock_modules["get_row_keys"].return_value = existing_row_keys

        # Setup records to be returned for deletion
        records_to_delete = [
            {
                "data": {
                    f"co_{CUSTOM_OBJECT_ID}_client_id": 1001,
                    f"co_{CUSTOM_OBJECT_ID}_email": "<EMAIL>",
                }
            },
            {
                "data": {
                    f"co_{CUSTOM_OBJECT_ID}_client_id": 1001,
                    f"co_{CUSTOM_OBJECT_ID}_email": "<EMAIL>",
                }
            },
        ]
        mock_modules["get_custom_object_data"].return_value = records_to_delete

        # Call the function
        result = get_records_to_delete(source_records, CUSTOM_OBJECT_ID)

        # Verify function calls
        mock_modules["get_row_keys"].assert_called_once_with(
            client_id=CLIENT_ID, custom_object_id=CUSTOM_OBJECT_ID
        )
        mock_modules["get_custom_object_data"].assert_called_once_with(
            client_id=CLIENT_ID,
            custom_object_id=CUSTOM_OBJECT_ID,
            options={"row_key": existing_row_keys},
        )

        # Verify result
        assert result == [record["data"] for record in records_to_delete]

    def test_different_client_ids(self, mock_modules):
        """Test handling of records with different client IDs"""
        # Setup source records with different client IDs
        source_records = [
            {"client_id": 1001, "email": "<EMAIL>"},
            {"client_id": 1002, "email": "<EMAIL>"},
        ]

        # Setup existing row keys
        existing_row_keys = [
            "1001#:::#<EMAIL>",
            "1002#:::#<EMAIL>",
            "1003#:::#<EMAIL>",  # This one should be deleted
        ]
        mock_modules["get_row_keys"].return_value = existing_row_keys

        # Setup records to be returned for deletion
        records_to_delete = [
            {
                "data": {
                    f"co_{CUSTOM_OBJECT_ID}_client_id": 1003,
                    f"co_{CUSTOM_OBJECT_ID}_email": "<EMAIL>",
                }
            }
        ]
        mock_modules["get_custom_object_data"].return_value = records_to_delete

        # Call the function
        result = get_records_to_delete(source_records, CUSTOM_OBJECT_ID)

        # Verify function calls
        mock_modules["get_row_keys"].assert_called_once_with(
            client_id=CLIENT_ID, custom_object_id=CUSTOM_OBJECT_ID
        )
        mock_modules["get_custom_object_data"].assert_called_once_with(
            client_id=CLIENT_ID,
            custom_object_id=CUSTOM_OBJECT_ID,
            options={"row_key": ["1003#:::#<EMAIL>"]},
        )

        # Verify result
        assert result == [records_to_delete[0]["data"]]

    def test_empty_existing_records(self, mock_modules):
        """Test when there are no existing records in the custom object"""
        # Setup source records
        source_records = [
            {"client_id": 1001, "email": "<EMAIL>"},
            {"client_id": 1001, "email": "<EMAIL>"},
        ]

        # Setup empty existing row keys
        mock_modules["get_row_keys"].return_value = []

        # No records should be returned for deletion
        mock_modules["get_custom_object_data"].return_value = []

        # Call the function
        result = get_records_to_delete(source_records, CUSTOM_OBJECT_ID)

        # Verify function calls
        mock_modules["get_row_keys"].assert_called_once_with(
            client_id=CLIENT_ID, custom_object_id=CUSTOM_OBJECT_ID
        )
        mock_modules["get_custom_object_data"].assert_called_once_with(
            client_id=CLIENT_ID,
            custom_object_id=CUSTOM_OBJECT_ID,
            options={"row_key": []},
        )

        # Verify result
        assert result == []
