from django import setup

from everstage_ddd.customer_analytics.utils.general_data import Task
from interstage_project.celery import CommonQueues
from interstage_project.utils import get_common_queue_name

setup()

TASK_NAME = Task.ANALYTICS_ACTIVE_USERS_CUSTOM_OBJECT_SYNC.value["name"]
MINUTE = "0"
HOUR = "5"  # Run at 5 AM
DAY_OF_WEEK = "*"
DAY_OF_MONTH = "*"  # Run every day
MONTH_OF_YEAR = "*"

if __name__ == "__main__":
    print("Creating daily active users sync task entry")

    from commission_engine.accessors.schedule_accessor import (
        CrontabScheduleAccessor,
        PeriodicTaskAccessor,
    )

    cron_tab_schedule = CrontabScheduleAccessor().get_or_create_cron_expression(
        minute=MINUTE,
        hour=HOUR,
        day_of_week=DAY_OF_WEEK,
        day_of_month=DAY_OF_MONTH,
        month_of_year=MONTH_OF_YEAR,
    )

    if not PeriodicTaskAccessor().does_task_exist(TASK_NAME):
        queue_name = get_common_queue_name(CommonQueues.ROUTER.value)
        PeriodicTaskAccessor().get_or_create_task(
            name=TASK_NAME,
            task=Task.ANALYTICS_ACTIVE_USERS_CUSTOM_OBJECT_SYNC.value["function"],
            args=[],
            cron_tab=cron_tab_schedule,
            queue=queue_name,
            routing_key=queue_name,
            kwargs={},
        )

    print("Created daily active users sync task entry")
