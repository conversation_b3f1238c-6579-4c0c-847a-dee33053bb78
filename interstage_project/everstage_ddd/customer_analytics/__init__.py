from .tasks.active_users_data import push_active_users_data_to_snowflake
from .tasks.aggregated_client_features_report import (
    push_aggregated_client_features_data,
)
from .tasks.aggregated_commission_summary_report import (
    push_aggregated_commission_summary_data_to_snowflake,
)
from .tasks.aggregated_data import push_aggregated_data_to_snowflake
from .tasks.analytics_commission_summary_report import (
    push_commission_summary_report_to_snowflake,
)
from .tasks.billing_information_sync import push_billing_information_to_snowflake
from .tasks.client_features_report import push_client_features_data
from .tasks.modules_usage_data import push_modules_usage_data_to_snowflake
from .tasks.sync_active_users_to_custom_object import sync_active_users_to_custom_object

__all__ = [
    "push_active_users_data_to_snowflake",
    "push_modules_usage_data_to_snowflake",
    "push_aggregated_data_to_snowflake",
    "push_commission_summary_report_to_snowflake",
    "push_aggregated_commission_summary_data_to_snowflake",
    "push_client_features_data",
    "push_aggregated_client_features_data",
    "push_billing_information_to_snowflake",
    "sync_active_users_to_custom_object",
]


# This function is defined here because Celery notices and registers the task
# only if its module is imported somewhere in the codebase.
# This sample function is imported in everstage_ddd/__init__.py
def sample():
    pass
