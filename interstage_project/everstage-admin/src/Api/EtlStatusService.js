const getAllRunningEtlSyncs = (accessToken, client = null, subscriptionPlans = null) => {
  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };
  
  let url = `/commission_engine/all_running_syncs/`;
  const params = new URLSearchParams();
  
  if (client !== null) {
    params.append('client_id', client);
  }
  
  if (subscriptionPlans && subscriptionPlans.length > 0) {
    subscriptionPlans.forEach(plan => {
      params.append('subscription_plans', plan);
    });
  }
  
  if (params.toString()) {
    url += '?' + params.toString();
  }
  
  return fetch(url, requestOptions);
};

export const removeEtlLock = (data, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    contentType: false,
    body: JSON.stringify(data),
  };
  return fetch("/commission_engine/remove_etl_lock", requestOptions);
};

export const getAllFailedCronJobs = (accessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };

  return fetch(`/commission_engine/cron_jobs/failed`, requestOptions);
};

export const markCronJobAsFailed = (accessToken, clientId, e2eSyncRunId) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };
  return fetch(
    `/commission_engine/cron_jobs/mark_as_failed/?client_id=${clientId}&e2e_sync_run_id=${e2eSyncRunId}`,
    requestOptions
  );
};

export const retryCronJob = (accessToken, clientId, e2eSyncRunId) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };
  return fetch(
    `/commission_engine/cron_jobs/retry/?client_id=${clientId}&e2e_sync_run_id=${e2eSyncRunId}`,
    requestOptions
  );
};

export const getAllFailedManualJobs = (accessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };

  return fetch(`/commission_engine/manual_jobs/failed`, requestOptions);
};

export const markManualJobAsFailed = (accessToken, clientId, e2eSyncRunId) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };
  return fetch(
    `/commission_engine/manual_jobs/mark_as_failed/?client_id=${clientId}&e2e_sync_run_id=${e2eSyncRunId}`,
    requestOptions
  );
};

export const markSyncAsSkipped = (
  accessToken,
  clientId,
  e2eSyncRunId,
  reason
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };

  console.log("API called");
  return fetch(
    `/commission_engine/syncs/mark_as_skipped/?client_id=${clientId}&e2e_sync_run_id=${e2eSyncRunId}&reason=${reason}`,
    requestOptions
  );
};

export default {
  getAllRunningEtlSyncs,
  removeEtlLock,
  getAllFailedCronJobs,
  markCronJobAsFailed,
  retryCronJob,
  getAllFailedManualJobs,
  markManualJobAsFailed,
  markSyncAsSkipped,
};
