import { useAuthStore } from "GlobalStores/AuthStore";
import { ENTITY_TYPE } from "~/AdminConsole/AgentStudio/enums";

export function useAgentStudioApi() {
  const authStore = useAuthStore();
  const agentExecutor = new AgentStudioApi(authStore.accessToken);
  return agentExecutor;
}

class AgentStudioApi {
  constructor(accessToken) {
    this.accessToken = accessToken;
  }

  getAuthHeaders() {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.accessToken}`,
    };
  }

  getAuthHeadersWithoutContentType() {
    return {
      Authorization: `Bearer ${this.accessToken}`,
    };
  }

  async getAllEntities(clientId, entityType) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getTools(clientId, skillId) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        skillId
          ? `/everstage_admin/llm_infra/tools?client_id=${clientId}&entity_id=${skillId}`
          : `/everstage_admin/llm_infra/tools?client_id=${clientId}`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createEntity(
    entityType,
    clientId,
    data,
    update = false,
    createNewRevision = false,
    entityId = null
  ) {
    const method = createNewRevision ? "POST" : update ? "PUT" : "POST";
    const requestOptions = {
      method: method,
      headers:
        entityType !== ENTITY_TYPE.AGENT
          ? this.getAuthHeaders()
          : this.getAuthHeadersWithoutContentType(),
      contentType: false,
      body: entityType !== ENTITY_TYPE.AGENT ? JSON.stringify(data) : data,
    };
    const updateUrl =
      update || createNewRevision
        ? `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}`
        : `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}`;

    try {
      const response = await fetch(updateUrl, requestOptions);
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return { data: responseData, status: response.status };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async deleteEntity(clientId, entityId, entityType) {
    const requestOptions = {
      method: "DELETE",
      headers: this.getAuthHeaders(),
    };
    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}`,
        requestOptions
      );
      if (!response.ok) throw response;
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async getEntitiesByVersion(clientId, entityId, selectedVersion, entityType) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    const versionId = selectedVersion?.versionId;
    const url = versionId
      ? `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}?version_id=${versionId}`
      : `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}`;

    try {
      const response = await fetch(url, requestOptions);
      const responseData = await response.json();
      if (!response.ok) {
        throw responseData;
      }
      return responseData;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async publishEntity(
    entityType,
    clientId,
    entityId,
    versionId,
    publish = true,
    runTests = false
  ) {
    const requestOptions = {
      method: "POST",
      headers: this.getAuthHeaders(),
      contentType: false,
      body: JSON.stringify({
        version_id: versionId,
        runTests: runTests,
        publish: publish,
      }),
    };
    const url = `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}/publish`;

    try {
      const response = await fetch(url, requestOptions);
      const responseData = await response.json();
      return { results: responseData, status: response.status };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getLLMs() {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/llm-models`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAdvancedPrompts(entityType) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/${entityType.toLowerCase()}/get-advanced-prompt-template`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getEntityVersions(clientId, entityId, entityType) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}/versions`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async runTests(payload, clientId, entityType) {
    // const requestOptions = {
    //   method: "POST",
    //   headers: this.getAuthHeaders(),
    //   body: JSON.stringify(payload),
    // };
    try {
      // const response = await fetch(
      //   `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/evaluate-prompts`,
      //   requestOptions
      // );
      // const responseData = await response.json();
      return { results: {}, status: 200 };
    } catch (error) {
      console.log(error);
    }
  }

  async deployEntity(payload, entityType) {
    const requestOptions = {
      method: "POST",
      headers: this.getAuthHeaders(),
      body: JSON.stringify(payload),
    };
    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/${entityType.toLowerCase()}/deploy`,
        requestOptions
      );
      const responseData = await response.json();
      return { results: responseData, status: response.status };
    } catch (error) {
      console.log(error);
    }
  }

  async upsertCustomTool(payload, clientId, skillId, isEdit = false) {
    const requestOptions = {
      method: isEdit ? "PUT" : "POST",
      headers: this.getAuthHeaders(),
      body: JSON.stringify(payload),
    };
    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/custom-tool/${clientId}/${skillId}`,
        requestOptions
      );
      const responseData = await response.json();
      return { results: responseData, status: response.status };
    } catch (error) {
      console.log(error);
    }
  }

  async getEntityLists(entityType, clientId) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/get_all_entities/${entityType.toLowerCase()}/${clientId}`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getContextData(clientId, contextType) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        clientId
          ? `/everstage_admin/llm_infra/get-context-data/${contextType.toLowerCase()}/${clientId}`
          : `/everstage_admin/llm_infra/get-context-data/${contextType.toLowerCase()}/0`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getDependenciesForEntities(entityType, entityId, clientId) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };

    try {
      const response = await fetch(
        `/everstage_admin/llm_infra/get-dependencies/${entityType.toLowerCase()}/${clientId}/${entityId}`,
        requestOptions
      );
      const responseData = await response.json();
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async upsertContextData(contextType, clientId, context) {
    const requestOptions = {
      method: "POST",
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ context }),
    };
    try {
      const response = await fetch(
        clientId
          ? `/everstage_admin/llm_infra/upsert-context-data/${contextType.toLowerCase()}/${clientId}`
          : `/everstage_admin/llm_infra/upsert-context-data/${contextType.toLowerCase()}/0`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async updateHelperState(entityType, clientId, entityId, isHelper = true) {
    const requestOptions = {
      method: "POST",
      headers: this.getAuthHeaders(),
      contentType: false,
      body: JSON.stringify({
        is_helper: isHelper,
      }),
    };
    const url = `/everstage_admin/llm_infra/${entityType.toLowerCase()}/${clientId}/${entityId}/helper`;

    try {
      const response = await fetch(url, requestOptions);
      const responseData = await response.json();
      return { results: responseData, status: response.status };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getSkillsUsingSkillAsHelper(entityType, clientId, skillTag) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };
    const url = `/everstage_admin/llm_infra/${entityType.toLowerCase()}/get-skill-helper/${clientId}/${skillTag}`;

    try {
      const response = await fetch(url, requestOptions);
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
