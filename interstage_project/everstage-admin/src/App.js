import React, { useEffect } from "react";
import { observer } from "mobx-react";
import AdminApp from "./AdminApp";
import { useAuth0 } from "@auth0/auth0-react";
import { isEmpty } from "lodash";
import { useAuthStore } from "GlobalStores/AuthStore";
import { TerminalIcon as LoginIcon } from "@heroicons/react/solid";
import { Button } from "Components";
import { everstage3ArrowsBg } from "./images";
const EMAIL_NAMESPACE = "https://everstage.com/email";

const App = observer((props) => {
  const { redirect } = props;
  const auth0client = useAuth0();
  const {
    isLoading,
    loginWithRedirect,
    user,
    isAuthenticated,
    getAccessTokenSilently,
    getIdTokenClaims,
  } = auth0client;

  const authStore = useAuthStore();

  const { isUserAllowedForAdminUILogin, isEmailVerified, loading } = authStore;

  useEffect(() => {
    if (isLoading) {
      authStore.setLoading({
        status: true,
        stage: "Authenticating...",
        percentage: 25,
      });
    } else {
      authStore.setLoading({ status: false });
    }
  }, [isLoading]);

  useEffect(() => {
    if (!isAuthenticated && !isLoading && redirect) {
      loginWithRedirect();
    }
  }, [isAuthenticated, isLoading]);

  const manageSession = async () => {
    authStore.setLoading({
      status: true,
      stage: "Setting up session for you...",
      percentage: 75,
    });
    try {
      const response = await authStore.sessionManager();
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error);
      } else {
        const { isTsarAccessible, permissions } = await response.json();
        authStore.setIsTsarAccessible(isTsarAccessible);
        authStore.setPermissions(permissions);
      }

      // Adding an event listener to check the token health; whenever the user switches back to the tab, after being inactive for a while.
      window.onfocus = async () => {
        const response = await authStore.checkTokenHealth();
        if (!response.ok) authStore.killSession();
      };

      //Adding an event listener to refresh the tab whenever there's a change in auth0 session_id or active user.
      const currentEmail = user?.[EMAIL_NAMESPACE]; // here email is of loggedin user (impersonatee; in case of impersonation; although there will be no impersonation in admin-ui but to make page refreshes in sync with main app, this should be set)
      localStorage.setItem(
        "sessionKeyAdminUI",
        `${authStore.idTokenClaims?.sid}##${currentEmail}`
      );
      window.addEventListener("storage", (event) => {
        if (event.key === "sessionKeyAdminUI") window.location.reload();
      });
      return true;
    } catch (error) {
      console.log("Session management failed due to error - ", error.message);
      return false;
    } finally {
      authStore.setLoading({ status: false });
    }
  };

  useEffect(() => {
    const getAccessToken = async () => {
      authStore.setLoading({
        status: true,
        stage: "Authorizing...",
        percentage: 50,
      });

      try {
        const accessToken = await getAccessTokenSilently();
        const claims = await getIdTokenClaims();

        authStore.setAccessToken(accessToken);
        authStore.setIdTokenClaims(claims);
        authStore.setUser(user);
        authStore.setAuth0(auth0client);
        authStore.setLoading({ status: false });

        const sessionManagementOK = await manageSession();
        authStore.setSessionManagementOK(sessionManagementOK);
      } catch (e) {
        console.error("error", e.message);
      }
    };
    if (isAuthenticated) getAccessToken();
  }, [isAuthenticated, user, auth0client]);

  let hint = null;
  const loginButton = (
    <Button onClick={() => loginWithRedirect()}>Login</Button>
  );
  const logoutButton = (
    <Button onClick={() => authStore.userLogout()}>Logout</Button>
  );
  let showApp = false;
  let action = authStore.accessToken ? logoutButton : <></>;

  if (loading.status) {
    hint = "Loading...";
  } else {
    if (!isAuthenticated) {
      action = loginButton;
    } else if (!isUserAllowedForAdminUILogin) {
      hint = "You are not authorized.";
    } else if (!isEmailVerified) {
      hint = "Your email is not verified ";
    } else if (isEmpty(authStore.accessibleScreens)) {
      hint = "No admin role assigned.";
    } else {
      showApp = authStore.sessionManagementOK;
    }
  }

  return showApp ? (
    <AdminApp />
  ) : (
    <div className="flex h-screen bg-ever-primary-900">
      <img src={everstage3ArrowsBg} alt="Everstage Logo" className="m-auto" />
      <div className="absolute inset-0 grid grid-rows-6 grid-cols-3 justify-items-center transition duration-500 ease-in-out transform hover:-translate-y-1 hover:scale-110 bg-ever-base rounded-xl shadow p-8 w-64 h-64 m-auto">
        <div className="row-start-1 col-start-2 ">
          <LoginIcon className="h-32 w-32 fill-current " />
        </div>
        <span className="row-start-5 col-start-1 col-span-3">{hint}</span>
        <div className="row-start-6 col-start-2">{action}</div>
      </div>
    </div>
  );
});

export default App;
