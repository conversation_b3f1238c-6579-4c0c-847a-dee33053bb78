import React from "react";

import { showMessage, Modal, useLoader } from "Components";
import { useAuthStore } from "GlobalStores/AuthStore";
import { NOTIFICATION_TYPE } from "Enums";
import { faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import EtlStatusService from "../../../Api/EtlStatusService";

const RetryETLModal = ({ open, onCancel, onClose, onConfirm, data }) => {
  const { accessToken } = useAuthStore();
  const { showHideLoader } = useLoader();

  const handleRetryETL = async () => {
    showHideLoader(true);
    try {
      const { clientId, e2eSyncRunId, isManualJob } = data;
      const jobType = isManualJob ? "manual" : "cron";

      // Call the appropriate API based on job type
      const response = isManualJob
        ? await EtlStatusService.retryManualJob(
            accessToken,
            clientId,
            e2eSyncRunId
          )
        : await EtlStatusService.retryCronJob(
            accessToken,
            clientId,
            e2eSyncRunId
          );

      if (response?.ok) {
        showHideLoader(false);
        showMessage(`${jobType} sync retried successfully!!!`);
        onConfirm();
      } else if (response?.status === 403) {
        showHideLoader(false);
        showMessage(`Permission denied to retry the ${jobType} sync!!!`, {
          type: NOTIFICATION_TYPE.ERROR,
        });
        onClose();
      } else {
        showHideLoader(false);
        showMessage(`Failed to retry the ${jobType} sync!!!`, {
          type: NOTIFICATION_TYPE.ERROR,
        });
      }
      onClose();
    } catch (e) {
      showHideLoader(false);
      console.log(e);
      showMessage("Failed to retry the sync!!!", {
        type: NOTIFICATION_TYPE.ERROR,
      });
      onClose();
    }
  };
  return (
    <Modal open={open}>
      <Modal.Title onClose={onClose} className={{ title: "text-yellow-500" }}>
        <FontAwesomeIcon
          className="h-5 w-5 mr-3 "
          icon={faExclamationTriangle}
        />
        Warning
      </Modal.Title>
      <Modal.Content>
        <p>{`Are you sure you want to retry the ${
          data?.isManualJob ? "manual" : "cron"
        } sync?`}</p>
      </Modal.Content>
      <Modal.Footer
        hasCancel
        hasConfirm
        onCancel={onCancel}
        onConfirm={handleRetryETL}
      />
    </Modal>
  );
};

export default RetryETLModal;
