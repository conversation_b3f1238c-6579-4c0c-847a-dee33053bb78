import React, { useEffect, useState, useCallback } from "react";
import { observer } from "mobx-react";

import { NOTIFICATION_TYPE } from "Enums";
import { Button, showMessage, Table } from "Components";
import { useAuthStore } from "GlobalStores/AuthStore";

import EtlStatusService from "../../Api/EtlStatusService";
import SetGlobalSyncService from "../../Api/SetGlobalSyncService";
import { resetGlobalSyncs } from "../../Api/ResetGlobalSyncService";
import SyncEnableOrDisableCheckService from "../../Api/SyncEnableOrDisableCheckService";
import RemoveLockModal from "./RemoveLockModal";
import SyncDetailsModal from "./SyncDetailsModal";
import RetryETLModal from "./RetryETLModal";
import MarkCronFailedModal from "./MarkCronFailedModal";
import MarkSyncSkippedModal from "./MarkSyncSkippedModal";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUnlockAlt as removeLock } from "@fortawesome/free-solid-svg-icons";
import { faRedo as retry } from "@fortawesome/free-solid-svg-icons";
import { faExclamationTriangle as warning } from "@fortawesome/free-solid-svg-icons";
import { faFastForward as skip } from "@fortawesome/free-solid-svg-icons";
import { faSync as refresh } from "@fortawesome/free-solid-svg-icons";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";
import { Tooltip } from "antd";

const EtlStatusPage = observer(() => {
  const [etlSyncsFetchLoading, setEtlSyncsFetchLoading] = useState(false);
  const [allEtlSyncs, setAllEtlSyncs] = useState([]);
  const [showLockConfirmModal, setShowLockConfirmModal] = useState(false);
  const [lockModalData, setLockModalData] = useState(null);
  const [syncEnable, setSyncEnable] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  const [loadStatus, setLoadStatus] = useState(false);

  const [allFailedJobs, setAllFailedJobs] = useState([]);
  const [allFailedJobsLoading, setAllFailedJobsLoading] = useState(false);
  const [showRetryETLModal, setShowRetryETLModal] = useState(false);
  const [retryETLModalData, setRetryETLModalData] = useState(null);
  const [showMarkCronFailedModal, setShowMarkCronFailedModal] = useState(false);
  const [markCronFailedModalData, setMarkCronFailedModalData] = useState(null);
  const [showMarkSyncSkippedModal, setShowMarkSyncSkippedModal] =
    useState(false);
  const [markSyncSkippedModalData, setMarkSyncSkippedModalData] =
    useState(null);

  // Toggle to show either ETL syncs or failed jobs
  const [showSyncSection, setShowSyncSection] = useState(true);
  // Toggle between Cron and Manual jobs
  const [showCronJobs, setShowCronJobs] = useState(true);

  const { accessToken, isOpsAdmin, isL1Ops, isValidEverstageAdminUser } =
    useAuthStore();

  const hasPermissionToRemoveLock = isOpsAdmin || isL1Ops;
  const hasPermissionToRetry = isOpsAdmin || isL1Ops;
  const hasPermissionToEnableDisableSync = isValidEverstageAdminUser;

  const [showSyncIdModal, setShowSyncIdModal] = useState(false);
  const [selectedSyncRow, setSelectedSyncRow] = useState(null);

  const handleSyncIdClick = (rowData) => {
    setSelectedSyncRow(rowData);
    setShowSyncIdModal(true);
  };

  const COLUMNS = [
    {
      key: "clientId",
      label: "Client Id",
    },
    {
      key: "clientName",
      label: "Client Name",
      searchable: true,
      minWidth: 150,
    },
    {
      key: "subscriptionPlan",
      label: "Subscription Plan",
      searchable: true,
      minWidth: 150,
    },
    {
      key: "e2eSyncRunId",
      label: "End to End Sync Id",
      searchable: true,
      minWidth: 150,
      onCellClick: handleSyncIdClick,
      render: (value, row) => (
        <span
          className="text-ever-primary hover:underline cursor-pointer"
          onClick={() => {
            handleSyncIdClick({ e2eSyncRunId: value, clientId: row.clientId });
          }}
        >
          {value}
        </span>
      ),
    },
    {
      key: "task",
      label: "Sync Type",
      searchable: true,
      minWidth: 100,
    },
    {
      key: "syncStartTime",
      label: "Start Time",
      align: "center",
      searchable: true,
    },
    {
      key: "syncStatus",
      label: "Status",
      searchable: true,
    },
    {
      key: "elapsedTime",
      label: "Elapsed Time (hrs)",
    },
    {
      key: "actions",
      label: "Actions",
      render: (value, data) => (
        <div>
          <ToolTip
            required={hasPermissionToRemoveLock ? false : true}
            text="Permission denied"
          >
            <span
              className={`text-xs ml-3 mr-6 ${
                hasPermissionToRemoveLock
                  ? "text-ever-primary hover:text-ever-primary cursor-pointer"
                  : "text-ever-base-400 hover:text-ever-base-content-low cursor-not-allowed"
              } `}
              onClick={() =>
                handleRemoveEtlLock(data.e2eSyncRunId, data.clientId)
              }
            >
              <FontAwesomeIcon
                className="h-5 w-5 mr-1.5 pt-1.5"
                icon={removeLock}
              />{" "}
              RELEASE LOCK
            </span>{" "}
          </ToolTip>
        </div>
      ),
    },
  ];

  // Common columns for both Cron and Manual jobs
  const getFailedJobsColumns = (isCronJob) => [
    {
      key: "clientId",
      label: "Client Id",
    },
    {
      key: "clientName",
      label: "Client Name",
      searchable: true,
      minWidth: 150,
    },
    {
      key: "e2eSyncRunId",
      label: "End to End Sync Id",
      searchable: true,
      minWidth: 150,
    },
    {
      key: "task",
      label: "Sync Type",
      searchable: true,
      minWidth: 100,
    },
    {
      key: "syncStartTime",
      label: "Start Time",
    },
    {
      key: "syncEndTime",
      label: "End Time",
    },
    {
      key: "syncStatus",
      label: "Status",
      searchable: true,
    },
    {
      key: "actions",
      label: (
        <div className="flex items-center">
          Actions
          <Tooltip title="Click to refer to Confluence page">
            {/* Wrap the icon in a span to ensure proper event handling */}
            <span
              className="ml-1 cursor-pointer text-ever-base-500 hover:text-ever-base-100"
              onClick={() =>
                window.open(
                  "https://interstage.atlassian.net/wiki/spaces/TECH/pages/1047953415/Retry+Failed+Cron+Syncs+from+the+Admin+UI",
                  "_blank"
                )
              }
            >
              <FontAwesomeIcon className="h-4 w-4" icon={faInfoCircle} />
            </span>
          </Tooltip>
        </div>
      ),
      render: (value, data) => (
        <div>
          <ToolTip
            required={hasPermissionToRetry ? false : true}
            text="Permission denied"
          >
            <div>
              {isCronJob && (
                <div
                  className={`text-xs ml-3 mr-6 ${
                    hasPermissionToRetry
                      ? "text-ever-primary hover:text-ever-primary cursor-pointer"
                      : "text-ever-base-400 hover:text-ever-base-content-low cursor-not-allowed"
                  } `}
                  onClick={() => {
                    handleRetryCronJob(data.clientId, data.e2eSyncRunId);
                  }}
                >
                  <FontAwesomeIcon
                    className="h-5 w-5 mr-1.5 pt-1.5"
                    icon={retry}
                  />{" "}
                  RETRY FAILED CRON JOB
                </div>
              )}

              <div
                className={`text-xs ml-3 mr-6 ${
                  hasPermissionToRetry
                    ? isCronJob
                      ? "text-ever-primary hover:text-ever-primary cursor-pointer"
                      : "text-purple-600 hover:text-purple-600 cursor-pointer"
                    : "text-ever-base-400 hover:text-ever-base-content-low cursor-not-allowed"
                } `}
                onClick={() => {
                  if (isCronJob) {
                    handleMarkCronJobAsFailed(data.clientId, data.e2eSyncRunId);
                  } else {
                    handleMarkManualJobAsFailed(
                      data.clientId,
                      data.e2eSyncRunId
                    );
                  }
                }}
              >
                <FontAwesomeIcon
                  className="h-5 w-5 mr-1.5 pt-1.5"
                  icon={warning}
                />{" "}
                MARK AS FAILED
              </div>

              <div
                className={`text-xs ml-3 mr-6 ${
                  hasPermissionToRetry
                    ? isCronJob
                      ? "text-ever-primary hover:text-ever-primary cursor-pointer"
                      : "text-purple-600 hover:text-purple-600 cursor-pointer"
                    : "text-ever-base-400 hover:text-ever-base-content-low cursor-not-allowed"
                } `}
                onClick={() => {
                  handleMarkSyncAsSkipped(data.clientId, data.e2eSyncRunId);
                }}
              >
                <FontAwesomeIcon
                  className="h-5 w-5 mr-1.5 pt-1.5"
                  icon={skip}
                />{" "}
                MARK AS SKIPPED
              </div>
            </div>
          </ToolTip>
        </div>
      ),
    },
  ];

  // Get the appropriate columns based on the current job type
  const failedJobsColumns = getFailedJobsColumns(showCronJobs);

  const ToolTip = (props) => {
    return props.required ? (
      <span className="group relative">
        <span className="pointer-events-none absolute -top-10 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-black px-2 py-1 text-ever-base opacity-0 transition before:absolute before:left-1/2 before:top-full before:-translate-x-1/2 before:border-4 before:border-transparent before:border-t-black before:content-[''] group-hover:opacity-100">
          {props.text}
        </span>
        {props.children}
      </span>
    ) : (
      <span>{props.children}</span>
    );
  };

  const handleRemoveEtlLock = (e2eSyncRunId, clientId) => {
    if (hasPermissionToRemoveLock) {
      setLockModalData({ e2eSyncRunId: e2eSyncRunId, clientId: clientId });
      setShowLockConfirmModal(true);
    }
  };

  const handleRetryCronJob = (clientId, e2eSyncRunId) => {
    setRetryETLModalData({
      clientId: clientId,
      e2eSyncRunId: e2eSyncRunId,
      isManualJob: false,
    });
    setShowRetryETLModal(true);
  };

  const fetchEtlSyncs = useCallback(() => {
    setEtlSyncsFetchLoading(true);
    EtlStatusService.getAllRunningEtlSyncs(accessToken)
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");

        const etlSyncs = await response.json();
        setAllEtlSyncs(etlSyncs);
      })
      .catch((error) => {
        console.error(error);
        showMessage("Could not fetch running etl syncs", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      })
      .finally(() => {
        setEtlSyncsFetchLoading(false);
      });
  }, [accessToken]);

  const refreshEtlSyncs = useCallback(() => {
    setAllEtlSyncs([]);
    fetchEtlSyncs();
  }, [fetchEtlSyncs]);

  const fetchFailedCronJobs = useCallback(() => {
    setAllFailedJobsLoading(true);
    EtlStatusService.getAllFailedCronJobs(accessToken)
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");

        const failedCronJobs = await response.json();
        setAllFailedJobs(failedCronJobs);
      })
      .catch((error) => {
        console.error(error);
        showMessage("Could not fetch failed cron jobs", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      })
      .finally(() => {
        setAllFailedJobsLoading(false);
      });
  }, [accessToken, setAllFailedJobsLoading, setAllFailedJobs]);

  const fetchFailedManualJobs = useCallback(() => {
    setAllFailedJobsLoading(true);
    EtlStatusService.getAllFailedManualJobs(accessToken)
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");

        const failedManualJobs = await response.json();
        setAllFailedJobs(failedManualJobs);
      })
      .catch((error) => {
        console.error(error);
        showMessage("Could not fetch failed manual jobs", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      })
      .finally(() => {
        setAllFailedJobsLoading(false);
      });
  }, [accessToken, setAllFailedJobsLoading, setAllFailedJobs]);

  const refreshFailedJobs = useCallback(() => {
    setAllFailedJobs([]);
    if (showCronJobs) {
      fetchFailedCronJobs();
    } else {
      fetchFailedManualJobs();
    }
  }, [showCronJobs, fetchFailedCronJobs, fetchFailedManualJobs]);

  const handleMarkCronJobAsFailed = (client_id, e2e_sync_run_id) => {
    setMarkCronFailedModalData({
      clientId: client_id,
      e2eSyncRunId: e2e_sync_run_id,
      isManualJob: false,
    });
    setShowMarkCronFailedModal(true);
  };

  const handleMarkManualJobAsFailed = (client_id, e2e_sync_run_id) => {
    setMarkCronFailedModalData({
      clientId: client_id,
      e2eSyncRunId: e2e_sync_run_id,
      isManualJob: true,
    });
    setShowMarkCronFailedModal(true);
  };

  const handleMarkSyncAsSkipped = (clientId, e2eSyncRunId) => {
    setMarkSyncSkippedModalData({
      clientId: clientId,
      e2eSyncRunId: e2eSyncRunId,
    });
    setShowMarkSyncSkippedModal(true);
  };

  const setSyncForAll = () => {
    setLoadStatus(true);
    SetGlobalSyncService.setGlobalSyncs(accessToken)
      .then(async (response) => {
        if (!response.ok) {
          if (response.status === 409) {
            throw new Error("Conflict: Global Sync is Already Set");
          } else if (response.status === 401) {
            throw new Error("Unauthorized: Sync Job cannot be submitted");
          } else {
            throw new Error("Error: Unexpected response");
          }
        }

        setSyncEnable(true);
        showMessage("Global sync successfully Enabled!", {
          type: NOTIFICATION_TYPE.SUCCESS,
        });
      })
      .catch((error) => {
        const errorMessage = error.message;

        if (errorMessage.includes("Conflict")) {
          showMessage("Global Sync is already on, please refresh the page.", {
            type: NOTIFICATION_TYPE.ERROR,
          });
        } else if (errorMessage.includes("Unauthorized")) {
          showMessage("Sync Job cannot be submitted, please try again later.", {
            type: NOTIFICATION_TYPE.ERROR,
          });
        } else {
          showMessage("Error: Global Sync cannot be set.", {
            type: NOTIFICATION_TYPE.ERROR,
          });
        }

        console.error(errorMessage);
      })
      .finally(() => {
        setLoadStatus(false);
      });
  };

  const resetSyncForAll = () => {
    setLoadStatus(true);
    resetGlobalSyncs(accessToken)
      .then(async (response) => {
        if (!response.ok) {
          if (response.status === 409) {
            throw new Error("Conflict: Global Sync is Already Disabled");
          } else if (response.status === 401) {
            throw new Error("Unauthorized: Sync Job cannot be submitted");
          } else {
            throw new Error("Error: Unexpected response");
          }
        }

        setSyncEnable(!syncEnable);
        showMessage("Global sync successfully Disabled!", {
          type: NOTIFICATION_TYPE.SUCCESS,
        });
      })
      .catch((error) => {
        const errorMessage = error.message;

        if (errorMessage.includes("Conflict")) {
          showMessage("Global Sync is already off, please refresh the page", {
            type: NOTIFICATION_TYPE.ERROR,
          });
        } else if (errorMessage.includes("Unauthorized")) {
          showMessage("Sync Job cannot be submitted, please try again later.", {
            type: NOTIFICATION_TYPE.ERROR,
          });
        } else {
          showMessage("Error: Global Sync cannot be disabled.", {
            type: NOTIFICATION_TYPE.ERROR,
          });
        }

        console.error(errorMessage);
      })
      .finally(() => {
        setLoadStatus(false);
      });
  };

  const syncEnableOrDisableCheck = useCallback(() => {
    SyncEnableOrDisableCheckService.syncEnableOrDisableFunc(accessToken)
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");

        const temp = await response.json();
        const globalSyncEnabled = temp["globalSync"];

        if (globalSyncEnabled === "enabled") {
          setSyncEnable(true);
        } else {
          setSyncEnable(false);
        }
      })
      .catch((error) => {
        console.error(error);
        showMessage("Could not get global sync status", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      })
      .finally(() => {
        setInitialLoad(false);
      });
  }, [accessToken]);

  // Toggle between ETL Syncs and Failed Jobs sections
  const toggleShowSection = useCallback(() => {
    const newShowSyncSection = !showSyncSection;
    setShowSyncSection(newShowSyncSection);

    if (!newShowSyncSection) {
      refreshFailedJobs();
    } else {
      refreshEtlSyncs();
    }
  }, [showSyncSection, refreshFailedJobs, refreshEtlSyncs]);

  // Toggle between Cron and Manual jobs
  const toggleJobType = useCallback(() => {
    const newShowCronJobs = !showCronJobs;
    setShowCronJobs(newShowCronJobs);

    // Immediately load data based on the new job type
    if (newShowCronJobs) {
      // If switching to Cron jobs, fetch the cron jobs data
      setAllFailedJobs([]);
      setAllFailedJobsLoading(true);
      fetchFailedCronJobs();
    } else {
      // If switching to Manual jobs, fetch the manual jobs data
      setAllFailedJobs([]);
      setAllFailedJobsLoading(true);
      fetchFailedManualJobs();
    }
  }, [showCronJobs, fetchFailedCronJobs, fetchFailedManualJobs]);

  useEffect(() => {
    if (accessToken) {
      fetchEtlSyncs();
      syncEnableOrDisableCheck();
      fetchFailedCronJobs();
    }
  }, [
    accessToken,
    fetchEtlSyncs,
    syncEnableOrDisableCheck,
    fetchFailedCronJobs,
  ]);

  const setSync = () => {
    setSyncForAll();
  };

  const resetSync = () => {
    resetSyncForAll();
  };

  const onCloseLockConfirmationModal = () => {
    setShowLockConfirmModal(false);
  };

  const onConfirmLockConfirmationModal = () => {
    setShowLockConfirmModal(false);
    refreshEtlSyncs();
  };

  const onCloseCronJobModal = () => {
    setShowRetryETLModal(false);
    setShowMarkCronFailedModal(false);
    setShowMarkSyncSkippedModal(false);
  };

  const onConfirmCronJobModal = () => {
    setShowRetryETLModal(false);
    setShowMarkCronFailedModal(false);
    setShowMarkSyncSkippedModal(false);
    refreshFailedJobs();
  };

  // Toggle Switch Component
  const ToggleSwitch = ({ isOn, handleToggle, leftLabel, rightLabel }) => {
    return (
      <div className="flex items-center mr-4 mt-2">
        <span
          className={`mr-2 text-sm ${
            !isOn ? "font-bold text-ever-primary" : "text-gray-500"
          }`}
        >
          {leftLabel}
        </span>
        <div
          className={`relative inline-block w-12 h-6 transition-colors duration-300 ease-in-out rounded-full cursor-pointer ${
            isOn ? "bg-purple-600" : "bg-ever-primary"
          }`}
          onClick={handleToggle}
        >
          <div
            className={`absolute w-5 h-5 transition-transform duration-300 ease-in-out transform bg-white rounded-full top-0.5 ${
              isOn ? "translate-x-6" : "translate-x-0.5"
            }`}
          />
        </div>
        <span
          className={`ml-2 text-sm ${
            isOn ? "font-bold text-purple-600" : "text-gray-500"
          }`}
        >
          {rightLabel}
        </span>
      </div>
    );
  };

  return (
    <div className="px-4 pt-10">
      <div>
        {!initialLoad && (
          <>
            {syncEnable ? (
              <Button
                title={
                  hasPermissionToEnableDisableSync
                    ? "Click to disable all further syncs"
                    : "Permission denied"
                }
                onClick={
                  hasPermissionToEnableDisableSync ? resetSync : undefined
                }
                disabled={loadStatus || !hasPermissionToEnableDisableSync}
                className={
                  !hasPermissionToEnableDisableSync
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }
              >
                Disable syncs
              </Button>
            ) : (
              <Button
                title={
                  hasPermissionToEnableDisableSync
                    ? "Click to start all syncs"
                    : "Permission denied"
                }
                onClick={hasPermissionToEnableDisableSync ? setSync : undefined}
                disabled={loadStatus || !hasPermissionToEnableDisableSync}
                className={
                  !hasPermissionToEnableDisableSync
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }
              >
                Start syncs
              </Button>
            )}
          </>
        )}

        <Button
          className="float-right mr-4"
          title={
            showSyncSection ? "Show Failed Jobs" : "Show Running ETL Syncs"
          }
          onClick={toggleShowSection}
        >
          {showSyncSection ? (
            <>
              <div className="relative inline-block">Show Failed Jobs</div>
              {allFailedJobs.length > 0 && (
                <span className="absolute top-0 right-0 -mt-1 -mr-1 bg-red-500 text-white w-3 h-3 rounded-full"></span>
              )}
            </>
          ) : (
            "Show Running ETL Syncs"
          )}
        </Button>

        {showSyncSection ? (
          <Button
            type="ghost"
            color="base"
            className="float-right mr-4"
            title="Refresh to get running etl syncs"
            onClick={refreshEtlSyncs}
          >
            <div className="flex items-center justify-center">
              <FontAwesomeIcon
                className="h-5 w-5 mr-1.5 pt-1.5"
                icon={refresh}
              />
              <span>Refresh</span>
            </div>
          </Button>
        ) : (
          <>
            <Button
              type="ghost"
              color="base"
              className="float-right mr-4"
              title="Refresh to get failed jobs"
              onClick={refreshFailedJobs}
            >
              <div className="flex items-center justify-center">
                <FontAwesomeIcon
                  className="h-5 w-5 mr-1.5 pt-1.5"
                  icon={refresh}
                />
                <span>Refresh</span>
              </div>
            </Button>

            <div className="float-right flex items-center mr-4">
              <ToggleSwitch
                isOn={!showCronJobs}
                handleToggle={toggleJobType}
                leftLabel="Cron"
                rightLabel="Manual"
              />
            </div>
          </>
        )}
      </div>

      {showSyncSection ? (
        <div className="mt-4">
          <div className="mt-4 mb-4">
            <h1 className="text-xl font-bold">Running ETL Syncs</h1>
          </div>
          <Table
            showSearch
            className={{
              table: "w-full",
              headCell: "py-3",
              search: "w-80 h-8",
            }}
            loading={etlSyncsFetchLoading}
            dataSource={allEtlSyncs}
            columns={COLUMNS}
          />
        </div>
      ) : (
        <div className="mt-4">
          <div className="mt-4 mb-4">
            <h1 className="text-xl font-bold">
              Failed {showCronJobs ? "Cron" : "Manual"} Jobs
            </h1>
          </div>
          <Table
            showSearch
            className={{
              table: "w-full",
              headCell: "py-3",
              search: "w-80 h-8",
            }}
            loading={allFailedJobsLoading}
            dataSource={allFailedJobs}
            columns={failedJobsColumns}
          />
        </div>
      )}

      <RemoveLockModal
        open={showLockConfirmModal}
        onCancel={onCloseLockConfirmationModal}
        onClose={onCloseLockConfirmationModal}
        onConfirm={onConfirmLockConfirmationModal}
        data={lockModalData}
      />

      <RetryETLModal
        open={showRetryETLModal}
        onCancel={onCloseCronJobModal}
        onClose={onCloseCronJobModal}
        onConfirm={onConfirmCronJobModal}
        data={retryETLModalData}
      />

      <MarkCronFailedModal
        open={showMarkCronFailedModal}
        onCancel={onCloseCronJobModal}
        onClose={onCloseCronJobModal}
        onConfirm={onConfirmCronJobModal}
        data={markCronFailedModalData}
      />

      <MarkSyncSkippedModal
        open={showMarkSyncSkippedModal}
        onCancel={onCloseCronJobModal}
        onClose={onCloseCronJobModal}
        onConfirm={onConfirmCronJobModal}
        data={markSyncSkippedModalData}
      />

      {showSyncIdModal && selectedSyncRow && (
        <SyncDetailsModal
          onClose={() => {
            setShowSyncIdModal(false);
            setSelectedSyncRow(null);
          }}
          data={selectedSyncRow}
        />
      )}
    </div>
  );
});

export default EtlStatusPage;
