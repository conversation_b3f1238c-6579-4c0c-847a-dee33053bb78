import React, { useState } from "react";

import { showMessage, Modal, useLoader } from "Components";
import { useAuthStore } from "GlobalStores/AuthStore";
import { NOTIFICATION_TYPE } from "Enums";
import { faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import EtlStatusService from "../../../Api/EtlStatusService";

const MarkSyncSkippedModal = ({ open, onCancel, onClose, onConfirm, data }) => {
  const { accessToken } = useAuthStore();
  const { showHideLoader } = useLoader();
  const [reason, setReason] = useState("");

  const handleMarkSyncAsSkipped = async () => {
    if (!reason.trim()) {
      showMessage("Please provide a reason for skipping the sync", {
        type: NOTIFICATION_TYPE.ERROR,
      });
      return;
    }

    showHideLoader(true);
    try {
      const { clientId, e2eSyncRunId } = data;

      const response = await EtlStatusService.markSyncAsSkipped(
        accessToken,
        clientId,
        e2eSyncRunId,
        reason.trim()
      );

      if (response?.ok) {
        showHideLoader(false);
        showMessage(`Sync marked as skipped successfully!!!`);
        onConfirm();
      } else if (response?.status === 403) {
        showHideLoader(false);
        showMessage(`Permission denied to mark the sync as skipped!!!`, {
          type: NOTIFICATION_TYPE.ERROR,
        });
        onClose();
      } else {
        showHideLoader(false);
        showMessage(`Failed to mark the sync as skipped!!!`, {
          type: NOTIFICATION_TYPE.ERROR,
        });
      }
      onClose();
    } catch (e) {
      showHideLoader(false);
      console.log(e);
      showMessage("Failed to mark the sync as skipped!!!", {
        type: NOTIFICATION_TYPE.ERROR,
      });
      onClose();
    }

    setReason("");
  };

  return (
    <Modal open={open}>
      <Modal.Title onClose={onClose} className={{ title: "text-yellow-500" }}>
        <FontAwesomeIcon
          className="h-5 w-5 mr-3 "
          icon={faExclamationTriangle}
        />
        Warning
      </Modal.Title>
      <Modal.Content>
        <p className="mb-4">{`Are you sure you want to mark the sync as skipped?`}</p>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Reason for skipping
          </label>
          <textarea
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-ever-primary focus:border-transparent"
            rows="3"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter reason for skipping the sync..."
          />
        </div>
      </Modal.Content>
      <Modal.Footer
        hasCancel
        hasConfirm
        onCancel={onCancel}
        onConfirm={handleMarkSyncAsSkipped}
      />
    </Modal>
  );
};

export default MarkSyncSkippedModal;
