import {
  ChartBarIcon,
  TerminalIcon,
  UsersIcon,
  BookmarkIcon,
  MailIcon,
  BeakerIcon,
  SupportIcon,
  TemplateIcon,
  DocumentReportIcon,
  FlagIcon,
} from "@heroicons/react/outline";
import { ChipIcon } from "@heroicons/react/outline";
import { useAuth0 } from "@auth0/auth0-react";
import { observer } from "mobx-react";
import React from "react";
import { Switch, Route, Redirect, useLocation } from "react-router-dom";
import { createClient } from "@supabase/supabase-js";
import { MenuList } from "Components";
import { useEffect, useState, useRef } from "react";
import { CountryCurrency, EmailTemplates, MFA } from "./Configure";
import Customers from "./Customers";
import CustomerEdit from "./Customers/CustomerEdit";
import ManualIntegration from "./Customers/CustomerEdit/ManualIntegration";
import AdditioinalDeleteSyncPage from "./Customers/CustomerEdit/AdditionalDeleteSync";
import SupportTeam from "./Customers/CustomerEdit/SupportTeam/SupportTeam";
import GlobalSearch from "./Customers/CustomerEdit/GlobalSearch";
import { ConfigureIcon, CountryIcon, GenieLamp, TableIcon } from "icons";
import {
  IntegrationPage,
  IntegrationsPage,
} from "./Customers/CustomerEdit/Integrations";
import EtlStatusPage from "./EtlStatusPage";
import DailySyncPage from "./DailySyncPage";
import DeploymentPage from "./DeploymentPage";
import AuditLogsPage from "./AuditLogs";
import { useAuthStore, SCREENS } from "GlobalStores/AuthStore";
import GenieWrapper from "./Genie";
import TsarWrapper from "./tsar";
import { Home } from "./AgentStudio";
import { AgentDeployment } from "./AgentDeployment";
import CustomTheme from "./CustomTheme";
import QaReportsPage from "./QAReports";
import Auth0UserManagementWrapper from "./Customers/CustomerEdit/Auth0User";
import HomeScreen from "./AgentStudio/studio-components/createWindow";
import {
  NewEntity,
  ViewEntity,
} from "./AgentStudio/studio-components/crudWindow";
import FeatureFlags from "./FeatureFlags";
import EngineToolsPage from "./EngineToolsPage";

const LOGO = "http://du4a3cteiciwm.cloudfront.net/Everstage-logo-icon.png";

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const anonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

const DEPLOYMENT_ENV = process.env.REACT_APP_ACTUAL_ENV;
const DEPLOYMENT_FINISH_KEY = `${DEPLOYMENT_ENV}-deployment-finish`;

const NAVIGATION_OPTIONS = {
  [SCREENS.CUSTOMERS]: {
    id: 2,
    name: "Customers",
    path: "/admin-ui/customers",
    icon: UsersIcon,
    current: false,
  },
  [SCREENS.ETL_STATUS]: {
    id: 3,
    name: "ETL Status",
    path: "/admin-ui/etl-status",
    icon: ChartBarIcon,
    current: false,
  },
  [SCREENS.ENGINE_TOOLS]: {
    id: 4,
    name: "Engine Tools",
    path: "/admin-ui/engine-tools",
    icon: ChipIcon,
    current: false,
  },
  [SCREENS.DAILY_SYNC]: {
    id: 5,
    name: "Daily Sync",
    path: "/admin-ui/daily-sync",
    icon: BookmarkIcon,
    current: false,
  },
  [SCREENS.CONFIGURE]: {
    id: 6,
    name: "Configure",
    path: "/admin-ui/configure",
    icon: ConfigureIcon,
    current: false,
    children: [
      {
        id: 6,
        name: "Country & Currency",
        path: "/admin-ui/configure/countries",
        icon: CountryIcon,
        current: false,
      },
      {
        id: 6,
        name: "Email Templates",
        path: "/admin-ui/configure/email-templates",
        icon: TableIcon,
        current: false,
      },
      {
        id: 6,
        name: "Multi-Factor Authentication",
        path: "/admin-ui/configure/mfa",
        icon: MailIcon,
        current: false,
      },
    ],
  },
  [SCREENS.DEPLOYMENT]: {
    id: 7,
    name: "Deployment",
    path: "/admin-ui/deployment",
    icon: TerminalIcon,
    current: false,
  },
  [SCREENS.GENIE]: {
    id: 8,
    name: "Genie",
    path: "/admin-ui/genie",
    icon: GenieLamp,
    current: false,
  },
  [SCREENS.AUDIT_LOGS]: {
    id: 9,
    name: "Audit Logs",
    path: "/admin-ui/audit-logs",
    icon: TerminalIcon,
    current: false,
  },
  [SCREENS.TSAR]: {
    id: 10,
    name: "TSAR",
    path: "/admin-ui/tsar",
    icon: SupportIcon,
    current: false,
  },
  [SCREENS.AGENT_STUDIO]: {
    id: 11,
    name: "Agent Studio",
    path: "/admin-ui/agent-studio",
    icon: BeakerIcon,
    current: false,
  },
  [SCREENS.AGENT_DEPLOYMENT]: {
    id: 12,
    name: "Agent Deployment",
    path: "/admin-ui/agent-deployment",
    icon: BeakerIcon,
    current: false,
  },
  [SCREENS.QA_REPORTS]: {
    id: 13,
    name: "QA Reports",
    path: "/admin-ui/qa-reports",
    icon: DocumentReportIcon,
    current: false,
  },
  [SCREENS.CUSTOM_THEME]: {
    id: 14,
    name: "Custom Theme",
    path: "/admin-ui/custom-theme",
    icon: TemplateIcon,
  },
  [SCREENS.FEATURE_FLAGS]: {
    id: 13,
    name: "Feature Flags",
    path: "/admin-ui/feature-flags",
    icon: FlagIcon,
    current: false,
  },
};

const constructNavigation = (screens) => {
  return screens.map((screen) => NAVIGATION_OPTIONS[screen]);
};

const AdminConsole = observer((props) => {
  const { user } = useAuth0();
  const { pathname, state } = useLocation();
  const authStore = useAuthStore();
  const { accessToken, accessibleScreens } = authStore;
  const accessibleScreensSet = new Set(accessibleScreens);

  const { picture, email, name: userName } = user;
  const [navigation, setNavigation] = useState([]);

  useEffect(() => {
    const navigation = constructNavigation(accessibleScreens);
    if (pathname) {
      const currNavIndex = navigation.findIndex((nav) =>
        pathname.includes(nav.path)
      );
      if (navigation[currNavIndex]?.id) {
        navigation[currNavIndex] = {
          ...navigation[currNavIndex],
          current: true,
        };
      }
    }
    setNavigation(navigation);
  }, [accessibleScreens, pathname, state]);

  const supabaseClientRef = useRef(null);
  const broadcastChannelRef = useRef(null);
  const broadcastStatusRef = useRef(null);
  const [retriggerOnTimeout, setRetriggerOnTimeout] = useState(0);

  // Creating supabase client and subscribing to "deployment-finish" channel for
  // broadcasting deployment finish message. Subscribed in the parent component
  // to avoid multiple re-subscriptions.
  useEffect(() => {
    (async () => {
      const supabaseClient = createClient(supabaseUrl, anonKey);
      const broadcastChannel = supabaseClient.channel(DEPLOYMENT_FINISH_KEY, {
        config: { broadcast: { self: true } },
      });
      broadcastChannel.on(
        "broadcast",
        { event: DEPLOYMENT_FINISH_KEY },
        (payload) => {
          console.log("Deployment finished payload: ", payload);
        }
      );

      broadcastChannel.subscribe((status) => {
        console.log("Deployment finished status (in subscribe): ", status);
        broadcastStatusRef.current = status;
        if (status === "TIMED_OUT") {
          supabaseClientRef.current?.removeAllChannels();
          supabaseClientRef.current = null;
          setRetriggerOnTimeout(retriggerOnTimeout + 1);
        }
      });

      supabaseClientRef.current = supabaseClient;
      broadcastChannelRef.current = broadcastChannel;
    })();
  }, [accessToken, retriggerOnTimeout]);

  const broadcastMessage = (eventKey, payloadData) => {
    const broadcastChannel = broadcastChannelRef.current;
    const broadcastStatus = broadcastStatusRef.current;
    if (broadcastChannel && broadcastStatus === "SUBSCRIBED") {
      broadcastChannel.send({
        type: "broadcast",
        event: eventKey,
        payload: payloadData,
      });
      return true;
    }
    return false;
  };

  const handleNotifyDeploymentFinish = () => {
    const resp = broadcastMessage(DEPLOYMENT_FINISH_KEY, {
      user: email,
      data: DEPLOYMENT_FINISH_KEY,
    });

    return resp;
  };

  const handleRevertNotifyDeployment = () => {
    const resp = broadcastMessage(DEPLOYMENT_FINISH_KEY, {
      user: email,
      data: "IGNORE",
    });

    return resp;
  };

  const routes = [
    // CUSTOMERS ************************************************/
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers",
      component: Customers,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id",
      component: CustomerEdit,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/auth0",
      component: Auth0UserManagementWrapper,
      wrapperClass: "py-6 h-full w-full",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/integrations",
      component: IntegrationsPage,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/manual-integrations",
      component: ManualIntegration,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/support-team",
      component: SupportTeam,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/global-search",
      component: GlobalSearch,
      wrapperClass: "h-full w-full",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/additional-delete",
      component: AdditioinalDeleteSyncPage,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CUSTOMERS,
      path: "/admin-ui/customers-edit/:id/integrations/:integrationId",
      component: IntegrationPage,
      wrapperClass: "py-6",
    },

    // ETL STATUS ************************************************/
    {
      screen: SCREENS.ETL_STATUS,
      path: "/admin-ui/etl-status",
      component: EtlStatusPage,
      wrapperClass: "py-6",
    },

    // DAILY SYNC ************************************************/
    {
      screen: SCREENS.DAILY_SYNC,
      path: "/admin-ui/daily-sync",
      component: DailySyncPage,
      wrapperClass: "py-6",
    },

    // CONFIGURE *************************************************/
    {
      screen: SCREENS.CONFIGURE,
      path: "/admin-ui/configure/countries",
      component: CountryCurrency,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CONFIGURE,
      path: "/admin-ui/configure/email-templates",
      component: EmailTemplates,
      wrapperClass: "py-6",
    },
    {
      screen: SCREENS.CONFIGURE,
      path: "/admin-ui/configure/mfa",
      component: MFA,
      wrapperClass: "p-6",
    },

    // DEPLOYMENT ************************************************/
    {
      screen: SCREENS.DEPLOYMENT,
      path: "/admin-ui/deployment",
      component: DeploymentPage,
      wrapperClass: "py-6",
      props: {
        onNotifyDeploymentFinish: handleNotifyDeploymentFinish,
        onRevertNotifyDeployment: handleRevertNotifyDeployment,
      },
    },

    // GENIE ******************************************************/
    {
      screen: SCREENS.GENIE,
      path: "/admin-ui/genie",
      component: GenieWrapper,
      wrapperClass: "h-full w-full",
    },

    // AUDIT LOGS  ************************************************/
    {
      screen: SCREENS.AUDIT_LOGS,
      path: "/admin-ui/audit-logs",
      component: AuditLogsPage,
      wrapperClass: "h-full w-full",
    },

    // QA Reports  ************************************************/
    {
      screen: SCREENS.QA_REPORTS,
      path: "/admin-ui/qa-reports",
      component: QaReportsPage,
      wrapperClass: "h-full w-full",
    },

    // AgentStudio - List Agents ******************************************************/
    {
      screen: SCREENS.AGENT_STUDIO,
      path: "/admin-ui/agent-studio",
      component: Home,
      wrapperClass: "h-full w-full",
    },

    // AgentStudio - List Clients ******************************************************/
    {
      screen: SCREENS.AGENT_STUDIO,
      path: "/admin-ui/agent-studio/:clientId",
      component: HomeScreen,
      wrapperClass: "h-full w-full",
    },

    // AgentStudio - Create Entity ******************************************************/
    {
      screen: SCREENS.AGENT_STUDIO,
      path: "/admin-ui/agent-studio/new/:clientId/:entityType",
      component: NewEntity,
      wrapperClass: "h-full w-full",
    },

    // AgentStudio - Edit Entity ******************************************************/
    {
      screen: SCREENS.AGENT_STUDIO,
      path: "/admin-ui/agent-studio/:clientId/:entityId/:entityType",
      component: ViewEntity,
      wrapperClass: "h-full w-full",
    },

    // Agent Deployment ******************************************************/
    {
      screen: SCREENS.AGENT_DEPLOYMENT,
      path: "/admin-ui/agent-deployment",
      component: AgentDeployment,
      wrapperClass: "h-full w-full",
    },

    // TSAR (Time-bound Support-user Access Regulation) ******************************************************/
    {
      screen: SCREENS.TSAR,
      path: "/admin-ui/tsar",
      component: TsarWrapper,
      wrapperClass: "h-full w-full",
    },
    {
      screen: SCREENS.CUSTOM_THEME,
      path: "/admin-ui/custom-theme",
      component: CustomTheme,
      wrapperClass: "h-full w-full",
    },
    {
      screen: SCREENS.FEATURE_FLAGS,
      path: "/admin-ui/feature-flags",
      component: FeatureFlags,
      wrapperClass: "h-full w-full",
    },
    {
      screen: SCREENS.ENGINE_TOOLS,
      path: "/admin-ui/engine-tools",
      component: EngineToolsPage,
      wrapperClass: "h-full w-full",
    },
  ];

  return (
    <>
      <div className="h-screen flex overflow-hidden bg-ever-base-100 ">
        {/* Static sidebar for desktop */}
        <div className="hidden md:flex md:flex-shrink-0">
          <div className="flex flex-col w-64">
            {/* Sidebar component, swap this element with another sidebar if you like */}
            <div className="flex flex-col h-0 flex-1 bg-gradient-to-bl bg-ever-base-content">
              <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
                <div className="flex items-center flex-shrink-0 px-4 gap-2">
                  <img
                    className="h-10 w-10 rounded-full"
                    src={LOGO}
                    alt="Everstage"
                  />
                  <div className="text-ever-base-100 text-xl">
                    Admin Console
                  </div>
                  <TerminalIcon className="h-5 w-5 text-ever-base-300" />
                </div>
                <div className="mt-8 pl-4 flex-1 bg-gradient-to-bl bg-ever-base-content">
                  {Object.values(navigation).map((item, index) => (
                    <MenuList key={index} item={item} index={index} />
                  ))}
                </div>
              </div>
              <div className="flex-shrink-0 flex bg-ever-base-content-mid p-4 ">
                <div
                  className="flex-shrink-0 w-full group block cursor-pointer"
                  onClick={() => authStore.userLogout()}
                >
                  <div className="flex items-center">
                    <div>
                      <img
                        className="inline-block h-9 w-9 rounded-full"
                        src={picture}
                        alt=""
                      />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-ever-base">
                        {userName}
                      </div>
                      <div className="text-xs font-medium text-ever-base-300 group-hover:text-ever-base-200">
                        Logout
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col w-0 flex-1 overflow-hidden">
          <main className="flex-1 relative z-0 overflow-y-auto focus:outline-none">
            <div className="h-full">
              <Switch>
                <Redirect
                  exact
                  from="/admin-ui"
                  to={{
                    pathname: NAVIGATION_OPTIONS[accessibleScreens[0]]?.path,
                    state: {},
                  }}
                />
                {routes.map((route) => {
                  return (
                    accessibleScreensSet.has(route.screen) && (
                      <Route exact path={route.path} key={route.path}>
                        <div className={route.wrapperClass}>
                          <route.component {...(route.props ?? {})} />
                        </div>
                      </Route>
                    )
                  );
                })}
              </Switch>
            </div>
          </main>
        </div>
      </div>
    </>
  );
});

export default AdminConsole;
