import { useParams } from "react-router-dom/cjs/react-router-dom.min";

import { InfoCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { EditIcon, XCloseIcon } from "@everstage/evericons/outlined";
import { Form, Input, Select, Tooltip } from "antd";
import { observer } from "mobx-react";
import { useEffect, useState, useCallback, useMemo } from "react";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { Card, EverLoader, Modal, Search, showMessage } from "~/Components";
import { EverButton } from "~/Components/ever-button/EverButton";
import { NOTIFICATION_TYPE } from "~/Enums";
import styles from "./styles.module.scss";

const ExpandToolDescriptionModal = (props) => {
  const { open, handleHideModal, selectedTool } = props;
  return (
    <Modal
      open={open}
      rootClassName={"w-[50%]"}
      className={"min-h-[20vh] max-h-[50vh]"}
    >
      <Modal.Content className="overflow-auto !p-3 shadow-sm">
        <div className="flex flex-col">
          <div className="flex justify-between text-ever-base-content text-sm font-medium leading-tight h-content p-2">
            <div className="text-lg">{selectedTool.name}</div>
            <div onClick={handleHideModal}>
              <XCloseIcon className="cursor-pointer w-4 h-4" />
            </div>
          </div>
          <div
            className={`text-ever-base-content-mid text-sm font-normal leading-tight px-3 h-content overflow-auto`}
          >
            <pre className="text-wrap overflow-hidden m-0">
              {selectedTool.description}
            </pre>
          </div>
        </div>
      </Modal.Content>
    </Modal>
  );
};

const ListTools = observer((props) => {
  const {
    open,
    handleHideModal,
    setSelectedOptions,
    selectedOptions,
    options,
    createMode,
    refetchTools,
  } = props;
  const agentStudioApi = useAgentStudioApi();
  const { clientId, entityId } = useParams();
  const [selectedOptionsModal, setSelectedOptionsModal] = useState([
    ...selectedOptions?.mannaiToolIds,
    ...selectedOptions?.customApiToolIds,
  ]);
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [uniqueCategories, setUniqueCategories] = useState([]);
  const [searchCategory, setSearchCategory] = useState("");
  const [showCreateTool, setShowCreateTool] = useState(false);
  const [selectedCustomToolId, setSelectedCustomToolId] = useState(null);
  const [editCustomTool, setEditCustomTool] = useState(false);
  const [createToolForm] = Form.useForm();
  const requests = [
    {
      label: "GET",
      value: "GET",
    },
    {
      label: "POST",
      value: "POST",
    },
    {
      label: "PUT",
      value: "PUT",
    },
    {
      label: "DELETE",
      value: "DELETE",
    },
  ];

  const toolTypes = [
    {
      label: "Write tool",
      value: "write",
    },
    {
      label: "Read tool",
      value: "read",
    },
  ];

  useEffect(() => {
    if (Boolean(Object.keys(options).length)) {
      const newToolIds = options?.map((option) => option.id);
      const updatedTools = newToolIds.filter(
        (element) =>
          selectedOptions.mannaiToolIds.includes(element) ||
          selectedOptions.customApiToolIds.includes(element)
      );
      setSelectedOptionsModal(updatedTools);
    }
  }, [options]);

  useEffect(() => {
    if (!open) {
      setSelectedOptionsModal([
        ...selectedOptions?.mannaiToolIds,
        ...selectedOptions?.customApiToolIds,
      ]);
    }
  }, [open, selectedOptions]);

  const handleCheckboxChange = (option) => {
    if (selectedOptionsModal.includes(option)) {
      setSelectedOptionsModal(
        selectedOptionsModal.filter((item) => item !== option)
      );
    } else {
      setSelectedOptionsModal([...selectedOptionsModal, option]);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const mannaiToolIds = [];
    const customApiToolIds = [];
    options?.forEach((item) => {
      if (selectedOptionsModal.includes(item.id)) {
        if (item.category === "Custom tools") {
          customApiToolIds.push(item.id);
        } else {
          mannaiToolIds.push(item.id);
        }
      }
    });
    setSelectedOptions({
      mannaiToolIds,
      customApiToolIds,
    });
    handleHideModal();
  };

  useEffect(() => {
    const categories = options?.map((option) => option.category);
    categories.push("Custom tools");
    const uniqueCategories = ["All", ...new Set(categories)];
    setUniqueCategories(uniqueCategories);
  }, [options]);

  const handleFinish = (values) => {
    try {
      const payload = {
        toolDetails: {
          toolId: selectedCustomToolId,
          toolName: values.name,
          requestUrl: values.endpoint,
          requestMethod: values.requestType,
          toolType: values.toolType,
          description: values.toolDescription,
          apiSchema: JSON.parse(values.requestPayload),
        },
      };

      agentStudioApi
        .upsertCustomTool(payload, clientId, entityId, editCustomTool)
        .then((res) => {
          if (res.status === 201) {
            refetchTools();
            setEditCustomTool(false);
            setShowCreateTool(false);
            showMessage(
              editCustomTool
                ? "Tool updated successfully"
                : "Tool created successfully",
              {
                type: NOTIFICATION_TYPE.SUCCESS,
              }
            );
          } else {
            setEditCustomTool(false);
            setShowCreateTool(false);
            showMessage(
              editCustomTool
                ? "Failed to update tool"
                : "Failed to create tool",
              {
                type: NOTIFICATION_TYPE.ERROR,
              }
            );
          }
        });
    } catch (e) {
      console.log(e);
      showMessage("Failed to create tool", {
        type: NOTIFICATION_TYPE.ERROR,
      });
      return;
    }
  };

  const CreateCustomTool = ({ editCustomTool }) => {
    return (
      <Form
        className="flex flex-col h-full w-full"
        layout="vertical"
        form={createToolForm}
        name="agentDeploymentForm"
        colon={false}
      >
        <div className="text-ever-base-content text-lg font-medium leading-tight pl-4">
          {editCustomTool ? "Edit" : "Create"} custom tools
        </div>
        <div className="flex gap-4 w-full px-4 pt-4">
          <Form.Item
            name="endpoint"
            rules={[
              {
                required: true,
                message: "Endpoint is required",
              },
            ]}
            className="w-3/4"
            label={
              <div className="flex items-center pt-1.5">
                <span className="text-ever-base-content text-sm font-normal leading-none tracking-normal">
                  Endpoint
                </span>
              </div>
            }
            required
          >
            <Input
              className="!w-full !h-[40px] !min-h-[40px] block rounded-md !border-ever-base-400 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5"
              value={createToolForm.getFieldValue("endpoint")}
              onChange={(e) =>
                createToolForm.setFieldsValue({
                  endpoint: e.target.value,
                })
              }
            />
          </Form.Item>
          <Form.Item
            name="requestType"
            rules={[
              {
                required: true,
                message: "Request type is required",
              },
            ]}
            label={
              <div className="flex items-center pt-1.5">
                <span className="text-ever-base-content text-sm font-normal leading-none tracking-normal">
                  Request type
                </span>
              </div>
            }
            className="w-1/4"
            required
          >
            <Select
              id="requestType"
              placeholder="Select request"
              options={requests.map((request) => ({
                value: request.value,
                label: request.label,
              }))}
              onChange={(value) => {
                createToolForm.setFieldsValue({
                  requestType: value,
                });
              }}
              listHeight={200}
              showSearch={false}
              className="!w-full !h-[40px] !min-h-[40px] block rounded-md !border-ever-base-300 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5 hover:bg-ever-base-100"
              filterOption={false}
            />
          </Form.Item>
        </div>
        <div className="flex gap-4 w-full px-4">
          <Form.Item
            name="name"
            rules={[
              {
                required: true,
                message: "Name is required",
              },
            ]}
            className="w-3/4"
            label={
              <div className="flex items-center pt-1.5">
                <span className="text-ever-base-content text-sm font-normal leading-none tracking-normal">
                  Name
                </span>
              </div>
            }
            required
          >
            <Input
              className="!w-full !h-[40px] !min-h-[40px] block rounded-md !border-ever-base-400 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5"
              value={createToolForm.getFieldValue("name")}
              onChange={(e) =>
                createToolForm.setFieldsValue({
                  name: e.target.value,
                })
              }
            />
          </Form.Item>
          <Form.Item
            name="toolType"
            rules={[
              {
                required: true,
                message: "Tool type is required",
              },
            ]}
            label={
              <div className="flex items-center pt-1.5">
                <span className="text-ever-base-content text-sm font-normal leading-none tracking-normal">
                  Tool type
                </span>
              </div>
            }
            className="w-1/4"
            required
          >
            <Select
              id="toolType"
              placeholder="Select tool type"
              options={toolTypes.map((request) => ({
                value: request.value,
                label: request.label,
              }))}
              onChange={(value) => {
                createToolForm.setFieldsValue({
                  toolType: value,
                });
              }}
              listHeight={200}
              showSearch={false}
              className="!w-full !h-[40px] !min-h-[40px] block rounded-md !border-ever-base-300 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5 hover:bg-ever-base-100"
              filterOption={false}
            />
          </Form.Item>
        </div>
        <div className="flex flex-col px-4 h-full gap-1.5">
          <Form.Item
            name="toolDescription"
            rules={[
              {
                required: true,
                message: "Description is required",
              },
            ]}
            label={
              <div className="flex items-center pt-1.5">
                <span className="text-ever-base-content text-sm font-normal leading-none tracking-normal">
                  Description
                </span>
              </div>
            }
            className="w-full"
            required
          >
            <textarea
              className="!w-full h-80 block rounded-md !border-ever-base-400 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5 p-2 resize-none"
              value={createToolForm.getFieldValue("toolDescription")}
              onChange={(e) =>
                createToolForm.setFieldsValue({
                  toolDescription: e.target.value,
                })
              }
            />
          </Form.Item>
          <Form.Item
            name="requestPayload"
            rules={[
              {
                required: true,
                message: "Request payload is required",
              },
            ]}
            className="w-full"
            label={
              <div className="flex items-center pt-1.5">
                <span className="text-ever-base-content text-sm font-normal leading-none tracking-normal">
                  Request payload
                </span>
              </div>
            }
            required
          >
            <textarea
              className="!w-full h-80 rounded-md !border-ever-base-400 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5 p-2 resize-none"
              value={createToolForm.getFieldValue("requestPayload")}
              onChange={(e) =>
                createToolForm.setFieldsValue({
                  requestPayload: e.target.value,
                })
              }
            />
          </Form.Item>
          <div className="flex w-full items-center justify-center">
            <EverButton
              className="w-32 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary text-white"
              onClick={(e) => {
                e.preventDefault();
                handleFinish(createToolForm.getFieldsValue());
              }}
            >
              {editCustomTool ? "Update" : "Create"} tool
            </EverButton>
          </div>
        </div>
      </Form>
    );
  };

  return (
    <Modal open={open} rootClassName={"w-[80%]"} className={"h-[95vh]"}>
      <form onSubmit={handleSubmit} className="flex flex-col h-[100%]">
        <Modal.Title onClose={handleHideModal}>Add tools</Modal.Title>
        <Modal.Content className="overflow-auto !p-3 shadow-sm">
          {options?.length ? (
            <div className="flex flex-row h-full">
              <div className="flex flex-col border-r border-0 border-solid border-ever-base-400 w-[20%] gap-4 px-6 py-2 overflow-auto">
                <div className="text-ever-base-content text-sm font-medium leading-tight flex gap-2">
                  Categories
                </div>
                <div>
                  {[...uniqueCategories].map((value, index) => (
                    <div key={index} className="flex items-center gap-2 pt-2">
                      <Card
                        className={`flex shadow-sm !h-12 rounded-lg w-full border border-solid border-ever-base-200 hover:!border-ever-primary
                        focus:!bg-ever-base-50 focus:!shadow-sm focus:!ring-2 focus:!ring-ever-primary hover:cursor-pointer
                        ${
                          selectedCategory === value &&
                          "!border-ever-primary !shadow-lg !bg-ever-base-100"
                        }`}
                        onClick={() => {
                          setSelectedCategory(value);
                          setShowCreateTool(false);
                          setEditCustomTool(false);
                        }}
                      >
                        <div className="text-ever-content-mid text-sm font-normal leading-tight overflow-auto w-full cursor-pointer h-full p-2 items-center flex">
                          {value || "Uncategorized"}
                        </div>
                      </Card>
                    </div>
                  ))}
                </div>
              </div>
              <div className="overflow-auto p-2 w-[80%] h-fit-content h-full">
                {!showCreateTool && !editCustomTool ? (
                  <div className="flex gap-4 w-full p-4">
                    <Search
                      placeholder="Search tools"
                      className="w-full"
                      onChange={(e) => setSearchCategory(e.target.value)}
                      inputClassName="!bg-ever-primary-content !border-ever-base-300 focus:!border-ever-primary focus:!shadow-lg placeholder-ever-base-500"
                      searchIconClassName="text-ever-base-300"
                    />
                    {selectedCategory === "Custom tools" &&
                    createMode === false ? (
                      <EverButton onClick={() => setShowCreateTool(true)}>
                        Create
                      </EverButton>
                    ) : (
                      <></>
                    )}
                  </div>
                ) : (
                  <></>
                )}
                {showCreateTool || editCustomTool ? (
                  <CreateCustomTool editCustomTool={editCustomTool} />
                ) : (
                  <div className="grid grid-cols-1 gap-8 lg:grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 p-6">
                    {/* Loop through options to generate checkboxes */}
                    {options
                      ?.filter((option) =>
                        selectedCategory === "All"
                          ? option
                          : option.category === selectedCategory
                      )
                      .filter(
                        (option) =>
                          option.skillId !== entityId ||
                          entityId === undefined ||
                          entityId === null
                      )
                      .filter(
                        (option) =>
                          option?.id
                            .toLowerCase()
                            .includes(searchCategory.toLowerCase()) ||
                          option?.description
                            .toLowerCase()
                            .includes(searchCategory.toLowerCase()) ||
                          (option?.name &&
                            option?.name
                              .toLowerCase()
                              .includes(searchCategory.toLowerCase()))
                      )
                      .map((option, index) => (
                        <label key={index} className="flex items-center gap-2">
                          <Card
                            className={`flex shadow-sm p-2 !p-3 !h-28 rounded-lg w-full border border-ever-base-200 hover:!border-ever-primary cursor-pointer
                          focus:!bg-ever-base-50 focus:!shadow-lg focus:!ring-2 focus:!ring-ever-primary
                          ${
                            selectedOptionsModal.includes(option.id) &&
                            "!bg-blue-50 !border-ever-primary !shadow-lg"
                          }`}
                          >
                            <div className="text-ever-content text-sm font-medium flex flex-col gap-2 rounded-lg">
                              <div className="text-ever-base-content text-sm font-medium font-['IBM Plex Sans'] leading-tight flex gap-2 items-center">
                                <input
                                  type="checkbox"
                                  onChange={() =>
                                    handleCheckboxChange(option.id)
                                  }
                                  checked={selectedOptionsModal.includes(
                                    option.id
                                  )}
                                  className="outline-none focus:ring-2 focus:ring-ever-primary focus:ring-opacity-0 rounded border"
                                />
                                {option.name}
                                {selectedCategory === "Custom tools" && (
                                  <div className="flex ml-auto justify-center">
                                    <EditIcon
                                      className="w-4 h-4 cursor-pointer"
                                      onClick={() => {
                                        // initialize form value in edit mode
                                        createToolForm.setFieldsValue({
                                          endpoint:
                                            option.argumentSpec.requestUrl,
                                          requestType:
                                            option.argumentSpec.requestMethod,
                                          name: option.name,
                                          toolType:
                                            option.argumentSpec.toolType,
                                          toolDescription: option.description,
                                          requestPayload: JSON.stringify(
                                            option.argumentSpec.apiSchema
                                          ),
                                        });
                                        setSelectedCustomToolId(option.id);
                                        setEditCustomTool(true);
                                      }}
                                    />
                                  </div>
                                )}
                              </div>
                              <div
                                className={` ${styles.select_tool_description} self-stretch text-ever-base-content-mid text-sm font-normal font-['Inter'] leading-tight overflow-auto`}
                              >
                                {option.description}
                              </div>
                            </div>
                          </Card>
                        </label>
                      ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex h-full w-full items-center justify-center">
              <EverLoader indicatorType="spinner" />
            </div>
          )}
        </Modal.Content>
        <div className="px-6 py-3 flex border-t border-0 border-solid border-ever-base-200 w-full justify-end">
          <EverButton htmlType="submit">Add</EverButton>
        </div>
      </form>
    </Modal>
  );
});

export const Tools = observer(
  ({ editMode, selectedTools, setSelectedTools, createMode, isHelper }) => {
    const agentStudioApi = useAgentStudioApi();
    const [options, setOptions] = useState(null);
    const { clientId, skillId = null } = useParams();
    const [openToolSelect, setOpenToolSelect] = useState(false);
    const [expandTool, setExpandTool] = useState(false);
    const [selectedTool, setSelectedTool] = useState({});
    const toolIdToNameMap = useMemo(() => {
      return options?.reduce((acc, tool) => {
        acc[tool.id] = tool.name;
        return acc;
      }, {});
    }, [options]);

    const fetchTools = useCallback(() => {
      agentStudioApi.getTools(clientId, skillId).then((data) => {
        const allTools = data.deterministicTools;
        for (const item of data.customTools) {
          const tool = {
            id: item.toolId,
            name: item.toolName,
            category: "Custom tools",
            description: item.description,
            argumentSpec: item,
          };
          allTools.push(tool);
        }
        if (isHelper) {
          const filteredTools = allTools.filter(
            (tool) => !tool.id.startsWith("__skill__")
          );
          setOptions(filteredTools);
        } else {
          setOptions(allTools);
        }
      });
    }, [clientId, skillId]);

    useEffect(() => {
      if (!options) {
        fetchTools();
      }
    }, [options, fetchTools]);

    const refetchTools = () => {
      fetchTools();
    };

    return (
      <div className={`flex flex-col w-1/4 h-full`}>
        {expandTool && (
          <ExpandToolDescriptionModal
            open={expandTool}
            handleHideModal={() => setExpandTool(false)}
            selectedTool={selectedTool}
          />
        )}
        {openToolSelect && (
          <ListTools
            open={openToolSelect}
            handleHideModal={() => setOpenToolSelect(false)}
            setSelectedOptions={setSelectedTools}
            selectedOptions={selectedTools}
            options={options}
            createMode={createMode}
            refetchTools={refetchTools}
          />
        )}

        <div className="flex flex-row justify-between w-full items-center">
          <label className="justify-start items-start gap-1 flex py-2.5">
            <div className="text-ever-base-content text-sm font-semibold leading-tight">
              Tools
            </div>
            <Tooltip
              title={
                "Tools are determinitic actions that the skill can perform."
              }
            >
              <InfoCircleOutlined className="ml-1 text-ever-base-content" />
            </Tooltip>
          </label>
          {editMode ? (
            <EverButton
              onClick={() => {
                refetchTools();
                setOpenToolSelect(true);
              }}
              prependIcon={<PlusCircleOutlined />}
              type="link"
            >
              Add tools
            </EverButton>
          ) : (
            <></>
          )}
        </div>

        <div className="flex-1 overflow-y-auto h-full relative rounded-lg border border-solid border-ever-base-300 shadow-none flex flex-col p-4 w-full gap-2">
          {Object.keys(options || {}).length > 0 &&
            [
              ...selectedTools?.mannaiToolIds,
              ...selectedTools?.customApiToolIds,
            ].map((option) => (
              <div
                className={`rounded-lg border border-solid border-ever-base-300 w-full flex flex-col gap-2 p-2 hover:!border-ever-primary focus:!bg-ever-base-50 focus:!shadow-md focus:!ring-2 focus:!ring-ever-primary hover:cursor-pointer`}
                key={option}
              >
                <div className="flex flex-col gap-2 overflow-auto">
                  <div
                    className="text-ever-base-content text-sm font-medium leading-tight cursor-pointer"
                    onClick={() => {
                      setSelectedTool(
                        options?.find((tool) => tool.id === option)
                      );
                      setExpandTool(true);
                    }}
                  >
                    {toolIdToNameMap[option]}
                  </div>
                  <div
                    className={`${styles.description} text-ever-base-content-mid text-sm font-normal leading-tight`}
                  >
                    {options?.find((tool) => tool.id === option)?.description}
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  }
);
