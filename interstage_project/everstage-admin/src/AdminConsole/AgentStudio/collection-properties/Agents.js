import { InfoCircleOutlined } from "@ant-design/icons";
import { Button, Checkbox, Dropdown, Space, Tooltip } from "antd";
import { observer } from "mobx-react";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { useState } from "react";
import { useCallback } from "react";
import { ENTITY_TYPE } from "../enums";

export const ListAgents = ({
  setSelectedOptions,
  selectedOptions,
  options = [],
  editMode,
}) => {
  // Handle checkbox changes
  const handleCheckboxChange = (agentId, checked) => {
    if (checked) {
      const filteredAgent = options.filter(
        (opt) => opt.id === agentId && opt.versions.length > 0
      );

      setSelectedOptions((prev) => ({
        agentIds: [
          ...prev.agentIds,
          {
            id: agentId,
            name: filteredAgent[0]?.name,
            systemName: filteredAgent[0]?.systemName,
            version: filteredAgent[0]?.versions[0]?.versionId,
            versionName: filteredAgent[0]?.versions[0]?.version,
            description: filteredAgent[0]?.versions[0]?.description,
          },
        ],
      }));
    }

    // Optionally reset version if unchecked
    if (!checked) {
      setSelectedOptions((prev) => ({
        agentIds: prev.agentIds.filter((item) => item.id !== agentId),
      }));
    }
  };

  // Handle version dropdown changes
  const handleVersionChange = (agentId, versionId, versionName) => {
    setSelectedOptions((prev) => ({
      agentIds: prev.agentIds.map((item) =>
        item.id === agentId
          ? {
              id: agentId,
              name: options.find((opt) => opt.id === agentId)?.name,
              version: versionId,
              versionName: versionName,
              description: options
                .find((opt) => opt.id === agentId)
                ?.versions.find((v) => v.versionId === versionId)?.description,
            }
          : item
      ),
    }));
  };

  // Filter out empty or invalid agent objects
  const validOptions = options.filter(
    (opt) => opt?.id && opt?.name && Array.isArray(opt?.versions)
  );

  return (
    <form className="flex flex-col h-full gap-2.5 overflow-y-auto">
      {validOptions.map((opt) => (
        <div
          key={opt.id}
          className="border-ever-base-400 flex border border-solid w-full min-h-[48px] items-center gap-3 px-3 py-2 rounded-lg justify-between hover:!border-ever-primary focus:!bg-ever-base-50 focus:!shadow-md focus:!ring-2 focus:!ring-ever-primary hover:cursor-pointer"
        >
          <Checkbox
            checked={
              !!selectedOptions?.agentIds?.find((item) => item.id === opt.id)
            }
            onChange={(e) => handleCheckboxChange(opt.id, e.target.checked)}
            disabled={!editMode}
          >
            <div
              className={`${
                selectedOptions?.agentIds?.find((item) => item.id === opt.id)
                  ? editMode
                    ? "text-ever-base-content"
                    : "text-ever-base-content"
                  : "text-ever-base-content-mid"
              } text-sm font-medium leading-tight`}
            >
              {opt.name}
            </div>
          </Checkbox>
          <Space direction="vertical">
            <Space wrap>
              <Dropdown
                menu={{
                  items: opt.versions.map((v) => ({
                    key: v.versionId,
                    value: v.version,
                    label: v.version,
                    onClick: () => {
                      handleVersionChange(opt.id, v.versionId, v.version);
                    },
                  })),
                }}
                placement="bottomLeft"
                disabled={
                  !selectedOptions?.agentIds?.find(
                    (item) => item.id === opt.id
                  ) || !editMode
                }
                trigger={["click"]}
                overlayClassName="bg-ever-primary-content"
              >
                <Button>
                  <div
                    className={`${
                      selectedOptions?.agentIds?.find(
                        (item) => item.id === opt.id
                      )
                        ? editMode
                          ? "text-ever-base-content-mid"
                          : "text-ever-base-content"
                        : "text-ever-base-content-mid"
                    }
                    text-sm font-medium leading-tight`}
                  >
                    {selectedOptions?.agentIds?.find(
                      (item) => item.id === opt.id
                    )?.versionName || "Select version"}
                  </div>
                </Button>
              </Dropdown>
            </Space>
          </Space>
        </div>
      ))}
    </form>
  );
};

export const Agents = observer(
  ({
    editMode,
    selectedAgentsForCollection,
    setSelectedAgentsForCollection,
  }) => {
    const agentStudioApi = useAgentStudioApi();
    const [options, setOptions] = useState(null);
    const { clientId } = useParams();

    const fetchAgents = useCallback(() => {
      agentStudioApi
        .getEntityLists(ENTITY_TYPE.AGENT, clientId)
        .then((data) => {
          setOptions(data?.entity);
        })
        .catch((error) => {
          console.error("Error fetching agents:", error);
        });
    }, [clientId]);

    useEffect(() => {
      if (!options) {
        fetchAgents();
      }
    }, [options, fetchAgents]);

    return (
      <div className={`flex flex-col w-3/6 h-full`}>
        <div className="flex flex-row justify-between w-full items-center">
          <label className="justify-start items-start gap-1 flex py-2.5">
            <div className="text-ever-base-content text-sm font-semibold leading-tight">
              Agents Configurations
            </div>
            <Tooltip
              title={
                "Agents are the workers that use the skills to perform actions. They can be added to a collection to be used in a conversation."
              }
            >
              <InfoCircleOutlined className="ml-1 text-ever-base-content" />
            </Tooltip>
          </label>
        </div>

        <div className="flex-1 overflow-y-auto h-full relative rounded-lg border border-solid border-ever-base-300 shadow-none flex flex-col p-4 w-full gap-2">
          <ListAgents
            setSelectedOptions={setSelectedAgentsForCollection}
            selectedOptions={selectedAgentsForCollection}
            options={options || []}
            editMode={editMode}
          />
        </div>
      </div>
    );
  }
);
