import { InfoCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { Button, Checkbox, Dropdown, Radio, Space, Tooltip } from "antd";
import { observer } from "mobx-react";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { useState } from "react";
import { useCallback } from "react";
import { ENTITY_TYPE } from "../enums";
import { Card, EverLoader, Modal, Search } from "~/Components";
import { EverButton } from "~/Components/ever-button/EverButton";

export const ListSkills = ({
  setSelectedOptions,
  selectedOptions,
  options = [],
  editMode,
  modalOpen,
  setModalOpen,
}) => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchSkills, setSearchSkills] = useState("");
  const [selectedOptionsModal, setSelectedOptionsModal] = useState({
    skillIds: [...selectedOptions.skillIds],
    technicalSkillIds: [...selectedOptions.technicalSkillIds],
  });

  // Handle checkbox changes
  const handleCheckboxChange = (skillId, checked, technical) => {
    if (checked) {
      const filteredSkill = options?.filter(
        (opt) =>
          opt.id === skillId &&
          opt.versions.length > 0 &&
          opt.technical === technical
      );

      if (technical) {
        setSelectedOptionsModal((prev) => ({
          ...prev,
          technicalSkillIds: [
            ...prev.technicalSkillIds,
            {
              id: skillId,
              name: filteredSkill[0]?.name,
              version: filteredSkill[0]?.versions[0]?.versionId,
              versionName: filteredSkill[0]?.versions[0]?.version,
              description: filteredSkill[0]?.versions[0]?.description,
            },
          ],
        }));
      } else {
        setSelectedOptionsModal((prev) => ({
          ...prev,
          skillIds: [
            ...prev.skillIds,
            {
              id: skillId,
              name: filteredSkill[0]?.name,
              version: filteredSkill[0]?.versions[0]?.versionId,
              versionName: filteredSkill[0]?.versions[0]?.version,
              description: filteredSkill[0]?.versions[0]?.description,
            },
          ],
        }));
      }
    }

    // Optionally reset version if unchecked
    if (!checked) {
      setSelectedOptionsModal((prev) => ({
        skillIds: prev.skillIds?.filter((item) => item.id !== skillId),
        technicalSkillIds: prev.technicalSkillIds?.filter(
          (item) => item.id !== skillId
        ),
      }));
    }
  };

  // Handle version dropdown changes
  const handleVersionChange = (skillId, versionId, versionName, technical) => {
    if (technical) {
      setSelectedOptionsModal((prev) => ({
        ...prev,
        technicalSkillIds: prev.technicalSkillIds?.map((item) => {
          if (item.id === skillId) {
            return {
              ...item,
              version: versionId,
              versionName: versionName,
              description: options
                .find((opt) => opt.id === skillId)
                ?.versions.find((v) => v.versionId === versionId)?.description,
            };
          }
          return item;
        }),
      }));
    } else {
      setSelectedOptionsModal((prev) => ({
        ...prev,
        skillIds: prev.skillIds?.map((item) => {
          if (item.id === skillId) {
            return {
              ...item,
              version: versionId,
              versionName: versionName,
              description: options
                .find((opt) => opt.id === skillId)
                ?.versions.find((v) => v.versionId === versionId)?.description,
            };
          }
          return item;
        }),
      }));
    }
  };

  // Filter out empty or invalid skill objects
  const validOptions = options?.filter(
    (opt) => opt?.id && opt?.name && Array.isArray(opt?.versions)
  );

  const handleSubmit = () => {
    // Update selectedOptions with selectedOptionsModal
    // If skillId is not found in options?.skillIds, then remove from selectedOptionsModal. Same for technicalSkillIds as well.
    // This is as a part of delete cascade
    setSelectedOptions({
      skillIds: selectedOptionsModal.skillIds?.filter((skill) =>
        options?.some((opt) => opt.id === skill.id)
      ),
      technicalSkillIds: selectedOptionsModal.technicalSkillIds?.filter(
        (skill) => options?.some((opt) => opt.id === skill.id)
      ),
    });
    setModalOpen(false);
  };

  return (
    <Modal open={modalOpen} rootClassName={"w-[60%]"} className={"h-[95vh]"}>
      <form className="flex flex-col h-full gap-2.5 " onSubmit={handleSubmit}>
        <Modal.Title
          onClose={() => {
            setModalOpen(false);
          }}
        >
          Add skills
        </Modal.Title>
        <Modal.Content className="!p-3 shadow-sm h-[50vh]">
          {validOptions?.length ? (
            <div className="flex flex-col w-full h-full">
              <Radio.Group
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                }}
                className="w-full flex items-center justify-center"
              >
                <Radio.Button value="all">All Skills</Radio.Button>
                <Radio.Button value="client">Client-level Skills</Radio.Button>
                <Radio.Button value="technical">Technical Skills</Radio.Button>
              </Radio.Group>
              <div className="overflow-auto p-2 h-fit-content h-full">
                <div className="flex gap-4 w-full p-4">
                  <Search
                    placeholder="Search skills"
                    className="w-full"
                    onChange={(e) => setSearchSkills(e.target.value)}
                    inputClassName="!bg-ever-primary-content !border-ever-base-300 focus:!border-ever-primary focus:!shadow-lg placeholder-ever-base-500"
                    // searchIconClassName="text-ever-base-300"
                  />
                </div>
                <div className="grid grid-cols-1 gap-8 lg:grid-cols-1 xl:grid-cols-2 2xl:grid-cols-2 p-6">
                  {/* Loop through options to generate checkboxes */}
                  {validOptions
                    ?.filter((option) =>
                      selectedCategory === "all"
                        ? option
                        : selectedCategory === "technical"
                        ? option.technical
                        : !option.technical
                    )
                    .filter((option) =>
                      option?.name
                        .toLowerCase()
                        .includes(searchSkills.toLowerCase())
                    )
                    .map((option, index) => (
                      <label key={index} className="flex items-center gap-2">
                        <Card
                          className={`flex w-full min-h-[80px] items-center gap-3 px-3 py-3 rounded-lg justify-between border border-ever-base-200 hover:!border-ever-primary cursor-pointer
                          focus:!bg-ever-base-50 focus:!shadow-lg focus:!ring-2 focus:!ring-ever-primary
                          ${
                            (selectedOptionsModal.skillIds?.find(
                              (item) => item.id === option.id
                            ) ||
                              selectedOptionsModal.technicalSkillIds?.find(
                                (item) => item.id === option.id
                              )) &&
                            "!bg-blue-50 !border-ever-primary !shadow-lg"
                          }`}
                        >
                          <div className="text-ever-content text-sm font-medium flex gap-2 rounded-lg">
                            <Checkbox
                              checked={
                                !!selectedOptionsModal.skillIds?.find(
                                  (item) => item.id === option.id
                                ) ||
                                !!selectedOptionsModal.technicalSkillIds?.find(
                                  (item) => item.id === option.id
                                )
                              }
                              onChange={(e) =>
                                handleCheckboxChange(
                                  option.id,
                                  e.target.checked,
                                  option.technical
                                )
                              }
                              disabled={!editMode}
                            >
                              <div
                                className={`${
                                  (selectedOptionsModal.skillIds?.find(
                                    (item) => item.id === option.id
                                  ) ||
                                    selectedOptionsModal.technicalSkillIds?.find(
                                      (item) => item.id === option.id
                                    )) &&
                                  editMode
                                    ? "text-ever-base-content"
                                    : "text-ever-base-content-mid"
                                } text-sm font-medium leading-tight`}
                              >
                                {option.name}
                              </div>
                            </Checkbox>
                          </div>
                          <Space direction="vertical">
                            <Space wrap>
                              <Dropdown
                                menu={{
                                  items: option.versions.map((v) => ({
                                    key: v.versionId,
                                    value: v.version,
                                    label: v.version,
                                    onClick: () => {
                                      handleVersionChange(
                                        option.id,
                                        v.versionId,
                                        v.version,
                                        option.technical
                                      );
                                    },
                                  })),
                                }}
                                placement="bottomLeft"
                                disabled={
                                  !(
                                    selectedOptionsModal.skillIds?.find(
                                      (item) => item.id === option.id
                                    ) ||
                                    selectedOptionsModal.technicalSkillIds?.find(
                                      (item) => item.id === option.id
                                    )
                                  ) || !editMode
                                }
                                trigger={["click"]}
                                overlayClassName="bg-ever-primary-content"
                                className={`${
                                  (selectedOptionsModal.skillIds?.find(
                                    (item) => item.id === option.id
                                  ) ||
                                    selectedOptionsModal.technicalSkillIds?.find(
                                      (item) => item.id === option.id
                                    )) &&
                                  "!bg-blue-100 !border-ever-primary !shadow-lg"
                                }`}
                              >
                                <Button>
                                  <div
                                    className={`${
                                      (selectedOptionsModal.skillIds?.find(
                                        (item) => item.id === option.id
                                      ) ||
                                        selectedOptionsModal.technicalSkillIds?.find(
                                          (item) => item.id === option.id
                                        )) &&
                                      editMode
                                        ? "text-ever-base-content-mid"
                                        : "text-ever-base-content"
                                    }
                    text-sm font-medium leading-tight`}
                                  >
                                    {selectedOptionsModal.skillIds?.find(
                                      (item) => item.id === option.id
                                    )?.versionName ||
                                      selectedOptionsModal.technicalSkillIds?.find(
                                        (item) => item.id === option.id
                                      )?.versionName ||
                                      "Select version"}
                                  </div>
                                </Button>
                              </Dropdown>
                            </Space>
                          </Space>
                        </Card>
                      </label>
                    ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex h-full w-full items-center justify-center">
              <EverLoader indicatorType="spinner" />
            </div>
          )}
        </Modal.Content>
        <div className="px-6 py-3 flex border-t border-0 border-solid border-ever-base-200 w-full justify-end">
          <EverButton htmlType="submit">Add</EverButton>
        </div>
      </form>
    </Modal>
  );
};

export const Skills = observer(
  ({ editMode, selectedSkillsForAgent, setSelectedSkillsForAgent }) => {
    const agentStudioApi = useAgentStudioApi();
    const [options, setOptions] = useState(null);
    const [modalOpen, setModalOpen] = useState(false);
    const { clientId } = useParams();

    const fetchAgents = useCallback(() => {
      agentStudioApi
        .getEntityLists(ENTITY_TYPE.SKILL, clientId)
        .then((data) => {
          setOptions(data?.entity);
        })
        .catch((error) => {
          console.error("Error fetching agents:", error);
        });
    }, [clientId]);

    useEffect(() => {
      if (!options) {
        fetchAgents();
      }
    }, [fetchAgents, options]);

    return (
      <div className={`flex flex-col w-2/6 h-full`}>
        <div className="flex flex-row justify-between w-full items-center">
          <label className="justify-start items-start gap-1 flex py-2.5">
            <div className="text-ever-base-content text-sm font-semibold leading-tight">
              Skills Configurations
            </div>
            <Tooltip
              title={
                "Skills are workers that make use of tools to perform actions."
              }
            >
              <InfoCircleOutlined className="ml-1 text-ever-base-content" />
            </Tooltip>
          </label>
          {editMode ? (
            <EverButton
              onClick={() => {
                setModalOpen(true);
              }}
              prependIcon={<PlusCircleOutlined />}
              type="link"
            >
              Add skills
            </EverButton>
          ) : (
            <></>
          )}
        </div>

        <div className="flex-1 overflow-y-auto h-full relative rounded-lg border border-solid border-ever-base-300 shadow-none flex flex-col p-4 w-full gap-2">
          {modalOpen && (
            <ListSkills
              setSelectedOptions={setSelectedSkillsForAgent}
              selectedOptions={selectedSkillsForAgent}
              options={options || []}
              editMode={editMode}
              modalOpen={modalOpen}
              setModalOpen={setModalOpen}
            />
          )}
          {selectedSkillsForAgent?.skillIds?.length > 0 ||
          selectedSkillsForAgent?.technicalSkillIds?.length > 0 ? (
            <div className="flex flex-col gap-2">
              {selectedSkillsForAgent.skillIds?.map((skill, index) => (
                <div key={index} className="flex items-center gap-1">
                  <Card className="flex !p-3 flex-col shadow-sm !h-20 rounded-lg w-full border border-solid border-ever-base-300 hover:!border-ever-primary focus:!bg-ever-base-50 focus:!shadow-md focus:!ring-2 focus:!ring-ever-primary hover:cursor-pointer text-ever-content text-sm font-normal leading-tight justify-between">
                    <div className="flex w-content text-md font-medium leading-tight">
                      {skill.name}
                    </div>
                    <div className="flex w-content text-ellipsis overflow-hidden text-sm text-ever-base-content-mid">
                      {skill.versionName}
                    </div>
                  </Card>
                </div>
              ))}
              {selectedSkillsForAgent.technicalSkillIds?.map((skill, index) => (
                <div key={index} className="flex items-center gap-1">
                  <Card className="flex !p-3 flex-col shadow-sm !h-20 bg-ever-base-50 rounded-lg w-full border border-solid border-ever-base-400 hover:!border-ever-primary focus:!bg-ever-base-50 focus:!shadow-md focus:!ring-2 focus:!ring-ever-primary hover:cursor-pointer text-ever-content text-sm font-normal leading-tight justify-between">
                    <div className="flex w-content text-md font-medium leading-tight">
                      {skill.name}
                    </div>
                    <div className="flex w-content text-ellipsis overflow-hidden text-sm justify-between">
                      <div className="text-ever-base-content-mid">
                        {skill.versionName}
                      </div>
                      <div className="text-ever-base-content-mid">
                        Technical
                      </div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>
    );
  }
);
