import { PencilIcon } from "@heroicons/react/outline";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { HelperRemovalConfirmationModal } from "../ListItems";
import { BackArrowIcon } from "~/icons";

import { EverLoader, showMessage } from "~/Components";
import { UpdateEntity } from "../../studio-components/common-components/UpdateEntity";
import { RunTest } from "../../studio-components/common-components/RunTest";
import {
  description,
  footer,
  promptsAndComponent,
  topbarSpec,
  versionControl,
} from "../../studio-components/common-components/GeneralComponents";
import {
  basicDetails,
  getEntityDescription,
  getEntityName,
  getEntityPayload,
  validateEntity,
} from "./EntityCommon";
import { ENTITY_TYPE } from "../../enums";
import customerStore from "../../store";
import { NOTIFICATION_TYPE } from "~/Enums";

export const EntityHome = observer(({ clientId, entityId, entityType }) => {
  const history = useHistory();

  const { listOfLlms } = customerStore;
  const [editMode, setEditMode] = useState(false); // Edit Mode for Agent Properties
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false); // Confirmation Modal for any confirmations
  const [saveMode, setSaveMode] = useState(false); // Save Mode for the confirmation modal
  const [createNewRevision, setCreateNewRevision] = useState(false); // Create New Revision Mode for the confirmation modal
  const [versions, setVersions] = useState([]);
  const [selectedVersion, setSelectedVersion] = useState({});
  const [promptsText, setPrompts] = useState({
    corePrompt: "",
    corePromptPrefix: "",
  });
  const [structure, setStructure] = useState([
    "str_response",
    "thought",
    "json_response",
    "input_required",
    "approval_required",
    "follow_up_questions",
  ]);

  const [showTools, setShowTools] = useState(true);
  const [showStructure, setShowStructure] = useState(true);
  const [includeClientContext, setIncludeClientContext] = useState(false);
  const [fetchDetails, setFetchDetails] = useState(false);
  const [loader, setLoader] = useState(false);
  const [menu, setMenu] = useState([]);
  const [runTestModalOpen, setRunTestModalOpen] = useState(false); // Run Test for testing the entity
  const [testResults, setTestResults] = useState(undefined);
  const [runtests, setRunTest] = useState(false);
  const [publish, setPublish] = useState(false);
  const [tag, setTag] = useState("");
  const [moveToDraft, setMoveToDraft] = useState(false);
  const [genUIEnabled, setGenUIEnabled] = useState(false);
  const [genUIPrompt, setGenUIPrompt] = useState("");
  const [helperRemovalModalOpen, setHelperRemovalModalOpen] = useState(false);
  const [dependentSkills, setDependentSkills] = useState([]);
  const [payload, setPayload] = useState({});
  const [allEntityFields, setAllEntityFields] = useState({});
  const refetch = () => {
    setFetchDetails(!fetchDetails);
  };
  const newEntityProperties =
    entityType === ENTITY_TYPE.SKILL
      ? {
          skillName: "",
          description: "",
          modelName: "",
        }
      : entityType === ENTITY_TYPE.AGENT
      ? {
          agentName: "",
          agentSystemName: "",
          description: "",
          modelName: "",
          avatar: null,
          avatarPath: "",
          S3_CDN: null,
        }
      : entityType === ENTITY_TYPE.COLLECTION
      ? {
          collectionName: "",
          description: "",
          modelName: "",
        }
      : {};
  const [entityProperties, setEntityProperties] = useState(newEntityProperties);
  const [selectedSkillsForAgent, setSelectedSkillsForAgent] = useState({
    skillIds: [],
    technicalSkillIds: [],
  });
  const [selectedTools, setSelectedTools] = useState({
    mannaiToolIds: [],
    customApiToolIds: [],
  });
  const [selectedAgentsForCollection, setSelectedAgentsForCollection] =
    useState({
      agentIds: [],
    });

  const agentStudioApi = useAgentStudioApi();

  useEffect(() => {
    if (!entityId) {
      customerStore.setSelectedEntity(entityType);
      history.push(`/admin-ui/agent-studio/${clientId}`);
      return;
    }
    if (listOfLlms.length === 0) {
      agentStudioApi.getLLMs().then((res) => {
        customerStore.setListOfLlms(res);
      });
    }
    agentStudioApi
      .getEntitiesByVersion(clientId, entityId, selectedVersion, entityType)
      .then((res) => {
        if (!res) {
          history.push(`/admin-ui/agent-studio/${clientId}`);
          return;
        }
        setAllEntityFields(res);
        setIncludeClientContext(res.includeClientContext);
        if (entityType === ENTITY_TYPE.SKILL) {
          setEntityProperties({
            skillName: res.skillName,
            description: res.description,
            modelName: res.modelName,
          });
          setSelectedTools({
            mannaiToolIds: res.toolIds || [],
            customApiToolIds: res.customApiToolIds || [],
          });
          setShowTools(res.showTools);
        } else if (entityType === ENTITY_TYPE.AGENT) {
          setEntityProperties({
            agentName: res.agentName,
            agentSystemName: res.agentSystemName,
            description: res.description,
            modelName: res.modelName,
            avatar: null,
            avatarPath: res.avatarPath,
            S3_CDN: res.S3_CDN,
          });
          setSelectedSkillsForAgent({
            skillIds: res.skillIds || [],
            technicalSkillIds: res.technicalSkillIds || [],
          });
        } else if (entityType === ENTITY_TYPE.COLLECTION) {
          setEntityProperties({
            collectionName: res.collectionName,
            description: res.description,
            modelName: res.modelName,
          });
          setSelectedAgentsForCollection({
            agentIds: res.agentIds || [],
          });
        }
        setPrompts({
          corePrompt: res.corePrompt,
          corePromptPrefix: res.corePromptPrefix,
        });
        setTag(res.tag);
        setGenUIEnabled(res.genUi || false);
        setGenUIPrompt(res.genUiPrompt || "");
        setSelectedVersion({
          versionId: res.versionId,
          version: res.version,
        });
        setShowStructure(res.showStructure);
        setStructure(res.structure);
      });
    agentStudioApi
      .getEntityVersions(clientId, entityId, entityType)
      .then((res) => {
        setVersions(res);
        if (createNewRevision) {
          setSelectedVersion(
            res.find((x) => x.versionId === selectedVersion.versionId)
          );
        }
      });
    setSaveMode(false);
    setCreateNewRevision(false);
  }, [fetchDetails]);

  useEffect(() => {
    if (entityType === "Agent") {
      if (
        entityProperties.avatar &&
        !entityProperties.avatarPath &&
        entityProperties.avatar instanceof File
      ) {
        let reader = new FileReader();
        reader.onloadend = () => {
          setEntityProperties({
            ...entityProperties,
            avatarPath: reader.result,
          });
        };
        reader.readAsDataURL(entityProperties.avatar);
      }
    }
  }, [entityProperties]);

  useEffect(() => {
    if (runtests) {
      setTestResults({});
      agentStudioApi.runTests(payload, clientId, entityType).then((data) => {
        if (data?.status === 200) {
          setTestResults({
            summary: data?.results?.summary,
            status: data?.results?.testResult,
            statusCode: data?.status,
          });
        } else {
          setTestResults({
            summary: data?.results,
            statusCode: data?.status || 500,
          });
        }
      });
      setRunTest(false);
    }
  }, [runtests]);

  function performHelperStateUpdate(isHelper) {
    agentStudioApi
      .updateHelperState(entityType, clientId, entityId, isHelper)
      .then((data) => {
        if (data?.status === 200) {
          showMessage(
            isHelper
              ? `${entityType} assigned as helper successfully`
              : `${entityType} removed as helper successfully`,
            "success"
          );
          refetch();
          history.push(
            `/admin-ui/agent-studio/${clientId}/${entityId}/${entityType}`
          );
        } else {
          showMessage(`Error updating helper state`, {
            type: NOTIFICATION_TYPE.ERROR,
          });
        }
      })
      .catch((err) => {
        console.error(err);
        showMessage(`Error updating helper state`, {
          type: NOTIFICATION_TYPE.ERROR,
        });
      });
  }

  function updateHelperState(isHelper) {
    // If we're removing helper status, check if any skills are using this skill as a helper
    if (!isHelper) {
      agentStudioApi
        .getSkillsUsingSkillAsHelper(entityType, clientId, tag)
        .then((data) => {
          if (data?.skills && data.skills.length > 0) {
            setDependentSkills(data.skills);
            setHelperRemovalModalOpen(true);
          } else {
            performHelperStateUpdate(isHelper);
          }
        })
        .catch((err) => {
          console.error(err);
          showMessage(`Error checking if this ${entityType.toLowerCase()} is used as a helper`, "error");
        });
    } else {
      performHelperStateUpdate(isHelper);
    }
  }

  const handleMenuClick = (key) => {
    const selectedVersion = versions.filter((x) => x.versionId === key)[0];
    setSelectedVersion(selectedVersion);
    setFetchDetails(!fetchDetails);
  };

  useEffect(() => {
    const versionObject = versions.map((item) => ({
      value: item.versionId,
      label: item.version,
    }));

    setMenu(versionObject);
  }, [versions]);

  useEffect(() => {
    setPayload(
      getEntityPayload(
        entityType,
        entityId,
        selectedVersion,
        entityProperties,
        tag,
        promptsText,
        structure,
        selectedSkillsForAgent,
        selectedTools,
        selectedAgentsForCollection,
        genUIEnabled,
        genUIPrompt
      )
    );
  }, [
    entityType,
    entityId,
    selectedVersion,
    entityProperties,
    tag,
    promptsText,
    structure,
    selectedSkillsForAgent,
    selectedTools,
    selectedAgentsForCollection,
    genUIEnabled,
    genUIPrompt,
  ]);

  if (loader) {
    return <EverLoader />;
  }

  return (
    <>
      {Boolean(Object.keys(allEntityFields).length) ? (
        <>
          {confirmationModalOpen && (
            <UpdateEntity
              entityType={entityType}
              entityId={entityId}
              open={confirmationModalOpen}
              handleHideModal={() => {
                setConfirmationModalOpen(false);
                setMoveToDraft(false);
              }}
              entityProperties={Object.assign(entityProperties, {
                selectedSkillsForAgent: selectedSkillsForAgent,
                selectedTools: selectedTools,
                selectedAgentsForCollection: selectedAgentsForCollection,
              })}
              prompts={promptsText}
              structure={structure}
              clientId={clientId}
              allEntityFields={allEntityFields}
              saveMode={saveMode}
              selectedVersion={selectedVersion}
              createNewRevision={createNewRevision}
              publishedVersion={versions.find((x) => x.isPublished === true)}
              setEditMode={setEditMode}
              setVersions={setVersions}
              refetch={refetch}
              publish={publish}
              setPublish={setPublish}
              setTestResults={setTestResults}
              setRunTestModalOpen={setRunTestModalOpen}
              tag={tag}
              setLoader={setLoader}
              showStructure={showStructure}
              showTools={showTools}
              includeClientContext={includeClientContext}
              moveToDraft={moveToDraft}
              genUIEnabled={genUIEnabled}
              genUIPrompt={genUIPrompt}
            />
          )}
          {/* {testResults && (
            <RunTest
              open={runTestModalOpen}
              handleHideModal={() => {
                setPublish(false);
                setFetchDetails(!fetchDetails);
                setRunTestModalOpen(false);
                setTestResults(undefined);
                customerStore.setSelectedEntity(entityType);
                history.push(
                  `/admin-ui/agent-studio/${clientId}/${entityId}/${entityType}`
                );
              }}
              testResults={testResults}
              publish={publish}
            />
          )} */}
          {loader ? <EverLoader /> : <></>}
          <div
            key={allEntityFields.isHelper}
            className={`flex flex-col justify-between h-full bg-ever-primary-content`}
          >
            <div className="flex flex-col px-8 pt-8 pb-4 gap-5 overflow-auto shadow-sm h-full">
              <div className="flex flex-row gap-4 pb-4 items-center w-full border-b border-0 border-ever-base-400 border-solid justify-between">
                <div className="flex gap-2 justify-start items-center w-6/12">
                  <BackArrowIcon
                    className="cursor-pointer"
                    onClick={() => {
                      customerStore.setSelectedEntity(entityType);
                      history.push(`/admin-ui/agent-studio/${clientId}`);
                    }}
                  />
                  <h1 className="text-ever-base-content text-lg font-semibold px-1">
                    {getEntityName(entityType, entityProperties)}
                  </h1>
                  {topbarSpec(allEntityFields)}
                </div>

                <div className="flex items-center gap-4 cursor-pointer justify-center">
                  {versionControl(
                    editMode,
                    selectedVersion,
                    setSelectedVersion,
                    menu,
                    handleMenuClick
                  )}
                  {!editMode && (
                    <div
                      className="min-w-28 max-w-40 h-9 px-3.5 py-2 bg-ever-primary-content rounded-lg border border-solid border-ever-primary justify-start items-center gap-2 inline-flex hover:bg-ever-base-100"
                      onClick={() => setEditMode(true)}
                    >
                      <div className="justify-start items-center gap-2 flex">
                        <div className="justify-start items-center gap-2 flex">
                          <PencilIcon
                            className="w-5 h-5 text-ever-primary"
                            aria-hidden="true"
                          />
                          <div className="text-ever-primary text-sm font-medium leading-tight">
                            Edit {entityType}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {basicDetails(
                entityType,
                editMode,
                entityProperties,
                setEntityProperties,
                tag,
                setTag
              )}
              {description(
                entityType,
                entityProperties,
                setEntityProperties,
                editMode
              )}
              {promptsAndComponent(
                editMode,
                promptsText,
                setPrompts,
                entityType,
                {
                  clientId: clientId,
                  selectedSkillsForAgent: selectedSkillsForAgent,
                  setSelectedSkillsForAgent: setSelectedSkillsForAgent,
                  selectedTools: selectedTools,
                  setSelectedTools: setSelectedTools,
                  selectedAgentsForCollection: selectedAgentsForCollection,
                  setSelectedAgentsForCollection:
                    setSelectedAgentsForCollection,
                },
                getEntityDescription(entityType),
                structure,
                setStructure,
                showTools,
                showStructure,
                setShowTools,
                setShowStructure,
                setGenUIEnabled,
                setGenUIPrompt,
                genUIEnabled,
                genUIPrompt,
                allEntityFields.isHelper,
                includeClientContext,
                setIncludeClientContext
              )}
            </div>
            {footer(
              entityType,
              entityId,
              editMode,
              setEditMode,
              setVersions,
              setFetchDetails,
              fetchDetails,
              history,
              clientId,
              setSaveMode,
              setConfirmationModalOpen,
              allEntityFields,
              setCreateNewRevision,
              setRunTestModalOpen,
              setRunTest,
              payload,
              validateEntity,
              setMoveToDraft,
              updateHelperState
            )}
          </div>
          <HelperRemovalConfirmationModal
            modalOpen={helperRemovalModalOpen}
            setModalOpen={setHelperRemovalModalOpen}
            entityType={entityType}
            clientId={clientId}
            dependentSkills={dependentSkills}
            onConfirm={() => {
              performHelperStateUpdate(false)
            }}
          />
        </>
      ) : (
        <div className="flex h-full w-full items-center justify-center bg-ever-base">
          <EverLoader indicatorType="spinner" />
        </div>
      )}
    </>
  );
});
