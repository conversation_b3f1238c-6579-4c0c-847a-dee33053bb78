import {
  basicAgentDetails,
  validateAgent,
  validateAgentOnCreate,
} from "../../agent-properties/AgentComponents";
import {
  basicSkillDetails,
  validateSkill,
  validateSkillOnCreate,
} from "../../skill-properties/SkillComponents";
import { ENTITY_TYPE } from "../../enums";
import {
  basicCollectionDetails,
  validateCollection,
  validateCollectionOnCreate,
} from "../../collection-properties/CollectionComponents";

const ENTITY_MAPPINGS = {
  [ENTITY_TYPE.AGENT]: {
    getPayload: (
      entityId,
      selectedVersion,
      entityProperties,
      tag,
      promptsText,
      structure,
      selectedSkillsForAgent,
      _,
      __,
      genUIEnabled,
      genUIPrompt
    ) => ({
      agentId: entityId,
      version: selectedVersion.version,
      agentVersionId: selectedVersion.versionId,
      agentSpec: {
        modelName: entityProperties.modelName,
        agentName: entityProperties.agentName,
        agentSystemName: entityProperties.agentSystemName,
        description: entityProperties.description,
        corePromptPrefix: promptsText.corePromptPrefix,
        structure: structure,
        corePrompt: promptsText.corePrompt,
        selectedSkills: selectedSkillsForAgent,
        tag: tag,
        avatar: entityProperties.avatar,
        skillName: entityProperties.skillName,
        genUi: genUIEnabled,
        genUiPrompt: genUIPrompt,
      },
    }),
    getId: (entity) => entity.agentId,
    validate: validateAgent,
    validateOnCreate: validateAgentOnCreate,
    getName: (entityProperties) => entityProperties.agentName,
    basicDetails: basicAgentDetails,
    getNameFromMeta: (entity) => entity.agentName,
    description:
      "This contains 2 parts. Fill into the basic prompt which is relevant to this agent. Give the guide about how the agent should utilise the skils. Give generic instructions and guidelines for the agent to use the skills. Do not touch the advanced prompt unless you are sure about what you are doing.",
  },
  [ENTITY_TYPE.SKILL]: {
    getPayload: (
      entityId,
      selectedVersion,
      entityProperties,
      tag,
      promptsText,
      structure,
      _,
      selectedTools,
      __,
      genUIEnabled,
      genUIPrompt
    ) => ({
      skillId: entityId,
      version: selectedVersion.version,
      skillVersionId: selectedVersion.versionId,
      skillSpec: {
        modelName: entityProperties.modelName,
        skillName: entityProperties.skillName,
        description: entityProperties.description,
        corePromptPrefix: promptsText.corePromptPrefix,
        structure: structure,
        corePrompt: promptsText.corePrompt,
        tag: tag,
        selectedTools: selectedTools,
        genUi: genUIEnabled,
        genUiPrompt: genUIPrompt,
      },
    }),
    getId: (entity) => entity.skillId,
    validate: validateSkill,
    validateOnCreate: validateSkillOnCreate,
    getName: (entityProperties) => entityProperties.skillName,
    basicDetails: basicSkillDetails,
    getNameFromMeta: (entity) => entity.skillName,
    description:
      "This contains 2 parts. Fill into the basic prompt which is relevant to this skill. Give the guide about how the skill should utilise the tools. Give generic instructions and guidelines for the skill to use the tools. Do not touch the advanced prompt unless you are sure about what you are doing.",
  },
  [ENTITY_TYPE.COLLECTION]: {
    getPayload: (
      entityId,
      selectedVersion,
      entityProperties,
      tag,
      promptsText,
      structure,
      _,
      __,
      selectedAgentsForCollection,
      genUIEnabled,
      genUIPrompt
    ) => ({
      collectionId: entityId,
      version: selectedVersion.version,
      collectionVersionId: selectedVersion.versionId,
      collectionSpec: {
        modelName: entityProperties.modelName,
        collectionName: entityProperties.collectionName,
        description: entityProperties.description,
        corePromptPrefix: promptsText.corePromptPrefix,
        structure: structure,
        corePrompt: promptsText.corePrompt,
        tag: tag,
        selectedAgents: selectedAgentsForCollection,
        genUi: genUIEnabled,
        genUiPrompt: genUIPrompt,
      },
    }),
    getId: (entity) => entity.collectionId,
    validate: validateCollection, // Add validation function if necessary
    validateOnCreate: validateCollectionOnCreate, // Add validation function if necessary
    getName: (entityProperties) => entityProperties.collectionName,
    basicDetails: basicCollectionDetails, // Add basic details function if necessary
    getNameFromMeta: (entity) => entity.collectionName,
    description:
      "This contains 2 parts. Fill into the basic prompt which is relevant to this collection. Give the guide about how the collection should utilise the agents. Give generic instructions and guidelines for the collection to use the agents. Do not touch the advanced prompt unless you are sure about what you are doing.",
  },
};

export const getEntityPayload = (
  entityType,
  entityId,
  selectedVersion,
  entityProperties,
  tag,
  promptsText,
  structure,
  selectedSkillsForAgent,
  selectedTools,
  selectedAgentsForCollection,
  genUIEnabled,
  genUIPrompt
) => {
  const entity = ENTITY_MAPPINGS[entityType];
  return entity?.getPayload(
    entityId,
    selectedVersion,
    entityProperties,
    tag,
    promptsText,
    structure,
    selectedSkillsForAgent,
    selectedTools,
    selectedAgentsForCollection,
    genUIEnabled,
    genUIPrompt
  );
};

export const validateEntity = (entityType, payload) => {
  const entity = ENTITY_MAPPINGS[entityType];
  return entity?.validate?.(payload);
};

export const validateEntityOnCreate = (entityType, entityProperties, tag) => {
  const entity = ENTITY_MAPPINGS[entityType];
  return entity?.validateOnCreate?.(entityProperties, tag);
};

export const getEntityName = (entityType, entityProperties) => {
  const entity = ENTITY_MAPPINGS[entityType];
  return entity?.getName?.(entityProperties);
};

export const getEntityNameFromMeta = (entity, entityType) => {
  const entityObj = ENTITY_MAPPINGS[entityType];
  return entityObj?.getNameFromMeta?.(entity);
};

export const getEntityDescription = (entityType) => {
  const entity = ENTITY_MAPPINGS[entityType];
  return entity?.description;
};

export const getEntityId = (entityType, entity) => {
  const entityObj = ENTITY_MAPPINGS[entityType];
  return entityObj?.getId?.(entity);
};

export const basicDetails = (
  entityType,
  editMode,
  entityProperties,
  setEntityProperties,
  tag,
  setTag
) => {
  const entity = ENTITY_MAPPINGS[entityType];
  return entity?.basicDetails?.(
    editMode,
    entityProperties,
    setEntityProperties,
    tag,
    setTag
  );
};
