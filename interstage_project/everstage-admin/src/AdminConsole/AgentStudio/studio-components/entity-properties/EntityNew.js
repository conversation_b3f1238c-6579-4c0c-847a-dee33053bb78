import { <PERSON><PERSON>oa<PERSON>, showMessage } from "~/Components";

import { PencilIcon } from "@heroicons/react/outline";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { BackArrowIcon } from "~/icons";
import customerStore from "../../store";
import { EverButton } from "~/Components/ever-button/EverButton";
import {
  description,
  promptsAndComponent,
} from "../../studio-components/common-components/GeneralComponents";
import { CreateEntity } from "../../studio-components/common-components/CreateEntity";
import {
  basicDetails,
  getEntityDescription,
  validateEntityOnCreate,
} from "./EntityCommon";
import { ENTITY_TYPE } from "../../enums";

export const EntityNew = observer(({ clientId, entityType }) => {
  const history = useHistory();

  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);
  const [structure, setStructure] = useState(["str_response", "thought"]);
  const [showStructure, setShowStructure] = useState(true);
  const [showTools, setShowTools] = useState(true);
  const [includeClientContext, setIncludeClientContext] = useState(false);
  const [promptsText, setPrompts] = useState({
    corePrompt: "",
    corePromptPrefix: "",
  });
  const [tag, setTag] = useState("");
  const [genUIEnabled, setGenUIEnabled] = useState(false);
  const [genUIPrompt, setGenUIPrompt] = useState("");
  const agentStudioApi = useAgentStudioApi();
  const newEntityProperties =
    entityType === ENTITY_TYPE.SKILL
      ? {
          skillName: "",
          description: "",
          modelName: "",
        }
      : entityType === ENTITY_TYPE.AGENT
      ? {
          agentName: "",
          agentSystemName: "",
          description: "",
          modelName: "",
          avatar: null,
          avatarPath: "",
        }
      : entityType === ENTITY_TYPE.COLLECTION
      ? {
          collectionName: "",
          description: "",
          modelName: "",
        }
      : {};
  const [entityProperties, setEntityProperties] = useState(newEntityProperties);
  const [loader, setLoader] = useState(false);
  const [isHelper, setIsHelper] = useState(false);
  const [selectedTools, setSelectedTools] = useState({
    mannaiToolIds: [],
    customApiToolIds: [],
  });
  const [selectedSkillsForAgent, setSelectedSkillsForAgent] = useState({
    skillIds: [],
    technicalSkillIds: [],
  });
  const [selectedAgentsForCollection, setSelectedAgentsForCollection] =
    useState({
      agentIds: [],
    });

  useEffect(() => {
    if (!entityProperties.modelName) {
      agentStudioApi.getLLMs().then((res) => {
        customerStore.setListOfLlms(res);
        setEntityProperties({
          ...entityProperties,
          modelName: res[0],
        });
      });
      if (promptsText.corePrompt === "") {
        agentStudioApi.getAdvancedPrompts(entityType).then((res) => {
          setPrompts({
            ...promptsText,
            corePrompt: res,
          });
        });
      }
    }
    if (entityType === "Agent") {
      if (entityProperties.avatar && !entityProperties.avatarPath) {
        let reader = new FileReader();
        reader.onloadend = () => {
          setEntityProperties({
            ...entityProperties,
            avatarPath: reader.result,
          });
        };
        reader.readAsDataURL(entityProperties.avatar);
      }
    }
  }, [entityProperties]);

  if (loader) {
    return <EverLoader />;
  }

  return (
    <>
      {confirmationModalOpen && (
        <CreateEntity
          open={confirmationModalOpen}
          handleHideModal={() => setConfirmationModalOpen(false)}
          entityProperties={Object.assign(entityProperties, {
            selectedSkillsForAgent: selectedSkillsForAgent,
            selectedTools: selectedTools,
            selectedAgentsForCollection: selectedAgentsForCollection,
          })}
          prompts={promptsText}
          structure={structure}
          showStructure={showStructure}
          showTools={showTools}
          clientId={clientId}
          tag={tag}
          entityType={entityType}
          validateEntity={validateEntityOnCreate}
          setLoader={setLoader}
          genUIEnabled={genUIEnabled}
          genUIPrompt={genUIPrompt}
          isHelper={isHelper}
          setIsHelper={setIsHelper}
          includeClientContext={includeClientContext}
        />
      )}
      <div className="flex flex-col justify-between h-full bg-ever-primary-content">
        <div className="flex flex-col px-8 pt-8 pb-4 gap-5 h-full overflow-auto shadow-sm">
          <div className="flex flex-row gap-4 pb-4 items-center w-full pl-4 border-b border-0 border-solid border-ever-base-400">
            <BackArrowIcon
              className="cursor-pointer"
              onClick={() => {
                customerStore.setSelectedEntity(entityType);
                history.push(`/admin-ui/agent-studio/${clientId}`);
              }}
            />
            <div className="text-ever-base-content text-xl font-semibold">
              Create a New {entityType}
            </div>
            <div className="w-max h-6 text-xs flex justify-around items-center rounded-2xl px-2 gap-1 border border-solid text-ever-warning-lite-content border-ever-warning/30 bg-ever-warning-lite">
              <PencilIcon className="w-full h-4 items-center text-ever-warning-lite-content" />
              <div className="text-center text-amber-700 text-xs font-medium leading-none">
                Draft
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-6 h-full px-4">
            {basicDetails(
              entityType,
              true,
              entityProperties,
              setEntityProperties,
              tag,
              setTag
            )}
            {description(
              entityType,
              entityProperties,
              setEntityProperties,
              true
            )}
            <div className="flex flex-col gap-6 h-full">
              {promptsAndComponent(
                true,
                promptsText,
                setPrompts,
                entityType,
                {
                  selectedTools: selectedTools,
                  setSelectedTools: setSelectedTools,
                  selectedSkillsForAgent: selectedSkillsForAgent,
                  setSelectedSkillsForAgent: setSelectedSkillsForAgent,
                  selectedAgentsForCollection: selectedAgentsForCollection,
                  setSelectedAgentsForCollection:
                    setSelectedAgentsForCollection,
                },
                getEntityDescription(entityType),
                structure,
                setStructure,
                showTools,
                showStructure,
                setShowTools,
                setShowStructure,
                setGenUIEnabled,
                setGenUIPrompt,
                genUIEnabled,
                genUIPrompt,
                isHelper,
                includeClientContext,
                setIncludeClientContext
              )}
            </div>
          </div>
        </div>
        <div className="w-full flex px-8 py-4 border-0 border-t border-solid border-ever-base-200 justify-end gap-4">
          <EverButton
            onClick={() => {
              showMessage(`${entityType} creation cancelled`, { type: "info" });
              customerStore.setSelectedEntity(entityType);
              history.push(`/admin-ui/agent-studio/${clientId}`);
            }}
            type="ghost"
          >
            Cancel
          </EverButton>

          <EverButton onClick={() => setConfirmationModalOpen(true)}>
            Submit
          </EverButton>
        </div>
      </div>
    </>
  );
});
