import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { Modal, showMessage } from "~/Components";
import { EverButton } from "~/Components/ever-button/EverButton";
import { NOTIFICATION_TYPE } from "~/Enums";
import { convertKeysToSnakeCase } from "./commonUtils";
import { ENTITY_TYPE } from "../../enums";

export const CreateEntity = observer((props) => {
  const {
    open,
    handleHideModal,
    entityProperties,
    prompts,
    structure,
    showStructure,
    showTools,
    clientId,
    tag,
    entityType,
    validateEntity,
    setLoader,
    genUIEnabled,
    genUIPrompt,
    isHelper,
    setIsHelper,
    includeClientContext,
  } = props;

  const [newVersionName, setVersionName] = useState("");
  const [submit, setSubmit] = useState(false);
  const history = useHistory();
  const handleSubmit = (e) => {
    e.preventDefault();
    setVersionName(newVersionName);
    setSubmit(true);
  };
  const agentStudioApi = useAgentStudioApi();

  useEffect(() => {
    if (open && submit) {
      if (newVersionName.length === 0) {
        showMessage("Please enter a version name", {
          type: NOTIFICATION_TYPE.ERROR,
        });
        setSubmit(false);
        return;
      }
      if (!validateEntity(entityType, entityProperties, tag)) {
        setSubmit(false);
        return;
      }

      const data = {
        details: {
          version: newVersionName,
          description: entityProperties.description,
          modelName: entityProperties.modelName,
          corePromptPrefix: prompts.corePromptPrefix,
          corePrompt: prompts.corePrompt,
          structure: structure,
          tag: tag,
          latestVersion: true,
          genUi: genUIEnabled,
          genUiPrompt: genUIPrompt,
          showStructure: showStructure,
          includeClientContext: includeClientContext,
        },
      };

      if (entityType === ENTITY_TYPE.AGENT) {
        data.details.agentName = entityProperties.agentName;
        data.details.agentSystemName = entityProperties.agentSystemName;
        data.details.avatar = entityProperties.avatar;
        data.details.skillIds =
          entityProperties.selectedSkillsForAgent.skillIds;
        data.details.technicalSkillIds =
          entityProperties.selectedSkillsForAgent.technicalSkillIds;
      } else if (entityType === ENTITY_TYPE.SKILL) {
        data.details.skillName = entityProperties.skillName;
        data.details.toolIds = entityProperties.selectedTools?.mannaiToolIds;
        data.details.customApiToolIds =
          entityProperties.selectedTools?.customApiToolIds;
        data.details.showTools = showTools;
        data.details.isHelper = isHelper;
      } else if (entityType === ENTITY_TYPE.COLLECTION) {
        data.details.collectionName = entityProperties.collectionName;
        data.details.agentIds =
          entityProperties.selectedAgentsForCollection?.agentIds;
      }

      let formData = new FormData();
      if (entityType === ENTITY_TYPE.AGENT) {
        formData.append("avatar", data.details.avatar);
        const { avatar, ...agentDetailsWithoutAvatar } = data.details;
        formData.append(
          "details",
          JSON.stringify(convertKeysToSnakeCase(agentDetailsWithoutAvatar))
        );
      }
      setLoader(true);

      agentStudioApi
        .createEntity(
          entityType,
          clientId,
          entityType === ENTITY_TYPE.AGENT ? formData : data
        )
        .then((res) => {
          setLoader(false);
          if (res.status === 200) {
            showMessage(`${entityType} created successfully`, {
              type: NOTIFICATION_TYPE.SUCCESS,
            });
            const entityId = res.data.entityId;
            history.push(
              `/admin-ui/agent-studio/${clientId}/${entityId}/${entityType}`
            );
          }
        })
        .catch((err) => {
          setLoader(false);
          console.error(err);
          showMessage(`Error creating ${entityType} - ${err.message}`, {
            type: NOTIFICATION_TYPE.ERROR,
          });
        });
      handleHideModal();
    }
  }, [open, submit]);

  return (
    <Modal open={open} className="min-w-[480px]">
      <Modal.Title onClose={handleHideModal}>Confirmation</Modal.Title>
      <Modal.Content className="overflow-auto p-8">
        <div className="max-w-md mx-auto">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="mb-4 flex flex-col gap-4">
              <label
                htmlFor="name"
                className="text-ever-content-mid text-sm font-normal leading-none tracking-normal"
              >
                Enter the version name
              </label>
              <input
                type="text"
                id="name"
                value={newVersionName}
                onChange={(e) => setVersionName(e.target.value)}
                className="mt-1 p-2 block w-full rounded-xl border-ever-base-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-5"
              />
            </div>
            {entityType === ENTITY_TYPE.SKILL && (
              <div className="mb-4 flex items-center gap-2">
                <input
                  type="checkbox"
                  id="isHelper"
                  checked={isHelper}
                  onChange={(e) => setIsHelper(e.target.checked)}
                  className="rounded border-ever-base-300 text-ever-primary focus:ring-ever-primary"
                />
                <label
                  htmlFor="isHelper"
                  className="text-ever-content-mid text-sm font-normal leading-none tracking-normal"
                >
                  Assign as helper
                </label>
              </div>
            )}
            <div className="flex">
              <div className="flex ml-auto pt-4">
                <EverButton htmlType="submit">Submit</EverButton>
              </div>
            </div>
          </form>
        </div>
      </Modal.Content>
    </Modal>
  );
});
