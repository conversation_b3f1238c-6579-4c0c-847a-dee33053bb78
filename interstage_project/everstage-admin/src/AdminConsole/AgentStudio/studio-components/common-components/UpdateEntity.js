import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { Modal, showMessage } from "~/Components";
import { NOTIFICATION_TYPE } from "~/Enums";
import { Button, Input, Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { convertKeysToSnakeCase } from "./commonUtils";
import { ENTITY_TYPE } from "../../enums";

export const UpdateEntity = observer((props) => {
  const {
    open,
    handleHideModal,
    entityProperties,
    prompts,
    structure,
    clientId,
    allEntityFields,
    saveMode,
    selectedVersion,
    createNewRevision,
    publishedVersion,
    setEditMode,
    setVersions,
    publish,
    setPublish,
    setTestResults,
    setRunTestModalOpen,
    tag,
    entityType,
    entityId,
    setLoader,
    showTools,
    showStructure,
    moveToDraft,
    refetch,
    genUIEnabled,
    genUIPrompt,
    includeClientContext,
  } = props;
  const [versionName, setVersionName] = useState("");
  const [submit, setSubmit] = useState(false);
  const [runTests, setRunTests] = useState(false);
  const history = useHistory();
  const handleSubmit = (e) => {
    e.preventDefault();
    setVersionName(versionName);
    setSubmit(true);
  };

  const handlePublish = (e) => {
    e.preventDefault();
    setPublish(true);
  };
  const agentStudioApi = useAgentStudioApi();

  useEffect(() => {
    if (open && submit) {
      setLoader(true);
      const data = {
        details: {
          version: createNewRevision ? versionName : selectedVersion.version,
          versionId: selectedVersion.versionId,
          description: entityProperties.description,
          modelName: entityProperties.modelName,
          corePromptPrefix: prompts.corePromptPrefix,
          structure: structure,
          corePrompt: prompts.corePrompt,
          tag: tag,
          genUi: genUIEnabled,
          genUiPrompt: genUIPrompt,
          latestVersion: true,
          showStructure: showStructure,
          includeClientContext: includeClientContext,
        },
      };

      if (entityType === ENTITY_TYPE.AGENT) {
        data.details.agentName = entityProperties.agentName;
        data.details.agentSystemName = entityProperties.agentSystemName;
        data.details.avatar = entityProperties.avatar;
        data.details.avatarPath = entityProperties.avatarPath?.replace(
          entityProperties.S3_CDN + "/",
          "/"
        );
        data.details.skillIds =
          entityProperties.selectedSkillsForAgent.skillIds;
        data.details.technicalSkillIds =
          entityProperties.selectedSkillsForAgent.technicalSkillIds;
      } else if (entityType === ENTITY_TYPE.SKILL) {
        data.details.skillName = entityProperties.skillName;
        data.details.toolIds = entityProperties.selectedTools?.mannaiToolIds;
        data.details.customApiToolIds =
          entityProperties.selectedTools?.customApiToolIds;
        data.details.showTools = showTools;
        data.details.isHelper = allEntityFields.isHelper;
      } else if (entityType === ENTITY_TYPE.COLLECTION) {
        data.details.collectionName = entityProperties.collectionName;
        data.details.agentIds =
          entityProperties.selectedAgentsForCollection?.agentIds;
      }

      const update = createNewRevision ? false : true;
      let formData = new FormData();
      if (entityType === ENTITY_TYPE.AGENT) {
        formData.append("avatar", data.details.avatar);
        const { avatar, ...agentDetailsWithoutAvatar } = data.details;
        formData.append(
          "details",
          JSON.stringify(convertKeysToSnakeCase(agentDetailsWithoutAvatar))
        );
      }
      setLoader(true);
      agentStudioApi
        .createEntity(
          entityType,
          clientId,
          entityType === ENTITY_TYPE.AGENT ? formData : data,
          update,
          createNewRevision,
          entityId
        )
        .then((res) => {
          setLoader(false);
          if (res.status === 200) {
            const msg = update
              ? ` ${entityType} updated successfully`
              : createNewRevision
              ? "New revision created successfully"
              : `${entityType} created successfully`;
            showMessage(msg, {
              type: NOTIFICATION_TYPE.SUCCESS,
            });

            if (createNewRevision) {
              selectedVersion.versionId = res.data.versionId;
              setVersions([]);
            }
            setEditMode(false);
            refetch();
          }
          setLoader(false);
          history.push(
            `/admin-ui/agent-studio/${clientId}/${entityId}/${entityType}`
          );
        })
        .catch((err) => {
          setLoader(false);
          console.error(err);
          showMessage(`Error updating ${entityType}`, "error");
          showMessage(`Error updating ${entityType} - ${err.message}`, {
            type: NOTIFICATION_TYPE.ERROR,
          });
        });
      handleHideModal();
    } else if (open && publish) {
      setTestResults({});
      // TODO: API Changes
      agentStudioApi
        .publishEntity(
          entityType,
          clientId,
          entityId,
          selectedVersion.versionId,
          // !moveToDraft || allEntityFields.isPublished
          moveToDraft ? false : true
        )
        .then((data) => {
          if (data?.status === 200) {
            setTestResults({
              status: "Success",
              statusCode: data?.status,
            });
            showMessage(
              moveToDraft
                ? `${entityType} moved to draft successfully`
                : `${entityType} published successfully`,
              "success"
            );
            setPublish(false);
            refetch();
            history.push(
              `/admin-ui/agent-studio/${clientId}/${entityId}/${entityType}`
            );
          } else if (data?.status === 400) {
            setTestResults({
              summary: data?.results?.testResult?.summary,
              status: data?.results?.testResult?.testResult,
              statusCode: data?.status,
            });
          } else {
            setTestResults({
              summary: data?.results,
              statusCode: data?.status || 500,
            });
          }
          setRunTestModalOpen(true);
        });
      handleHideModal();
    }
  }, [open, submit, publish]);

  return (
    <Modal
      open={open}
      className={`${createNewRevision ? "h-[280px]" : "min-h-[200px]"} ${
        saveMode ? "min-w-[360px]" : "min-w-[480px]"
      }`}
    >
      <Modal.Title onClose={handleHideModal}>Confirmation</Modal.Title>
      <Modal.Content className="overflow-auto p-4">
        {saveMode ? (
          <form
            onSubmit={handleSubmit}
            className="flex flex-col w-full h-full justify-between"
          >
            <div className="mb-4 flex flex-col gap-4">
              <label
                htmlFor="name"
                className="text-ever-content-mid text-sm font-normal leading-none tracking-normal"
              >
                Do you want to override the changes?
              </label>
            </div>
            <div className="flex ml-auto pt-4">
              <Button
                htmlType="submit"
                className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary text-white"
              >
                Save
              </Button>
            </div>
          </form>
        ) : createNewRevision ? (
          <form
            onSubmit={handleSubmit}
            className="flex flex-col w-full h-full justify-between"
          >
            <div className="mb-4 flex flex-col gap-4">
              <label
                htmlFor="name"
                className="text-ever-content-mid text-sm font-normal leading-none tracking-normal"
              >
                Enter the new version name
              </label>
              <input
                type="text"
                id="name"
                value={versionName}
                onChange={(e) => setVersionName(e.target.value)}
                className="mt-1 p-2 block w-full rounded-xl border-ever-base-400 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-5"
                required
              />
            </div>
            <div className="flex ml-auto pt-4">
              <Button
                htmlType="submit"
                className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary text-white"
              >
                Submit
              </Button>
            </div>
          </form>
        ) : (
          <form
            onSubmit={handlePublish}
            className="flex flex-col w-full h-full justify-between"
          >
            <div className="mb-4 flex flex-col gap-4">
              <label
                htmlFor="name"
                className="text-ever-content-mid text-sm font-normal leading-none tracking-normal text-wrap leading-6"
              >
                {moveToDraft ? (
                  "Are you sure you want to move this entity back to draft? It will no longer be considered published."
                ) : publishedVersion ? (
                  <>
                    <span className="font-bold">
                      {publishedVersion.version}
                    </span>{" "}
                    is already published. Do you wish to replace this version in
                    publish?
                  </>
                ) : (
                  "Are you sure you want to publish this version?"
                )}
              </label>
            </div>
            <div className="flex ml-auto pt-4">
              <Button
                htmlType="submit"
                className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary text-white"
              >
                Confirm
              </Button>
            </div>
          </form>
        )}
      </Modal.Content>
    </Modal>
  );
});
