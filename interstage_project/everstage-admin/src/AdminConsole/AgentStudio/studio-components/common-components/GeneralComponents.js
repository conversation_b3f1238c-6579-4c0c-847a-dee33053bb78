import { Select, Tooltip } from "antd";
import { Button, Multiselect, showMessage } from "~/Components";
import {
  CheckCircleFilled,
  InfoCircleOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { DocumentDuplicateIcon } from "@heroicons/react/outline";
import { Prompts } from "./Prompts";
import React from "react";
import { Switch } from "Components";

import { Tools } from "../../skill-properties/Tools";
import { Skills } from "../../agent-properties/Skills";
import { ENTITY_TYPE } from "../../enums";
import { Agents } from "../../collection-properties/Agents";

export function topbarSpec(allEntityFields) {
  const classes =
    "w-max h-6 text-xs flex justify-around items-center rounded-2xl px-2 gap-1 border border-solid";
  return (
    <div className="flex gap-2">
      {!allEntityFields.isPublished && (
        <div
          className={`${classes} text-ever-warning-lite-content border-ever-warning/30 bg-ever-warning-lite`}
        >
          <EditOutlined className="w-full h-4 items-center text-ever-warning-lite-content" />
          <div className="text-center text-amber-700 text-xs font-medium leading-none">
            Draft
          </div>
        </div>
      )}
      {allEntityFields.isPublished && (
        <div
          className={`${classes} text-ever-success-lite-content border-ever-success/30 bg-ever-success-lite`}
        >
          <CheckCircleFilled className="w-full h-4 text-ever-success-lite-content" />
          <div className="text-center text-ever-success-lite-content text-xs font-medium leading-none">
            Published
          </div>
        </div>
      )}
      {allEntityFields.isHelper && (
        <div
          className={`${classes} text-ever-primary-lite-content border-ever-primary/30 bg-ever-primary-lite`}
        >
          <InfoCircleOutlined className="w-full h-4 text-ever-primary-lite-content" />
          <div className="text-center text-ever-primary-lite-content text-xs font-medium leading-none">
            Helper
          </div>
        </div>
      )}
    </div>
  );
}
export function versionControl(
  editMode,
  selectedVersion,
  setSelectedVersion,
  menu,
  handleMenuClick
) {
  return (
    <>
      {editMode ? (
        <>
          <div className="text-ever-base-content-mid text-sm font-normal leading-none tracking-normal flex flex-row gap-1">
            Version name
            <div className="text-red-600 text-xs font-normal leading-tight">
              *
            </div>
          </div>
          <input
            type="version"
            id="version"
            value={selectedVersion.version}
            onChange={(e) => {
              setSelectedVersion({
                ...selectedVersion,
                version: e.target.value,
              });
            }}
            className="text-ever-base-content text-sm font-normal leading-tight h-9 px-3 py-2.5 bg-ever-primary-content rounded-lg shadow-sm border border-ever-base-300 border-solid justify-start items-center focus:ring focus:ring-ever-primary focus:ring-opacity-5 focus:border-ever-primary"
          />
        </>
      ) : (
        <div className="flex h-9 justify-center items-end inline-flex">
          <div className="flex flex-row items-center">
            <Select
              name="version"
              placeholder="Select version"
              value={selectedVersion.version}
              defaultValue={selectedVersion.version}
              onChange={handleMenuClick}
              options={[...new Set(menu)]}
              showSearch={false}
              className="!h-9 !w-[180px] mt-1 block w-full rounded-md border-ever-base-200 focus:border-ever-primary focus:ring focus:ring-ever-primary focus:ring-opacity-5 hover:bg-ever-base-100"
              filterOption={false}
            />
          </div>
        </div>
      )}
    </>
  );
}

export const LabelComponent = ({ name, tooltip, important = false }) => {
  return (
    <label className="justify-start items-start gap-1 flex">
      <div className="text-ever-base-content text-sm font-semibold leading-tight">
        {name}
      </div>
      {important ? (
        <div className="text-red-600 text-xs font-normal leading-tight">*</div>
      ) : (
        <></>
      )}
      <Tooltip title={tooltip}>
        <InfoCircleOutlined className="ml-1 text-ever-base-content" />
      </Tooltip>
    </label>
  );
};

export function footer(
  entityType,
  entityId,
  editMode,
  setEditMode,
  setVersions,
  setFetchDetails,
  fetchDetails,
  history,
  clientId,
  setSaveMode,
  setConfirmationModalOpen,
  allEntityFields,
  setCreateNewRevision,
  setRunTestModalOpen,
  setRunTest,
  payload,
  validateEntity,
  setMoveToDraft,
  updateHelperState
) {
  return (
    <>
      {editMode ? (
        <div className="w-full flex justify-end p-4 gap-4">
          <Button
            className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-base-200 hover:!bg-ever-base-300 outline-none focus:ring-2 focus:ring-ever-primary focus:ring-opacity-0"
            onClick={() => {
              showMessage("Changes discarded", { type: "info" });
              setEditMode(false);
              setVersions([]);
              setFetchDetails(!fetchDetails);
              history.push(
                `/admin-ui/agent-studio/${clientId}/${entityId}/${entityType}`
              );
            }}
            color="base"
          >
            <div className="text-ever-base-content text-sm font-medium leading-tight">
              Cancel
            </div>
          </Button>
          <Button
            onClick={() => {
              if (validateEntity(entityType, payload)) {
                setSaveMode(true);
                setConfirmationModalOpen(true);
              }
            }}
            className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary outline-none focus:ring-2 focus:ring-ever-primary focus:ring-opacity-0"
          >
            <div className="text-white text-sm font-medium leading-tight">
              Save
            </div>
          </Button>
        </div>
      ) : (
        <div className="w-full flex px-8 py-4 border-0 border-t border-solid border-ever-base-200">
          <div className="ml-auto gap-3 flex">
            <Button
              className="w-52 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-base-200 hover:!bg-ever-base-300 gap-2 outline-none focus:ring-2 focus:ring-ever-primary focus:ring-opacity-0"
              onClick={() => {
                setSaveMode(false);
                setCreateNewRevision(true);
                setMoveToDraft(false);
                setConfirmationModalOpen(true);
              }}
              color="base"
            >
              <DocumentDuplicateIcon className="w-5 h-5 text-ever-base-content" />
              <div className="text-ever-base-content text-sm font-medium leading-tight">
                Create a new revision
              </div>
            </Button>
            {/* Helper button for skills and technical skills */}
            {entityType === ENTITY_TYPE.SKILL && (
              <Button
                onClick={() => {
                  updateHelperState(!allEntityFields.isHelper);
                }}
                className="rounded-xl text-sm font-medium h-10 px-4 py-2 border border-ever-primary text-ever-primary hover:!text-white hover:!bg-ever-primary"
              >
                {allEntityFields.isHelper
                  ? "Remove as helper"
                  : "Assign as helper"}
              </Button>
            )}
            {!allEntityFields.isPublished && (
              <Button
                onClick={() => {
                  setSaveMode(false);
                  setCreateNewRevision(false);
                  setMoveToDraft(false);
                  setConfirmationModalOpen(true);
                }}
                className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary outline-none focus:ring-2 focus:ring-ever-primary focus:ring-opacity-0"
              >
                <div className="text-white text-sm font-medium leading-tight">
                  Publish
                </div>
              </Button>
            )}
            {allEntityFields.isPublished && (
              <Button
                onClick={() => {
                  setSaveMode(false);
                  setCreateNewRevision(false);
                  setMoveToDraft(true);
                  setConfirmationModalOpen(true);
                }}
                className="rounded-xl text-sm font-medium h-10 px-4 py-2 border border-ever-primary text-ever-primary hover:!text-white hover:!bg-ever-primary"
              >
                Move to Draft
              </Button>
            )}
          </div>
        </div>
      )}
    </>
  );
}

export function promptsAndComponent(
  editMode,
  promptsText,
  setPrompts,
  entityType,
  entityObjects,
  entityTooltip,
  structure,
  setStructure,
  showTools,
  showStructure,
  setShowTools,
  setShowStructure,
  setGenUIEnabled,
  setGenUIPrompt,
  genUIEnabled,
  genUIPrompt,
  isHelper,
  includeClientContext,
  setIncludeClientContext
) {
  return (
    <div className="w-full h-full flex-row flex gap-4 grow-0 min-h-0">
      <div className="flex flex-col h-full justify-between w-full">
        <div className="flex w-full py-2.5 justify-between">
          <div className="flex gap-2 w-full">
            <div className="flex mr-auto">
              <LabelComponent
                name="Prompt Configurations"
                tooltip={entityTooltip}
              />
            </div>
            <div className="flex items-center">
              <span className="mr-2 text-sm">Include client context</span>
              <Switch
                enabled={includeClientContext}
                toggleEnabled={() =>
                  setIncludeClientContext(!includeClientContext)
                }
                isDisabled={!editMode}
                toggleColor="bg-ever-primary"
              />
            </div>
            {entityType === ENTITY_TYPE.SKILL ? (
              <div className="flex items-center">
                {showTools && (
                  <>
                    <span className="mr-2 text-sm">Structure</span>
                    <Switch
                      className="text-ever-primary"
                      enabled={showStructure && showTools}
                      toggleEnabled={() => setShowStructure(!showStructure)}
                      isDisabled={!editMode || !showTools}
                      toggleColor="bg-ever-primary"
                    />
                  </>
                )}
              </div>
            ) : (
              <div className="flex items-center">
                <span className="mr-2 text-sm">Structure</span>
                <Switch
                  className="text-ever-primary"
                  enabled={showStructure}
                  toggleEnabled={() => setShowStructure(!showStructure)}
                  isDisabled={!editMode}
                  toggleColor="bg-ever-primary"
                />
              </div>
            )}
            {entityType === ENTITY_TYPE.SKILL && (
              <div className="flex items-center">
                <span className="ml-4 mr-2 text-sm">Tools</span>
                <Switch
                  enabled={showTools}
                  toggleEnabled={() => {
                    const newShowTools = !showTools;
                    setShowTools(newShowTools);
                    if (!newShowTools) {
                      setShowStructure(false);
                    } else {
                      setShowStructure(true);
                    }
                  }}
                  isDisabled={!editMode}
                  toggleColor="bg-ever-primary"
                />
              </div>
            )}

            <div className="flex items-center">
              <span className="ml-4 mr-2 text-sm">GenUI</span>
              <Switch
                enabled={genUIEnabled}
                toggleEnabled={() => {
                  setGenUIEnabled(!genUIEnabled);
                }}
                isDisabled={!editMode}
                toggleColor="bg-ever-primary"
              />
            </div>
          </div>
        </div>
        <Prompts
          editMode={editMode}
          promptsText={promptsText}
          setPrompts={setPrompts}
          showTools={showTools}
          genUIPrompt={genUIPrompt}
          setGenUIPrompt={setGenUIPrompt}
          genUIEnabled={genUIEnabled}
        />
        {showTools && showStructure && (
          <>
            <div className="flex w-full pt-5 py-2.5 justify-between">
              <div className="flex gap-2">
                <LabelComponent
                  name="Structure"
                  tooltip="Select structure for final output"
                />
              </div>
            </div>
            {editMode ? (
              <Multiselect
                listOptions={[
                  "str_response",
                  "thought",
                  "json_response",
                  "input_required",
                  "approval_required",
                  "follow_up_questions",
                ]}
                onChange={(_selectedOptions) => {
                  setStructure(_selectedOptions);
                }}
                placeholder="Select variables for final structure"
                defaultSelectedOptions={structure}
                disableEdit={editMode}
                isUpsideDown
              />
            ) : (
              <div className="flex flex-wrap gap-2 p-2 rounded-lg border border-ever-base-300 border-solid">
                {structure.map((el) => (
                  <span key={el} className="flex gap-y-5">
                    <span className="flex items-center px-2 py-0.5 rounded-md text-ever-base-content border border-solid border-ever-base-400 text-sm flex grow align-center w-max cursor-pointer bg-ever-base-50 active:bg-ever-base-300 transition duration-300 ease">
                      {el}
                    </span>
                  </span>
                ))}
              </div>
            )}
          </>
        )}
      </div>
      {showTools && entityType === ENTITY_TYPE.SKILL && (
        <Tools
          editMode={editMode}
          selectedTools={entityObjects.selectedTools}
          setSelectedTools={entityObjects.setSelectedTools}
          createMode={false}
          isHelper={isHelper}
        />
      )}
      {entityType === ENTITY_TYPE.AGENT && (
        <Skills
          editMode={editMode}
          selectedSkillsForAgent={entityObjects.selectedSkillsForAgent}
          setSelectedSkillsForAgent={entityObjects.setSelectedSkillsForAgent}
        />
      )}
      {entityType === ENTITY_TYPE.COLLECTION && (
        <Agents
          editMode={editMode}
          selectedAgentsForCollection={
            entityObjects.selectedAgentsForCollection
          }
          setSelectedAgentsForCollection={
            entityObjects.setSelectedAgentsForCollection
          }
        />
      )}
    </div>
  );
}

export function description(
  entityType,
  entityProperties,
  setEntityProperties,
  editMode
) {
  return (
    <div className="flex w-full flex-col gap-2 justify-start h-16">
      <LabelComponent
        name="Description"
        tooltip={`Describe the ${entityType} in a few words to give a context about what it does`}
        important={true}
      />
      <input
        type="text"
        id="description"
        value={entityProperties.description}
        onChange={(e) =>
          setEntityProperties({
            ...entityProperties,
            description: e.target.value,
          })
        }
        disabled={!editMode}
        className="text-ever-base-content text-sm font-normal leading-tight h-10 px-3 py-2.5 bg-ever-primary-content rounded-lg shadow-sm border border-ever-base-300 border-solid justify-start items-center focus:ring focus:ring-ever-primary focus:ring-opacity-5"
      />
    </div>
  );
}
