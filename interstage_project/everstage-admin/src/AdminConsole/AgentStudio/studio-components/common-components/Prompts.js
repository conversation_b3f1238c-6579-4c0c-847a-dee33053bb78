import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/outline";
import { observer } from "mobx-react";
import { useState } from "react";

export const Prompts = observer(
  ({
    editMode,
    setPrompts,
    promptsText,
    showTools,
    genUIPrompt,
    setGenUIPrompt,
    genUIEnabled,
  }) => {
    const handlePromptChange = (event) => {
      const { name, value } = event.target;
      setPrompts({
        ...promptsText,
        [name]: value,
      });
    };
    const [showAdvancedPrompt, setShowAdvancedPrompt] = useState(false);
    const [showGenUIPrompt, setShowGenUIPrompt] = useState(true);

    return (
      <div className="relative p-4 w-full rounded-lg border border-ever-base-300 border-solid flex-col gap-3 flex h-full flex-1 overflow-y-auto">
        <div className="h-full flex flex-col gap-2 w-full">
          <label className="gap-1 flex justify-between w-full">
            <div className="text-ever-base-content text-sm font-medium leading-tight">
              Basic Prompt
            </div>
          </label>

          <textarea
            name="corePromptPrefix"
            value={promptsText.corePromptPrefix}
            onChange={handlePromptChange}
            className="bg-ever-primary-content resize-y rounded-lg shadow-sm border border-ever-base-300 border-solid text-ever-base-content text-sm font-normal leading-tight w-full h-full justify-start items-start gap-2 inline-flex px-3.5 py-3 focus:ring focus:ring-ever-primary focus:ring-opacity-5 focus:border-ever-primary min-h-[200px]"
            readOnly={!editMode}
          />
        </div>
        {genUIEnabled && (
          <div
            className={`flex flex-row gap-4 w-full ${
              showGenUIPrompt ? "h-1/2" : "h-0 pb-4"
            }`}
          >
            <div className="w-full flex flex-col gap-2 h-full">
              <label
                className="flex items-center gap-1 flex cursor-pointer"
                onClick={() => setShowGenUIPrompt(!showGenUIPrompt)}
              >
                {showGenUIPrompt ? (
                  <ChevronUpIcon className="w-5 h-5" />
                ) : (
                  <ChevronDownIcon className="w-5 h-5" />
                )}
                <div className="text-ever-base-content text-sm font-medium leading-tight">
                  GenUI Prompt
                </div>
              </label>
              {showGenUIPrompt && (
                <textarea
                  name="genUIPrompt"
                  value={genUIPrompt}
                  onChange={(e) => setGenUIPrompt(e.target.value)}
                  className="bg-ever-primary-content resize-y rounded-lg shadow-sm border border-ever-base-300 border-solid text-ever-base-content text-sm font-normal leading-tight w-full h-full justify-start items-start gap-2 inline-flex px-3.5 py-3 focus:ring focus:ring-ever-primary focus:ring-opacity-5 focus:border-ever-primary min-h-[50px]"
                  readOnly={!editMode}
                />
              )}
            </div>
          </div>
        )}
        <div
          className={`flex flex-row gap-4 pb-4 w-full ${
            showAdvancedPrompt ? "h-full" : "h-0"
          }`}
        >
          <div className="w-full flex flex-col gap-2 h-full">
            <label
              className="flex items-center gap-1 flex cursor-pointer"
              onClick={() => setShowAdvancedPrompt(!showAdvancedPrompt)}
            >
              {showTools && (
                <>
                  {showAdvancedPrompt ? (
                    <ChevronUpIcon className="w-5 h-5" />
                  ) : (
                    <ChevronDownIcon className="w-5 h-5" />
                  )}
                </>
              )}
              {showTools && (
                <div className="text-ever-base-content resize-y text-sm font-medium leading-tight">
                  Advanced Prompt
                </div>
              )}
            </label>
            {showAdvancedPrompt && showTools && (
              <textarea
                name="corePrompt"
                value={promptsText.corePrompt}
                onChange={handlePromptChange}
                className="bg-ever-primary-content rounded-lg shadow-sm border border-ever-base-300 border-solid text-ever-base-content text-sm font-normal leading-tight w-full h-full justify-start items-start gap-2 inline-flex px-3.5 py-3 focus:ring focus:ring-ever-primary focus:ring-opacity-5 focus:border-ever-primary min-h-[200px]"
                readOnly={!editMode}
              />
            )}
          </div>
        </div>
      </div>
    );
  }
);
