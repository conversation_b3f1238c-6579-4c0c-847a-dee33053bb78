import { DotsVerticalIcon } from "@heroicons/react/outline";
import { Dropdown, Menu } from "antd";
import { formatInTimeZone } from "date-fns-tz";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { Card, EverLoader, Modal, showMessage } from "~/Components";
import { DeleteIconRed } from "~/icons";
import { ENTITY_TYPE } from "../enums";
import {
  getEntityId,
  getEntityNameFromMeta,
} from "./entity-properties/EntityCommon";
import { lostAstronaut } from "~/images";
import { EverButton } from "~/Components/ever-button/EverButton";
import customerStore from "../store";

const deleteEntityId = ({
  clientId,
  entityId,
  entityType,
  entityList,
  setEntityList,
  reRender,
  agentStudio<PERSON>pi,
}) => {
  const response = agentStudioApi.deleteEntity(clientId, entityId, entityType);
  if (response) {
    setEntityList(entityList.filter((entity) => entity[entityId] !== entityId));
    showMessage(`${entityType} deleted successfully`, {
      type: "success",
    });
    setTimeout(() => {
      reRender();
    }, 500);
  } else {
    showMessage("Something went wrong", { type: "danger" });
  }
};

const DeleteModalConfirmationModal = observer(
  ({
    deleteModalOpen,
    setDeleteModalOpen,
    entityType,
    clientId,
    entityId,
    entityList,
    setEntityList,
    reRender,
  }) => {
    const [dependencies, setDependencies] = useState([]);
    const agentStudioApi = useAgentStudioApi();

    useEffect(() => {
      if (!clientId) return;
      agentStudioApi
        .getDependenciesForEntities(entityType, entityId, clientId)
        .then((res) => {
          setDependencies(res);
        });
    }, []);
    return (
      <Modal
        open={deleteModalOpen}
        className="min-h-[200px] min-w-[400px] max-w-[600px]"
      >
        <Modal.Title onClose={() => setDeleteModalOpen(false)}>
          Delete Confirmation
        </Modal.Title>
        <Modal.Content className="flex flex-col items-center">
          <div>
            {/* Below 0 represent client_id=0 for technical skills */}
            {dependencies?.listOfEntityDependencies?.length === 0 &&
            clientId !== 0 ? (
              <></>
            ) : clientId === 0 ? (
              <div className="text-md text-red-500 pt-1">
                Deleting a technical skill may result in loss of mapping in
                agents across clients.
              </div>
            ) : (
              <div className="text-md text-red-500 pt-1">
                This {entityType} is used in the following{" "}
                {entityType === ENTITY_TYPE.AGENT
                  ? "collections"
                  : entityType === ENTITY_TYPE.SKILL
                  ? "agents"
                  : ""}
                : {dependencies?.listOfEntityDependencies?.join(", ")}
              </div>
            )}
            Are you sure you want to delete this {entityType}?
          </div>
          <EverButton
            onClick={() => {
              deleteEntityId({
                clientId,
                entityId,
                entityType,
                entityList,
                setEntityList,
                reRender,
                agentStudioApi,
              });
              setDeleteModalOpen(false);
            }}
            className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 !bg-ever-primary hover:!bg-ever-primary text-white"
          >
            Delete
          </EverButton>
        </Modal.Content>
      </Modal>
    );
  }
);

export const HelperRemovalConfirmationModal = observer(
  ({
    modalOpen,
    setModalOpen,
    entityType,
    clientId,
    dependentSkills,
    onConfirm,
  }) => {
    return (
      <Modal
        open={modalOpen}
        className="min-h-[200px] min-w-[400px] max-w-[600px]"
      >
        <Modal.Title onClose={() => setModalOpen(false)}>
          Remove Helper Confirmation
        </Modal.Title>
        <Modal.Content className="flex flex-col items-center">
          <div>
            {dependentSkills?.length > 0 ? (
              <div className="text-md text-red-500 pt-1">
                This {entityType.toLowerCase()} is currently used as a helper in the following skills:
                <ul className="list-disc pl-5 mt-2">
                  {dependentSkills.map((skill, index) => (
                    <li key={index}>{skill.skillName} - {skill.version}</li>
                  ))}
                </ul>
              </div>
            ) : (
              <></>
            )}
            <div className="mt-4 text-center">
              Are you sure you want to remove helper status from this {entityType.toLowerCase()}?
            </div>
          </div>
          <EverButton
            onClick={() => {
              onConfirm();
              setModalOpen(false);
            }}
            className="w-24 rounded-xl text-sm font-medium h-10 px-4 py-2 mt-4 !bg-ever-primary hover:!bg-ever-primary text-white"
          >
            Confirm
          </EverButton>
        </Modal.Content>
      </Modal>
    );
  }
);

export const ListItems = observer(({ clientId, entityType, reRender }) => {
  const history = useHistory();
  const agentStudioApi = useAgentStudioApi();

  const [entityList, setEntityList] = useState([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedEntityId, setSelectedEntityId] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    agentStudioApi.getAllEntities(clientId, entityType).then((res) => {
      setEntityList(res.entitiesMetaData);
      setLoading(false);
      if (entityType === ENTITY_TYPE.AGENT) {
        customerStore.setAllAgents(res.entitiesMetaData);
      } else if (entityType === ENTITY_TYPE.SKILL) {
        customerStore.setAllSkills(res.entitiesMetaData);
      } else if (entityType === ENTITY_TYPE.COLLECTION) {
        customerStore.setAllCollections(res.entitiesMetaData);
      }
    });
  }, []);

  const menu = (entityId) => (
    <Menu>
      <Menu.Item key="delete">
        <div
          className="w-16 h-full flex justify-center items-center"
          onClick={() => {
            setDeleteModalOpen(true);
            setSelectedEntityId(entityId);
          }}
        >
          <DeleteIconRed className="w-4 h-4 text-red-500 mr-3" />
          <div className="text-sm text-red-500">Delete</div>
        </div>
      </Menu.Item>
    </Menu>
  );
  if (entityList.length === 0) {
    return (
      <div className="h-[75vh] w-full flex flex-col items-center justify-center gap-4 min-h-0">
        {loading ? (
          <EverLoader />
        ) : (
          <>
            <img src={lostAstronaut} />
            <div className="flex flex-col gap-4">
              <div className="text-ever-base-content">
                Start creating your first {entityType}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }
  return (
    <div className="grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-3 2xl:grid-cols-4 gap-4">
      {deleteModalOpen && (
        <DeleteModalConfirmationModal
          deleteModalOpen={deleteModalOpen}
          setDeleteModalOpen={setDeleteModalOpen}
          entityType={entityType}
          clientId={clientId}
          entityId={selectedEntityId}
          entityList={entityList}
          setEntityList={setEntityList}
          reRender={reRender}
        />
      )}
      {entityList?.map((entity) => (
        <div
          onClick={() => {
            if (entityType === ENTITY_TYPE.AGENT) {
              history.push(
                `/admin-ui/agent-studio/${clientId}/${getEntityId(
                  entityType,
                  entity
                )}/${ENTITY_TYPE.AGENT}`
              );
            } else if (entityType === ENTITY_TYPE.SKILL) {
              history.push(
                `/admin-ui/agent-studio/${clientId}/${getEntityId(
                  entityType,
                  entity
                )}/${ENTITY_TYPE.SKILL}`
              );
            } else if (entityType === ENTITY_TYPE.COLLECTION) {
              history.push(
                `/admin-ui/agent-studio/${clientId}/${getEntityId(
                  entityType,
                  entity
                )}/${ENTITY_TYPE.COLLECTION}`
              );
            }
          }}
          key={getEntityId(entityType, entity)}
        >
          <Card className="flex shadow-sm rounded-lg w-full justify-center items-center p-2 divide-transparent hover:bg-ever-base-100 hover:shadow-lg hover:border-ever-primary hover:border hover:border-solid cursor-pointer transition-all border border-solid duration-300 ease-in-out hover:scale-105 transform-gpu">
            <div className="flex flex-col p-1 gap-4 justify-between w-full">
              <div className="flex justify-between w-full">
                <div className="flex flex-col w-full gap-1.5">
                  <div className="text-ever-base-content text-base font-medium">
                    {getEntityNameFromMeta(entity, entityType)}
                  </div>
                  <div className="text-ever-base-content-mid text-xs">
                    Created on{" "}
                    {formatInTimeZone(
                      entity.createdDate,
                      "IST",
                      `${"MMM d, yyyy"} 'at' ${"h:mm a"}`
                    )}
                  </div>
                </div>
                <div className="flex justify-end">
                  <div
                    className="pt-1 px-1 hover:bg-ever-base-100 hover:rounded-lg h-[30px]"
                    onClick={(event) => {
                      setSelectedEntityId(getEntityId(entityType, entity));
                      event.stopPropagation();
                    }}
                  >
                    <Dropdown
                      overlay={menu(getEntityId(entityType, entity))}
                      trigger={["click"]}
                    >
                      <DotsVerticalIcon className="h-5 w-5 text-ever-base-content-mid" />
                    </Dropdown>
                  </div>
                </div>
              </div>
              <div className="text-ever-base-content-mid text-xs">
                {entity.isPublished
                  ? `published: ${entity.version}`
                  : `latest: ${entity.version}`}
              </div>
            </div>
          </Card>
        </div>
      ))}
    </div>
  );
});
