import { observer } from "mobx-react";
import { useState, useMemo, useEffect } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { Tabs } from "antd";
import { CONTEXT_TYPES, ENTITY_TYPE, HOME_SCREEN_OPTIONS } from "../enums";
import { ListItems } from "./ListItems";
import { ListClients } from "./ListClients";
import { PlusCircleIcon } from "@heroicons/react/outline";
import { EverButton } from "~/Components/ever-button/EverButton";
import { Search } from "~/Components";
import customerStore from "../store";
import ContextScreen from "./context";

export const ListHome = observer(() => {
  const history = useHistory();
  const [reRenderHome, setReRenderHome] = useState(false);
  const [filteredClients, setFilteredClients] = useState("");

  const reRender = () => {
    setReRenderHome(!reRenderHome);
  };

  useEffect(() => {
    if (customerStore.selectedOptionHome === null) {
      customerStore.setSelectedOptionHome(HOME_SCREEN_OPTIONS.CLIENTS);
    }
  }, []);

  const items = [
    {
      key: HOME_SCREEN_OPTIONS.CLIENTS,
      label: (
        <div className="text-ever-base-content text-md">
          {HOME_SCREEN_OPTIONS.CLIENTS}
        </div>
      ),
      children: <ListClients filteredClients={filteredClients} />,
    },
    {
      key: HOME_SCREEN_OPTIONS.TECHNICAL_SKILLS,
      label: (
        <div className="text-ever-base-content text-md">
          {HOME_SCREEN_OPTIONS.TECHNICAL_SKILLS}
        </div>
      ),
      children: (
        <ListItems
          clientId={0}
          entityType={ENTITY_TYPE.SKILL}
          reRender={reRender}
        />
      ),
    },
    {
      key: HOME_SCREEN_OPTIONS.GLOBAL_CONTEXT,
      label: (
        <div className="text-ever-base-content text-md">
          {HOME_SCREEN_OPTIONS.GLOBAL_CONTEXT}
        </div>
      ),
      children: <ContextScreen contextType={CONTEXT_TYPES.GLOBAL_CONTEXT} />,
    },
  ];
  // Above 0 represent client_id=0
  const slot = useMemo(() => {
    return {
      right:
        customerStore.selectedOptionHome === HOME_SCREEN_OPTIONS.CLIENTS ? (
          <Search
            placeholder="Search customer"
            className="w-[400px] pb-[10px]"
            onChange={(e) => {
              setFilteredClients(e.target.value);
            }}
            inputClassName="!bg-ever-primary-content !border-ever-base-300 focus:!border-ever-primary focus:!shadow-lg placeholder-ever-base-500 text-ever-base-500"
            searchIconClassName="text-ever-base-400"
          />
        ) : (
          <div
            className="w-50 h-5 px-3.5 py-2 pr-2 bg-ever-primary-content rounded-lg justify-start items-center gap-2 inline-flex cursor-pointer"
            onClick={() => {
              history.push(`/admin-ui/agent-studio/new/0/${ENTITY_TYPE.SKILL}`);
            }}
          >
            {/* Above 0 represent client_id=0 */}
            {customerStore.selectedOptionHome !==
              HOME_SCREEN_OPTIONS.GLOBAL_CONTEXT && (
              <div className="justify-start items-center gap-2 flex">
                <EverButton
                  color="base"
                  prependIcon={<PlusCircleIcon className="h-5 w-5" />}
                >
                  Create New
                </EverButton>
              </div>
            )}
          </div>
        ),
    };
  }, [customerStore.selectedOptionHome]);

  return (
    <div className="flex flex-col px-4 pt-2 h-screen bg-ever-primary-content">
      <h1 className="text-ever-base-content text-lg font-semibold pl-4">
        Agent Studio
      </h1>
      <div className="px-4" key={reRenderHome}>
        <Tabs
          tabBarExtraContent={slot}
          defaultActiveKey={customerStore.selectedOptionHome}
          items={items}
          onChange={(key) => {
            customerStore.setSelectedOptionHome(key);
          }}
        />
      </div>
    </div>
  );
});

export default ListHome;
