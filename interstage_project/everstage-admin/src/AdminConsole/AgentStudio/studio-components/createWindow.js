import { PlusCircleIcon } from "@heroicons/react/outline";
import { observer } from "mobx-react";
import { useMemo, useEffect, useState } from "react";
import {
  useHistory,
  useParams,
} from "react-router-dom/cjs/react-router-dom.min";

import { Tabs } from "antd";
import { EverButton } from "~/Components/ever-button/EverButton";
import { useMetaInformation } from "..";
import { CONTEXT_TYPES, ENTITY_PLURAL, ENTITY_TYPE } from "../enums";
import CustomerStore from "../store";
import { ListItems } from "./ListItems";
import ContextScreen from "./context";

const HomeScreen = observer(() => {
  const history = useHistory();
  const { clientId } = useParams();
  const { selectedClient, selectedEntity } = CustomerStore;
  const [renderNow, setRenderNow] = useState(true);

  const reRender = () => {
    setRenderNow(!renderNow);
  };

  useEffect(() => {
    if (clientId === "0") {
      history.push(`/admin-ui/agent-studio`);
    }
    if (selectedEntity === null) {
      CustomerStore.setSelectedEntity(ENTITY_TYPE.SKILL);
    }
  }, []);

  if (Object.keys(selectedClient).length === 0) {
    useMetaInformation();
  }

  const items = [
    {
      key: ENTITY_TYPE.SKILL,
      label: (
        <div className="text-ever-base-content text-md">
          {ENTITY_PLURAL.SKILLS}
        </div>
      ),
      children: (
        <ListItems
          clientId={clientId}
          entityType={ENTITY_TYPE.SKILL}
          reRender={reRender}
        />
      ),
    },
    {
      key: ENTITY_TYPE.AGENT,
      label: (
        <div className="text-ever-base-content text-md">
          {ENTITY_PLURAL.AGENTS}
        </div>
      ),
      children: (
        <ListItems
          clientId={clientId}
          entityType={ENTITY_TYPE.AGENT}
          reRender={reRender}
        />
      ),
    },
    {
      key: ENTITY_TYPE.COLLECTION,
      label: (
        <div className="text-ever-base-content text-md">
          {ENTITY_PLURAL.COLLECTIONS}
        </div>
      ),
      children: (
        <ListItems
          clientId={clientId}
          entityType={ENTITY_TYPE.COLLECTION}
          reRender={reRender}
        />
      ),
    },
    {
      key: CONTEXT_TYPES.CLIENT_CONTEXT,
      label: (
        <div className="text-ever-base-content text-md">Client Context</div>
      ),
      children: (
        <ContextScreen
          clientId={clientId}
          contextType={CONTEXT_TYPES.CLIENT_CONTEXT}
        />
      ),
    },
  ];

  const slot = useMemo(() => {
    return {
      right: selectedEntity !== CONTEXT_TYPES.CLIENT_CONTEXT && (
        <div
          className="w-50 h-5 px-3.5 py-2 pr-2 bg-ever-primary-content rounded-lg justify-start items-center gap-2 inline-flex cursor-pointer"
          onClick={() => {
            history.push(
              `/admin-ui/agent-studio/new/${clientId}/${selectedEntity}`
            );
          }}
        >
          <div className="justify-start items-center gap-2 flex">
            <EverButton
              color="base"
              prependIcon={<PlusCircleIcon className="h-5 w-5" />}
            >
              Create New {selectedEntity}
            </EverButton>
          </div>
        </div>
      ),
    };
  }, [selectedEntity]);

  return (
    <div className="flex flex-col px-4 pt-2 h-screen bg-ever-primary-content">
      <h1 className="text-ever-base-content text-lg font-semibold pl-4">
        Agent Studio
      </h1>
      <div className="px-4" key={renderNow}>
        <Tabs
          tabBarExtraContent={slot}
          defaultActiveKey={selectedEntity}
          items={items}
          onChange={(key) => {
            CustomerStore.setSelectedEntity(key);
          }}
        />
      </div>
    </div>
  );
});

export default HomeScreen;
