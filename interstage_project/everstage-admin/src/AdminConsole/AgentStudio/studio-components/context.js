import { EverButton } from "~/Components/ever-button/EverButton";
import { observer } from "mobx-react";
import { LabelComponent } from "./common-components/GeneralComponents";
import { CONTEXT_TYPES } from "../enums";
import { useState, useCallback, useEffect } from "react";
import { useAgentStudioApi } from "~/Api/AgentStudioApis";
import { EverLoader } from "~/Components/ever-loader/EverLoader";

const CONTEXT_LABEL_MAP = {
  [CONTEXT_TYPES.GLOBAL_CONTEXT]: "Everstage Context",
  [CONTEXT_TYPES.CLIENT_CONTEXT]: "Client Context",
};

const CONTEXT_TOOLTIP_MAP = {
  [CONTEXT_TYPES.GLOBAL_CONTEXT]:
    "Company-wide knowledge and internal standards used by EverAI.",
  [CONTEXT_TYPES.CLIENT_CONTEXT]:
    "Client-specific rules and customizations that guide EverAI's behavior for a particular client account.",
};

const ContextScreen = observer(({ contextType, clientId = null }) => {
  const [corePrompt, setCorePrompt] = useState("");
  const [initialPrompt, setInitialPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [charCount, setCharCount] = useState(0);
  const agentStudioApi = useAgentStudioApi();

  const getContextData = useCallback(async () => {
    const contextData = await agentStudioApi.getContextData(
      clientId,
      contextType
    );
    setCorePrompt(contextData.context);
    setInitialPrompt(contextData.context);
    setCharCount(contextData.context.length);
  }, [contextType, clientId]);

  useEffect(() => {
    getContextData();
  }, []);

  async function refetchContextData() {
    await getContextData();
  }

  const handleTextChange = (e) => {
    setCorePrompt(e.target.value);
    setCharCount(e.target.value.length);
  };

  async function upsertContextData() {
    setIsLoading(true);
    await agentStudioApi.upsertContextData(contextType, clientId, corePrompt);
    await refetchContextData();
    setIsLoading(false);
  }

  const isChanged = corePrompt !== initialPrompt;

  return isLoading ? (
    <div className="flex h-screen w-full items-center justify-center">
      <EverLoader indicatorType="spinner" />
    </div>
  ) : (
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <LabelComponent
          name={CONTEXT_LABEL_MAP[contextType]}
          tooltip={CONTEXT_TOOLTIP_MAP[contextType]}
        />
      </div>
      <div className="flex-1 min-h-0 h-[200px] relative">
        <textarea
          name="corePrompt"
          value={corePrompt}
          onChange={handleTextChange}
          className="w-full h-[600px] bg-ever-primary-content rounded-lg shadow-sm border border-ever-base-300 border-solid text-ever-base-content text-sm font-normal leading-tight justify-start items-start gap-2 inline-flex px-3.5 py-3 focus:ring focus:ring-ever-primary focus:ring-opacity-5 focus:border-ever-primary"
          // readOnly={!editMode}
        />
        <div className="absolute top-2 right-2 text-xs text-ever-base-content">
          {charCount} characters
        </div>
      </div>
      {isChanged && (
        <div className="flex flex-row gap-2 justify-end mt-4">
          <EverButton
            color="primary"
            onClick={upsertContextData}
            loading={isLoading}
          >
            Save
          </EverButton>
          <EverButton
            color="base"
            disabled={isLoading}
            onClick={() => setCorePrompt(initialPrompt)}
          >
            Cancel
          </EverButton>
        </div>
      )}
    </div>
  );
});

export default ContextScreen;
