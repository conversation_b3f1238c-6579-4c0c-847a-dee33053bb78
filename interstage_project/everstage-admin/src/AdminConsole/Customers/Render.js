import {
  Card,
  Button,
  Search,
  Ribbon,
  useLoader,
  Select,
  RadioGroup,
  Table,
  Badge,
} from "Components";
import { observer } from "mobx-react";
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import CustomerDetails from "./CustomerDetails";
import NewCustomerDialog from "./NewCustomerDialog";
import {
  EyeIcon,
  ChevronRightIcon,
  TrashIcon,
  LayoutGridIcon,
  ListIcon,
} from "@everstage/evericons/outlined";
import DeleteCustomerModal from "./DeleteCustomerModal";
import { useAuthStore } from "GlobalStores/AuthStore";
import InvalidateCacheModal from "./InvalidateCacheModal";
import { NOTIFICATION_TYPE } from "Enums";
import { formatDate } from "Utils";
import { isEmpty } from "lodash";
import { getAllUserRoles } from "Api/userRoleService";
import { EverAvatar } from "~/Components/EverAvatar";
import { twMerge } from "tailwind-merge";

const SortOptions = [
  { id: "ascending", name: "Filter(A-Z)" },
  { id: "desending", name: "<PERSON>lter(Z-A)" },
];

const CustomerOptions = [
  { name: "All", disabled: false },
  { name: "Live", disabled: false },
  { name: "Deleted", disabled: false },
];

const Image = React.memo(function Image({ customer }) {
  return (
    <EverAvatar
      src={`${customer.logoUrl}?ts=${new Date().getTime()}`}
      name={customer.name}
      size="60"
    />
  );
});

const Render = observer((props) => {
  const { customers, customersStore, loading, refetchCustomers } = props;
  const [showNC, setShowNC] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInvalidateCacheModal, setShowInvalidateCacheModal] =
    useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [sortvalue, setSortValue] = useState(SortOptions[0]);
  const [customerType, setCustomerType] = useState(CustomerOptions[0]);
  const [isGridView, setIsGridView] = useState(true);
  const { showHideLoader } = useLoader();
  const [allUserRoles, setAllUserRoles] = useState([]);
  const authStore = useAuthStore();

  const columns = [
    {
      key: "logoUrl",
      label: "Logo",
      align: "center",
      render: (value, data) => <Image customer={data} />,
    },
    {
      key: "name",
      label: "Customer Name",
      minWidth: 200,
      align: "center",
    },
    {
      key: "metaInfo",
      label: "Creation Date",
      render: (value) => (
        <span>{value && value.createdAt && formatDate(value.createdAt)}</span>
      ),
      align: "center",
    },
    {
      key: "status",
      label: "Status",
      render: (value, data) => <Badge {...getBadgeProps(data)} />,
      align: "center",
    },
    {
      key: "action",
      label: "Action",
      render: (value, data) => (
        <div>
          <div className="-mt-px flex divide-x divide-ever-base-200">
            <div className="w-0 flex-1 flex">
              {authStore.permissions.isAdminUiManageable && (
                <div
                  onClick={() => {
                    if (!data?.isDeleted) {
                      setSelectedCustomer(data);
                      setShowDetails(true);
                    }
                  }}
                  className="cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-bl-lg hover:text-ever-base-content-mid"
                >
                  <EyeIcon className="w-5 h-5" />
                </div>
              )}
            </div>
            <div
              className={`w-0 flex-1 flex ${
                data.metaInfo?.deleteNotified &&
                !data.isDeleted &&
                !isUserApprovedForDelete(data)
                  ? "bg-ever-base-100"
                  : ""
              }`}
            >
              {authStore.permissions.isAdminUiManageable && (
                <>
                  {isUserApprovedForDelete(data) ? (
                    <div
                      onClick={() => {
                        if (!data.isDeleted) {
                          setSelectedCustomer(data);
                          setShowDeleteModal(true);
                        }
                      }}
                      className="cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-bl-lg hover:text-ever-base-content-mid border-solid border-x-1 border-y-0 border-x-ever-base-200"
                    >
                      {data.isDeleted ? (
                        <TrashIcon className="w-5 h-5 text-ever-base-content-mid" />
                      ) : (
                        <TrashIcon className="w-5 h-5 text-ever-base-content" />
                      )}
                    </div>
                  ) : (
                    <div
                      onClick={() => {
                        if (!data.isDeleted && !data.metaInfo?.deleteNotified) {
                          setSelectedCustomer(data);
                          setShowDeleteModal(true);
                        }
                      }}
                      className="cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-bl-lg hover:text-ever-base-content-mid border-solid border-x-1 border-y-0 border-x-ever-base-200"
                    >
                      {data.isDeleted || data.metaInfo?.deleteNotified ? (
                        <TrashIcon className="w-5 h-5 text-ever-base-content-mid" />
                      ) : (
                        <TrashIcon className="w-5 h-5 text-ever-base-content" />
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
            <div className="-ml-px w-0 flex-1 flex">
              <Link
                to={
                  !data?.isDeleted
                    ? `/admin-ui/customers-edit/${data.clientId}`
                    : "/admin-ui/customers"
                }
                className="relative w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-br-lg hover:text-ever-base-content-mid"
              >
                <ChevronRightIcon className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      ),
      minWidth: 100,
      align: "center",
    },
  ];
  useEffect(() => {
    if (selectedCustomer) {
      const clientId = selectedCustomer.clientId;
      getAllUserRoles(authStore.accessToken, clientId)
        .then((userRoles) => {
          if (!isEmpty(userRoles)) {
            setAllUserRoles(userRoles);
          } else {
            setAllUserRoles([]);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    }
  }, [selectedCustomer]);
  useEffect(() => {
    showHideLoader(loading);
  }, [loading]);

  const onChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCloseDeletePopup = () => {
    setShowDeleteModal(false);
    setSelectedCustomer(null);
  };

  const getBadgeProps = (customer) => {
    let label = "";
    let type = "";

    if (isEmpty(customer)) {
      return { label, type };
    }

    if (customer.isDeleted) {
      label = "Deleted";
      type = NOTIFICATION_TYPE.ERROR;
    } else if (customer.metaInfo?.deleteNotified) {
      label = customer.metaInfo?.isLive
        ? "Delete Notified (Live)"
        : "Delete Notified";
      type = NOTIFICATION_TYPE.WARNING;
    } else if (customer.metaInfo?.isLive) {
      label = "Live";
      type = NOTIFICATION_TYPE.SUCCESS;
    }
    return {
      label,
      type,
    };
  };

  const getRibbonProps = (customer) => {
    let text = "";
    let type = "";

    if (isEmpty(customer)) {
      return { text, type };
    }

    if (customer.isDeleted) {
      text = "Deleted";
      type = NOTIFICATION_TYPE.ERROR;
    } else if (customer.metaInfo?.deleteNotified) {
      text = customer.metaInfo?.isLive
        ? "Delete Notified (Live)"
        : "Delete Notified";
      // WARNING type notification shows a ribbon in yellow color.
      type = NOTIFICATION_TYPE.WARNING;
    } else if (customer.metaInfo?.isLive) {
      text = "Live";
      type = NOTIFICATION_TYPE.SUCCESS;
    }
    return {
      text,
      type,
    };
  };

  const filterdCustomers = customers
    ?.filter(
      (x) =>
        searchTerm === "" ||
        x.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter((x) =>
      customerType.name === "Live"
        ? x.metaInfo?.isLive
        : customerType.name === "Deleted"
        ? x.isDeleted
        : x
    )
    .sort((a, b) => {
      const x = a.name.toUpperCase(),
        y = b.name.toUpperCase();
      if (sortvalue.id === "ascending") return x === y ? 0 : x > y ? 1 : -1;
      else return x === y ? 0 : x > y ? -1 : 1;
    });

  const isUserApprovedForDelete = (customer) => {
    const approveUsers = customer?.clientFeatures?.deleteApprovers ?? [];
    return approveUsers.some((email) => email === authStore.email);
  };

  return (
    <div className="flex flex-col">
      <NewCustomerDialog
        open={showNC}
        onClose={() => setShowNC(false)}
        customersStore={customersStore}
      />
      {showDetails && (
        <CustomerDetails
          customer={selectedCustomer}
          open={showDetails}
          onClose={() => setShowDetails(false)}
          customersStore={customersStore}
          userRoles={allUserRoles}
        />
      )}
      <div className="flex flex-row items-center gap-6 mx-6 flex-wrap sm:flex-nowrap">
        <Search
          placeholder="Search Customers"
          className="flex-grow"
          onChange={onChange}
        />
        <RadioGroup
          options={CustomerOptions}
          onChange={setCustomerType}
          value={customerType}
        />
        <Button
          type="ghost"
          color="base"
          size="small"
          title={isGridView ? "Table View" : "Grid View"}
          onClick={() => setIsGridView(!isGridView)}
        >
          {isGridView ? (
            <ListIcon className="w-5 h-5" aria-hidden="true" />
          ) : (
            <LayoutGridIcon className="w-5 h-5" aria-hidden="true" />
          )}
        </Button>
        <Select
          options={SortOptions}
          selected={sortvalue}
          setSelected={setSortValue}
        />
        <div className="flex">
          <Button size="small" onClick={() => setShowNC(true)}>
            Create a new Customer
          </Button>
          {/*<Button
            onClick={() => setShowInvalidateCacheModal(true)}
            className="h-9"
          >
            Invalidate Cache
          </Button>*/}
        </div>
      </div>
      {!loading ? (
        <>
          {isGridView ? (
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 p-6">
              {filterdCustomers?.map((customer) => (
                <Ribbon {...getRibbonProps(customer)} key={customer.name}>
                  <Card
                    name={`${customer.name}`}
                    className={twMerge(
                      "divide-y divide-ever-base-200 rounded-lg flex w-full flex-col justify-between p-0",
                      customer?.isDeleted ? "blur-sm" : ""
                    )}
                  >
                    <div className="flex justify-stretch gap-4 px-6 pt-6 pb-2 border-0 border-b border-solid border-b-ever-base-300">
                      <Image customer={customer} />
                      <div className="flex flex-col gap-1 grow max-w-[75%]">
                        <div className="flex items-center">
                          <div className="max-w-full text-ever-base-content text-xl font-medium truncate">
                            {`${customer.name}`}
                          </div>
                          {customer.metaInfo && customer.metaInfo.isTest && (
                            <span className="flex-shrink-0 inline-block px-2 py-0.5 text-pruple-800 text-xs font-medium bg-purple-100 rounded-full">
                              Test
                            </span>
                          )}
                        </div>
                        <div className="text-ever-base-content-mid text-base truncate">
                          Client ID: {customer.clientId}
                        </div>
                        <div className="mt-6 text-ever-base-content-mid text-xs truncate flex justify-end">
                          {customer.metaInfo &&
                            customer.metaInfo.createdAt &&
                            formatDate(customer.metaInfo.createdAt)}
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="-mt-px flex divide-x divide-ever-base-200">
                        <div className="w-0 flex-1 flex">
                          {authStore.permissions.isAdminUiManageable && (
                            <div
                              onClick={() => {
                                if (!customer?.isDeleted) {
                                  setSelectedCustomer(customer);
                                  setShowDetails(true);
                                }
                              }}
                              className="cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-bl-lg hover:text-ever-base-content-mid"
                            >
                              <EyeIcon className="w-5 h-5" />
                            </div>
                          )}
                        </div>
                        <div
                          // Making the delete region "gray" when the client is already
                          // notified for delete and the user is unable to actually
                          // delete the client.
                          className={`w-0 flex-1 flex ${
                            customer.metaInfo?.deleteNotified &&
                            !customer.isDeleted &&
                            !isUserApprovedForDelete(customer)
                              ? "bg-ever-base-100"
                              : ""
                          }`}
                        >
                          {/* When deleting by approved users */}
                          {authStore.permissions.isAdminUiManageable && (
                          <>
                          {isUserApprovedForDelete(customer) ? (
                            <div
                              onClick={() => {
                                if (!customer.isDeleted) {
                                  setSelectedCustomer(customer);
                                  setShowDeleteModal(true);
                                }
                              }}
                              className="cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-bl-lg hover:text-ever-base-content-mid border-solid border-x-1 border-y-0 border-x-ever-base-200"
                            >
                              {/*
                                  If the user is an approver of delete, they can delete
                                  the client irrespective of notified for delete or not.
                                */}
                              {customer.isDeleted ? (
                                <TrashIcon className="w-5 h-5 text-ever-base-content-mid" />
                              ) : (
                                <TrashIcon className="w-5 h-5 text-ever-base-content" />
                              )}
                            </div>
                          ) : (
                            <div
                              onClick={() => {
                                if (
                                  !customer.isDeleted &&
                                  !customer.metaInfo?.deleteNotified
                                ) {
                                  setSelectedCustomer(customer);
                                  setShowDeleteModal(true);
                                }
                              }}
                              className="cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-bl-lg hover:text-ever-base-content-mid border-solid border-x-1 border-y-0 border-x-ever-base-200"
                            >
                              {/*
                                  If the user is not an approver of delete and already notified
                                  for delete, make the delete button disabled by coloring it as gray.
                                  Else color as yellow.
                                */}
                              {customer.isDeleted ||
                              customer.metaInfo?.deleteNotified ? (
                                <TrashIcon className="w-5 h-5 text-ever-base-content-mid" />
                              ) : (
                                <TrashIcon className="w-5 h-5 text-ever-base-content" />
                              )}
                            </div>
                            )}
                         </>
                         )}
                        </div>
                        <div className="-ml-px w-0 flex-1 flex">
                          <Link
                            to={
                              !customer?.isDeleted
                                ? `/admin-ui/customers-edit/${customer.clientId}`
                                : "/admin-ui/customers"
                            }
                            className="relative w-0 flex-1 inline-flex items-center justify-center py-4 text-sm  font-medium border border-transparent rounded-br-lg hover:text-ever-base-content-mid"
                          >
                            <ChevronRightIcon className="w-5 h-5" />
                          </Link>
                        </div>
                      </div>
                    </div>
                  </Card>
                </Ribbon>
              ))}
            </div>
          ) : (
            <div className="p-6">
              <Table
                className={{
                  table: "w-full",
                  headCell: "py-3",
                  search: "w-80 h-8",
                }}
                columns={columns}
                dataSource={filterdCustomers}
                loading={loading}
              />
            </div>
          )}
        </>
      ) : null}
      <InvalidateCacheModal
        open={showInvalidateCacheModal}
        onClose={() => setShowInvalidateCacheModal(false)}
      />
      <DeleteCustomerModal
        open={showDeleteModal}
        selectedCustomer={selectedCustomer}
        onClose={handleCloseDeletePopup}
        onSuccess={refetchCustomers}
        isUserApprovedForDelete={isUserApprovedForDelete(selectedCustomer)}
      />
    </div>
  );
});

export default Render;
