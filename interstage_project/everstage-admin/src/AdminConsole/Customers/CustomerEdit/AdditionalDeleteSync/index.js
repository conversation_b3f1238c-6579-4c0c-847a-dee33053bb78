import React, { useCallback, useEffect, useState } from "react";
import { observer } from "mobx-react";

import { format } from "date-fns";
import { NOTIFICATION_TYPE } from "Enums";
import { Button, showMessage, Table, Switch, Badge } from "Components";
import { useAuthStore } from "GlobalStores/AuthStore";
import { useParams } from "react-router-dom";
import { formatDate } from "Utils";
import {
  getAllAdditionalDeleteSyncStatusForClient,
  updateAdditionalDeleteSyncForClient,
  deleteAdditionalDeleteSync,
} from "Api/AdditionalDeleteSyncService";
import AddEditDeleteSyncModal from "./AddEditDeleteSyncModal";
import { MODES, INTERVAL_TYPES } from "./constants";

import { faTrash as deleteIcon } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const AdditioinalDeleteSyncPage = observer(() => {
  const [
    additionalDeleteSyncsFetchLoading,
    setAdditionalDeleteSyncsFetchLoading,
  ] = useState(false);
  const [allAdditionalDeleteSyncStatus, setAllAdditionalDeleteSyncStatus] =
    useState([]);
  const [showSyncModal, setShowSyncModal] = useState(false);
  const [syncModalMode, setSyncModalMode] = useState(null);
  const [editModeData, setEditModeData] = useState(null);
  const { accessToken, isValidEverstageAdminUser } = useAuthStore();
  const { id } = useParams();

  const hasPermissionToManageDeleteSync = isValidEverstageAdminUser;

  const getCronExpressionType = (cronExpression) => {
    if (
      cronExpression?.dayOfMonth === "*" &&
      cronExpression?.dayOfWeek === "*"
    ) {
      return INTERVAL_TYPES.DAILY;
    } else if (cronExpression?.dayOfWeek !== "*") {
      return INTERVAL_TYPES.WEEKLY;
    } else if (cronExpression?.dayOfMonth !== "*") {
      return INTERVAL_TYPES.MONTHLY;
    }
  };

  const columns = [
    {
      key: "clientId",
      label: "Client Id",
      align: "center",
    },
    {
      key: "clientName",
      label: "Client Name",
      minWidth: 150,
      align: "center",
    },
    {
      key: "intervalType",
      label: "Interval Type",
      align: "center",
      render: (_, data) => (
        <Badge {...getCronExpressionType(data.cronExpression)} />
      ),
    },
    {
      key: "dateChanged",
      label: "Last modified at (IST)",
      align: "center",
      render: (_, data) =>
        data.dateChanged === null
          ? "-"
          : formatDate(data.dateChanged, "MMM d yyyy, hh:mm aa"),
    },
    {
      key: "enabled",
      label: "Disabled/Enabled",
      align: "center",
      render: (value, data) => (
        <Switch
          id={data.clientId}
          enabled={data.enabled}
          toggleEnabled={() =>
            handleSyncStatusChange(
              value,
              data.clientId,
              data.name,
              data.cronExpression
            )
          }
        />
      ),
    },
    {
      key: "actions",
      label: "Actions",
      align: "center",
      render: (_, data) => (
        <div>
          <FontAwesomeIcon
            className={`h-5 w-5 ${
              hasPermissionToManageDeleteSync
                ? "text-red-500 cursor-pointer hover:text-red-700"
                : "text-ever-base-400 cursor-not-allowed"
            }`}
            icon={deleteIcon}
            onClick={() =>
              hasPermissionToManageDeleteSync && 
              handleDeleteDailySync(data.clientId, data.name)
            }
          />
        </div>
      ),
    },
  ];

  const handleDeleteDailySync = (clientId, taskName) => {
    if (!hasPermissionToManageDeleteSync) return;
    
    setAdditionalDeleteSyncsFetchLoading(true);
    deleteAdditionalDeleteSync(
      {
        clientId: clientId,
        taskName: taskName,
      },
      accessToken
    )
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");
        refreshAdditionalDeleteEtlSyncs();
      })
      .catch((error) => {
        console.error(error);
        setAdditionalDeleteSyncsFetchLoading(false);
        showMessage("Failed to delete daily sync", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      });
  };

  const handleSyncStatusChange = (
    value,
    clientId,
    taskName,
    cronExpression
  ) => {
    setAdditionalDeleteSyncsFetchLoading(true);
    updateAdditionalDeleteSyncForClient(
      {
        clientId: clientId,
        cronExpression: {
          minute: cronExpression.minute,
          hour: cronExpression.hour,
          dayOfMonth: cronExpression.dayOfMonth,
          dayOfWeek: cronExpression.dayOfWeek,
        },
        status: !value,
        taskName: taskName,
        verifyUpstream: false,
      },
      accessToken
    )
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");
        refreshAdditionalDeleteEtlSyncs();
      })
      .catch((error) => {
        console.error(error);
        setAdditionalDeleteSyncsFetchLoading(false);
        showMessage("Failed to change Additional delete syncs status", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      });
  };

  const fetchAdditionalDeleteSyncs = useCallback(() => {
    if (!id || !accessToken) return;
    setAdditionalDeleteSyncsFetchLoading(true);
    getAllAdditionalDeleteSyncStatusForClient(id, accessToken)
      .then(async (response) => {
        if (!response.ok) throw new Error("Error");
        const resp = await response.json();
        setAllAdditionalDeleteSyncStatus(resp.additionalDeleteSyncs);
      })
      .catch((error) => {
        console.error(error);
        showMessage("Could not fetch all daily syncs status", {
          type: NOTIFICATION_TYPE.ERROR,
        });
      })
      .finally(() => {
        setAdditionalDeleteSyncsFetchLoading(false);
      });
  }, [id, accessToken]);

  useEffect(() => {
    fetchAdditionalDeleteSyncs();
  }, [fetchAdditionalDeleteSyncs]);

  const refreshAdditionalDeleteEtlSyncs = () => {
    setAllAdditionalDeleteSyncStatus([]);
    fetchAdditionalDeleteSyncs();
  };

  return (
    <div className="px-4 pt-10">
      <div className="pb-4">
        <Button
          className={
            !hasPermissionToManageDeleteSync
              ? "opacity-50 cursor-not-allowed float-left"
              : "float-left"
          }
          title={
            hasPermissionToManageDeleteSync
              ? "Click to add additional delete sync for this client"
              : "Permission denied"
          }
          onClick={
            hasPermissionToManageDeleteSync
              ? () => {
                  setSyncModalMode(MODES.NEW);
                  setEditModeData(null);
                  setShowSyncModal(true);
                }
              : undefined
          }
          disabled={
            allAdditionalDeleteSyncStatus?.length || 
            !hasPermissionToManageDeleteSync
          }
        >
          Add New Entry
        </Button>
      </div>
      <div className="mt-10">
        <Table
          className={{ table: "w-full", headCell: "py-3", search: "w-80 h-8" }}
          loading={additionalDeleteSyncsFetchLoading}
          dataSource={allAdditionalDeleteSyncStatus}
          columns={columns}
        />
      </div>
      <AddEditDeleteSyncModal
        clientId={Number(id)}
        open={showSyncModal}
        mode={syncModalMode}
        editModeData={editModeData}
        onClose={() => setShowSyncModal(false)}
        onSuccess={() => refreshAdditionalDeleteEtlSyncs()}
        getCronExpressionType={getCronExpressionType}
      />
    </div>
  );
});

export default AdditioinalDeleteSyncPage;
