import { useHistory, useParams } from "react-router-dom";
import {
  Auth0Icon,
  IntegrationsIcon,
  ManualIntegrationIcon,
  SupportTeamIcon,
  GlobalSearchIcon,
  AdditionalDeleteIcon,
} from "icons";
import { useAuthStore } from "GlobalStores/AuthStore";

const cardList = [
  // {
  //   key: "connection",
  //   name: "Connection",
  //   icon: <ConnectionIcon />,
  // },
  {
    key: "auth0",
    name: "Auth0",
    icon: <Auth0Icon className="my-1 w-8 h-8" />,
    requiredPermission: "isAuth0UsersManageable",
  },
  {
    key: "integrations",
    name: "Integrations",
    icon: <IntegrationsIcon className="my-1 w-8 h-8" />,
    requiredPermission: "isIntegrationsManageable",
  },
  {
    key: "manual-integrations",
    name: "Manual Integration",
    icon: <ManualIntegrationIcon className="my-1 w-8 h-8" />,
    requiredPermission: "isIntegrationsManageable",
  },
  {
    key: "support-team",
    name: "Support Team",
    icon: <SupportTeamIcon className="my-1 w-8 h-8" />,
    requiredPermission: "isAdminUiManageable",
  },
  {
    key: "global-search",
    name: "Global Search",
    icon: <GlobalSearchIcon className="my-1 w-8 h-8" />,
    requiredPermission: "isAdminUiManageable",
  },
  {
    key: "additional-delete",
    name: "Additional Delete sync",
    icon: <AdditionalDeleteIcon className="my-1 w-8 h-8" />,
    requiredPermission: "isIntegrationsManageable",
  },
];

const CustomerEdit = (props) => {
  const history = useHistory();
  const { id } = useParams();
  const authStore = useAuthStore();

  const shouldRenderCard = (card) => {
    if (card.requiredPermission in authStore.permissions) {
      return authStore.permissions[card.requiredPermission];
    }
    return authStore.permissions.isAdminUiManageable;
  };

  return (
    <div className="flex flex-wrap gap-10 px-6 py-4">
      {cardList.map(
        (card) =>
          shouldRenderCard(card) && (
            <div
              className="flex items-center justify-center flex-col"
              key={card.key}
            >
              <div
                className="bg-ever-base w-52 py-16 flex items-center justify-center flex-col shadow-lg hover:shadow-xl rounded-lg cursor-pointer focus:shadow-xl"
                onClick={() =>
                  history.push(`/admin-ui/customers-edit/${id}/${card.key}`)
                }
              >
                {card.icon}
                <div className="mt-2 text-sm font-bold">{card.name}</div>
              </div>
            </div>
          )
      )}
    </div>
  );
};

export default CustomerEdit;
