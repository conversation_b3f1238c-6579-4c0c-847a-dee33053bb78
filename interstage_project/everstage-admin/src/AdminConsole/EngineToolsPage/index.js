import React, { useState, useEffect, useCallback } from "react";
import { ChipIcon } from "@heroicons/react/outline";
import SyncsList from "./SyncsList";
import SyncDetailsModal from "../EtlStatusPage/SyncDetailsModal";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import EtlConfigService from "~/Api/EtlConfigService";
import EtlStatusService from "~/Api/EtlStatusService";

const EngineToolsPage = () => {
  // AuthState
  const { accessToken } = useAuthStore();

  // Engine Sync States
  const [engineOptions, setEngineOptions] = useState([]);
  const [engineSubPlanMapping, setEngineSubPlanMapping] = useState({});
  const [selectedEngine, setSelectedEngine] = useState("all");
  const [allSyncs, setAllSyncs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // SyncDetailsModal state
  const [showSyncIdModal, setShowSyncIdModal] = useState(false);
  const [selectedSyncRow, setSelectedSyncRow] = useState(null);

  const getSubPlansForEngine = (engineId) => {
    return engineSubPlanMapping[engineId] || [];
  };

  const fetchEngineConfig = async () => {
    try {
      const configResponse = await EtlConfigService.getEtlConfig(
        accessToken, 
        'engine_sub_plan_map_config'
      );

      if (configResponse.ok) {
        const configData = await configResponse.json();
        const engineConfig = configData.config || {};
        
        setEngineSubPlanMapping(engineConfig);
        
        const engines = Object.keys(engineConfig).map(engineKey => {
          // Convert camelCase to readable format
          // e.g., "basicEngine" -> "BASIC ENGINE"
          // purely vibecoded.
          const readableName = engineKey
            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
            .replace(/Engine$/, '') // Remove "Engine" suffix if present
            .trim() // Remove leading/trailing spaces
            .toUpperCase() // Convert to uppercase
            + ' ENGINE'; // Add "ENGINE" suffix
            
          return {
            id: engineKey,
            name: readableName,
            key: engineKey,
            subPlans: engineConfig[engineKey] || []
          };
        });

        setEngineOptions(engines);
        return engineConfig;
        
      } else {
        throw new Error("Failed to fetch engine config from server");
      }
    } catch (error) {

      throw error;
    }
  };

  const fetchSyncs = async (subscriptionPlans = null) => {
    try {
      const syncsResponse = await EtlStatusService.getAllRunningEtlSyncs(
        accessToken,
        null,
        subscriptionPlans
      );

      if (syncsResponse.ok) {
        const data = await syncsResponse.json();
        const syncData = Array.isArray(data) ? data : [];
        
        setAllSyncs(syncData);
        return syncData;
        
      } else {
        setAllSyncs([]);
        return [];
      }
    } catch (error) {
      console.error("Error fetching syncs:", error);
      setAllSyncs([]);
      return [];
    }
  };

  const initializeData = async () => {
    try {
      setLoading(true);
      await fetchEngineConfig();
      await fetchSyncs();
    } catch (error) {
      console.error("Error initializing data:", error);
      setAllSyncs([]);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (accessToken) {
      initializeData();
    }
  }, [accessToken]);

  const handleEngineSelect = useCallback((engineId) => {
    setSelectedEngine(engineId);

    if (engineId === "all") {
      fetchSyncs();
    } else {
      const subscriptionPlans = getSubPlansForEngine(engineId);
      fetchSyncs(subscriptionPlans);
    }
  }, [engineSubPlanMapping]);

  const handleSearch = useCallback((query) => {
    setSearchQuery(query.toLowerCase());
  }, []);

  const filteredSyncs = React.useMemo(() => {
    return allSyncs.filter(sync => {
      if (!sync) return false;
      
      const matchesSearch = !searchQuery || 
        (sync.clientName && String(sync.clientName).toLowerCase().includes(searchQuery)) ||
        (sync.clientId && String(sync.clientId).toLowerCase().includes(searchQuery)) ||
        (sync.task && String(sync.task).toLowerCase().includes(searchQuery));
      
      return matchesSearch;
    });
  }, [allSyncs, searchQuery]);

  const handleSyncIdClick = (rowData) => {
    setSelectedSyncRow(rowData);
    setShowSyncIdModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-3">
            <ChipIcon className="h-6 w-6 text-blue-600 mr-2 transform scale-100 hover:scale-110 duration-300 ease-in-out" />
            <div className="group">
              <div className="text-xl font-bold text-gray-900 hidden group-hover:block">
                <span className="text-blue-600">Engine Tools</span>
                <span className="text-gray-900"> - Manage and monitor sync operations for different engine types</span>
              </div>
              <div className="text-xl font-bold text-gray-900 group-hover:hidden">
                Engine Tools
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="space-y-4">
          <SyncsList
            syncs={filteredSyncs}
            loading={loading}
            engineOptions={engineOptions}
            selectedEngine={selectedEngine}
            onEngineSelect={handleEngineSelect}
            searchQuery={searchQuery}
            onSearch={handleSearch}
            onSyncIdClick={handleSyncIdClick}
          />
        </div>
      </main>

      {showSyncIdModal && selectedSyncRow && (
        <SyncDetailsModal
          onClose={() => {
            setShowSyncIdModal(false);
            setSelectedSyncRow(null);
          }}
          data={selectedSyncRow}
        />
      )}
    </div>
  );
};

export default EngineToolsPage;
