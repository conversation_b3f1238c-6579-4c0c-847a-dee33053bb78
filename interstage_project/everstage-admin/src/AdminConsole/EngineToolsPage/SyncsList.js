import React from 'react';
import { 
  LoadingIcon
} from "@everstage/evericons/solid";
import { SearchIcon } from "@everstage/evericons/outlined";
import { ChevronDownIcon } from "@heroicons/react/outline";

const SyncsList = ({
  syncs,
  loading,
  engineOptions,
  selectedEngine,
  onEngineSelect,
  searchQuery,
  onSearch,
  onSyncIdClick
}) => {
  const getStatusColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800';
    const statusStr = String(status).toLowerCase();
    switch (statusStr) {
      case 'started':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'partially_failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (timeString) => {
    // Return the time string as-is from the API (e.g., "2025-04-17 15:54:42 IST")
    return timeString;
  };

  const formatElapsedTime = (elapsedTime) => {
    if (!elapsedTime) return 'N/A';
    try {
      const hours = parseFloat(elapsedTime);
      if (isNaN(hours)) return 'N/A';
      return hours.toFixed(2);
    } catch (error) {
      return 'N/A';
    }
  };

  const activeSyncs = syncs.filter(sync => {
    if (!sync) return false;
    const statusStr = String(sync.syncStatus || '').toLowerCase();
    return statusStr !== 'completed' && statusStr !== 'success' && statusStr !== 'successful';
  });

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <div className="flex items-center justify-center">
          <LoadingIcon className="h-6 w-6 text-blue-600 animate-spin mr-2" />
          <p className="text-gray-500">Loading syncs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Search and Filter Controls */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {/* Search Bar - Left */}
          <div className="relative w-80">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search by client name, ID, or sync type..."
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
            />
          </div>

          {/* Engine Filter - Right */}
          <div className="relative">
            <select
              value={selectedEngine}
              onChange={(e) => onEngineSelect(e.target.value)}
              className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
              style={{ backgroundImage: 'none' }}
            >
              <option value="all">All Engines</option>
              {engineOptions.map((engine) => (
                <option key={engine.id} value={engine.id}>
                  {engine.name}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <ChevronDownIcon className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      {activeSyncs.length === 0 ? (
        <div className="p-8 text-center">
          <p className="text-gray-500">No active syncs found.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  <span>ID</span>
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                  <span>NAME</span>
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                  <span>END TO END SYNC ID</span>
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  <span>TYPE</span>
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                  <span>START TIME</span>
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  <span>STATUS</span>
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  <span>HOURS ELAPSED</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {activeSyncs.map((sync, index) => (
                <tr key={sync.e2eSyncRunId || sync.clientId || index} className="hover:bg-gray-50">
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{sync.clientId || 'N/A'}</div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="relative group">
                      <div 
                        className="text-sm text-gray-900 truncate cursor-help"
                      >
                        {sync.clientName || 'N/A'}
                      </div>
                      {/* Custom tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-3 py-2 bg-gray-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                        <span className="font-medium">{sync.clientName || 'N/A'}</span>
                        <span className="text-gray-300"> - Plan: </span>
                        <span className="font-medium text-blue-300">{sync.subscriptionPlan || 'No Plan Info'}</span>
                        {/* Tooltip arrow */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div 
                      className="text-sm text-blue-600 hover:underline cursor-pointer truncate" 
                      title={sync.e2eSyncRunId}
                      onClick={() => onSyncIdClick && onSyncIdClick({ e2eSyncRunId: sync.e2eSyncRunId, clientId: sync.clientId })}
                    >
                      {sync.e2eSyncRunId || 'N/A'}
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900 truncate" title={sync.task}>{sync.task || 'N/A'}</div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{formatTime(sync.syncStartTime)}</div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(sync.syncStatus)}`}>
                        {sync.syncStatus ? 
                          sync.syncStatus.charAt(0).toUpperCase() + sync.syncStatus.slice(1) : 
                          'Unknown'
                        }
                      </span>
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{formatElapsedTime(sync.elapsedTime)}</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default SyncsList; 