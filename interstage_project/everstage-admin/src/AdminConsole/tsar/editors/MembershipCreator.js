import { observer } from "mobx-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTsarStore } from "../store";
import { Form, Modal, message } from "antd";
import TimeWindowUnavailabilityAlert from "../components/TimeWindowUnavailabilityAlert";
import { DEFAULT_TIME_WINDOW, SYSTEM_TIME_FORMAT } from "../constants";
import { convertToUtc, getCurrentUtc } from "../utils";
import JiraUrl from "./form-items/JiraUrl";
import Description from "./form-items/Description";
import { PlusSquareOutlined } from "@ant-design/icons";
import "./style.scss";
import Application from "./form-items/Application";
import Client from "./form-items/Client";
import Role from "./form-items/Role";
import Users from "./form-items/Users";
import Duration from "./form-items/Duration";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import RequiredMarkComponent from "./form-items/RequiredMarkComponent";

const MembershipCreateForm = observer(
  ({ initialValues, onFormInstanceReady }) => {
    const [form] = Form.useForm();
    const store = useTsarStore();
    const { email } = useAuthStore();
    const {
      currentApp,
      clients,
      relativeDurations,
      durationLimits,
      isDesktopWebApp,
      newMembershipFormConfig: formConfig,
      hasPermissionToCreateMembershipsForOthers,
    } = store;
    const [clientSearchValue, setClientSearchValue] = useState("");
    const [filteredClients, setFilteredClients] = useState(clients);
    const [showRangePicker, setShowRangePicker] = useState(false);
    const [timeWindow, setTimeWindow] = useState(DEFAULT_TIME_WINDOW); // [startsAt, endsAt]
    const [selectedRole, setSelectedRole] = useState(null);
    const abortController = useRef(null);

    useEffect(() => {
      onFormInstanceReady(form);
    }, [form, onFormInstanceReady]);

    useEffect(() => {
      form.resetFields();
    }, [currentApp, form]);

    /*****************  CLIENT, USERS and ROLES ****************/

    useEffect(() => {
      const filtered = clients.filter((client) =>
        client.name.toLowerCase().includes(clientSearchValue.toLowerCase())
      );
      setFilteredClients(filtered);
    }, [clients, clientSearchValue]);

    const handleClientSearch = (value) => {
      setClientSearchValue(value ?? "");
    };

    const handleclientSelect = (value) => {
      if (isDesktopWebApp) {
        form.setFieldsValue({ role: null });
        store.fetchDesktopWebClientRoles(value);
      }
      setClientSearchValue("");
    };

    const handleRoleChange = (value) => {
      setSelectedRole(value);
    };

    /*****************  TIME HANDLERS ****************/

    const handleRelativeDurationChange = (event) => {
      setShowRangePicker(event.target.value === "Custom");
    };

    const validateRelativeDuration = async (_, value) => {
      if (!value) {
        return Promise.reject("Please select the duration");
      }
      if (!relativeDurations.includes(value)) {
        return Promise.reject("Please select the valid duration");
      }
      if (value === "Custom") return Promise.resolve();

      return checkTimeWindowAvailability({
        relativeDuration: value,
        startsAt: null,
        endsAt: null,
      });
    };

    const validateCustomTimeWindow = (_, value) => {
      const now = getCurrentUtc();
      let [start, end] = timeWindow;
      if (!start) {
        start = now;
      }

      if (!end) {
        return Promise.reject(new Error("Please select an end date"));
      }

      if (start.isBefore(now)) {
        return Promise.reject(new Error("Start date can't be in the past"));
      }

      // check if the end date is within the limits
      if (
        end.isBefore(start.add(durationLimits.lowerLimitInSeconds, "second"))
      ) {
        return Promise.reject(
          new Error(
            `End date should be at least ${durationLimits.lowerLimitReadableText} after the start date`
          )
        );
      }
      if (
        end.isAfter(start.add(durationLimits.upperLimitInSeconds, "second"))
      ) {
        return Promise.reject(
          new Error(
            `End date should be within ${durationLimits.upperLimitReadableText} from the start date`
          )
        );
      }

      return checkTimeWindowAvailability({
        relativeDuration: "Custom",
        startsAt: start.format(SYSTEM_TIME_FORMAT),
        endsAt: end.format(SYSTEM_TIME_FORMAT),
      });
    };

    const checkTimeWindowAvailability = async ({
      relativeDuration,
      startsAt,
      endsAt,
    }) => {
      const clientFieldConfig = formConfig?.fields.find(
        (item) => item.key === "client"
      );
      const isClientFieldRequired =
        typeof clientFieldConfig.required === "function"
          ? clientFieldConfig.required(selectedRole)
          : clientFieldConfig.required;
      const clientId = form.getFieldValue("clientId");
      if (isClientFieldRequired && !clientId)
        return Promise.reject("Please select a client");

      const members = form.getFieldValue("members");
      if (!members) return Promise.reject("Please select users");

      const role = form.getFieldValue("role");

      try {
        abortController.current?.abort();
        abortController.current = new AbortController();
        const response = await store.checkTimeWindowAvailability(
          {
            clientId,
            role,
            members,
            relativeDuration,
            startsAt,
            endsAt,
          },
          abortController.current.signal
        );

        const responseData = await response.json();

        if (responseData.isTimeWindowAvailable) {
          return Promise.resolve();
        } else {
          return Promise.reject(<TimeWindowUnavailabilityAlert />);
        }
      } catch (error) {
        console.log(
          "Error in checkTimeWindowAvailability: ",
          error.name,
          error
        );
        return Promise.reject(
          "Failed to check time window availability. Please contact support."
        );
      }
    };

    const handleTimeWindowChange = (value, dateString) => {
      const start = dateString?.[0],
        end = dateString?.[1];

      setTimeWindow([convertToUtc(start), convertToUtc(end)]);
    };

    /* *********** RENDER ************* */
    return (
      <Form
        layout="vertical"
        form={form}
        name="membership_create_form"
        initialValues={{
          application: currentApp,
          timeWindow: timeWindow,
          ...(!hasPermissionToCreateMembershipsForOthers && {
            members: [email], // if the user doesn't have permission to create memberships for others, the users field will have its own email
          }),
          ...(initialValues ?? {}),
        }}
        requiredMark={RequiredMarkComponent}
      >
        {formConfig?.fields.map((item) => {
          const required =
            typeof item.required === "function"
              ? item.required(selectedRole)
              : item.required;
          const disabled = item.disabled ?? false;

          switch (item.key) {
            case "application":
              return <Application key={item.key} />;
            case "client":
              return (
                <Client
                  key={item.key}
                  required={required}
                  disabled={disabled}
                  clientSearchValue={clientSearchValue}
                  handleClientSearch={handleClientSearch}
                  handleclientSelect={handleclientSelect}
                  filteredClients={filteredClients}
                />
              );
            case "role":
              return (
                <Role
                  key={item.key}
                  required={required}
                  disabled={disabled}
                  onChange={handleRoleChange}
                />
              );
            case "users":
              return <Users key={item.key} disabled={disabled} />;
            case "duration":
              return (
                <Duration
                  key={item.key}
                  showRangePicker={showRangePicker}
                  handleRelativeDurationChange={handleRelativeDurationChange}
                  validateRelativeDuration={validateRelativeDuration}
                  validateCustomTimeWindow={validateCustomTimeWindow}
                  handleTimeWindowChange={handleTimeWindowChange}
                />
              );
            case "description":
              return <Description key={item.key} />;
            case "jiraUrl":
              return <JiraUrl key={item.key} />;
            default:
              return null;
          }
        })}
      </Form>
    );
  }
);

const MemshipshipEditor = ({
  initialValues,
  successCbk = () => {},
  cancelCbk = () => {},
}) => {
  const store = useTsarStore();
  const [isModalOpen, setIsModalOpen] = useState(true); // On first render, the modal is open
  const [loading, setLoading] = useState(false);
  const [formInstance, setFormInstance] = useState(null);

  const onFormInstanceReady = useCallback((instance) => {
    setFormInstance(instance);
  }, []);

  const onOK = async () => {
    let values = null;
    try {
      values = await formInstance.validateFields();
    } catch (error) {
      console.log("error:", error.name, error);
      message.info("Please fill all the required fields", 3);
      return;
    }

    setLoading(true);
    try {
      const requestData = {
        clientId: values.clientId,
        members: values.members,
        role: values.role,
        relativeDuration: values.relativeDuration,
        startsAt: values.timeWindow?.[0]?.format(SYSTEM_TIME_FORMAT),
        endsAt: values.timeWindow?.[1]?.format(SYSTEM_TIME_FORMAT),
        description: values.description,
        jiraUrl: values.jiraUrl,
      };
      const response = await store.createMembership(requestData);
      const responseData = await response.json();
      if (response.ok) {
        message.success(responseData.message, 5);
        setIsModalOpen(false);
        // call successCbk after the modal is closed
        setTimeout(() => {
          successCbk();
        }, 100);
      } else {
        throw new Error(
          responseData.message ||
            "Failed to create membership. Please contact support."
        );
      }
    } catch (error) {
      console.log("error:", error.name, error);
      message.error(error.message, 5);
    } finally {
      setLoading(false);
    }
  };

  const onCancel = () => {
    // close the modal
    setIsModalOpen(false);
    // call cancelCbk after the modal is closed
    setTimeout(() => {
      cancelCbk();
    }, 100);
  };

  return (
    <>
      <Modal
        wrapClassName="w-full h-full overflow-auto p-8"
        className="max-w-[40rem] max-h-full overflow-auto p-0 top-1/2 transform !-translate-y-1/2"
        width="70%"
        open={isModalOpen}
        title={
          <div className="font-black text-zinc-700 flex gap-2">
            <PlusSquareOutlined />
            <span>New Membership</span>
          </div>
        }
        destroyOnClose
        // OK
        okText="Create"
        okButtonProps={{
          loading,
          disabled: false, // TODO: add condition
          type: "primary",
          key: "submit",
          className:
            "border-none bg-gradient-to-r from-zinc-900 via-zinc-700 to-zinc-500 hover:from-black hover:to-black overflow-auto",
          autoFocus: true,
        }}
        onOk={onOK}
        // Cancel
        cancelText="Cancel"
        cancelButtonProps={{
          type: "link",
          danger: true,
          key: "cancel",
          className: "hover:border hover:border-red-500 ",
        }}
        onCancel={onCancel}
        // closeable only on clicking cancel
        maskClosable={false}
        closeIcon={null}
        keyboard={false}
      >
        <MembershipCreateForm
          initialValues={initialValues}
          onFormInstanceReady={onFormInstanceReady}
        />
      </Modal>
    </>
  );
};

export default observer(MemshipshipEditor);
