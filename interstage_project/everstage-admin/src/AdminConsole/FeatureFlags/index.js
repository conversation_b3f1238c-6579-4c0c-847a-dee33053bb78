import { observer } from "mobx-react";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Card, Search } from "Components";
import { useLoader } from "Components";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { Modal, Select } from "antd";

import { getAllFeatureFlags } from "Api/FeatureFlags";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
} from "@heroicons/react/outline";

// Debounce utility function
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const FeatureFlags = observer(() => {
  const [flags, setFlags] = useState([]);
  const [clients, setClients] = useState([]);
  const [totalClients, setTotalClients] = useState(0);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [modalContent, setModalContent] = useState(null);
  const [visibleFlags, setVisibleFlags] = useState([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  const { showHideLoader } = useLoader();
  const { accessToken } = useAuthStore();

  const fetchFeatureFlags = useCallback(
    async (page = currentPage, size = pageSize, search = "") => {
      try {
        setLoading(true);
        const response = await getAllFeatureFlags(
          accessToken,
          page,
          size,
          search
        );
        const data = await response.json();

        const { totalClients, totalPages } = data;
        const clientMap = new Map();
        const uniqueFlags = new Set();

        Object.entries(data.results).forEach(([clientKey, flagDict]) => {
          const [clientName, clientIdRaw] = clientKey
            .match(/(.*) \((\d+)\)$/)
            .slice(1);
          const clientId = parseInt(clientIdRaw, 10);

          const clientObj = {
            clientId,
            clientName,
            ...flagDict,
          };

          Object.keys(flagDict).forEach((flag) => uniqueFlags.add(flag));
          clientMap.set(clientId, clientObj);
        });

        setFlags(Array.from(uniqueFlags));
        setClients(Array.from(clientMap.values()));
        setTotalClients(totalClients);
        setTotalPages(totalPages);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    },
    [accessToken]
  );

  // Stable debounced version (created once)
  const debouncedFetch = useMemo(
    () =>
      debounce((term) => {
        fetchFeatureFlags(1, pageSize, term);
        setCurrentPage(1); // ensure page resets when searching
      }, 500),
    [fetchFeatureFlags, pageSize]
  );

  // Initial fetch
  useEffect(() => {
    fetchFeatureFlags();
  }, [fetchFeatureFlags]);

  // Handle search term changes
  useEffect(() => {
    if (searchTerm !== undefined) {
      debouncedFetch(searchTerm);
    }
  }, [searchTerm]);

  // Handle pagination changes
  useEffect(() => {
    if (!searchTerm) {
      fetchFeatureFlags(currentPage, pageSize);
    }
  }, [currentPage, pageSize, fetchFeatureFlags]);

  useEffect(() => {
    showHideLoader(loading);
  }, [loading]);

  const openDialog = (value) => {
    setModalContent(value);
    setModalIsOpen(true);
  };

  const closeDialog = () => {
    setModalIsOpen(false);
    setModalContent(null);
  };

  const getCellRenderer = (value) => {
    if (typeof value === "boolean") {
      return (
        <span
          className={`font-semibold px-2 py-1 rounded ${
            value === true
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          {value === true ? "Enabled" : "Disabled"}
        </span>
      );
    } else if (typeof value === "object" && value !== null) {
      return (
        <div
          onClick={() => openDialog(value)}
          className="text-blue-600 underline cursor-pointer truncate max-w-[200px]"
          title="Click to view"
        >
          {JSON.stringify(value).slice(0, 40)}...
        </div>
      );
    } else if (typeof value === "string" && value.trim() !== "") {
      return (
        <span className="font-semibold px-2 py-1 rounded bg-blue-100 text-blue-700">
          {value}
        </span>
      );
    } else {
      // Covers undefined, null, empty string, etc.
      return (
        <span className="font-semibold px-2 py-1 rounded bg-gray-100 text-gray-500">
          Not Set
        </span>
      );
    }
  };

  const columnDefs = useMemo(() => {
    const baseColumns = [
      {
        headerName: "Client ID",
        field: "clientId",
        minWidth: 100,
        pinned: "left",
      },
      {
        headerName: "Client Name",
        field: "clientName",
        minWidth: 200,
        sortable: true,
        filter: true,
        pinned: "left",
      },
    ];

    if (visibleFlags.length === 0) {
      const flagColumns = flags.map((flagName) => ({
        headerName: flagName,
        field: flagName,
        minWidth: 150,
        sortable: true,
        editable: false,
        cellRenderer: ({ value }) => getCellRenderer(value),
      }));

      return [...baseColumns, ...flagColumns];
    } else {
      const flagColumns = visibleFlags.map((flagName) => ({
        headerName: flagName,
        field: flagName,
        minWidth: 150,
        sortable: true,
        editable: false,
        cellRenderer: ({ value }) => getCellRenderer(value),
      }));

      return [...baseColumns, ...flagColumns];
    }
  }, [flags, visibleFlags]);

  const defaultColDef = useMemo(
    () => ({
      resizable: true,
      sortable: true,
      filter: true,
    }),
    []
  );

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="h3 font-semibold">Feature Flags</h1>
      </div>

      <Card className="p-4">
        <div className="flex items-end gap-6 w-full">
          {/* Search Clients */}
          <div className="flex-1 w-[200px]">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Clients
            </label>
            <Search
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Filter by Feature Flags */}
          <div className="flex-1 w-[200px]">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by
            </label>
            <Select
              mode="multiple"
              style={{ width: "100%" }}
              placeholder="Select feature flags to display"
              value={visibleFlags}
              onChange={setVisibleFlags}
              options={flags.map((flag) => ({ label: flag, value: flag }))}
              maxTagCount="responsive"
              className="w-full"
            />
          </div>
        </div>
      </Card>

      <Card className="flex flex-col h-full flex-1">
        <div
          className="ag-theme-alpine"
          style={{ height: "60vh", width: "100%" }}
        >
          <AgGridReact
            rowData={clients}
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            animateRows={true}
            suppressClickEdit={true}
            enableCellTextSelection={true}
            ensureDomOrder={true}
          />
        </div>
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 px-4 py-3 border-t mt-4">
          <div className="flex items-center gap-2 text-sm text-gray-700">
            <span>
              Showing {(currentPage - 1) * pageSize + 1} to{" "}
              {Math.min(currentPage * pageSize, totalClients)} of {totalClients}{" "}
              clients
            </span>
            <select
              value={pageSize}
              onChange={(e) => {
                const newSize = Number(e.target.value);
                setPageSize(newSize);
                setCurrentPage(1);
                fetchFeatureFlags(1, newSize, searchTerm);
              }}
              className="border rounded px-2 py-1 text-sm w-[150px]"
            >
              {[10, 20, 50, 100].map((size) => (
                <option key={size} value={size}>
                  {size} per page
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-3 text-sm">
            <ChevronDoubleLeftIcon
              className={`w-4 h-4 ${
                currentPage === 1 ? "text-gray-400" : "text-gray-700"
              }`}
              onClick={() => setCurrentPage(1)}
            />
            <ChevronLeftIcon
              className={`w-4 h-4 ${
                currentPage === 1 ? "text-gray-400" : "text-gray-700"
              }`}
              onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
            />
            <span className="text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            <ChevronRightIcon
              className={`w-4 h-4 ${
                currentPage === totalPages ? "text-gray-400" : "text-gray-700"
              }`}
              onClick={() =>
                currentPage < totalPages && setCurrentPage(currentPage + 1)
              }
            />
            <ChevronDoubleRightIcon
              className={`w-4 h-4 ${
                currentPage === totalPages ? "text-gray-400" : "text-gray-700"
              }`}
              onClick={() => setCurrentPage(totalPages)}
            />
          </div>
        </div>
      </Card>

      <Modal
        title="Feature Flag Detail"
        open={modalIsOpen}
        onCancel={closeDialog}
        onOk={closeDialog}
        okText="Close"
        cancelButtonProps={{ style: { display: "none" } }}
      >
        <pre className="bg-gray-100 p-4 rounded text-sm max-h-[400px] overflow-auto whitespace-pre-wrap">
          {JSON.stringify(modalContent, null, 2)}
        </pre>
      </Modal>
    </div>
  );
});

export default FeatureFlags;
